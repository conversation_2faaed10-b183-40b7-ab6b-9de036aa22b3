name: 'Validate PR title'

on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize

jobs:
  main:
    name: Validate PR title
    runs-on: ubuntu-latest
    steps:
      # Please look up the latest version from
      # https://github.com/amannn/action-semantic-pull-request/releases
      - uses: amannn/action-semantic-pull-request@v5.5.3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          # Configure which types are allowed.
          # Default: https://github.com/commitizen/conventional-commit-types
          types: |
            fix
            feat
            docs
            ci
            chore
          # Configure that a scope must always be provided.
          requireScope: false
          # Configure additional validation for the subject based on a regex.
          # This example ensures the subject starts with an uppercase character.
          subjectPattern: ^[A-Z].+$
          # If `subjectPattern` is configured, you can use this property to override
          # the default error message that is shown when the pattern doesn't match.
          # The variables `subject` and `title` can be used within the message.
          subjectPatternError: |
            The subject "{subject}" found in the pull request title "{title}"
            didn't match the configured pattern. Please ensure that the subject
            starts with an uppercase character.
          # For work-in-progress PRs you can typically use draft pull requests
          # from Github. However, private repositories on the free plan don't have
          # this option and therefore this action allows you to opt-in to using the
          # special "[WIP]" prefix to indicate this state. This will avoid the
          # validation of the PR title and the pull request checks remain pending.
          # Note that a second check will be reported if this is enabled.
          wip: true
          # When using "Squash and merge" on a PR with only one commit, GitHub
          # will suggest using that commit message instead of the PR title for the
          # merge commit, and it's easy to commit this by mistake. Enable this option
          # to also validate the commit message for one commit PRs.
          validateSingleCommit: false
