data "aws_caller_identity" "current" {}

moved {
  from = module.podpoint_installs_s3.aws_s3_bucket_lifecycle_configuration.this["noncurrent_object_expiration"]
  to   = module.podpoint_installs_s3.aws_s3_bucket_lifecycle_configuration.this[0]
}

moved {
  from = module.podpoint_installs_s3_access_logs.aws_s3_bucket_logging.access_log_bucket
  to   = module.podpoint_installs_s3.aws_s3_bucket_logging.this[0]
}

module "podpoint_installs_s3" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.1.2"

  bucket_prefix = "podpoint-installs-${var.environment}"

  versioning = {
    status = true
  }

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
      bucket_key_enabled = false
    }
  }

  lifecycle_rule = [{
    id      = "noncurrent_object_expiration"
    enabled = true
    noncurrent_version_expiration = {
      days = 30
    }
  }]

  logging = {
    target_bucket = module.podpoint_installs_s3_access_logs.s3_bucket_id
    target_prefix = "logs/"
  }
}

locals {
  log_bucket_name = "podpoint-installs-${var.environment}-access-logs"
}

moved {
  from = module.podpoint_installs_s3_access_logs.module.access_log_bucket.aws_s3_bucket_server_side_encryption_configuration.this
  to   = module.podpoint_installs_s3_access_logs.aws_s3_bucket_server_side_encryption_configuration.this[0]
}

moved {
  from = module.podpoint_installs_s3_access_logs.module.access_log_bucket.aws_s3_bucket_public_access_block.this
  to   = module.podpoint_installs_s3_access_logs.aws_s3_bucket_public_access_block.this[0]
}

moved {
  from = module.podpoint_installs_s3_access_logs.module.access_log_bucket.aws_s3_bucket.this
  to   = module.podpoint_installs_s3_access_logs.aws_s3_bucket.this[0]
}

moved {
  from = module.podpoint_installs_s3_access_logs.aws_s3_bucket_policy.access_log_bucket
  to   = module.podpoint_installs_s3_access_logs.aws_s3_bucket_policy.this[0]
}

module "podpoint_installs_s3_access_logs" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.1.2"

  bucket_prefix = "podpoint-installs-${var.environment}-access-logs"

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
      bucket_key_enabled = false
    }
  }

  attach_policy = true
  policy        = data.aws_iam_policy_document.logging_permissions.json
}

data "aws_iam_policy_document" "logging_permissions" {
  statement {
    sid    = "S3ServerAccessLogsPolicy"
    effect = "Allow"
    actions = [
      "s3:PutObject",
    ]

    principals {
      type        = "Service"
      identifiers = ["logging.s3.amazonaws.com"]
    }

    resources = [
      module.podpoint_installs_s3_access_logs.s3_bucket_arn,
      "${module.podpoint_installs_s3_access_logs.s3_bucket_arn}/*"
    ]

    condition {
      test     = "StringEquals"
      variable = "aws:SourceAccount"
      values   = [data.aws_caller_identity.current.account_id]
    }

    condition {
      test     = "ArnLike"
      variable = "aws:SourceArn"
      values   = [module.podpoint_installs_s3.s3_bucket_arn]
    }
  }
}
