resource "aws_security_group_rule" "api_load_balancer_ingress" {
  description              = "Permit ingress traffic from the load balancer."
  type                     = "ingress"
  from_port                = local.api_container_port
  to_port                  = local.api_container_port
  protocol                 = "tcp"
  source_security_group_id = module.alb.security_group_id
  security_group_id        = module.api.security_group_id
}

resource "aws_security_group_rule" "api_ingress_codebuild" {
  for_each = toset(var.codebuild_security_group_ids)

  description              = "Access permitted from codebuild for e2e testing."
  type                     = "ingress"
  from_port                = local.api_container_port
  to_port                  = local.api_container_port
  protocol                 = "tcp"
  source_security_group_id = each.value
  security_group_id        = module.api.security_group_id
}

resource "aws_security_group_rule" "queue_worker_ingress_codebuild" {
  for_each = toset(var.codebuild_security_group_ids)

  description              = "Access permitted from codebuild for e2e testing."
  type                     = "ingress"
  from_port                = local.queue_worker_container_port
  to_port                  = local.queue_worker_container_port
  protocol                 = "tcp"
  source_security_group_id = each.value
  security_group_id        = module.queue_worker.security_group_id
}

resource "aws_security_group_rule" "experience_cluster_ingress" {
  for_each = toset([module.api.security_group_id, module.queue_worker.security_group_id])

  description              = "Permit ingress traffic from the experience cluster."
  type                     = "ingress"
  from_port                = var.destination_cluster_port
  to_port                  = var.destination_cluster_port
  protocol                 = "tcp"
  source_security_group_id = each.value
  security_group_id        = var.experience_cluster_security_group_id
}

resource "aws_security_group_rule" "mobile_api_ingress" {
  description              = "Permit ingress traffic from the mobile api."
  type                     = "ingress"
  from_port                = 8102
  to_port                  = 8102
  protocol                 = "tcp"
  source_security_group_id = var.mobile_api_security_group_id
  security_group_id        = module.api.security_group_id
}
