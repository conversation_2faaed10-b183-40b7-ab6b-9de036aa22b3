resource "aws_secretsmanager_secret" "api" {
  name                    = format("aws/%s-api/secrets", local.identifier)
  description             = format("Used for storing secrets for %s-api.", local.identifier)
  kms_key_id              = aws_kms_key.backend.id
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "api" {
  secret_id = aws_secretsmanager_secret.api.id
  secret_string = jsonencode({
    commercial_db_admin_url      = format("postgresql://%s:%s@%s:%s/commercial", local.destination_cluster_admin_username, local.destination_cluster_password, var.destination_cluster_endpoint, var.destination_cluster_port),
    commercial_db_admin_ro_url   = format("postgresql://%s:%s@%s:%s/commercial", local.destination_cluster_admin_username, local.destination_cluster_password, var.destination_reader_endpoint, var.destination_cluster_port),
    commercial_db_service_url    = format("postgresql://%s:REPLACE_ME@%s:%s/commercial", local.destination_cluster_service_username, var.destination_cluster_endpoint, var.destination_cluster_port),
    commercial_db_service_ro_url = format("postgresql://%s:REPLACE_ME@%s:%s/commercial", local.destination_cluster_service_username, var.destination_reader_endpoint, var.destination_cluster_port),
    config_cat_sdk_key           = "MANUAL_INTERACTION_REQUIRED_REPLACE_ME"
    tesco_oidc_client_id         = "MANUAL_INTERACTION_REQUIRED_REPLACE_ME"
    tesco_oidc_client_secret     = "MANUAL_INTERACTION_REQUIRED_REPLACE_ME"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}
