locals {
  alarm_actions_enabled = true

  alb_alarm_dimensions = {
    LoadBalancer = module.alb.load_balancer_arn_suffix
    TargetGroup  = module.alb.target_group_arn_suffix[0]
  }

  cloudwatch_event_rule_event_patterns = {
    ecs-deployment-state-change-error = {
      source : ["aws.ecs"],
      detail-type : ["ECS Deployment State Change"],
      detail : { clusterArn = [module.ecs_cluster.arn], eventType = ["ERROR"] }
    }
    ecs-task-state-change-task-failed-to-start = {
      source : ["aws.ecs"],
      detail-type : ["ECS Task State Change"],
      detail : { clusterArn = [module.ecs_cluster.arn], stopCode = ["TaskFailedToStart"] }
    }
  }

  opsgenie_cw_topic_arn        = "arn:aws:sns:eu-west-1:${data.aws_caller_identity.current.account_id}:opsgenie-cw-experience-commercial"
  opsgenie_cw_events_topic_arn = "arn:aws:sns:eu-west-1:${data.aws_caller_identity.current.account_id}:opsgenie-cw-events-experience-commercial"
}

resource "aws_cloudwatch_metric_alarm" "alb_unhealthy_host_count" {
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-%s-alb-unhealthy-host-count", local.identifier, var.environment)
  dimensions = local.alb_alarm_dimensions

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "UnHealthyHostCount"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 0

  actions_enabled = local.alarm_actions_enabled
  alarm_actions   = [local.opsgenie_cw_topic_arn]
  ok_actions      = [local.opsgenie_cw_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "alb_http_code_elb_4xx_count" {
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-%s-alb-http-code-elb-4xx-count", local.identifier, var.environment)
  dimensions = local.alb_alarm_dimensions

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "HTTPCode_ELB_4XX_Count"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 100

  actions_enabled = local.alarm_actions_enabled
  alarm_actions   = [local.opsgenie_cw_topic_arn]
  ok_actions      = [local.opsgenie_cw_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "alb_http_code_target_4xx_count" {
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-%s-alb-http-code-target-4xx-count", local.identifier, var.environment)
  dimensions = local.alb_alarm_dimensions

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "HTTPCode_Target_4XX_Count"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 100

  actions_enabled = local.alarm_actions_enabled
  alarm_actions   = [local.opsgenie_cw_topic_arn]
  ok_actions      = [local.opsgenie_cw_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "alb_http_code_elb_5xx_count" {
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-%s-alb-http-code-elb-5xx-count", local.identifier, var.environment)
  dimensions = local.alb_alarm_dimensions

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "HTTPCode_ELB_5XX_Count"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 0

  actions_enabled = local.alarm_actions_enabled
  alarm_actions   = [local.opsgenie_cw_topic_arn]
  ok_actions      = [local.opsgenie_cw_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "alb_http_code_target_5xx_count" {
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-%s-alb-http-code-target-5xx-count", local.identifier, var.environment)
  dimensions = local.alb_alarm_dimensions

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "HTTPCode_Target_5XX_Count"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 0

  actions_enabled = local.alarm_actions_enabled
  alarm_actions   = [local.opsgenie_cw_topic_arn]
  ok_actions      = [local.opsgenie_cw_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "alb_target_response_time" {
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-%s-alb-target-response-time", local.identifier, var.environment)
  dimensions = local.alb_alarm_dimensions

  period              = 60
  evaluation_periods  = 10
  statistic           = "Average"
  metric_name         = "TargetResponseTime"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 1
  treat_missing_data  = "ignore"

  actions_enabled = local.alarm_actions_enabled
  alarm_actions   = [local.opsgenie_cw_topic_arn]
  ok_actions      = [local.opsgenie_cw_topic_arn]
}


resource "aws_cloudwatch_event_rule" "alarms" {
  for_each = local.cloudwatch_event_rule_event_patterns

  event_bus_name = "default"
  event_pattern  = jsonencode(each.value)
  name           = format("%s-%s", module.ecs_cluster.name, each.key)
  state          = local.alarm_actions_enabled == true ? "ENABLED" : "DISABLED"
}

resource "aws_cloudwatch_event_target" "alarms" {
  for_each = local.cloudwatch_event_rule_event_patterns

  arn            = local.opsgenie_cw_events_topic_arn
  event_bus_name = "default"
  rule           = aws_cloudwatch_event_rule.alarms[each.key].name
}
