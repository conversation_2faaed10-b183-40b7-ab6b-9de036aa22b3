moved {
  from = aws_route53_record.this
  to   = aws_route53_record.pod_point_com
}

resource "aws_route53_record" "pod_point_com" {
  provider = aws.route53

  zone_id         = local.pod_point_com_hosted_zone
  name            = local.pod_point_com_r53_record_name
  type            = "A"
  allow_overwrite = false

  alias {
    name                   = var.environment == "prod" ? aws_cloudfront_distribution.this[0].domain_name : module.alb.load_balancer_dns_name
    zone_id                = var.environment == "prod" ? aws_cloudfront_distribution.this[0].hosted_zone_id : module.alb.load_balancer_zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "podenergy_com" {
  provider = aws.route53

  zone_id         = local.podenergy_com_hosted_zone
  name            = local.podenergy_com_r53_record_name
  type            = "A"
  allow_overwrite = false

  alias {
    name                   = var.environment == "prod" ? aws_cloudfront_distribution.this[0].domain_name : module.alb.load_balancer_dns_name
    zone_id                = var.environment == "prod" ? aws_cloudfront_distribution.this[0].hosted_zone_id : module.alb.load_balancer_zone_id
    evaluate_target_health = true
  }
}
