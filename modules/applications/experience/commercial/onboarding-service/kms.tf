resource "aws_kms_key" "backend" {
  tags                    = local.tags
  description             = format("Used for encrypting secrets for %s.", local.identifier)
  policy                  = data.aws_iam_policy_document.backend.json
  deletion_window_in_days = 7
}

resource "aws_kms_alias" "backend" {
  name          = format("alias/%s/backend", local.identifier)
  target_key_id = aws_kms_key.backend.key_id
}
