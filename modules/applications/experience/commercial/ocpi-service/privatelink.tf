module "privatelink_client_connectivity_commands_api" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws//modules/privatelink-client"
  version = "2.4.0"
  tags    = local.tags

  client_ingress_rules = {
    port_80_connectivity_commands_api_service = {
      description           = "Connectivity permitted from ${local.identifier} to connectivity-commands-api."
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.api.security_group_id
    }
    permit_vpn_access = {
      description = "AWS Client VPN"
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cidrs  = ["**********/22"]
    }
  }

  target = {
    name         = "${local.identifier}-connectivity-commands-api"
    service_name = var.privatelink_client_service_names.connectivity_commands_api
  }

  vpc_id     = var.vpc_id
  subnet_ids = var.ecs_private_subnets_ids
}

module "privatelink_client_connectivity_status_api" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws//modules/privatelink-client"
  version = "2.4.0"
  tags    = local.tags

  client_ingress_rules = {
    port_80_connectivity_status_api_service = {
      description           = "Connectivity permitted from ${local.identifier}-api to connectivity-status-api."
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.api.security_group_id
    }
    port_80_connectivity_status_api_service_queue_worker = {
      description           = "Connectivity permitted from ${local.identifier}-queue-worker to connectivity-status-api."
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.queue_worker.security_group_id
    }
    permit_vpn_access = {
      description = "AWS Client VPN"
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cidrs  = ["**********/22"]
    }
  }

  target = {
    name         = "${local.identifier}-connectivity-status-api"
    service_name = var.privatelink_client_service_names.connectivity_status_api
  }

  vpc_id     = var.vpc_id
  subnet_ids = var.ecs_private_subnets_ids
}
