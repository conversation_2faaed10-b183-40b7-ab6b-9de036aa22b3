locals {
  api_environment = [
    {
      name : "CONNECTIVITY_COMMANDS_API_BASE_URL"
      value : "http://${module.privatelink_client_connectivity_commands_api.dns_entry[0].dns_name}"
    },
    {
      name : "CONNECTIVITY_STATUS_API_BASE_URL"
      value : "http://${module.privatelink_client_connectivity_status_api.dns_entry[0].dns_name}"
    },
    {
      name : "DATA_PLATFORM_API_BASE_URL"
      value : "http://data-platform-api.destination.cluster.com:2830"
    },
    {
      name : "DB_DATABASE"
      value : "podpoint"
    },
    {
      name : "DB_HOST"
      value : var.podadmin_host
    },
    {
      name : "DB_HOST_RO"
      value : var.podadmin_host_ro
    },
    {
      name : "DB_PORT"
      value : "3306"
    },
    {
      name : "EVENTS_TOPIC_ARN",
      value : aws_sns_topic.ocpi_events.arn
    },
    {
      name : "START_SESSION_COMMANDS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-start-session-commands", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "STOP_SESSION_COMMANDS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-stop-session-commands", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "SUPPORT_TOOL_API_BASE_URL"
      value : "http://api.support-tool.cluster.com:7102"
    },
    {
      name : "UNLOCK_CONNECTOR_COMMANDS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-unlock-connector-commands", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    }
  ]

  api_secrets = [
    {
      name : "COMMERCIAL_DB_ADMIN_URL"
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "commercial_db_admin_url")
    },
    {
      name : "COMMERCIAL_DB_ADMIN_RO_URL"
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "commercial_db_admin_ro_url")
    },
    {
      name : "COMMERCIAL_DB_URL"
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "commercial_db_service_url")
    },
    {
      name : "COMMERCIAL_DB_RO_URL"
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "commercial_db_service_ro_url")
    },
    {
      name : "DB_PASSWORD"
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "podadmin_password")
    },
    {
      name : "DB_USERNAME"
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "podadmin_username")
    },
    {
      name : "PRISMA_FIELD_ENCRYPTION_KEY"
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "prisma_field_encryption_key")
    }
  ]

  queue_worker_environment = concat(local.api_environment, [
    {
      name : "CHARGE_NOTIFICATIONS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-charge-notifications", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "CHARGER_STATUS_UPDATES_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-charger-status-updates", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "INDEX_LOCATIONS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-index-locations", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "PUSH_CDRS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-push-cdrs", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "PUSH_EVSE_UPDATES_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-push-evse-updates", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "PUSH_LOCATIONS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-push-locations", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "PUSH_TARIFFS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-push-tariffs", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "REFRESH_STATUSES_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-refresh-statuses", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "START_SESSION_COMMANDS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-start-session-commands", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "STOP_SESSION_COMMANDS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-stop-session-commands", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "UNLOCK_CONNECTOR_COMMANDS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-unlock-connector-commands", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    }
  ])

  queue_worker_secrets = concat(local.api_secrets, [])

  scheduled_tasks_environment = concat(local.api_environment, [
    {
      name : "INDEX_LOCATIONS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-index-locations", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "PUSH_EVSE_UPDATES_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-push-evse-updates", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "PUSH_LOCATIONS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-push-locations", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "PUSH_TARIFFS_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-push-tariffs", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "REFRESH_STATUSES_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-refresh-statuses", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
  ])

  scheduled_tasks_secrets = concat(local.api_secrets, [])
}

resource "aws_service_discovery_service" "api" {
  name = "api"
  dns_config {
    namespace_id = aws_service_discovery_private_dns_namespace.this.id
    dns_records {
      ttl  = 0
      type = "A"
    }
  }
  health_check_custom_config {
    failure_threshold = 1
  }
}

resource "aws_service_discovery_service" "queue_worker" {
  name = "queue-worker"
  dns_config {
    namespace_id = aws_service_discovery_private_dns_namespace.this.id
    dns_records {
      ttl  = 0
      type = "A"
    }
  }
  health_check_custom_config {
    failure_threshold = 1
  }
}

module "ecs_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "12.7.4"
  tags    = local.tags

  identifier         = local.identifier
  repo_names         = ["experience"]
  container_insights = "enhanced"

  additional_github_ci_policy_statements = [{
    sid    = "AllowDescribeSecurityGroupsForECSTaskRunGitHubAction"
    effect = "Allow"
    actions = [
      "ec2:DescribeSecurityGroups"
    ]
    resources = [
      "*"
    ]
  }]
}

module "api" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "12.7.4"
  tags    = local.tags

  attach_custom_ecs_task_execution_iam_policy = true
  attach_custom_ecs_task_iam_policy           = true
  capacity_fargate_base                       = var.use_spot_capacity ? 0 : var.api_desired_task_count
  capacity_fargate_spot_base                  = var.use_spot_capacity ? var.api_desired_task_count : 0
  capacity_fargate_spot_weight                = 1
  capacity_fargate_weight                     = 0
  cluster_arn                                 = module.ecs_cluster.arn
  cluster_name                                = module.ecs_cluster.name
  cpu                                         = 1024
  ecs_task_custom_policy                      = data.aws_iam_policy_document.ecs_task_custom_policy.json
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.ecs_task_execution_custom_policy.json
  enable_auto_scaling                         = true
  force_new_deployment                        = true
  identifier                                  = format("%s-api", local.identifier)
  memory                                      = 2048
  pipeline_role_name                          = module.ecs_cluster.github_role_name
  scaling_max_capacity                        = var.api_desired_task_count * 2
  scaling_min_capacity                        = var.api_desired_task_count
  service_registry                            = { registry_arn : aws_service_discovery_service.api.arn }
  service_type                                = "rolling"
  subnet_ids                                  = var.ecs_private_subnets_ids
  vpc_id                                      = var.vpc_id

  container_definitions = [
    {
      "command" : [],
      "cpu" : 0,
      "environment" : local.api_environment,
      "essential" : true,
      "healthCheck" : {
        "command" : ["CMD-SHELL", "wget -qO - http://localhost:${local.api_container_port}/health || exit 1"],
        "interval" : 30,
        "retries" : 3,
        "startPeriod" : 60,
        "timeout" : 5
      }
      "image" : "TERRAFORM_IMAGE_PLACEHOLDER",
      "linuxParameters" : { "initProcessEnabled" : false },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/${format("%s-api", local.identifier)}",
          "awslogs-region" : data.aws_region.current.name,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "name" : format("%s-api", local.identifier),
      "networkMode" : "awsvpc",
      "portMappings" : [
        {
          "containerPort" : local.api_container_port,
          "hostPort" : local.api_container_port
        }
      ],
      "secrets" : local.api_secrets,
    },
    {
      "command" : ["--config=/etc/ecs/ecs-xray.yaml"],
      "cpu" : 0,
      "environment" : [],
      "essential" : false,
      "healthCheck" : {
        "command" : ["CMD-SHELL", "exit 0"],
        "interval" : 30,
        "retries" : 3,
        "startPeriod" : 60,
        "timeout" : 5
      }
      "image" : "public.ecr.aws/aws-observability/aws-otel-collector:v0.40.0",
      "linuxParameters" : { "initProcessEnabled" : false },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/${format("%s-api", local.identifier)}/ecs-aws-otel-sidecar-collector",
          "awslogs-create-group" : "true",
          "awslogs-region" : "eu-west-1",
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "name" : "aws-otel-collector",
      "networkMode" : "awsvpc",
      "portMappings" : [],
      "secrets" : [],
    }
  ]

  load_balancing_configuration = [
    {
      target_group_arn = module.alb.target_group_arns[0]
      container_name   = format("%s-api", local.identifier)
      container_port   = local.api_container_port
    }
  ]

  scheduled_scaling = var.environment != "prod" ? {
    scale_in_schedules = [{
      schedule     = "cron(0 18 ? * MON-FRI *)"
      min_capacity = 0
      max_capacity = 0
    }]
    scale_out_schedules = [{
      schedule     = "cron(0 8 ? * MON-FRI *)"
      min_capacity = var.api_desired_task_count
      max_capacity = var.api_desired_task_count * 2
    }]
  } : null
}

module "queue_worker" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "12.7.4"
  tags    = local.tags

  attach_custom_ecs_task_execution_iam_policy   = true
  attach_custom_ecs_task_iam_policy             = true
  capacity_fargate_base                         = var.use_spot_capacity ? 0 : var.queue_worker_desired_task_count
  capacity_fargate_spot_base                    = var.use_spot_capacity ? var.queue_worker_desired_task_count : 0
  capacity_fargate_spot_weight                  = 1
  capacity_fargate_weight                       = 0
  cluster_arn                                   = module.ecs_cluster.arn
  cluster_name                                  = module.ecs_cluster.name
  cpu                                           = 512
  default_step_scaling_high_cpu_usage_stat      = "Average"
  default_step_scaling_high_cpu_usage_threshold = 80
  ecs_task_custom_policy                        = data.aws_iam_policy_document.ecs_task_custom_policy.json
  ecs_task_execution_custom_policy              = data.aws_iam_policy_document.ecs_task_execution_custom_policy.json
  enable_auto_scaling                           = true
  force_new_deployment                          = true
  identifier                                    = format("%s-queue-worker", local.identifier)
  memory                                        = 1024
  pipeline_role_name                            = module.ecs_cluster.github_role_name
  scaling_max_capacity                          = var.queue_worker_desired_task_count * 2
  scaling_min_capacity                          = var.queue_worker_desired_task_count
  service_registry                              = { registry_arn : aws_service_discovery_service.queue_worker.arn }
  service_type                                  = "rolling"
  subnet_ids                                    = var.ecs_private_subnets_ids
  vpc_id                                        = var.vpc_id

  container_definitions = [
    {
      "command" : [],
      "cpu" : 0,
      "environment" : local.queue_worker_environment,
      "essential" : true,
      "healthCheck" : {
        "command" : ["CMD-SHELL", "wget -qO - http://localhost:${local.queue_worker_container_port}/health || exit 1"],
        "interval" : 30,
        "retries" : 3,
        "startPeriod" : 60,
        "timeout" : 5
      }
      "image" : "TERRAFORM_IMAGE_PLACEHOLDER",
      "linuxParameters" : { "initProcessEnabled" : false },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/${format("%s-queue-worker", local.identifier)}",
          "awslogs-region" : data.aws_region.current.name,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "name" : format("%s-queue-worker", local.identifier),
      "networkMode" : "awsvpc",
      "portMappings" : [
        {
          "containerPort" : local.queue_worker_container_port,
          "hostPort" : local.queue_worker_container_port
        }
      ],
      "secrets" : local.queue_worker_secrets,
    },
    {
      "command" : ["--config=/etc/ecs/ecs-xray.yaml"],
      "cpu" : 0,
      "environment" : [],
      "essential" : false,
      "healthCheck" : {
        "command" : ["CMD-SHELL", "exit 0"],
        "interval" : 30,
        "retries" : 3,
        "startPeriod" : 60,
        "timeout" : 5
      }
      "image" : "public.ecr.aws/aws-observability/aws-otel-collector:v0.40.0",
      "linuxParameters" : { "initProcessEnabled" : false },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/${format("%s-queue-worker", local.identifier)}/ecs-aws-otel-sidecar-collector",
          "awslogs-create-group" : "true",
          "awslogs-region" : "eu-west-1",
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "name" : "aws-otel-collector",
      "networkMode" : "awsvpc",
      "portMappings" : [],
      "secrets" : [],
    }
  ]

  scheduled_scaling = var.environment != "prod" ? {
    scale_in_schedules = [{
      schedule     = "cron(0 18 ? * MON-FRI *)"
      min_capacity = 0
      max_capacity = 0
    }]
    scale_out_schedules = [{
      schedule     = "cron(0 8 ? * MON-FRI *)"
      min_capacity = var.queue_worker_desired_task_count
      max_capacity = var.queue_worker_desired_task_count * 2
    }]
  } : null
}
