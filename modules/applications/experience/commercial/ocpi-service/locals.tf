locals {
  identifier = "ocpi-service"

  api_container_port          = 6102
  pod_point_com_domain_name   = "pod-point.com"
  pod_point_com_hosted_zone   = "ZI1YF8KE9MFAW"
  podenergy_com_domain_name   = "podenergy.com"
  podenergy_com_hosted_zone   = "Z0275178UFD5TRC1H8T"
  queue_worker_container_port = 6103

  pod_point_com_r53_record_name = var.environment == "prod" ? format("roaming.%s", local.pod_point_com_domain_name) : format("roaming-%s.%s", var.environment, local.pod_point_com_domain_name)
  podenergy_com_r53_record_name = var.environment == "prod" ? format("roaming.%s", local.podenergy_com_domain_name) : format("roaming-%s.%s", var.environment, local.podenergy_com_domain_name)

  destination_cluster_admin_username   = "postgres"
  destination_cluster_service_username = "ocpi_service"
  destination_cluster_password         = jsondecode(data.aws_secretsmanager_secret_version.destination_cluster_password.secret_string)["password"]

  charge_notifications_sns_topic_name = "charge-notifications"

  charger_status_updates_account_id     = var.charger_status_updates_account_id
  charger_status_updates_sns_topic_name = "cs-status-updates"

  tags = merge(data.aws_default_tags.current.tags, {
    "pp:owner"   = "experience:workplace-public"
    "pp:service" = local.identifier
  })
}
