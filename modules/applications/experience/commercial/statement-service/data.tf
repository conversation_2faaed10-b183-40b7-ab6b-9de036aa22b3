data "aws_caller_identity" "current" {}

data "aws_default_tags" "current" {}

data "aws_region" "current" {}

data "aws_secretsmanager_secret_version" "alb_oidc_config" {
  provider  = aws.shared-services
  secret_id = var.oidc_config_secret_id
}

data "aws_secretsmanager_secret_version" "destination_cluster_password" {
  secret_id = var.admin_user_secret_manager_arn
}

data "aws_secretsmanager_secret_version" "api" {
  secret_id = aws_secretsmanager_secret.api.id

  depends_on = [aws_secretsmanager_secret_version.api]
}

data "aws_secretsmanager_secret_version" "webapp" {
  secret_id = aws_secretsmanager_secret.webapp.id

  depends_on = [aws_secretsmanager_secret_version.webapp]
}
