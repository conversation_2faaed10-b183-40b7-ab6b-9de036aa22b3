variable "admin_user_secret_manager_arn" {
  description = "ARN of the secret containing the admin password for the RDS cluster to be used by the service."
  type        = string
}

variable "alb_public_subnets" {
  description = "The public subnets to assign to the ALB."
  type        = list(string)
}

variable "api_database_cluster_resource_id" {
  description = "Cluster resource id for the RDS cluster to be used by the service."
  type        = string
}

variable "api_desired_task_count" {
  description = "API desired task count."
  type        = number
}

variable "api_source_security_group_ids" {
  type        = list(string)
  description = "List of the security group ids that need to request data from the API"
}

variable "cloudfront_enable_logging" {
  description = "Whether to enable logging for the cloudfront distribution."
  type        = bool
  default     = false
}

variable "cloudfront_realtime_metrics" {
  description = "Whether to realtime metrics for the cloudfront distribution."
  type        = string
  default     = "Disabled"
}

variable "codebuild_security_group_ids" {
  description = "Security group ids of codebuild for post deploy testing."
  type        = list(string)
}

variable "destination_cluster_endpoint" {
  description = "The DNS address of the RDS instance to be used by the service."
  type        = string
}

variable "destination_cluster_port" {
  description = "The port used by the RDS cluster to be used by the service."
  type        = string
}

variable "destination_reader_endpoint" {
  description = "The DNS address of the RDS replica instance to be used by the service."
  type        = string
}

variable "ecs_private_subnets_ids" {
  description = "The public subnets to be used by ECS services and tasks."
  type        = list(string)
}

variable "environment" {
  type        = string
  description = "The name of the environment. This will be used when it is necessary to namespace resources (e.g. S3 buckets)."
}

variable "experience_cluster_security_group_id" {
  description = "Security group id for the RDS cluster to be used by the service."
  type        = string
}

variable "oidc_config_secret_id" {
  description = "The ID of the secret containing the OIDC configuration for the load balancer."
  type        = string
}

variable "queue_worker_desired_task_count" {
  description = "Queue worker desired task count."
  type        = number
}

variable "route53_record_name" {
  type        = string
  description = "Route 53 record name for this service."
  default     = null
}

variable "stripe_exclusive_tax_rate_ids" {
  description = "The Stripe exclusive tax rate IDs to be used by the service."
  type        = string
  default     = "txr_1PBfpZFfRqcfiHYMBJBDrZO8"
}

variable "stripe_inclusive_tax_rate_ids" {
  description = "The Stripe inclusive tax rate IDs to be used by the service."
  type        = string
  default     = "txr_1PFuGoFfRqcfiHYMKzD4YpAb"
}

variable "use_spot_capacity" {
  description = "Whether or not to use spot capacity."
  type        = bool
}

variable "vpc_id" {
  description = "The ID of the VPC to be used by the service."
  type        = string
}

variable "webapp_desired_task_count" {
  description = "Webapp desired task count."
  type        = number
}
