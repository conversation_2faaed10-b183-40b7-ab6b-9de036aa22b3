data "aws_iam_policy_document" "ecs_task_execution_custom_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend.arn]
  }
  statement {
    sid     = "RetrieveSecretManagerSecretValues"
    actions = ["secretsmanager:GetSecretValue"]
    resources = [
      aws_secretsmanager_secret.api.arn,
      aws_secretsmanager_secret.webapp.arn,
    ]
  }
  statement {
    sid     = "AllowCreateLogGroup"
    actions = ["logs:CreateLogGroup"]
    resources = [
      "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/ecs/${format("%s-*", local.identifier)}/ecs-aws-otel-sidecar-collector:log-stream:",
    ]
  }
}

data "aws_iam_policy_document" "webapp_ecs_task_container_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend.arn]
  }
  statement {
    sid = "AllowSessionManager"
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
    ]
    resources = ["*"]
  }
}

data "aws_iam_policy_document" "ecs_task_custom_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend.arn]
  }
  statement {
    sid = "AllowSessionManager"
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
    ]
    resources = ["*"]
  }
  statement {
    sid = "AllowAPIInvoke"

    actions = [
      "execute-api:Invoke"
    ]

    resources = [
      "*",
    ]
  }
  statement {
    sid = "AllowRDSConnect"

    actions = [
      "rds-db:connect"
    ]

    resources = ["arn:aws:rds-db:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:dbuser:${var.api_database_cluster_resource_id}/statements", ]
  }

  statement {
    sid = "AllowAccessToSqsQueues"
    actions = [
      "sqs:DeleteMessage",
      "sqs:ReceiveMessage",
      "sqs:SendMessage"
    ]
    resources = [
      aws_sqs_queue.statements_to_generate.arn,
      aws_sqs_queue.statements_to_send.arn
    ]
  }
  statement {
    sid = "AllowAccessToDocumentsS3Bucket"
    actions = [
      "s3:DeleteObject",
      "s3:GetObject",
      "s3:ListBucket",
      "s3:PutObject",
    ]
    resources = [
      aws_s3_bucket.generated_documents.arn,
      format("%s/*", aws_s3_bucket.generated_documents.arn)
    ]
  }

  statement {
    sid = "SendEmail"

    actions = [
      "ses:SendEmail",
      "ses:SendRawEmail",
    ]

    resources = [
      "arn:aws:ses:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:configuration-set/*",
      "arn:aws:ses:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:identity/*",
    ]
  }

  statement {
    sid = "SuppressedDestinations"

    actions = [
      "ses:DeleteSuppressedDestination",
      "ses:GetSuppressedDestination",
      "ses:ListSuppressedDestinations",
    ]

    resources = [
      "*",
    ]
  }

}

data "aws_iam_policy_document" "backend" {
  statement {
    sid       = "EnableAccountAdministration"
    actions   = ["kms:*"]
    resources = ["*"]

    principals {
      type        = "AWS"
      identifiers = concat([format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id)])
    }
  }

  statement {
    sid       = "AllowKMSDecrypt"
    actions   = ["kms:Decrypt"]
    resources = ["*"]

    principals {
      type = "AWS"
      identifiers = [
        module.api.execution_role_arn,
        module.api.task_role_arn,
        module.queue_worker.execution_role_arn,
        module.queue_worker.task_role_arn,
        module.webapp.execution_role_arn,
        module.webapp.task_role_arn,
        module.scheduled_tasks["create-transfers"].execution_role_arn,
        module.scheduled_tasks["create-transfers"].task_role_arn,
        module.scheduled_tasks["enqueue-statements"].execution_role_arn,
        module.scheduled_tasks["enqueue-statements"].task_role_arn,
        module.scheduled_tasks["generate-work-items"].execution_role_arn,
        module.scheduled_tasks["generate-work-items"].task_role_arn,
        module.scheduled_tasks["mark-work-items-ready"].execution_role_arn,
        module.scheduled_tasks["mark-work-items-ready"].task_role_arn,
        module.scheduled_tasks["update-configs"].execution_role_arn,
        module.scheduled_tasks["update-configs"].task_role_arn,
        module.scheduled_tasks["update-deferred"].execution_role_arn,
        module.scheduled_tasks["update-deferred"].task_role_arn,
      ]
    }
  }

  statement {
    sid = "AllowKMSDecryptAdminBreakGlass"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    condition {
      test     = "StringLike"
      variable = "aws:PrincipalArn"
      values   = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"]
    }
  }
}

resource "aws_iam_role_policy_attachment" "webapp_role_policy_attachments" {
  for_each = toset([
    "AmazonSSMManagedInstanceCore",
    "CloudWatchAgentServerPolicy"
  ])
  role       = module.webapp.task_role_name
  policy_arn = format("arn:aws:iam::aws:policy/%s", each.key)
}
