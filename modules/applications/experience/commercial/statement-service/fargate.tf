locals {
  api_environment = [
    {
      name : "DATA_PLATFORM_API_BASE_URL",
      value : "http://data-platform-api.destination.cluster.com:2830"
    },
    {
      name : "S3_GENERATED_DOCUMENTS_BUCKET_NAME",
      value : format("%s-generated-documents-%s", local.identifier, var.environment)
    },
    {
      name : "SITE_ADMIN_API_URL"
      value : "http://internal-site-admin-api.destination.cluster.com:4102"
    },
    {
      name : "STATEMENTS_TO_GENERATE_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-statements-to-generate", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "STATEMENTS_TO_SEND_QUEUE_URL",
      value : format("https://sqs.%s.amazonaws.com/%s/%s-statements-to-send", data.aws_region.current.name, data.aws_caller_identity.current.account_id, local.identifier)
    },
    {
      name : "STATEMENT_PDF_GENERATION_URL"
      value : "http://webapp.${aws_service_discovery_private_dns_namespace.this.name}:${local.webapp_container_port}/api/statements/{{statementId}}/pdf?filename={{filename}}"
    },
    {
      name : "STATEMENT_SERVICE_WEBAPP_URL"
      value : "http://webapp.${aws_service_discovery_private_dns_namespace.this.name}:${local.webapp_container_port}"
    },
    {
      name : "STRIPE_EXCLUSIVE_TAX_RATE_IDS"
      value : var.stripe_exclusive_tax_rate_ids
    },
    {
      name : "STRIPE_INCLUSIVE_TAX_RATE_IDS"
      value : var.stripe_inclusive_tax_rate_ids
    },
  ]
  api_secrets = [
    {
      name : "COMMERCIAL_DB_ADMIN_URL"
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "commercial_db_admin_url")
    },
    {
      name : "COMMERCIAL_DB_ADMIN_RO_URL"
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "commercial_db_admin_ro_url")
    },
    {
      name : "COMMERCIAL_DB_URL",
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "commercial_db_service_url")
    },
    {
      name : "COMMERCIAL_DB_RO_URL"
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "commercial_db_service_ro_url")
    },
    {
      name : "CONFIGCAT_SDK_KEY",
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "config_cat_sdk_key")
    },
    {
      name : "STRIPE_API_KEY",
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "stripe_api_key")
    },
    {
      name : "STRIPE_WEBHOOK_SIGNING_SECRET",
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "stripe_webhook_signing_secret")
    },
    {
      name : "STRIPE_CONNECT_WEBHOOK_SIGNING_SECRET",
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "stripe_connect_webhook_signing_secret")
    },
    {
      name : "STRIPE_CLIENT_ID"
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.api.arn, "stripe_client_id")
    },
  ]
  webapp_environment = [
    {
      name : "STATEMENT_SERVICE_API_URL"
      value : "http://api.${aws_service_discovery_private_dns_namespace.this.name}:${local.api_container_port}"
    },
    {
      name : "SITE_ADMIN_API_URL"
      value : "http://internal-site-admin-api.destination.cluster.com:4102"
    },
  ]
  webapp_secrets = [
    {
      name : "CONFIGCAT_SDK_KEY"
      valueFrom : format("%s:%s::", aws_secretsmanager_secret.webapp.arn, "configcat_sdk_key")
    },
  ]
  queue_worker_environment = concat(local.api_environment, [

  ])

  queue_worker_secrets = concat(local.api_secrets, [])
}


resource "aws_service_discovery_service" "webapp" {
  name = "webapp"
  dns_config {
    namespace_id = aws_service_discovery_private_dns_namespace.this.id
    dns_records {
      ttl  = 0
      type = "A"
    }
  }
  health_check_custom_config {
    failure_threshold = 1
  }
}

resource "aws_service_discovery_service" "api" {
  name = "api"
  dns_config {
    namespace_id = aws_service_discovery_private_dns_namespace.this.id
    dns_records {
      ttl  = 0
      type = "A"
    }
  }
  health_check_custom_config {
    failure_threshold = 1
  }
}

resource "aws_service_discovery_service" "queue_worker" {
  name = "queue-worker"
  dns_config {
    namespace_id = aws_service_discovery_private_dns_namespace.this.id
    dns_records {
      ttl  = 0
      type = "A"
    }
  }
  health_check_custom_config {
    failure_threshold = 1
  }
}

module "ecs_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "12.7.4"
  tags    = local.tags

  identifier         = local.identifier
  repo_names         = ["experience"]
  container_insights = "enabled"

  additional_github_ci_policy_statements = [{
    sid    = "AllowDescribeSecurityGroupsForECSTaskRunGitHubAction"
    effect = "Allow"
    actions = [
      "ec2:DescribeSecurityGroups"
    ]
    resources = [
      "*"
    ]
  }]
}

module "webapp" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "12.7.4"
  tags    = local.tags

  attach_custom_ecs_task_execution_iam_policy = true
  attach_custom_ecs_task_iam_policy           = true
  capacity_fargate_base                       = var.use_spot_capacity ? 0 : var.webapp_desired_task_count
  capacity_fargate_spot_base                  = var.use_spot_capacity ? var.webapp_desired_task_count : 0
  capacity_fargate_spot_weight                = 1
  capacity_fargate_weight                     = 0
  cluster_arn                                 = module.ecs_cluster.arn
  cluster_name                                = module.ecs_cluster.name
  cpu                                         = 1024
  ecs_task_custom_policy                      = data.aws_iam_policy_document.webapp_ecs_task_container_policy.json
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.ecs_task_execution_custom_policy.json
  enable_auto_scaling                         = true
  force_new_deployment                        = false
  identifier                                  = format("%s-webapp", local.identifier)
  memory                                      = 2048
  pipeline_role_name                          = module.ecs_cluster.github_role_name
  scaling_max_capacity                        = var.webapp_desired_task_count * 2
  scaling_min_capacity                        = var.webapp_desired_task_count
  service_registry                            = { registry_arn : aws_service_discovery_service.webapp.arn }
  service_type                                = "rolling"
  subnet_ids                                  = var.ecs_private_subnets_ids
  vpc_id                                      = var.vpc_id

  container_definitions = [
    {
      "command" : [],
      "cpu" : 0,
      "environment" : local.webapp_environment,
      "essential" : true,
      "healthCheck" : {
        "command" : ["CMD-SHELL", "wget -qO - http://localhost:${local.webapp_container_port} || exit 1"],
        "interval" : 30,
        "retries" : 3,
        "startPeriod" : 60,
        "timeout" : 5
      }
      "image" : "TERRAFORM_IMAGE_PLACEHOLDER",
      "linuxParameters" : { "initProcessEnabled" : false },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/${format("%s-webapp", local.identifier)}",
          "awslogs-region" : data.aws_region.current.name,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "name" : format("%s-webapp", local.identifier),
      "networkMode" : "awsvpc",
      "portMappings" : [
        {
          "containerPort" : local.webapp_container_port,
          "hostPort" : local.webapp_container_port
        }
      ],
      "secrets" : local.webapp_secrets,
    },
    {
      "command" : ["--config=/etc/ecs/ecs-xray.yaml"],
      "cpu" : 0,
      "environment" : [],
      "essential" : false,
      "healthCheck" : {
        "command" : ["CMD-SHELL", "exit 0"],
        "interval" : 30,
        "retries" : 3,
        "startPeriod" : 60,
        "timeout" : 5
      }
      "image" : "public.ecr.aws/aws-observability/aws-otel-collector:v0.40.0",
      "linuxParameters" : { "initProcessEnabled" : false },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/${format("%s-webapp", local.identifier)}/ecs-aws-otel-sidecar-collector",
          "awslogs-create-group" : "true",
          "awslogs-region" : "eu-west-1",
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "name" : "aws-otel-collector",
      "networkMode" : "awsvpc",
      "portMappings" : [],
      "secrets" : [],
    }
  ]

  load_balancing_configuration = [
    {
      target_group_arn = module.alb.target_group_arns[0]
      container_name   = format("%s-webapp", local.identifier)
      container_port   = local.webapp_container_port
    }
  ]

  scheduled_scaling = var.environment != "prod" ? {
    scale_in_schedules = [{
      schedule     = "cron(0 18 ? * MON-FRI *)"
      min_capacity = 0
      max_capacity = 0
    }]
    scale_out_schedules = [{
      schedule     = "cron(0 8 ? * MON-FRI *)"
      min_capacity = var.webapp_desired_task_count
      max_capacity = var.webapp_desired_task_count * 2
    }]
  } : null
}

module "api" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "12.7.4"
  tags    = local.tags

  attach_custom_ecs_task_execution_iam_policy = true
  attach_custom_ecs_task_iam_policy           = true
  capacity_fargate_base                       = var.use_spot_capacity ? 0 : var.api_desired_task_count
  capacity_fargate_spot_base                  = var.use_spot_capacity ? var.api_desired_task_count : 0
  capacity_fargate_spot_weight                = 1
  capacity_fargate_weight                     = 0
  cluster_arn                                 = module.ecs_cluster.arn
  cluster_name                                = module.ecs_cluster.name
  cpu                                         = 1024
  ecs_task_custom_policy                      = data.aws_iam_policy_document.ecs_task_custom_policy.json
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.ecs_task_execution_custom_policy.json
  enable_auto_scaling                         = true
  force_new_deployment                        = false
  identifier                                  = format("%s-api", local.identifier)
  memory                                      = 2048
  pipeline_role_name                          = module.ecs_cluster.github_role_name
  scaling_max_capacity                        = var.api_desired_task_count * 2
  scaling_min_capacity                        = var.api_desired_task_count
  service_registry                            = { registry_arn : aws_service_discovery_service.api.arn }
  service_type                                = "rolling"
  subnet_ids                                  = var.ecs_private_subnets_ids
  vpc_id                                      = var.vpc_id

  container_definitions = [
    {
      "command" : [],
      "cpu" : 0,
      "environment" : local.api_environment,
      "essential" : true,
      "healthCheck" : {
        "command" : ["CMD-SHELL", "wget -qO - http://localhost:${local.api_container_port}/health || exit 1"],
        "interval" : 30,
        "retries" : 3,
        "startPeriod" : 60,
        "timeout" : 5
      }
      "image" : "TERRAFORM_IMAGE_PLACEHOLDER",
      "linuxParameters" : { "initProcessEnabled" : false },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/${format("%s-api", local.identifier)}",
          "awslogs-region" : data.aws_region.current.name,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "name" : format("%s-api", local.identifier),
      "networkMode" : "awsvpc",
      "portMappings" : [
        {
          "containerPort" : local.api_container_port,
          "hostPort" : local.api_container_port
        }
      ],
      "secrets" : local.api_secrets,
    },
    {
      "command" : ["--config=/etc/ecs/ecs-xray.yaml"],
      "cpu" : 0,
      "environment" : [],
      "essential" : false,
      "healthCheck" : {
        "command" : ["CMD-SHELL", "exit 0"],
        "interval" : 30,
        "retries" : 3,
        "startPeriod" : 60,
        "timeout" : 5
      }
      "image" : "public.ecr.aws/aws-observability/aws-otel-collector:v0.40.0",
      "linuxParameters" : { "initProcessEnabled" : false },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/${format("%s-api", local.identifier)}/ecs-aws-otel-sidecar-collector",
          "awslogs-create-group" : "true",
          "awslogs-region" : "eu-west-1",
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "name" : "aws-otel-collector",
      "networkMode" : "awsvpc",
      "portMappings" : [],
      "secrets" : [],
    }
  ]

  load_balancing_configuration = [
    {
      target_group_arn = module.alb.target_group_arns[1]
      container_name   = format("%s-api", local.identifier)
      container_port   = local.api_container_port
    }
  ]

  scheduled_scaling = var.environment != "prod" ? {
    scale_in_schedules = [{
      schedule     = "cron(0 18 ? * MON-FRI *)"
      min_capacity = 0
      max_capacity = 0
    }]
    scale_out_schedules = [{
      schedule     = "cron(0 8 ? * MON-FRI *)"
      min_capacity = var.api_desired_task_count
      max_capacity = var.api_desired_task_count * 2
    }]
  } : null
}

module "queue_worker" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "12.7.4"
  tags    = local.tags

  attach_custom_ecs_task_execution_iam_policy   = true
  attach_custom_ecs_task_iam_policy             = true
  capacity_fargate_base                         = var.use_spot_capacity ? 0 : var.queue_worker_desired_task_count
  capacity_fargate_spot_base                    = var.use_spot_capacity ? var.queue_worker_desired_task_count : 0
  capacity_fargate_spot_weight                  = 1
  capacity_fargate_weight                       = 0
  cluster_arn                                   = module.ecs_cluster.arn
  cluster_name                                  = module.ecs_cluster.name
  cpu                                           = 1024
  default_step_scaling_high_cpu_usage_stat      = "Average"
  default_step_scaling_high_cpu_usage_threshold = 80
  ecs_task_custom_policy                        = data.aws_iam_policy_document.ecs_task_custom_policy.json
  ecs_task_execution_custom_policy              = data.aws_iam_policy_document.ecs_task_execution_custom_policy.json
  enable_auto_scaling                           = true
  force_new_deployment                          = false
  identifier                                    = format("%s-queue-worker", local.identifier)
  memory                                        = 2048
  pipeline_role_name                            = module.ecs_cluster.github_role_name
  scaling_max_capacity                          = var.queue_worker_desired_task_count * 2
  scaling_min_capacity                          = var.queue_worker_desired_task_count
  service_type                                  = "rolling"
  subnet_ids                                    = var.ecs_private_subnets_ids
  vpc_id                                        = var.vpc_id
  service_registry                              = { registry_arn : aws_service_discovery_service.queue_worker.arn }

  container_definitions = [
    {
      "command" : [],
      "cpu" : 0,
      "environment" : local.queue_worker_environment,
      "essential" : true,
      "healthCheck" : {
        "command" : ["CMD-SHELL", "wget -qO - http://localhost:${local.queue_worker_container_port}/health || exit 1"],
        "interval" : 30,
        "retries" : 3,
        "startPeriod" : 60,
        "timeout" : 5
      }
      "image" : "TERRAFORM_IMAGE_PLACEHOLDER",
      "linuxParameters" : { "initProcessEnabled" : false },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/${format("%s-queue-worker", local.identifier)}",
          "awslogs-region" : data.aws_region.current.name,
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "name" : format("%s-queue-worker", local.identifier),
      "networkMode" : "awsvpc",
      "portMappings" : [
        {
          "containerPort" : local.queue_worker_container_port,
          "hostPort" : local.queue_worker_container_port
        }
      ],
      "secrets" : local.queue_worker_secrets,
    },
    {
      "command" : ["--config=/etc/ecs/ecs-xray.yaml"],
      "cpu" : 0,
      "environment" : [],
      "essential" : false,
      "healthCheck" : {
        "command" : ["CMD-SHELL", "exit 0"],
        "interval" : 30,
        "retries" : 3,
        "startPeriod" : 60,
        "timeout" : 5
      }
      "image" : "public.ecr.aws/aws-observability/aws-otel-collector:v0.40.0",
      "linuxParameters" : { "initProcessEnabled" : false },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/${format("%s-queue-worker", local.identifier)}/ecs-aws-otel-sidecar-collector",
          "awslogs-create-group" : "true",
          "awslogs-region" : "eu-west-1",
          "awslogs-stream-prefix" : "ecs"
        }
      },
      "name" : "aws-otel-collector",
      "networkMode" : "awsvpc",
      "portMappings" : [],
      "secrets" : [],
    }
  ]

  scheduled_scaling = var.environment != "prod" ? {
    scale_in_schedules = [{
      schedule     = "cron(0 18 ? * MON-FRI *)"
      min_capacity = 0
      max_capacity = 0
    }]
    scale_out_schedules = [{
      schedule     = "cron(0 8 ? * MON-FRI *)"
      min_capacity = var.queue_worker_desired_task_count
      max_capacity = var.queue_worker_desired_task_count * 2
    }]
  } : null
}
