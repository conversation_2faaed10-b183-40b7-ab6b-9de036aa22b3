locals {
  identifier = "statement-service"

  api_container_port          = 5102
  pod_point_com_domain_name   = "pod-point.com"
  pod_point_com_hosted_zone   = "ZI1YF8KE9MFAW"
  podenergy_com_domain_name   = "podenergy.com"
  podenergy_com_hosted_zone   = "Z0275178UFD5TRC1H8T"
  queue_worker_container_port = 5103
  webapp_container_port       = 5101

  # Route53 record names for both domains
  statement_service_pod_point_com_r53_record_name = var.environment == "prod" ? format("statements.%s", local.pod_point_com_domain_name) : format("statements-%s.%s", var.environment, local.pod_point_com_domain_name)
  statement_service_podenergy_com_r53_record_name = var.environment == "prod" ? format("statements.%s", local.podenergy_com_domain_name) : format("statements-%s.%s", var.environment, local.podenergy_com_domain_name)

  destination_cluster_admin_username   = "postgres"
  destination_cluster_service_username = "statement_service"
  destination_cluster_password         = jsondecode(data.aws_secretsmanager_secret_version.destination_cluster_password.secret_string)["password"]

  tags = merge(data.aws_default_tags.current.tags, {
    "pp:owner"   = "experience:workplace-public"
    "pp:service" = local.identifier
  })
}
