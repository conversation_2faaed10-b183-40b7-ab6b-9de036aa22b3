locals {
  managed_rule_groups = [
    "AWSManagedRulesAmazonIpReputationList",
    "AWSManagedRulesKnownBadInputsRuleSet",
    "AWSManagedRulesLinuxRuleSet",
    "AWSManagedRulesUnixRuleSet",
    "AWSManagedRulesCommonRuleSet",
    "AWSManagedRulesSQLiRuleSet"
  ]
}

data "aws_wafv2_ip_set" "cf_allow_list" {
  provider = aws.us-east-1

  name  = "cf-allow-list"
  scope = "CLOUDFRONT"
}

data "aws_wafv2_ip_set" "cf_block_list" {
  provider = aws.us-east-1

  name  = "cf-block-list"
  scope = "CLOUDFRONT"
}

resource "aws_wafv2_web_acl" "this" {
  provider = aws.us-east-1
  tags     = local.tags

  name        = local.identifier
  description = format("WAF to monitor and protect traffic for the %s Cloudfront distribution", local.identifier)
  scope       = "CLOUDFRONT"

  default_action {
    allow {}
  }

  visibility_config {
    metric_name                = local.identifier
    cloudwatch_metrics_enabled = true
    sampled_requests_enabled   = true
  }

  rule {
    name     = "BlockUntrustedAddresses"
    priority = 0

    action {
      block {}
    }

    statement {
      ip_set_reference_statement {
        arn = data.aws_wafv2_ip_set.cf_block_list.arn
      }
    }

    visibility_config {
      metric_name                = "BlockUntrustedAddressesMetric"
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AllowTrustedAddresses"
    priority = 5

    action {
      allow {}
    }

    statement {
      ip_set_reference_statement {
        arn = data.aws_wafv2_ip_set.cf_allow_list.arn
      }
    }

    visibility_config {
      metric_name                = "AllowTrustedAddressesMetric"
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  dynamic "rule" {
    for_each = local.managed_rule_groups

    content {
      name     = rule.value
      priority = (rule.key * 5) + 10

      override_action {
        count {}
      }

      statement {
        managed_rule_group_statement {
          name        = rule.value
          vendor_name = "AWS"
        }
      }

      visibility_config {
        metric_name                = format("%sMetric", rule.value)
        cloudwatch_metrics_enabled = true
        sampled_requests_enabled   = true
      }
    }
  }

  rule {
    name     = "RateLimitAllAddresses"
    priority = 50

    action {
      count {}
    }

    statement {
      rate_based_statement {
        aggregate_key_type    = "IP"
        evaluation_window_sec = 300
        limit                 = 300
      }
    }

    visibility_config {
      metric_name                = "RateLimitAllAddressesMetric"
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }
}

module "waf_access_logs_database" {
  providers = {
    aws = aws.us-east-1
  }

  source  = "terraform-enterprise.pod-point.com/technology/waf/aws//modules/waf_access_logs_database"
  version = "3.0.0"
  tags    = local.tags

  acl_scope                            = "CLOUDFRONT"
  athena_database_identifier           = replace(local.identifier, "-", "_")
  athena_workgroup_description         = format("WAF for the %s cloudfront distribution.", local.identifier)
  athena_workgroup_identifier          = format("%s-waf", local.identifier)
  glue_catalogue_identifier            = format("%s_waf", replace(local.identifier, "-", "_"))
  log_bucket_s3_bucket_identifier      = local.identifier
  query_execution_s3_bucket_identifier = format("%s-query", local.identifier)
  waf_acl_arn                          = aws_wafv2_web_acl.this.arn
  waf_acl_name                         = aws_wafv2_web_acl.this.name
}
