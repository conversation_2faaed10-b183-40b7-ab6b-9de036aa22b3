resource "random_string" "random_cf_header_val" {
  length  = 16
  special = false
  numeric = true
  upper   = true
  lower   = true
}

resource "aws_cloudfront_distribution" "this" {
  provider = aws.us-east-1
  tags     = local.tags

  enabled    = true
  comment    = "Cloudfront distribution for ${local.identifier} service."
  aliases    = [local.support_tool_pod_point_com_r53_record_name, local.support_tool_podenergy_com_r53_record_name]
  web_acl_id = aws_wafv2_web_acl.this.arn

  origin {
    origin_id   = local.identifier
    domain_name = module.alb.load_balancer_dns_name

    custom_header {
      name  = "X-CF-Auth-Secret"
      value = random_string.random_cf_header_val.result
    }

    custom_origin_config {
      http_port              = "80"
      https_port             = "443"
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1", "TLSv1.1", "TLSv1.2", "SSLv3"]
    }
  }

  default_cache_behavior {
    compress                 = true
    allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods           = ["GET", "HEAD"]
    target_origin_id         = local.identifier
    cache_policy_id          = data.aws_cloudfront_cache_policy.no_cache_aws_managed_policy.id
    origin_request_policy_id = aws_cloudfront_origin_request_policy.this.id
    viewer_protocol_policy   = "redirect-to-https"
  }

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  dynamic "logging_config" {
    for_each = var.cloudfront_enable_logging ? [1] : []

    content {
      bucket          = one(module.cloudfront_log_bucket[*].s3_bucket_bucket_domain_name)
      include_cookies = false
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = false
    acm_certificate_arn            = module.pod_point_and_energy_acm_us_east_1.acm_certificate_arn
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }
}

resource "aws_cloudfront_monitoring_subscription" "this" {
  provider = aws.us-east-1

  count = var.cloudfront_realtime_metrics == "Enabled" ? 1 : 0

  distribution_id = aws_cloudfront_distribution.this.id
  monitoring_subscription {
    realtime_metrics_subscription_config {
      realtime_metrics_subscription_status = "Enabled"
    }
  }
}

data "aws_cloudfront_cache_policy" "no_cache_aws_managed_policy" {
  name = "Managed-CachingDisabled"
}

resource "aws_cloudfront_origin_request_policy" "this" {
  provider = aws.us-east-1

  name = "${local.identifier}-request-policy"

  cookies_config {
    cookie_behavior = "all"
  }

  headers_config {
    header_behavior = "allViewerAndWhitelistCloudFront"

    headers {
      items = [
        "CloudFront-Viewer-Address",
        "CloudFront-Viewer-Country-Region",
        "CloudFront-Viewer-JA3-Fingerprint",
      ]
    }
  }

  query_strings_config {
    query_string_behavior = "all"
  }
}

module "cloudfront_log_bucket" {
  providers = {
    aws = aws.us-east-1
  }
  tags = local.tags

  count = var.cloudfront_enable_logging ? 1 : 0

  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.1.0"

  bucket = format("%s-%s-cf-accesslogs", local.identifier, var.environment)

  versioning = {
    enabled = false
  }

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
}
