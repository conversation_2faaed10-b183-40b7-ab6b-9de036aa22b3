module "acm" {
  source = "../../../common/acm"
  tags   = local.tags

  providers = {
    aws.acm     = aws,
    aws.route53 = aws.route53
  }

  domain_name                = local.pod_point_com_domain_name
  hosted_zone_id             = local.pod_point_com_hosted_zone
  additional_aliased_domains = [format("*.%s", local.pod_point_com_domain_name)]
}

// certificate supporting both pod-point.com and podenergy.com domains

locals {
  zone_mapping = {
    (local.pod_point_com_domain_name) = local.pod_point_com_hosted_zone,
    (local.podenergy_com_domain_name) = local.podenergy_com_hosted_zone,
  }
}

module "pod_point_and_energy_acm_us_east_1" {
  source  = "terraform-aws-modules/acm/aws"
  version = "5.1.1"

  providers = {
    aws = aws.us-east-1,
  }

  domain_name = local.pod_point_com_domain_name
  subject_alternative_names = [
    format("*.%s", local.pod_point_com_domain_name),
    local.podenergy_com_domain_name,
    format("*.%s", local.podenergy_com_domain_name),
  ]

  create_route53_records  = false
  validation_method       = "DNS"
  validation_record_fqdns = module.pod_point_and_energy_acm_us_east_1_dns_validation.validation_route53_record_fqdns

  tags = local.tags
}

module "pod_point_and_energy_acm_us_east_1_dns_validation" {
  source  = "terraform-aws-modules/acm/aws"
  version = "5.1.1"

  providers = {
    aws = aws.route53
  }

  create_certificate          = false
  create_route53_records_only = true
  validation_method           = "DNS"

  distinct_domain_names                     = module.pod_point_and_energy_acm_us_east_1.distinct_domain_names
  acm_certificate_domain_validation_options = module.pod_point_and_energy_acm_us_east_1.acm_certificate_domain_validation_options

  zone_id = local.pod_point_com_hosted_zone
  zones = {
    (local.pod_point_com_domain_name) = local.pod_point_com_hosted_zone,
    (local.podenergy_com_domain_name) = local.podenergy_com_hosted_zone,
  }

  tags = local.tags
}
