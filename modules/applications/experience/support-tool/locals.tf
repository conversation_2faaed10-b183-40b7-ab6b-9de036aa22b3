locals {
  identifier = "support-tool"

  api_container_port        = 7102
  pod_point_com_domain_name = "pod-point.com"
  pod_point_com_hosted_zone = "ZI1YF8KE9MFAW"
  podenergy_com_domain_name = "podenergy.com"
  podenergy_com_hosted_zone = "Z0275178UFD5TRC1H8T"
  webapp_container_port     = 7101

  # Route53 record names for both domains
  support_tool_pod_point_com_r53_record_name = var.route53_record_name
  support_tool_podenergy_com_r53_record_name = var.environment == "prod" ? format("support.%s", local.podenergy_com_domain_name) : format("support-%s.%s", var.environment, local.podenergy_com_domain_name)

  vpn_cidr_block = ["**********/22"]
  vpn_vpc_id     = "vpc-075fca74292f0ff47"

  tags = merge(data.aws_default_tags.current.tags, {
    "pp:service" = local.identifier
  })
}
