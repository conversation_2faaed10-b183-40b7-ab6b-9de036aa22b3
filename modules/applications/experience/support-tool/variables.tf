variable "alb_public_subnets" {
  description = "The public subnets to assign to the ALB."
  type        = list(string)
}

variable "api_desired_task_count" {
  description = "API desired task count."
  type        = number
}

variable "api_source_security_group_ids" {
  type        = list(string)
  description = "Security group ids of external applications with access to the API."
  default     = []
}

variable "cloudfront_enable_logging" {
  description = "Whether to enable logging for the cloudfront distribution."
  type        = bool
  default     = false
}

variable "cloudfront_realtime_metrics" {
  description = "Whether to realtime metrics for the cloudfront distribution."
  type        = string
  default     = "Disabled"
}

variable "codebuild_security_group_ids" {
  description = "Security group ids of codebuild for post deploy testing."
  type        = list(string)
}

variable "ecs_private_subnets_ids" {
  description = "The public subnets to be used by ECS services and tasks."
  type        = list(string)
}

variable "environment" {
  type        = string
  description = "The name of the environment. This will be used when it is necessary to namespace resources (e.g. S3 buckets)."
}

variable "oidc_config_secret_id" {
  description = "The ID of the secret containing the OIDC configuration for the load balancer."
  type        = string
}

variable "privatelink_client_service_names" {
  description = "The VPC endpoint service names used to connect to load balancers in other AWS Account VPCs."
  type = object({
    assets_api                   = string
    assets_configuration_api     = string
    assets_provisioning_api      = string
    connectivity_commands_api    = string
    connectivity_status_api      = string
    state_competitions_api       = string
    state_diagnostics_api        = string
    state_firmware_api           = string
    state_smart_charging_service = string
    state_tariffs_api            = string
  })
}

variable "use_spot_capacity" {
  description = "Whether or not to use spot capacity."
  type        = bool
}

variable "vpc_id" {
  description = "The ID of the VPC to be used by the service."
  type        = string
}

variable "webapp_desired_task_count" {
  description = "Webapp desired task count."
  type        = number
}
