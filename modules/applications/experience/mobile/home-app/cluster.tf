module "ecs_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "11.3.1"
  tags    = local.tags

  identifier         = local.cluster_identifier
  repo_names         = ["experience"]
  container_insights = "enabled"

  additional_github_ci_policy_statements = [{
    sid    = "AllowDescribeSecurityGroupsForECSTaskRunGitHubAction"
    effect = "Allow"
    actions = [
      "ec2:DescribeSecurityGroups"
    ]
    resources = [
      "*"
    ]
  }]
}
