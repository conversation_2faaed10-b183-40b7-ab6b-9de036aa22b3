locals {
  vpn_vpc_id     = "vpc-075fca74292f0ff47"
  vpn_cidr_block = ["**********/22"]

  enable_home_app_cluster_vpn_resolution = var.environment == "stage"

  vpn_access_services = {
    "rewards-api" : {
      security_group_id = module.rewards_api.security_group_id
      port              = local.rewards_api_container_port
    }
    "notifications-api" : {
      security_group_id = module.notifications_api.security_group_id
      port              = local.notifications_api_container_port
    },
    "subscriptions-api" : {
      security_group_id = module.subscriptions_api.security_group_id
      port              = local.subscriptions_api_port
    },
    "payments-api" : {
      security_group_id = module.payments_api.security_group_id
      port              = local.payments_api_port
    }
  }
}

resource "aws_route53_vpc_association_authorization" "home_app_cluster_to_vpn" {
  count = local.enable_home_app_cluster_vpn_resolution == true ? 1 : 0

  vpc_id  = local.vpn_vpc_id
  zone_id = aws_service_discovery_private_dns_namespace.this.hosted_zone
}

resource "aws_route53_zone_association" "home_app_cluster_to_vpn" {
  count = local.enable_home_app_cluster_vpn_resolution == true ? 1 : 0

  provider = aws.vpn

  vpc_id  = aws_route53_vpc_association_authorization.home_app_cluster_to_vpn[0].vpc_id
  zone_id = aws_route53_vpc_association_authorization.home_app_cluster_to_vpn[0].zone_id
}

resource "aws_security_group_rule" "allow_home_app_cluster_vpn_access" {
  for_each = local.enable_home_app_cluster_vpn_resolution ? local.vpn_access_services : {}

  type        = "ingress"
  from_port   = each.value.port
  to_port     = each.value.port
  protocol    = "TCP"
  description = "Access permitted from VPN"

  cidr_blocks       = local.vpn_cidr_block
  security_group_id = each.value.security_group_id
}
