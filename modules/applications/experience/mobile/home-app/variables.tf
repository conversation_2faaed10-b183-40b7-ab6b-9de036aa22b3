variable "account_software_engineer_iam_role_arn" {
  description = "ARN of the Software Engineer role for the account."
  type        = list(string)
  default     = []
}

variable "alb_public_subnets" {
  description = "The public subnets to assign to the ALB."
  type        = list(string)
}

variable "notifications_api_desired_task_count" {
  description = "Notofications API desired task count."
  type        = number
}

variable "codebuild_security_group_ids" {
  description = "Security group ids of codebuild for post deploy testing."
  type        = list(string)
}

variable "ecs_private_subnets_ids" {
  description = "The public subnets to be used by ECS services and tasks."
  type        = list(string)
}

variable "environment" {
  type        = string
  description = "The name of the environment. This will be used when it is necessary to namespace resources (e.g. S3 buckets)."
}

variable "use_spot_capacity" {
  description = "Whether or not to use spot capacity."
  type        = bool
}

variable "vpc_id" {
  description = "The ID of the VPC to be used by the service."
  type        = string
}

variable "vpc_cidr_block" {
  description = "The CIDR block of the VPC to be used by the service."
  type        = string
}

variable "driver_account_security_group_id" {
  description = "Security group id for Driver Account API to be used by the service."
  type        = string
}

variable "driver_account_api_port" {
  description = "The port used by driver account api in destination cluster."
  type        = string
}

variable "experience_notification_eventbridge_rule_arn" {
  description = "The arn of cloudwatch eventbridge rule for notification events"
  type        = string
}

variable "experience_enode_credential_intervention_rule_arn" {
  description = "The ARN of the cloudwatch eventbridge rule for enode credential intervention events"
  type        = string
}

variable "experience_enode_vehicle_intervention_rule_arn" {
  description = "The ARN of the cloudwatch eventbridge rule for enode vehicle intervention events"
  type        = string
}

variable "rewards_api_desired_task_count" {
  description = "Rewards API desired task count."
  type        = number
}

variable "mobile_api_port" {
  description = "The port used by the Mobile API in the destination cluster"
  type        = string
}

variable "mobile_api_security_group_id" {
  description = "The port used by the Mobile API in the destination cluster"
  type        = string
}

variable "experience_user_pool_rule_arn" {
  description = "The ARN of the cloudwatch eventbridge rule for user pool events"
  type        = string
}

variable "aurora_cluster_resource_id" {
  description = "The Resource ID of the Aurora Cluster"
  type        = string
}

variable "aurora_cluster_user" {
  description = "The user used for the Aurora Cluster"
  type        = string
}

variable "podadmin_environment" {
  description = "The environment of the podadmin database."
  type        = string
}

variable "service_names" {
  description = "The VPC endpoint service names used to connect to other AWS account VPC load balancers"
  type = object({
    smart_charging_service_api = string
  })
}

variable "subscriptions_api_desired_task_count" {
  description = "Subscriptions API desired task count."
  type        = number
}

variable "payments_api_desired_task_count" {
  description = "Payments API desired task count."
  type        = number
}

variable "support_tool_security_group_id" {
  description = "The security group id of the support tool."
  type        = string
}

variable "experience_subscription_rule_arns" {
  description = "An array of arns for event bridge rules permitted to send messages to the Subscriptions API SQS event queue"
  type        = list(string)
}

variable "experience_rewards_rule_arns" {
  description = "An array of arns for event bridge rules permitted to send messages to the Rewards API SQS event queue"
  type        = list(string)
}

variable "subscriptions_api_subdomain" {
  description = "The subdomain of podenergy.com to associate with the Subscriptions API Gateway"
  type        = string
}

variable "podenergy_cf_acm_arn" {
  description = "The ARN of the ACM certificate for Cloudfront *.podenergy.com"
  type        = string
}

variable "network_assets_account_id" {
  description = "The network assets account id"
  type        = string
}

variable "salesforce_account_id" {
  description = "The ID of the Salesforce AWS account"
  type        = string
}

variable "cs_state_account_id" {
  description = "The ID of the CS State AWS account"
  type        = string
}
