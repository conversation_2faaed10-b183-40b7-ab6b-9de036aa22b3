module "rewards_api_load_balancer" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws"
  version = "3.0.0"
  tags    = local.tags

  access_logs = {
    create_bucket_policy = true
    enable_logs          = true
  }

  enable_internal_lb = true

  http_tcp_listeners = [
    {
      port        = 80
      protocol    = "HTTP"
      action_type = "redirect"
      redirect = {
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  ]

  https_listeners = [
    {
      port               = 443
      protocol           = "HTTPS"
      certificate_arn    = module.acm.arn
      target_group_index = 0
    }
  ]

  https_listener_rules = []

  load_balancer_name    = local.rewards_api_identifier
  load_balancer_subnets = var.ecs_private_subnets_ids
  load_balancer_type    = "application"

  security_group_name        = format("%s-lb", local.rewards_api_identifier)
  security_group_description = format("Security group for the %s load balanacer", local.rewards_api_identifier)
  security_group_ingress_rules = {
    port_http_all = {
      description = "HTTP permitted from the Internet"
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cirdrs = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }

    port_https_all = {
      description = "HTTPS permitted from the Internet"
      from_port   = 443
      to_port     = 443
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }

  target_groups = [
    {
      backend_port         = local.rewards_api_container_port
      backend_protocol     = "HTTP"
      deregistration_delay = 60
      name                 = local.rewards_api_identifier
      target_type          = "ip"
      vpc_id               = var.vpc_id

      health_check = {
        enabled             = true
        healthy_threshold   = 2
        interval            = 10
        matcher             = "200"
        path                = "/health"
        port                = "traffic-port"
        protocol            = "HTTP"
        timeout             = 3
        unhealthy_threshold = 3
      }
    }
  ]

  vpc_id = var.vpc_id
}

module "subscriptions_api_load_balancer" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws"
  version = "3.0.0"
  tags    = local.subscriptions_api_tags

  access_logs = {
    create_bucket_policy = true
    enable_logs          = true
  }

  enable_internal_lb = true

  http_tcp_listeners = [
    {
      port        = 80
      protocol    = "HTTP"
      action_type = "redirect"
      redirect = {
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  ]

  https_listeners = [
    {
      port               = 443
      protocol           = "HTTPS"
      certificate_arn    = module.acm.arn
      target_group_index = 0
    }
  ]

  https_listener_rules = []

  load_balancer_name    = local.subscriptions_api_identifier
  load_balancer_subnets = var.ecs_private_subnets_ids
  load_balancer_type    = "application"

  security_group_name        = format("%s-lb", local.subscriptions_api_identifier)
  security_group_description = format("Security group for the %s load balancer", local.subscriptions_api_identifier)
  security_group_ingress_rules = {
    port_http_all = {
      description = "HTTP permitted from the Internet"
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cirdrs = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }

    port_https_all = {
      description = "HTTPS permitted from the Internet"
      from_port   = 443
      to_port     = 443
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }

  target_groups = [
    {
      backend_port         = local.subscriptions_api_port
      backend_protocol     = "HTTP"
      deregistration_delay = 60
      name                 = local.subscriptions_api_identifier
      target_type          = "ip"
      vpc_id               = var.vpc_id

      health_check = {
        enabled             = true
        healthy_threshold   = 2
        interval            = 10
        matcher             = "200"
        path                = "/health"
        port                = "traffic-port"
        protocol            = "HTTP"
        timeout             = 3
        unhealthy_threshold = 3
      }
    }
  ]

  vpc_id = var.vpc_id
}
