locals {
  alarm_actions_enabled = true

  cloudwatch_event_rule_event_patterns = {
    ecs-deployment-state-change-error = {
      source : ["aws.ecs"],
      detail-type : ["ECS Deployment State Change"],
      detail : { clusterArn = [module.ecs_cluster.arn], eventType = ["ERROR"] }
    }
    ecs-task-state-change-task-failed-to-start = {
      source : ["aws.ecs"],
      detail-type : ["ECS Task State Change"],
      detail : { clusterArn = [module.ecs_cluster.arn], stopCode = ["TaskFailedToStart"] }
    }
  }

  opsgenie_cw_topic_arn        = "arn:aws:sns:eu-west-1:${data.aws_caller_identity.current.account_id}:opsgenie-cw-experience-mobile"
  opsgenie_cw_events_topic_arn = "arn:aws:sns:eu-west-1:${data.aws_caller_identity.current.account_id}:opsgenie-cw-events-experience-mobile"
}


resource "aws_cloudwatch_event_rule" "alarms" {
  for_each = local.cloudwatch_event_rule_event_patterns

  event_bus_name = "default"
  event_pattern  = jsonencode(each.value)
  name           = format("%s-%s", module.ecs_cluster.name, each.key)
  state          = local.alarm_actions_enabled == true ? "ENABLED" : "DISABLED"
}

resource "aws_cloudwatch_event_target" "alarms" {
  for_each = local.cloudwatch_event_rule_event_patterns

  arn            = local.opsgenie_cw_events_topic_arn
  event_bus_name = "default"
  rule           = aws_cloudwatch_event_rule.alarms[each.key].name
}

resource "aws_cloudwatch_metric_alarm" "notification_events_dlq_alarm" {
  alarm_name          = "notification-events-dlq-alarm-${var.environment}"
  alarm_description   = "Message received on notifications-events-dlq"
  metric_name         = "ApproximateNumberOfMessagesVisible"
  namespace           = "AWS/SQS"
  threshold           = "1"
  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm = "1"
  evaluation_periods  = "1"
  period              = "900"
  treat_missing_data  = "ignore"
  actions_enabled     = "true"
  alarm_actions       = [local.opsgenie_cw_topic_arn]
  ok_actions          = [local.opsgenie_cw_topic_arn]

  dimensions = {
    QueueName = aws_sqs_queue.notification_events_dlq.name
  }
}

resource "aws_cloudwatch_metric_alarm" "enode_credential_intervention_events_dlq_alarm" {
  alarm_name          = "enode-credential-intervention-events-dlq-alarm-${var.environment}"
  alarm_description   = "Message received on enode-credential-intervention-events-dlq"
  metric_name         = "ApproximateNumberOfMessagesVisible"
  namespace           = "AWS/SQS"
  threshold           = "1"
  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm = "1"
  evaluation_periods  = "1"
  period              = "900"
  treat_missing_data  = "ignore"
  actions_enabled     = "true"
  alarm_actions       = [local.opsgenie_cw_topic_arn]
  ok_actions          = [local.opsgenie_cw_topic_arn]

  dimensions = {
    QueueName = aws_sqs_queue.enode_credential_intervention_events_dlq.name
  }
}


resource "aws_cloudwatch_metric_alarm" "enode_vehicle_intervention_events_dlq_alarm" {
  alarm_name          = "enode-vehicle-intervention-events-dlq-alarm-${var.environment}"
  alarm_description   = "Message received on enode-vehicle-intervention-events-dlq"
  metric_name         = "ApproximateNumberOfMessagesVisible"
  namespace           = "AWS/SQS"
  threshold           = "1"
  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm = "1"
  evaluation_periods  = "1"
  period              = "900"
  treat_missing_data  = "ignore"
  actions_enabled     = "true"
  alarm_actions       = [local.opsgenie_cw_topic_arn]
  ok_actions          = [local.opsgenie_cw_topic_arn]

  dimensions = {
    QueueName = aws_sqs_queue.enode_vehicle_intervention_events_dlq.name
  }
}


resource "aws_cloudwatch_metric_alarm" "user_profile_events_dlq_alarm" {
  alarm_name          = "user-profile-events-dlq-alarm-${var.environment}"
  alarm_description   = "Message received on user-profile-events-dlq"
  metric_name         = "ApproximateNumberOfMessagesVisible"
  namespace           = "AWS/SQS"
  threshold           = "1"
  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm = "1"
  evaluation_periods  = "1"
  period              = "900"
  treat_missing_data  = "ignore"
  actions_enabled     = "true"
  alarm_actions       = [local.opsgenie_cw_topic_arn]
  ok_actions          = [local.opsgenie_cw_topic_arn]

  dimensions = {
    QueueName = aws_sqs_queue.user_profile_events_dlq.name
  }
}

resource "aws_cloudwatch_metric_alarm" "payments_events_dlq_alarm" {
  alarm_name          = "${aws_sqs_queue.payments_events_dlq.name}-alarm-${var.environment}"
  alarm_description   = "Message received on ${aws_sqs_queue.payments_events_dlq.name}"
  metric_name         = "ApproximateNumberOfMessagesVisible"
  namespace           = "AWS/SQS"
  threshold           = "1"
  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm = "1"
  evaluation_periods  = "1"
  period              = "900"
  treat_missing_data  = "ignore"

  actions_enabled = "true"
  alarm_actions   = [local.opsgenie_cw_topic_arn]
  ok_actions      = [local.opsgenie_cw_topic_arn]

  dimensions = {
    QueueName = aws_sqs_queue.payments_events_dlq.name
  }
}

# Subscriptions API

resource "aws_cloudwatch_metric_alarm" "subscriptions_api_incoming_events_dlq_alarm" {
  alarm_name          = "${aws_sqs_queue.subscriptions_api_incoming_events_dlq.name}-alarm-${var.environment}"
  alarm_description   = "Message recieved on ${aws_sqs_queue.subscriptions_api_incoming_events_dlq.name}"
  metric_name         = "ApproximateNumberOfMessagesVisible"
  namespace           = "AWS/SQS"
  threshold           = "1"
  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm = "1"
  evaluation_periods  = "1"
  period              = "900"
  treat_missing_data  = "ignore"

  actions_enabled = "true"
  alarm_actions   = [local.opsgenie_cw_topic_arn]
  ok_actions      = [local.opsgenie_cw_topic_arn]

  dimensions = {
    QueueName = aws_sqs_queue.subscriptions_api_incoming_events_dlq.name
  }
}

resource "aws_cloudwatch_log_metric_filter" "subscriptions_api_4xx_count" {
  log_group_name = module.subscriptions_api.log_group_name
  name           = "subscriptions_api_4xx"
  pattern        = "{ $.res.statusCode = %4[0-9]{2}% }"

  metric_transformation {
    name      = "subscriptions_api_4xx"
    namespace = local.cloudwatch_metric_namespace

    value         = 1
    default_value = 0
    unit          = "Count"
  }
}

resource "aws_cloudwatch_metric_alarm" "subscriptions_api_target_4xx_count" {
  namespace  = local.cloudwatch_metric_namespace
  alarm_name = "${local.subscriptions_api_identifier}-target-4xx-count-alarm-${var.environment}"

  period              = 60
  evaluation_periods  = 5
  datapoints_to_alarm = 5
  statistic           = "Maximum"
  metric_name         = aws_cloudwatch_log_metric_filter.subscriptions_api_4xx_count.name
  comparison_operator = "GreaterThanThreshold"
  threshold           = 0

  actions_enabled = "true"
  alarm_actions   = [local.opsgenie_cw_topic_arn]
  ok_actions      = [local.opsgenie_cw_topic_arn]
}

resource "aws_cloudwatch_log_metric_filter" "subscriptions_api_5xx_count" {
  log_group_name = module.subscriptions_api.log_group_name
  name           = "subscriptions_api_5xx"
  pattern        = "{ $.res.statusCode = %5[0-9]{2}% }"

  metric_transformation {
    name      = "subscriptions_api_5xx"
    namespace = local.cloudwatch_metric_namespace

    value         = 1
    default_value = 0
    unit          = "Count"
  }
}

resource "aws_cloudwatch_metric_alarm" "subscriptions_api_target_5xx_count" {
  namespace  = local.cloudwatch_metric_namespace
  alarm_name = "${local.subscriptions_api_identifier}-target-5xx-count-alarm-${var.environment}"

  period              = 60
  evaluation_periods  = 1
  datapoints_to_alarm = 1
  statistic           = "Maximum"
  metric_name         = aws_cloudwatch_log_metric_filter.subscriptions_api_5xx_count.name
  comparison_operator = "GreaterThanThreshold"
  threshold           = 0

  actions_enabled = "true"
  alarm_actions   = [local.opsgenie_cw_topic_arn]
  ok_actions      = [local.opsgenie_cw_topic_arn]
}

# Rewards API
resource "aws_cloudwatch_metric_alarm" "rewards_api_incoming_events_dlq_alarm" {
  alarm_name          = "${aws_sqs_queue.rewards_api_incoming_events_dlq.name}-alarm-${var.environment}"
  alarm_description   = "Message recieved on ${aws_sqs_queue.rewards_api_incoming_events_dlq.name}"
  metric_name         = "ApproximateNumberOfMessagesVisible"
  namespace           = "AWS/SQS"
  threshold           = "1"
  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  datapoints_to_alarm = "1"
  evaluation_periods  = "1"
  period              = "900"
  treat_missing_data  = "ignore"

  actions_enabled = "true"
  alarm_actions   = [local.opsgenie_cw_topic_arn]
  ok_actions      = [local.opsgenie_cw_topic_arn]

  dimensions = {
    QueueName = aws_sqs_queue.rewards_api_incoming_events_dlq.name
  }
}
