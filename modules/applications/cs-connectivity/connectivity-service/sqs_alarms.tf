locals {
  sqs_queues_with_alarms = [
    aws_sqs_queue.ocpp_connections.name,
    aws_sqs_queue.ocpp_disconnections.name,
    aws_sqs_queue.ppcp_messages.name,
    aws_sqs_queue.device_commands_processor_fifo.name,
    aws_sqs_queue.online_status_processor.name,
    aws_sqs_queue.ocpp16_cs_requests.name,
    aws_sqs_queue.ocpp_raw_comms.name,
    aws_sqs_queue.status_notification_events.name,
    aws_sqs_queue.ppcp_connections.name,
    aws_sqs_queue.delayed_commands_processor_instructions.name,
  ]

  sqs_alarms = {
    "ApproximateAgeOfOldestMessage" = {
      threshold           = 120 // 2 minutes
      evaluation_periods  = 5
      datapoints_to_alarm = 5
      period              = 60
      comparison_operator = "GreaterThanOrEqualToThreshold"
      statistic           = "Maximum"
      description         = "This alarm is used to detect whether the age of the oldest message in the queue is too high. High age can be an indication that messages are not processed quickly enough or that there are some poison-pill messages that are stuck in the queue and can't be processed."
    }
    "ApproximateNumberOfMessagesNotVisible" = {
      // This depends on the queue but 20k should probably alarm for most of them
      threshold           = 20000
      evaluation_periods  = 5
      datapoints_to_alarm = 5
      period              = 60
      comparison_operator = "GreaterThanOrEqualToThreshold"
      statistic           = "Average"
      description         = "This alarm helps to detect a high number of in-flight messages. A high number of in-flight messages can be an indication that the consumers are processing but not deleting the messages from the queue."
    }
    "ApproximateNumberOfMessagesVisible" = {
      // This depends on the queue but 1k should probably alarm for most of them
      threshold           = 1000
      evaluation_periods  = 5
      datapoints_to_alarm = 5
      period              = 60
      comparison_operator = "GreaterThanThreshold"
      statistic           = "Average"
      description         = "This alarm is used to detect whether the message count of the active queue is too high and consumers are slow to process the messages or there are not enough consumers to process them."
    }
    "NumberOfMessagesSent" = {
      threshold           = 0
      evaluation_periods  = 5
      datapoints_to_alarm = 5
      period              = 60
      comparison_operator = "LessThanOrEqualToThreshold"
      statistic           = "Sum"
      description         = "This alarm is used to detect when a producer stops sending messages."
      treat_missing_data  = "breaching"
    }
  }

  sqs_alarm_override_per_queue = {
    "${aws_sqs_queue.status_notification_events.name}" = {
      "ApproximateAgeOfOldestMessage" = {
        // Required by PCPR regulation. We should notify them is response takes more than 30 seconds
        evaluation_periods  = 2
        datapoints_to_alarm = 2
        threshold           = 30
      }
    }
    "${aws_sqs_queue.online_status_processor.name}" = {
      "ApproximateAgeOfOldestMessage" = {
        // Required by PCPR regulation. We should notify them is response takes more than 30 seconds
        evaluation_periods  = 2
        datapoints_to_alarm = 2
        threshold           = 30
      }
    }
  }

  sqs_alarms_computed = flatten([
    for queueName in local.sqs_queues_with_alarms : [
      for metric, alarm in local.sqs_alarms :
      merge(alarm, try(local.sqs_alarm_override_per_queue[queueName][metric], {}), {
        queueName = queueName
        metric    = metric
      })
    ]
  ])
}

resource "aws_cloudwatch_metric_alarm" "sqs_alarms" {
  for_each = length(var.alarm_actions) > 0 ? tomap({
    for alarm in local.sqs_alarms_computed : format("%s-%s", alarm.queueName, alarm.metric) => alarm
  }) : {}

  alarm_name          = format("sqs-%s", each.key)
  alarm_description   = try(each.value.description, null)
  comparison_operator = each.value.comparison_operator
  evaluation_periods  = each.value.evaluation_periods
  datapoints_to_alarm = each.value.datapoints_to_alarm
  treat_missing_data  = try(each.value.treat_missing_data, "notBreaching")

  metric_name = each.value.metric
  namespace   = "AWS/SQS"

  threshold          = each.value.threshold
  period             = each.value.period
  statistic          = try(each.value.statistic, null)
  extended_statistic = try(each.value.extended_statistic, null)
  unit               = try(each.value.unit, null)

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions

  dimensions = {
    QueueName = each.value.queueName
  }
}

resource "aws_cloudwatch_metric_alarm" "sqs_dead_letter_queue_alarms" {
  for_each = length(var.alarm_actions) > 0 ? toset([
    aws_sqs_queue.ocpp16_cs_requests_dlq.name,
    aws_sqs_queue.ocpp_connections_dlq.name,
    aws_sqs_queue.ocpp_disconnections_dlq.name,
    aws_sqs_queue.ppcp_messages_dlq.name,
    aws_sqs_queue.device_commands_processor_dlq.name,
    aws_sqs_queue.online_status_processor_dlq.name,
    aws_sqs_queue.ocpp_raw_comms_dlq.name,
    aws_sqs_queue.status_notifications_evnts_dlq.name,
    aws_sqs_queue.ppcp_messages_dlq.name,
    aws_sqs_queue.delayed_commands_processor_instructions_dlq.name,
  ]) : toset([])

  alarm_name        = format("sqs-%s-ApproximateNumberOfMessagesVisible", each.key)
  alarm_description = format("This alarm fires when the %s dead-letter queue is being filled", each.key)

  comparison_operator = "GreaterThanOrEqualToThreshold"
  period              = 60 # 1 minute
  evaluation_periods  = 1
  datapoints_to_alarm = 1

  metric_name        = "ApproximateNumberOfMessagesVisible"
  namespace          = "AWS/SQS"
  statistic          = "Average"
  threshold          = 1
  alarm_actions      = var.alarm_actions
  ok_actions         = var.alarm_actions
  treat_missing_data = "notBreaching"

  dimensions = {
    QueueName = each.key
  }

  tags = local.tags
}
