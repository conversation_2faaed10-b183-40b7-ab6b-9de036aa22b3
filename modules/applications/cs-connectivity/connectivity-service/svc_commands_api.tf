locals {
  commands_api_container_definition = merge(local.container_definition, {
    name : format("%s-commands-api", local.identifier),
    logConfiguration : merge(local.container_definition["logConfiguration"], {
      options : merge(local.container_definition["logConfiguration"]["options"], {
        awslogs-group : "/ecs/${local.identifier}/commands-api",
      })
    }),
    environment : concat(local.container_definition["environment"], [
      {
        name : "DB_USERNAME",
        value : "commands_api"
      },
      {
        name : "POW_API_GATEWAY_MANAGEMENT_URL",
        value : local.pow_api_connections_management_url
      },
      {
        name : "OCPP_PROXY_URL",
        value : local.ocpp_proxy_url
      },
      {
        name : "DEVICE_COMMANDS_QUEUE_URL",
        value : aws_sqs_queue.device_commands_processor_fifo.url
      },
      {
        name : "REDIS_HOST",
        value : format("%s:%s", module.redis.endpoint, module.redis.port),
      },
      {
        name : "REDIS_TLS_ENABLED",
        value : "1",
      },
      {
        name : "ASSETS_SERVICE_BASE_URL",
        value : format("http://%s", var.vpc_endpoint_assets_service_dns),
      },
    ]),
    portMappings : [
      {
        containerPort : 80,
        hostPort : 80
      }
    ],
  })
}

module "commands_api" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "13.0.0"

  service_type       = "rolling"
  identifier         = format("%s-commands-api", local.identifier)
  cluster_name       = module.ecs_cluster.name
  cluster_arn        = module.ecs_cluster.arn
  pipeline_role_name = module.ecs_cluster.github_role_name

  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.task_role_commands_api.json
  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.task_execution_common.json

  memory = var.memory
  cpu    = var.cpu

  capacity_fargate_base        = var.capacity_fargate_base
  capacity_fargate_weight      = var.capacity_fargate_weight
  capacity_fargate_spot_base   = var.capacity_fargate_spot_base
  capacity_fargate_spot_weight = var.capacity_fargate_spot_weight

  container_definitions = jsonencode([
    local.commands_api_container_definition,
    merge(local.appconfig_container_definition, {
      name : format("%s-commands-api-appconfig", local.identifier),
    }),
  ])

  vpc_id     = var.vpc_id
  subnet_ids = var.vpc_private_subnet_ids

  scaling_min_capacity = var.scaling_min_capacity
  enable_auto_scaling  = false

  log_group_name         = local.commands_api_container_definition["logConfiguration"]["options"]["awslogs-group"]
  logs_retention_in_days = var.logs_retention_in_days

  depends_on = [
    module.ecs_cluster,
  ]

  load_balancing_configuration = [
    {
      target_group_arn = module.privatelink_commands_api_target.alb_target_group_arn
      container_name   = format("%s-commands-api", local.identifier)
      container_port   = local.commands_api_container_definition["portMappings"][0]["containerPort"]
    }
  ]

  additional_kms_administrators = var.kms_admins

  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"
      ]
    }]
  }]

  tags = merge(local.tags, {
    "pp:service" = "connectivity-service:commands-api"
  })
}

resource "aws_security_group_rule" "fargate_commands_api_egress" {
  for_each = tomap({
    "ports_all_open" = local.fargate_egress_rule_ports_all_open
  })

  type              = "egress"
  from_port         = each.value.from_port
  to_port           = each.value.to_port
  protocol          = each.value.protocol
  description       = each.value.description
  cidr_blocks       = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks  = lookup(each.value, "ipv6_cidrs", null)
  security_group_id = module.commands_api.security_group_id
}

data "aws_iam_policy_document" "task_role_commands_api" {
  statement {
    sid     = "allowAccessToRDSUser"
    actions = ["rds-db:connect"]
    resources = [
      format("arn:aws:rds-db:%s:%s:dbuser:%s/commands_api",
        data.aws_region.current.name,
        data.aws_caller_identity.current.id,
        module.aurora.cluster_resource_id,
      )
    ]
  }

  statement {
    sid    = "allowAccessToAppConfig"
    effect = "Allow"
    actions = [
      "appconfig:GetLatestConfiguration",
      "appconfig:StartConfigurationSession"
    ]
    resources = [
      format("arn:aws:appconfig:%s:%s:application/%s/environment/%s/configuration/%s",
        data.aws_region.current.name,
        data.aws_caller_identity.current.id,
        aws_appconfig_application.connectivity_service.id,
        aws_appconfig_environment.connectivity_service.environment_id,
        aws_appconfig_configuration_profile.connectivity_service_feature_flags.configuration_profile_id
      )
    ]
  }

  statement {
    sid       = "allowAccessApiGatewayManagement"
    actions   = ["execute-api:ManageConnections"]
    resources = [local.pow_api_arn]
  }

  statement {
    sid     = "allowAccessToOcpp16RestApisConnectionsManager"
    actions = ["execute-api:Invoke"]
    resources = [
      format(
        "arn:aws:execute-api:%s:%s:%s/%s/POST/connections",
        var.ocpp16_rest_api.region,
        var.ocpp16_rest_api.account_id,
        var.ocpp16_rest_api.api_id,
        var.ocpp16_rest_api.stage,
      ),
    ]
  }

  statement {
    sid     = "allowAccessToOcpp16RestApisCreateTransaction"
    actions = ["execute-api:Invoke"]
    resources = [
      format(
        "arn:aws:execute-api:%s:%s:%s/%s/POST/charge-points/*/transactions",
        var.ocpp16_rest_api.region,
        var.ocpp16_rest_api.account_id,
        var.ocpp16_rest_api.api_id,
        var.ocpp16_rest_api.stage,
      ),
    ]
  }

  statement {
    sid     = "allowAccessToOcpp16RestApisUpdateTransaction"
    actions = ["execute-api:Invoke"]
    resources = [
      format(
        "arn:aws:execute-api:%s:%s:%s/%s/PATCH/charge-points/*/transactions/*",
        var.ocpp16_rest_api.region,
        var.ocpp16_rest_api.account_id,
        var.ocpp16_rest_api.api_id,
        var.ocpp16_rest_api.stage,
      ),
    ]
  }

  statement {
    sid = "permissionsToDeviceCommandsProcessorSQSFifoForCommandsApi"
    actions = [
      "sqs:SendMessage",
    ]
    resources = [
      aws_sqs_queue.device_commands_processor_fifo.arn,
    ]
  }
}
