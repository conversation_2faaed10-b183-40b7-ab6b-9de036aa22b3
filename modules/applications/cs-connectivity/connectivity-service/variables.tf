variable "environment" {
  description = <<EOF
    The environment of the service.

    This needs to be declared while the service is in the old Pod Point account.

    Otherwise naming conflicts will be present in our terraform apply runs.
  EOF
  type        = string
}

variable "tags" {
  description = "key, value pair of tags to associate to your resources."
  type        = map(any)
  default     = {}
}

variable "github_action_repo_sources" {
  description = "The permitted repositories allowed to assume the role that will be used by GitHub Actions."
  type        = list(string)
}

variable "cpu" {
  description = "CPU allocated to fargate."
  type        = number
}

variable "memory" {
  description = "Memory allocated to fargate."
  type        = number
}

variable "components_fargate_task_size" {
  description = "Task size of the various components of the Cluster."
  type = map(object({
    cpu    = string
    memory = string
  }))
  default = {}
}

variable "scaling_min_capacity" {
  description = "The minimum scaling capacity for the ECS tasks"
  type        = number
  default     = 0
}

variable "logs_retention_in_days" {
  description = "Specifies the number of days you want to retain the container logs."
  type        = number
  default     = 7
}

variable "kms_admins" {
  description = "A list of administrators to assign to module managed KMS keys and infrastructure."
  type        = list(string)
}

variable "capacity_fargate_base" {
  description = "Base number of tasks to run on Fargate on-demand capacity provider. Applies to all ECS services in the cluster."
  type        = number
}

variable "capacity_fargate_weight" {
  description = "Weight of tasks to run on Fargate on-demand capacity provider. Applies to all ECS services in the cluster."
  type        = number
}

variable "capacity_fargate_spot_base" {
  description = "Base number of tasks to run on Fargate spot capacity provider. Applies to all ECS services in the cluster."
  type        = number
}

variable "capacity_fargate_spot_weight" {
  description = "Weight of tasks to run on Fargate spot capacity provider. Applies to all ECS services in the cluster."
  type        = number
}

variable "alarm_actions" {
  type        = list(string)
  default     = []
  description = "Define the actions to perform when the alarms change state."
}

variable "route53_connectivity_zone_id" {
  description = "The zone id of the route53 zone where the connectivity service records should be created."
  type        = string
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *            Aurora
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "aurora_cluster_instance_class" {
  description = "The instance class used for the Connectivity Service Aurora database cluster"
  type        = string
  default     = "db.t4g.medium"
}

variable "aurora_cluster_instance_count" {
  description = "The number of instances used for the Connectivity Service Aurora database cluster"
  type        = number
  default     = 2
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *            SNS Topics
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "ocpp_connections_sns_topic_arn" {
  description = "The ARN of the SNS topic to which to subscribe for OCPP connections"
}

variable "ocpp_disconnections_sns_topic_arn" {
  description = "The ARN of the SNS topic to which to subscribe for OCPP disconnections"
}

variable "pod_unit_event_sns_topic_arn" {
  description = "The ARN of the SNS topic to subscribe to for Charge Station messages"
}

variable "ocpp20_raw_comms_sns_topic_arn" {
  description = "The ARN of the SNS topic to subscribe for OCPP2.0 Raw Comms"
}

variable "cs_status_updates_sns_topic_arn" {
  description = "The ARN of the SNS topic to which to publish charging station status updates"
}

variable "status_messages_sns_topic_arn" {
  description = "The ARN of the SNS topic to which to publish status messages"
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *            VPC
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "vpc_cidr_block" {
  description = "VPC cidr block."
  type        = string
}

variable "vpc_id" {
  description = "The VPC ID."
  type        = string
}

variable "vpc_private_subnet_ids" {
  type        = list(string)
  description = "A list of private subnet ids."
}

variable "vpc_availability_zone_names" {
  type        = list(string)
  description = "List of availability zones."
}

variable "vpc_endpoint_execute_api_security_group_id" {
  description = "Id of security group of the VPC endpoint to execute AWS API Gateway."
  type        = string
}

variable "vpc_endpoint_execute_api_dns" {
  type        = string
  description = "The DNS of the VPC endpoint for executing private APIs."
}

variable "vpc_endpoint_assets_service_security_group_id" {
  description = "Id of security group of the VPC endpoint to the assets service."
  type        = string
}

variable "vpc_endpoint_assets_service_dns" {
  type        = string
  description = "The DNS of the VPC endpoint fro the assets service."
}

variable "vpc_endpoint_transactions_api_security_group_id" {
  description = "Id of security group of the VPC endpoint to the transactions api."
  type        = string
}

variable "vpc_endpoint_transactions_api_dns" {
  type        = string
  description = "The DNS of the VPC endpoint fro the transactions api."
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *             S3
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "permitted_redshift_spectrum_accounts_and_roles" {
  type = list(object({
    account_id    = string
    iam_role_name = string
  }))
  default = []
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *           Redis
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "redis_cluster_port" {
  description = "The port used to connect to the redis cluster"
  type        = string
  default     = "6379"
}

variable "redis_cluster_instance_type" {
  description = "The instance type for the redis cluster"
  type        = string
  default     = "cache.t4g.micro"
}

variable "redis_cluster_number_of_node_groups" {
  description = "The number of node groups in the cluster"
  type        = number
  default     = 1
}

variable "redis_cluster_replicas_per_node_group" {
  description = "The number of read replicas per node group"
  type        = number
  default     = 2
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *            APIs
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "pow_api_gateway" {
  description = "Object containing details of the POW Websocket API Gateway."
  type = object({
    region     = string
    account_id = string
    api_id     = string
    stage      = string
  })
}

variable "status_api_client_account_ids" {
  description = "List of AWS accounts ID that consume the status API."
  type        = list(string)
}

variable "commands_api_client_account_ids" {
  description = "List of AWS accounts ID that consume the commands API."
  type        = list(string)
}

variable "ocpp16_rest_api" {
  description = "Object containing details of the OCPP 1.6 Rest API Gateway."
  type = object({
    region     = string
    account_id = string
    api_id     = string
    stage      = string
  })
}

variable "certificate_service_api" {
  default     = null
  description = "Object containing details of the Certificate Service API Gateway."
  type = object({
    region     = string
    account_id = string
    api_id     = string
    stage      = string
  })
}

variable "charge_data_platform_api_dns" {
  type        = string
  description = "The DNS of the VPC endpoint fro the transactions api."
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *           Grafana
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "logging_comms_grafana_cidr_block" {
  description = "CIDR block for logging comms Grafana access."
  type        = string
  default     = ""
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *            Roles
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "ocpp16_websocket_lambda_execution_role" {
  type        = string
  description = "The execution role used by the OCPP16 WebSockets API Lambda functions"
}

variable "external_telemetry_publisher_roles" {
  type        = list(string)
  description = "The execution roles of external (to this account) publishers to the transaction-telemetry kinesis stream"
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  Autoscaling Related Variables
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/
variable "commands_processor_autoscaling_enabled" {
  description = "Flag to enable autoscaling for commands processor"
  type        = bool
  default     = false
}

variable "min_commands_processor_capacity" {
  description = "The min number of tasks for commands processor"
  type        = number
  default     = 1
}

variable "max_commands_processor_capacity" {
  description = "The max number of tasks for commands processor"
  type        = number
  default     = 2
}

variable "ocpp_broadcaster_autoscaling_enabled" {
  description = "Flag to enable autoscaling for ocpp broadcaster"
  type        = bool
  default     = false
}

variable "min_ocpp_broadcaster_capacity" {
  description = "The min number of tasks for ocpp broadcaster"
  type        = number
  default     = 1
}

variable "max_ocpp_broadcaster_capacity" {
  description = "The max number of tasks for ocpp broadcaster"
  type        = number
  default     = 2
}
