locals {
  redis_alarms = {
    "CPUUtilization" = {
      comparison_operator = "GreaterThanThreshold"
      evaluation_periods  = 5
      datapoints_to_alarm = 5
      period              = 60
      threshold           = 90
      statistic           = "Average"
    }
    "EngineCPUUtilization" = {
      comparison_operator = "GreaterThanThreshold"
      evaluation_periods  = 5
      datapoints_to_alarm = 5
      period              = 60
      threshold           = 90
      statistic           = "Average"
    }
    "CurrConnections" = {
      comparison_operator = "GreaterThanThreshold"
      evaluation_periods  = 10
      datapoints_to_alarm = 10
      period              = 60
      threshold           = 500 // Each node can support up to 65k connections. For our use case, we see > 500 connections it’s probably due to some issue on the app (i.e. not closing connections)
      statistic           = "Average"
    }
    "DatabaseMemoryUsagePercentage" = {
      comparison_operator = "GreaterThanThreshold"
      evaluation_periods  = 5
      datapoints_to_alarm = 5
      period              = 60
      threshold           = 70 // If we are consistently above 70% of memory we might want to scale up
      statistic           = "Average"
    }
    "ReplicationLag" = {
      comparison_operator = "GreaterThanThreshold"
      evaluation_periods  = 15
      datapoints_to_alarm = 15
      period              = 60
      threshold           = 0.100 // We’ve never had any datapoints greater than 10ms so far. 100ms is a good threshold to start with
      statistic           = "Average"
    }
  }

  redis_alarm_computed = flatten([
    for memberId in module.redis.member_clusters :
    [
      for metric, alarm in local.redis_alarms : merge(alarm, {
        memberId = memberId
        metric   = metric
      })
    ]
  ])
}

resource "aws_cloudwatch_metric_alarm" "redis_alarms" {
  for_each = length(var.alarm_actions) > 0 ? tomap({
    for alarm in local.redis_alarm_computed : format("%s-%s", alarm.memberId, alarm.metric) => alarm
  }) : {}

  alarm_name          = format("redis-%s", each.key)
  alarm_description   = try(each.value.description, null)
  comparison_operator = each.value.comparison_operator
  evaluation_periods  = each.value.evaluation_periods
  datapoints_to_alarm = each.value.datapoints_to_alarm
  treat_missing_data  = try(each.value.treat_missing_data, null)

  metric_name = each.value.metric
  namespace   = "AWS/ElastiCache"

  threshold          = each.value.threshold
  period             = each.value.period
  statistic          = try(each.value.statistic, null)
  extended_statistic = try(each.value.extended_statistic, null)
  unit               = try(each.value.unit, null)

  dimensions = {
    CacheClusterId = each.value.memberId
  }

  ok_actions    = var.alarm_actions
  alarm_actions = var.alarm_actions

  tags = {
    "pp:applicationRole" = "cache"
  }
}
