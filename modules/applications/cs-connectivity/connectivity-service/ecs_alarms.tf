/*
*  Cloudwatch Alarms for ECS Services
*/
locals {
  ecs_services = [
    module.connections.service_name,
    module.disconnections.service_name,
    module.ppcp_messages.service_name,
    module.status_api.service_name,
    module.commands_api.service_name,
    module.status_processor.service_name,
    module.commands_processor.service_name,
    module.ocpp_broadcaster.service_name,
    module.ocpp16_responder.service_name,
    module.online_status_processor.service_name,
    module.connections_pruner.service_name,
    module.ppcp_connections.service_name,
  ]

  ecs_alarms = {
    CPUUtilization = {
      namespace           = "AWS/ECS"
      comparison_operator = "GreaterThanOrEqualToThreshold"
      evaluation_periods  = 5
      datapoints_to_alarm = 5
      period              = 60
      threshold           = 85
      statistic           = "Average"
      description         = format("Alerts on CPU usage of 85 percent or higher over a window of 5 minutes consistently in each minute for service")
    },
    MemoryUtilization = {
      namespace           = "AWS/ECS"
      comparison_operator = "GreaterThanOrEqualToThreshold"
      evaluation_periods  = 5
      datapoints_to_alarm = 5
      period              = 60
      threshold           = 80
      statistic           = "Average"
      description         = format("Alerts on memory usage of 80 percent or higher over a window of 5 minutes consistently in each minute for service")
    },
    RunningTaskCount = {
      namespace           = "ECS/ContainerInsights"
      comparison_operator = "LessThanOrEqualToThreshold"
      evaluation_periods  = 1
      datapoints_to_alarm = 1
      period              = 60
      threshold           = 0
      statistic           = "Average"
      description         = format("Alerts if number of running tasks is 0 for more than a minute")
    },
    EphemeralStorageUtilized = {
      comparison_operator = "GreaterThanOrEqualToThreshold"
      evaluation_periods  = 1
      datapoints_to_alarm = 1
      threshold           = 60
      description         = "Alerts if ephemeral storage utilized is 60 percent or higher over a window of 5 minutes consistently in each minute for service"

      composite = true

      metric_queries = [
        {
          id          = "ephemeralStorageUtilized"
          expression  = "utilised/reserved*100"
          label       = "PercentageOfEphemeralStorageUsed"
          return_data = "true"
        },
        {
          id = "utilised"
          metric = {
            metric_name = "EphemeralStorageUtilized"
            namespace   = "ECS/ContainerInsights"
            period      = 300
            stat        = "Average"
          }
        },
        {
          id = "reserved"
          metric = {
            metric_name = "EphemeralStorageReserved"
            namespace   = "ECS/ContainerInsights"
            period      = 300
            stat        = "Average"
          }
        }
      ]
    }
  }

  ecs_alarm_computed = {
    false = {}
    true = flatten([
      for service in local.ecs_services :
      [
        for metric, alarm in local.ecs_alarms : merge(alarm, {
          service_name = service,
          metric       = metric,
        })
      ]
    ])
  }
}

resource "aws_cloudwatch_metric_alarm" "cs_component_ecs_alarms" {
  for_each = {
    for alarm in local.ecs_alarm_computed[length(var.alarm_actions) > 0] :
    format("%s-%s", alarm.service_name, alarm.metric) => alarm
  }

  alarm_name        = each.key
  alarm_description = format("%s: %s", each.value.description, each.value.service_name)

  namespace   = try(each.value.composite, false) ? null : each.value.namespace
  statistic   = try(each.value.composite, false) ? null : each.value.statistic
  metric_name = try(each.value.composite, false) ? null : each.value.metric
  period      = try(each.value.composite, false) ? null : each.value.period

  evaluation_periods  = each.value.evaluation_periods
  datapoints_to_alarm = each.value.datapoints_to_alarm
  threshold           = each.value.threshold
  comparison_operator = each.value.comparison_operator

  ok_actions    = var.alarm_actions
  alarm_actions = var.alarm_actions

  dimensions = try(each.value.composite, false) ? null : {
    ClusterName = local.identifier
    ServiceName = each.value.service_name
  }

  dynamic "metric_query" {
    for_each = try(each.value.metric_queries, [])

    content {
      id          = metric_query.value.id
      expression  = try(metric_query.value.expression, null)
      label       = try(metric_query.value.label, null)
      return_data = try(metric_query.value.return_data, null)

      dynamic "metric" {
        for_each = try(metric_query.value.metric, null) != null ? [
          metric_query.value.metric
        ] : []

        content {
          metric_name = metric_query.value.metric.metric_name
          namespace   = metric_query.value.metric.namespace
          period      = metric_query.value.metric.period
          stat        = metric_query.value.metric.stat
          dimensions = {
            ClusterName = local.identifier
            ServiceName = each.value.service_name
          }
        }
      }
    }
  }

  tags = {
    "pp:service" = each.value.service_name
  }
}
