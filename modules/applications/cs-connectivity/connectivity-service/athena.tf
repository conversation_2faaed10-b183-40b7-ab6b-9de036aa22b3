resource "aws_athena_database" "ocpp_logs" {
  name   = "ocpp_logs"
  bucket = aws_s3_bucket.athena_query_execution_bucket.bucket
}

resource "aws_athena_workgroup" "this" {
  name        = "ocpp_logs"
  description = "ocpp_logs workgroup"
  state       = "ENABLED"

  tags = merge(local.tags, {
    GrafanaDataSource : true
  })
}

resource "aws_glue_catalog_table" "ocpp_logs" {
  name          = "ocpp_logs"
  database_name = aws_athena_database.ocpp_logs.name

  table_type = "EXTERNAL_TABLE"

  parameters = {
    "EXTERNAL"                              = "TRUE"
    "projection.enabled"                    = "true"
    "projection.p_protocol.type"            = "enum"
    "projection.p_protocol.values"          = "OCPP1.6,OCPP2.0"
    "projection.p_receivedat.type"          = "date"
    "projection.p_receivedat.format"        = "yyyy/MM/dd/HH"
    "projection.p_receivedat.range"         = "2023/08/22/00,NOW"
    "projection.p_receivedat.interval.unit" = "HOURS"
    "projection.p_receivedat.interval"      = "1"
    "storage.location.template"             = format("s3://%s/$${p_protocol}/$${p_receivedat}/", aws_s3_bucket.ocpp_logs.bucket)
  }

  partition_keys {
    name = "p_protocol"
    type = "string"
  }

  partition_keys {
    name = "p_receivedat"
    type = "string"
  }

  storage_descriptor {
    location      = format("s3://%s/", aws_s3_bucket.ocpp_logs.bucket)
    input_format  = "org.apache.hadoop.mapred.TextInputFormat"
    output_format = "org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat"

    bucket_columns            = []
    compressed                = false
    stored_as_sub_directories = true
    number_of_buckets         = -1

    columns {
      name = "metadata"
      type = "struct<ppid:string,action:string,serialnumber:string,macaddress:string,evseid:tinyint,firmwareversion:string>"
    }
    columns {
      name = "rawevent"
      type = "string"
    }
    columns {
      name = "event"
      type = "struct<messageType:string,messageId:string,action:string,payload:string>"
    }
    columns {
      name = "origin"
      type = "string"
    }
    columns {
      name = "protocol"
      type = "string"
    }
    columns {
      name = "receivedat"
      type = "string"
    }
    ser_de_info {
      parameters = {
        "paths" = "event,metadata,origin,protocol,rawEvent,receivedAt"
      }
      serialization_library = "org.openx.data.jsonserde.JsonSerDe"
    }
  }
}
