resource "aws_iam_role" "transaction_telemetry_publisher" {
  name               = "transaction-telemetry-publisher"
  assume_role_policy = data.aws_iam_policy_document.transaction_telemetry_publisher_trust_policy.json
}

resource "aws_iam_role_policy" "transaction_telemetry_publisher_inline" {
  name = "kinesis"

  role   = aws_iam_role.transaction_telemetry_publisher.id
  policy = data.aws_iam_policy_document.transaction_telemetry_publish_to_kinesis.json
}

data "aws_iam_policy_document" "transaction_telemetry_publisher_trust_policy" {
  statement {
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "AWS"
      identifiers = var.external_telemetry_publisher_roles
    }
  }
}

data "aws_iam_policy_document" "transaction_telemetry_publish_to_kinesis" {
  statement {
    effect = "Allow"
    actions = [
      "kinesis:PutRecord",
      "kinesis:PutRecords",
      "kinesis:DescribeStreamSummary",
      "kinesis:ListShards",
    ]

    resources = [aws_kinesis_stream.transaction_telemetry.arn]
  }
}
