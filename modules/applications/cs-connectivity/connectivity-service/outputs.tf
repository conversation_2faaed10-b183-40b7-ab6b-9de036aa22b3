output "status_api_nlb_dns_name" {
  description = "Dns name of the Status APIs Network Load Balancer."
  value       = module.privatelink_status_api_target.nlb_dns_name
}

output "status_api_nlb_dns_zone_id" {
  description = "Dns zone id of the Status APIs Network Load Balancer."
  value       = module.privatelink_status_api_target.nlb_dns_zone_id
}

output "status_api_vpc_endpoint_service_name" {
  description = "The vpc endpoint service name for the status api."
  value       = module.privatelink_status_api_target.service_name
}

output "commands_api_nlb_dns_name" {
  description = "Dns name of the Commands APIs Network Load Balancer."
  value       = module.privatelink_commands_api_target.nlb_dns_name
}

output "commands_api_nlb_dns_zone_id" {
  description = "Dns zone id of the Commands APIs Network Load Balancer."
  value       = module.privatelink_commands_api_target.nlb_dns_zone_id
}

output "commands_api_vpc_endpoint_service_name" {
  description = "The vpc endpoint service name for the commands api."
  value       = module.privatelink_commands_api_target.service_name
}

output "ocpp16_responder_role_arn" {
  description = "The role ARN of the OCPP 1.6 responder component."
  value       = module.ocpp16_responder.task_role_arn
}

output "transaction_telemetry_stream_arn" {
  description = "The ARN of the transaciton-telemetry Kinesis stream."
  value       = aws_kinesis_stream.transaction_telemetry.arn
}

output "ocpp_cs_requests_sns_topic_arn" {
  description = "The ARN of the ocpp-cs-requests SNS topic."
  value       = aws_sns_topic.ocpp_cs_requests.arn
}

output "transaction_telemetry_publisher_role_arn" {
  description = "The ARN of the role with permissions to publish to the transaction-telemetry kinesis stream"
  value       = aws_iam_role.transaction_telemetry_publisher.arn
}

output "ocpp16_responder_sg_id" {
  description = "Id of security group of the ocpp16 responder."
  value       = module.ocpp16_responder.security_group_id
}
