locals {
  connections_container_definition = merge(local.container_definition, {
    name : format("%s-connections", local.identifier),
    logConfiguration : merge(local.container_definition["logConfiguration"], {
      options : merge(local.container_definition["logConfiguration"]["options"], {
        awslogs-group : "/ecs/${local.identifier}/connections",
      })
    }),
    environment : concat(local.container_definition["environment"], [
      {
        name : "DB_USERNAME",
        value : "connections"
      },
      {
        name : "SQS_QUEUE_URL",
        value : aws_sqs_queue.ocpp_connections.url
      },
      {
        name : "DEVICE_CONNECTION_EVENTS_SNS_TOPIC",
        value : aws_sns_topic.device_connection_events.arn
      },
      {
        name : "ONLINE_STATUS_PROCESSOR_SQS_QUEUE_URL",
        value : aws_sqs_queue.online_status_processor.url
      },
    ])
  })
}
module "connections" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "13.0.0"

  service_type       = "rolling"
  identifier         = format("%s-connections", local.identifier)
  cluster_name       = module.ecs_cluster.name
  cluster_arn        = module.ecs_cluster.arn
  pipeline_role_name = module.ecs_cluster.github_role_name

  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.task_role_connections.json
  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.task_execution_common.json

  memory = try(var.components_fargate_task_size["connections"]["memory"], var.memory)
  cpu    = try(var.components_fargate_task_size["connections"]["cpu"], var.cpu)

  capacity_fargate_base        = var.capacity_fargate_base
  capacity_fargate_weight      = var.capacity_fargate_weight
  capacity_fargate_spot_base   = var.capacity_fargate_spot_base
  capacity_fargate_spot_weight = var.capacity_fargate_spot_weight
  container_definitions        = jsonencode([local.connections_container_definition])

  vpc_id     = var.vpc_id
  subnet_ids = var.vpc_private_subnet_ids

  scaling_min_capacity = var.scaling_min_capacity
  enable_auto_scaling  = false

  log_group_name         = local.connections_container_definition["logConfiguration"]["options"]["awslogs-group"]
  logs_retention_in_days = var.logs_retention_in_days

  depends_on = [
    module.ecs_cluster,
  ]

  additional_kms_administrators = var.kms_admins

  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"
      ]
    }]
  }]

  tags = merge(local.tags, {
    "pp:service" = "connectivity-service:connections"
  })
}

resource "aws_security_group_rule" "fargate_connections_egress" {
  for_each = tomap({
    "ports_all_open" = local.fargate_egress_rule_ports_all_open
  })

  type              = "egress"
  from_port         = each.value.from_port
  to_port           = each.value.to_port
  protocol          = each.value.protocol
  description       = each.value.description
  cidr_blocks       = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks  = lookup(each.value, "ipv6_cidrs", null)
  security_group_id = module.connections.security_group_id
}

data "aws_iam_policy_document" "task_role_connections" {
  statement {
    sid     = "allowAccessToRDSUser"
    actions = ["rds-db:connect"]
    resources = [
      format("arn:aws:rds-db:%s:%s:dbuser:%s/connections",
        data.aws_region.current.name,
        data.aws_caller_identity.current.id,
        module.aurora.cluster_resource_id,
      )
    ]
  }

  statement {
    sid = "allowAccessToSqsQueueForConnections"
    actions = [
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage",
      "sqs:ChangeMessageVisibility",
      "sqs:GetQueueAttributes"
    ]
    resources = [
      aws_sqs_queue.ocpp_connections.arn
    ]
  }

  statement {
    sid = "allowSendMessageToOnlineStatusProcessorSQS"
    actions = [
      "sqs:SendMessage",
    ]
    resources = [
      aws_sqs_queue.online_status_processor.arn
    ]
  }
}
