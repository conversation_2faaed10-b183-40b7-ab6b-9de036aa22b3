locals {
  lambda_iam_role_name    = format("arch2_lambda_execution_role_%s", var.environment)
  codebuild_iam_role_name = format("%s-codebuild", local.identifier)
}

resource "aws_iam_role" "arch2_lambda_execution_role" {
  name               = local.lambda_iam_role_name
  assume_role_policy = data.aws_iam_policy_document.arch2_execution_assume_role.json

  tags = local.tags
}

resource "aws_iam_role_policy" "arch2_lambda_execution_role_inline" {
  for_each = {
    arch2_publish_sns                     = data.aws_iam_policy_document.arch2_publish_sns.json
    arch2_put_record_kinesis              = data.aws_iam_policy_document.arch2_put_record_kinesis.json
    mysql_passwword_secrets_handling      = data.aws_iam_policy_document.arch2_lambda_mysql_user.json
    network_connectivity_secrets_handling = data.aws_iam_policy_document.software_secrets.json
    arch2_invoke_certificate_service      = data.aws_iam_policy_document.arch2_invoke_execute_api.json
    arch2_get_appconfig                   = data.aws_iam_policy_document.arch2_access_appconfig.json
  }

  name = each.key

  role   = aws_iam_role.arch2_lambda_execution_role.id
  policy = each.value
}

resource "aws_iam_role_policy" "arch2_lambda_execution_role_inline_energy_metrics_stream_cross_account" {
  count = var.energy_metrics_stream_cross_account_role != null ? 1 : 0

  name = "arch2_access_energy_metrics_stream"

  role   = aws_iam_role.arch2_lambda_execution_role.id
  policy = data.aws_iam_policy_document.arch2_access_energy_metrics_stream[0].json
}

resource "aws_iam_role_policy" "arch2_lambda_execution_role_inline_certificate_service" {
  count = var.certificate_service_db_role != null ? 1 : 0

  name = "arch2_access_certificate_service_db"

  role   = aws_iam_role.arch2_lambda_execution_role.id
  policy = data.aws_iam_policy_document.arch2_access_certificate_service_database[0].json
}

resource "aws_iam_role_policy" "arch2_lambda_execution_role_inline_smart_charging" {
  count = var.smart_charging_service_db_role != null ? 1 : 0

  name = "arch2_access_smart_charging_service_db"

  role   = aws_iam_role.arch2_lambda_execution_role.id
  policy = data.aws_iam_policy_document.arch2_access_smart_charging_database[0].json
}



data "aws_iam_policy_document" "arch2_execution_assume_role" {
  statement {
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "arch2_publish_sns" {
  statement {
    effect  = "Allow"
    actions = ["sns:Publish"]
    resources = [
      var.charging_station_events_topic,
      var.controller_firmware_update_started_topic
    ]
  }
  statement {
    effect    = "Allow"
    actions   = ["kms:GenerateDataKey*", "kms:Decrypt"]
    resources = [var.controller_firmware_update_started_kms_key]
  }
}

data "aws_iam_policy_document" "arch2_put_record_kinesis" {
  statement {
    effect    = "Allow"
    actions   = ["kinesis:PutRecord"]
    resources = [var.kinesis_stream_arn]
  }
}

data "aws_iam_policy_document" "arch2_lambda_mysql_user" {
  statement {
    sid = "PermitDecryptSecretKMSKey"
    actions = [
      "kms:Decrypt",
    ]
    resources = [
      module.arch2_lambda_db_user_dedicated_kms_key.arn
    ]
  }

  statement {
    sid       = "AllowRetrievalOfDBUserPassword"
    effect    = "Allow"
    actions   = ["secretsmanager:GetSecretValue"]
    resources = [aws_secretsmanager_secret.database_service_user.arn]
  }
}

data "aws_iam_policy_document" "software_secrets" {
  statement {
    sid = "PermitDecryptSecretKMSKey"
    actions = [
      "kms:Decrypt",
    ]
    resources = [
      module.network_connectivity_secrets_kms_key.arn
    ]
  }

  statement {
    sid       = "AllowRetrievalOfDBUserPassword"
    effect    = "Allow"
    actions   = ["secretsmanager:GetSecretValue"]
    resources = [aws_secretsmanager_secret.lambda_secrets.arn]
  }
}

data "aws_iam_policy_document" "arch2_invoke_execute_api" {
  statement {
    effect  = "Allow"
    actions = ["execute-api:Invoke"]
    resources = [
      var.certificate_service_api_gateway_arn
    ]
  }
}

data "aws_iam_policy_document" "arch2_access_smart_charging_database" {
  count = var.smart_charging_service_db_role != null ? 1 : 0

  statement {
    effect    = "Allow"
    actions   = ["sts:AssumeRole"]
    resources = [var.smart_charging_service_db_role]
  }
}

data "aws_iam_policy_document" "arch2_access_energy_metrics_stream" {
  count = var.energy_metrics_stream_cross_account_role != null ? 1 : 0

  statement {
    effect    = "Allow"
    actions   = ["sts:AssumeRole"]
    resources = [var.energy_metrics_stream_cross_account_role]
  }
}

data "aws_iam_policy_document" "arch2_access_certificate_service_database" {
  count = var.certificate_service_db_role != null ? 1 : 0

  statement {
    effect    = "Allow"
    actions   = ["sts:AssumeRole"]
    resources = [var.certificate_service_db_role]
  }
}

data "aws_iam_policy_document" "arch2_access_appconfig" {
  statement {
    effect = "Allow"
    actions = [
      "appconfig:GetLatestConfiguration",
      "appconfig:StartConfigurationSession"
    ]
    resources = [var.lambda_appconfig_arn]
  }
}

resource "aws_iam_role_policy_attachment" "arch2_lambda_vpc_access_execution_role" {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
  role       = aws_iam_role.arch2_lambda_execution_role.name
}

// This is assumed by the csms websocket handlers to invoke the functions to implement POW
resource "aws_iam_role" "arch2_lambda_invocation_role" {
  name               = format("arch2_lambda_invocation_role_%s", var.environment)
  assume_role_policy = data.aws_iam_policy_document.arch2_invocation_assume_role.json

  tags = local.tags
}

resource "aws_iam_role_policy" "arch2_lambda_invocation_role_inline" {
  name = "arch2_invoke_function"

  role   = aws_iam_role.arch2_lambda_invocation_role.id
  policy = data.aws_iam_policy_document.arch2_invoke_function_policy.json
}

data "aws_iam_policy_document" "arch2_invocation_assume_role" {
  statement {
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "AWS"
      identifiers = var.csms_websockets_execution_role_arns
    }
  }
}

data "aws_iam_policy_document" "arch2_invoke_function_policy" {
  statement {
    effect    = "Allow"
    actions   = ["lambda:InvokeFunction"]
    resources = var.lambda_invocation_arns
  }
}

data "aws_iam_policy_document" "codebuild_assume_role" {
  statement {
    effect  = "Allow"
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["codebuild.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "codebuild_serverless" {
  statement {
    sid       = "AllowAPIGatewayPolicies"
    effect    = "Allow"
    actions   = ["apigateway:UpdateRestApiPolicy"]
    resources = ["arn:aws:apigateway:eu-west-1::*"]
  }
  statement {
    sid    = "AllowManagementOfSecurityGroups"
    effect = "Allow"
    actions = [
      "ec2:AuthorizeSecurityGroupEgress",
      "ec2:AuthorizeSecurityGroupIngress",
      "ec2:UpdateSecurityGroupRuleDescriptionsEgress",
      "ec2:DeleteTags",
      "ec2:CreateTags",
      "ec2:ModifySecurityGroupRules",
      "ec2:DescribeSecurityGroups",
      "ec2:UpdateSecurityGroupRuleDescriptionsIngress",
      "ec2:RevokeSecurityGroupIngress",
      "ec2:DescribeSecurityGroupRules",
      "ec2:DescribeSecurityGroupReferences",
      "ec2:CreateSecurityGroup",
      "ec2:RevokeSecurityGroupEgress",
      "ec2:DeleteSecurityGroup",
      "ec2:DescribeStaleSecurityGroups"
    ]
    resources = ["*"]
  }
  statement {
    sid    = "CloudFormationGenerals"
    effect = "Allow"
    actions = [
      "cloudformation:List*",
      "cloudformation:Get*",
      "cloudformation:PreviewStackUpdate",
      "cloudformation:ValidateTemplate"
    ]
    resources = ["*"]
  }
  statement {
    sid    = "CloudFormationStack"
    effect = "Allow"
    actions = [
      "cloudformation:CreateStack",
      "cloudformation:CreateUploadBucket",
      "cloudformation:DeleteStack",
      "cloudformation:DescribeStackEvents",
      "cloudformation:DescribeStackResource",
      "cloudformation:DescribeStackResources",
      "cloudformation:UpdateStack",
      "cloudformation:DescribeStacks"
    ]
    resources = [
      format("arn:aws:cloudformation:*:*:stack/%s*/*", local.serverless_service_name)
    ]
  }
  statement {
    sid    = "CloudFormationChangeSet"
    effect = "Allow"
    actions = [
      "cloudformation:CreateChangeSet",
      "cloudformation:DeleteChangeSet",
      "cloudformation:DescribeChangeSet",
      "cloudformation:ExecuteChangeSet",
    ]
    resources = [
      format("arn:aws:cloudformation:*:*:stack/%s*", local.serverless_service_name)
    ]
  }
  statement {
    sid    = "LambdaGenerals"
    effect = "Allow"
    actions = [
      "lambda:Get*",
      "lambda:List*",
      "lambda:CreateFunction",
      "lambda:UntagResource",
      "lambda:tagResource"
    ]
    resources = ["*"]
  }
  statement {
    sid    = "LambdaFunctions"
    effect = "Allow"
    actions = [
      "lambda:AddPermission",
      "lambda:CreateAlias",
      "lambda:DeleteFunction",
      "lambda:InvokeFunction",
      "lambda:PublishVersion",
      "lambda:RemovePermission",
      "lambda:Update*"
    ]
    resources = [
      format("arn:aws:lambda:*:*:function:%s-*-*", local.serverless_service_name)
    ]
  }
  statement {
    sid     = "DeploymentBucket"
    effect  = "Allow"
    actions = ["s3:*"]
    resources = [
      format("arn:aws:s3:::%s-*-serverlessdeploymentbucket*", lower(local.serverless_service_name))
    ]
  }
  statement {
    sid    = "ApiGateway"
    effect = "Allow"
    actions = [
      "apigateway:GET",
      "apigateway:POST",
      "apigateway:PUT",
      "apigateway:DELETE",
      "apigateway:PATCH"
    ]
    resources = [
      "arn:aws:apigateway:*::/restapis*",
      "arn:aws:apigateway:*::/tags*"
    ]
  }
  statement {
    sid       = "SNS"
    effect    = "Allow"
    actions   = ["sns:Subscribe"]
    resources = ["arn:aws:sns:eu-west-1:959744386191:pod-unit-event-received*"]
  }
  statement {
    sid    = "PassRole"
    effect = "Allow"
    actions = [
      "iam:PassRole"
    ]
    resources = ["arn:aws:iam::*:role/*"]
  }
  statement {
    sid    = "Logs"
    effect = "Allow"
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:DeleteLogGroup",
      "logs:PutLogEvents",
      "logs:DescribeLogStreams",
      "logs:DescribeLogGroups",
      "logs:FilterLogEvents"
    ]
    resources = ["arn:aws:logs:*:*:*"]
  }
  statement {
    sid    = "Events"
    effect = "Allow"
    actions = [
      "events:DescribeRule",
      "events:Put*",
      "events:Remove*",
      "events:Delete*"
    ]
    resources = [format("arn:aws:events:*:*:rule/%s-*-*", local.serverless_service_name)]
  }
  statement {
    sid    = "NetworkInterfaces"
    effect = "Allow"
    actions = [
      "ec2:DeleteNetworkInterface",
      "ec2:DescribeNetworkInterfaces",
      "ec2:DescribeSecurityGroups",
      "ec2:DescribeSubnets",
      "ec2:DescribeVpcs"
    ]
    resources = ["*"]
  }
}

data "aws_iam_policy_document" "codebuild_secrets_permissions" {
  statement {
    sid     = "KmsDecrypt"
    effect  = "Allow"
    actions = ["kms:Decrypt"]
    resources = [
      module.arch2_lambda_db_user_dedicated_kms_key.arn,
      module.network_connectivity_secrets_kms_key.arn
    ]
  }
  statement {
    sid     = "AllowSecretsAccess"
    effect  = "Allow"
    actions = ["secretsmanager:GetSecretValue"]
    resources = [
      aws_secretsmanager_secret.database_service_user.arn,
      aws_secretsmanager_secret.lambda_secrets.arn
    ]
  }
}

resource "aws_iam_role" "codebuild_role" {
  name               = local.codebuild_iam_role_name
  assume_role_policy = data.aws_iam_policy_document.codebuild_assume_role.json

  tags = local.tags
}

resource "aws_iam_role_policy" "codebuild_role_inline" {
  for_each = {
    serverless_permissions = data.aws_iam_policy_document.codebuild_serverless.json
    secret_permissions     = data.aws_iam_policy_document.codebuild_secrets_permissions.json
  }

  name = each.key

  role   = aws_iam_role.codebuild_role.id
  policy = each.value
}