const defaultUsername = process.env.SLACK_DEFAULT_USERNAME;
const defaultChannel = process.env.SLACK_DEFAULT_CHANNEL;
const icon_emoji = ":parrot:";

const channels = JSON.parse(process.env.SLACK_CHANNELS || '{}');

const titleIcons = {
    success: ':greentick:',
    error: ':redtick:',
    info: ':information_source:',
}

/**
 * @typedef {Object} Request
 * @property {string} origin - The message origin, used for the username of the message
 * @property {string} title - The message title
 * @property {string} body - The message body
 * @property {string} type - The message type
 * @property {string} scope - The message scope
 * @property {Object} fields - Additional fields
 */

/**
 * @param {Object} event
 * @param {Array} event.Records
 * @param {Object} event.Records[].Sns
 * @param {string} event.Records[].Sns.Message
 * @returns {Promise<void>}
 */
exports.handler = async (event) => {
    const webhookUrl = await getSlackWebhookUrl();

    for (let record of event.Records) {
        /**
         * @type {Request}
         */
        const request = JSON.parse(record.Sns.Message);
        const subject = record.Sns.Subject;

        const blocks = [];

        // Set title
        const title = `${titleIcons[request.type] || ''} ${request.title || subject || 'Unknown title'}`
        blocks.push(
            {
                type: "section",
                text: {
                    type: "mrkdwn",
                    text: title
                }
            }
        );

        // Set body
        if (request.body) {
            blocks.push(
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: request.body
                    }
                }
            );
        }

        // Additional fields
        if (request.fields && Object.keys(request.fields).length > 0) {
            blocks.push(
                {
                    type: "divider"
                },
                {
                    type: "section",
                    fields: Object.keys(request.fields).map((key) => ({
                        type: "mrkdwn",
                        text: `*${key}:*\n${request.fields[key]}`
                    }))
                }
            );
        }

        blocks.push({
            "type": "context",
            "elements": [
                {
                    "type": "plain_text",
                    "text": `Environment: ${process.env.ENVIRONMENT}`,
                }
            ]
        })

        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                channel: channels[request.scope] || defaultChannel,
                username: request.origin || defaultUsername,
                icon_emoji,
                blocks,
            })
        });

        console.log("Result:", await response.text());
    }
};

async function getSlackWebhookUrl() {
    const httpPort = 2773;
    const url = `http://localhost:${httpPort}/secretsmanager/get?secretId=${process.env.SECRET_ID}`;
    const options = {
        hostname: 'localhost',
        port: httpPort,
        headers: {'X-Aws-Parameters-Secrets-Token': process.env.AWS_SESSION_TOKEN},
        method: 'GET',
    };

    try {
        const response = await fetch(url, options);

        if (!response.ok) {
            const json = await response.json().catch((err) => err.message);
            console.error('Invalid response - response:', response);
            console.error('Invalid response - json:', json);

            throw new Error(`Invalid ${response.status} response`);
        }

        const result = await response.json();

        const {slackWebhookUrl} = JSON.parse(result.SecretString);

        return slackWebhookUrl;
    } catch (error) {
        console.error(`Error getting secret:`, error);

        throw error;
    }
}
