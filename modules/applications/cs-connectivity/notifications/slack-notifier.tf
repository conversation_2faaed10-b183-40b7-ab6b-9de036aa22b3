data "aws_iam_policy_document" "kms_secrets_slack_notifier" {
  statement {
    sid       = "EnableAccountAdministration"
    actions   = ["kms:*"]
    resources = ["*"]
    principals {
      type = "AWS"
      identifiers = concat([
        format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id),
      ], var.kms_admins)
    }
  }

  statement {
    sid     = "PermitSlackNotifierLambdaFunction"
    actions = ["kms:Decrypt"]

    principals {
      type        = "AWS"
      identifiers = [aws_iam_role.slack_notifier.arn]
    }

    resources = ["*"]
  }

  statement {
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }
    condition {
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }
  }
}


resource "aws_kms_key" "secrets_slack_notifier" {
  description             = format("Used for encrypting secrets for %s.", local.slack_notifier.function_name)
  policy                  = data.aws_iam_policy_document.kms_secrets_slack_notifier.json
  deletion_window_in_days = 7
}

resource "aws_kms_alias" "secrets_slack_notifier" {
  name          = format("alias/%s/secrets", local.slack_notifier.function_name)
  target_key_id = aws_kms_key.secrets_slack_notifier.key_id
}

resource "aws_secretsmanager_secret" "slack_notifier" {
  name       = "/lambda/slack-notifier"
  kms_key_id = aws_kms_key.secrets_slack_notifier.id

  recovery_window_in_days = 7
}

resource "aws_secretsmanager_secret_version" "slack_notifier" {
  secret_id = aws_secretsmanager_secret.slack_notifier.id
  secret_string = jsonencode({
    slackWebhookUrl = var.slack_webhook_url
  })
}

resource "aws_secretsmanager_secret_policy" "slack_notifier" {
  secret_arn = aws_secretsmanager_secret.slack_notifier.arn
  policy     = data.aws_iam_policy_document.secrets_slack_notifier.json
}

data "aws_iam_policy_document" "secrets_slack_notifier" {
  statement {
    sid     = "PermitSlackNotifierLambdaFunction"
    actions = ["secretsmanager:GetSecretValue"]

    principals {
      type        = "AWS"
      identifiers = [aws_iam_role.slack_notifier.arn]
    }

    resources = ["*"]
  }
}

resource "aws_security_group" "slack_notifier" {
  name_prefix = "slack-notifier-"
  vpc_id      = var.vpc_id

  tags = {
    Name = "slack-notifier"
  }
}

resource "aws_security_group_rule" "slack_notifier_egress" {
  security_group_id = aws_security_group.slack_notifier.id

  protocol    = "TCP"
  type        = "egress"
  description = "Allow outbound traffic over https"

  from_port = 443
  to_port   = 443

  cidr_blocks      = ["0.0.0.0/0"]
  ipv6_cidr_blocks = ["::/0"]
}

resource "aws_sns_topic_subscription" "slack_notifier" {
  topic_arn = aws_sns_topic.topic.arn
  protocol  = "lambda"
  endpoint  = aws_lambda_function.slack_notifier.arn

  filter_policy_scope = "MessageAttributes"
  filter_policy = jsonencode(
    {
      "target" : ["SLACK"],
    }
  )
}

resource "aws_cloudwatch_log_group" "slack_notifier" {
  name              = format("/aws/lambda/%s", local.slack_notifier.function_name)
  retention_in_days = 7
}

resource "aws_lambda_function" "slack_notifier" {
  function_name = local.slack_notifier.function_name
  role          = aws_iam_role.slack_notifier.arn

  description      = "Lambda function that sends notifications to Slack."
  filename         = data.archive_file.slack_notifier.output_path
  source_code_hash = data.archive_file.slack_notifier.output_base64sha256

  runtime = "nodejs20.x"
  handler = "slack-notifier.handler"
  publish = true
  timeout = 10

  vpc_config {
    security_group_ids = [aws_security_group.slack_notifier.id]
    subnet_ids         = var.subnet_ids
  }

  layers = local.slack_notifier.layers

  environment {
    variables = {
      ENVIRONMENT            = var.environment
      SECRET_ID              = aws_secretsmanager_secret.slack_notifier.id
      SLACK_DEFAULT_USERNAME = "Connectivity"
      SLACK_DEFAULT_CHANNEL  = "squad-connectivity-notifications"
      SLACK_CHANNELS = jsonencode({
        // Scope -> Channel
        "deployment" : "squad-connectivity-deployments",
      })
    }
  }
}

resource "aws_lambda_permission" "sns_slack_notifier" {
  statement_id  = "AllowExecutionFromSNS"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.slack_notifier.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = aws_sns_topic.topic.arn
}

data "archive_file" "slack_notifier" {
  type        = "zip"
  source_file = "${path.module}/lambda/slack-notifier.js"
  output_path = "${path.module}/slack-notifier.zip"
}

data "aws_iam_policy_document" "slack_notifier_assume_role" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "slack_notifier" {
  name               = local.slack_notifier.function_name
  assume_role_policy = data.aws_iam_policy_document.slack_notifier_assume_role.json
}

resource "aws_iam_role_policy" "slack_notifier_inline" {
  name = "Permissions"

  role   = aws_iam_role.slack_notifier.id
  policy = data.aws_iam_policy_document.slack_notifier_permissions.json
}

data "aws_iam_policy_document" "slack_notifier_permissions" {
  statement {
    sid    = "PermitLogging"
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]

    resources = [
      format("arn:aws:logs:*:%s:log-group:/aws/lambda/%s*", data.aws_caller_identity.current.account_id, local.slack_notifier.function_name),
      format("arn:aws:logs:*:%s:log-group:/aws/lambda/%s*:*", data.aws_caller_identity.current.account_id, local.slack_notifier.function_name)
    ]
  }

  statement {
    sid    = "PermitVPCInteraction"
    effect = "Allow"

    actions = [
      "ec2:CreateNetworkInterface",
      "ec2:DescribeNetworkInterfaces",
      "ec2:DeleteNetworkInterface"
    ]

    resources = ["*"]
  }

  statement {
    sid    = "PermitLayersInteraction"
    effect = "Allow"
    actions = [
      "lambda:GetLayerVersion"
    ]
    resources = local.slack_notifier.layers
  }
}
