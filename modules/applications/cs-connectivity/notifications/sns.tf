resource "aws_sns_topic" "topic" {
  name = local.topic_name
}

resource "aws_sns_topic_policy" "default" {
  arn    = aws_sns_topic.topic.arn
  policy = data.aws_iam_policy_document.topic_policy.json
}

data "aws_iam_policy_document" "topic_policy" {
  statement {
    sid = "AllowSNSForGivenPrincipals"
    actions = [
      "SNS:Subscribe",
      "SNS:Publish",
    ]
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = local.topic_principals
    }
    resources = [
      aws_sns_topic.topic.arn,
    ]
  }
}
