locals {
  lambda_functions = toset([
    "connection_authoriser",
    "connection",
    "message",
    "migrator",
    "update_charge_point",
    "get_charge_point",
    "create_transaction",
    "update_transaction",
    "get_charge_point_config",
    "connections_manager",
    "transactions_create"
  ])
}

resource "aws_security_group" "lambda_functions" {
  for_each = local.lambda_functions

  name                   = format("ocpp_lambda_%s_handler_%s", each.value, var.environment)
  description            = "Allow all egress traffic for the lambda associated."
  vpc_id                 = var.vpc_id
  revoke_rules_on_delete = true

  egress {
    description = "Permit all ipv4 egress traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    description      = "Permit all ipv6 egress traffic"
    from_port        = 0
    to_port          = 0
    protocol         = "-1"
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = local.tags
}
