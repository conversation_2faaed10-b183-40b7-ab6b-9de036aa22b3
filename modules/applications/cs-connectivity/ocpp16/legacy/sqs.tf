locals {
  messagesQueueParams = {
    // This must be greater than the lambda timeout
    // Note: with low traffic, AWS can wait up to 20s before invoking the function. So in non-prod envs the visibility timeout must be greater than 20s
    visibility_timeout_seconds = var.environment == "prod" ? 20 : 25

    message_retention_seconds = 600
    receiveCountToDriveToDLQ  = 2
  }
}

resource "aws_sqs_queue" "messages" {
  name = format("%s-messages", local.slug)

  visibility_timeout_seconds = local.messagesQueueParams.visibility_timeout_seconds
  message_retention_seconds  = local.messagesQueueParams.message_retention_seconds

  tags = local.tags
}

resource "aws_sqs_queue_redrive_policy" "messages_dlq" {
  queue_url = aws_sqs_queue.messages.id

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.messages-DLQ.arn
    maxReceiveCount     = local.messagesQueueParams.receiveCountToDriveToDLQ
  })
}

resource "aws_sqs_queue" "messages-DLQ" {
  name = format("%s-messages-DLQ", local.slug)

  message_retention_seconds = 604800 // 7 days

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.messages.arn]
  })

  tags = local.tags
}

resource "aws_cloudwatch_metric_alarm" "messages_DLQ" {
  count = length(var.alarm_actions) > 0 ? 1 : 0

  alarm_name        = format("%s-messages-DLQ", local.slug)
  alarm_description = "This alarm triggers when the messages DLQ has messages in it."

  comparison_operator = "GreaterThanOrEqualToThreshold"
  period              = 60 # 1 minute
  evaluation_periods  = 1
  datapoints_to_alarm = 1

  metric_name        = "ApproximateNumberOfMessagesVisible"
  namespace          = "AWS/SQS"
  statistic          = "Sum"
  threshold          = 1
  alarm_actions      = var.alarm_actions
  ok_actions         = var.alarm_actions
  treat_missing_data = "notBreaching"

  dimensions = {
    QueueName = aws_sqs_queue.messages-DLQ.name
  }

  tags = local.tags
}

resource "aws_cloudwatch_metric_alarm" "messages_not_consumed" {
  count = length(var.alarm_actions) > 0 ? 1 : 0

  alarm_name        = format("%s-messages-too-old", local.slug)
  alarm_description = format("This alarm triggers when the messages don't get processed fast enough.")

  comparison_operator = "GreaterThanThreshold"
  period              = 60 # 1 minute
  evaluation_periods  = 2
  datapoints_to_alarm = 2

  metric_name        = "ApproximateAgeOfOldestMessage"
  namespace          = "AWS/SQS"
  statistic          = "Maximum"
  threshold          = local.messagesQueueParams.visibility_timeout_seconds * local.messagesQueueParams.receiveCountToDriveToDLQ + 5
  alarm_actions      = var.alarm_actions
  ok_actions         = var.alarm_actions
  treat_missing_data = "notBreaching"

  dimensions = {
    QueueName = aws_sqs_queue.messages.name
  }

  tags = local.tags
}
