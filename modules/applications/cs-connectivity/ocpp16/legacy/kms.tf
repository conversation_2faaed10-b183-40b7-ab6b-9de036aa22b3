data "aws_iam_policy_document" "kms_secrets" {
  statement {
    sid       = "Enable Account Administration"
    actions   = ["kms:*"]
    resources = ["*"]

    principals {
      type = "AWS"
      identifiers = concat([
        format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id),
      ], var.kms_admins)
    }
  }

  statement {
    sid = "PermitBreakGlass"
    actions = [
      "kms:*"
    ]
    resources = ["*"]

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    condition {
      test = "ArnLike"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"
      ]
      variable = "aws:PrincipalArn"
    }
  }

  statement {
    sid     = "Permit GitHub Actions KMS Decrypt"
    actions = ["kms:Decrypt"]

    principals {
      type        = "AWS"
      identifiers = [aws_iam_role.github_actions.arn]
    }

    resources = ["*"]
  }
}

resource "aws_kms_key" "secrets" {
  description             = format("Used for encrypting secrets for %s.", local.slug)
  policy                  = data.aws_iam_policy_document.kms_secrets.json
  deletion_window_in_days = 7

  tags = local.tags
}

resource "aws_kms_alias" "secrets" {
  name          = format("alias/%s/secrets", local.slug)
  target_key_id = aws_kms_key.secrets.key_id
}
