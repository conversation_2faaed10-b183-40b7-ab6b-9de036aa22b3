data "aws_iam_policy_document" "assume_role" {
  statement {
    effect = "Allow"

    principals {
      type = "Service"
      identifiers = [
        "lambda.amazonaws.com",
        "edgelambda.amazonaws.com"
      ]
    }

    actions = ["sts:AssumeRole"]
  }
}

data "aws_iam_policy_document" "cloudwatch_logs" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]

    resources = [
      format("arn:aws:logs:*:%s:log-group:/aws/lambda/%s*", data.aws_caller_identity.current.account_id, local.function_name),
      format("arn:aws:logs:*:%s:log-group:/aws/lambda/%s*:*", data.aws_caller_identity.current.account_id, local.function_name)
    ]
  }
}

resource "aws_iam_role" "function" {
  name               = local.function_name
  assume_role_policy = data.aws_iam_policy_document.assume_role.json
  tags               = local.tags
}

resource "aws_iam_role_policy" "function_inline" {
  name = "AllowCloudWatchLogs"

  role   = aws_iam_role.function.id
  policy = data.aws_iam_policy_document.cloudwatch_logs.json
}

data "archive_file" "lambda" {
  type        = "zip"
  source_file = "${path.module}/lambda/index.js"
  output_path = "${path.module}/lambda_function.zip"
}

/*
* Logs of Lambda@Edge functions are stored in CloudWatch Logs for the region in which the function is executed.
* For more information, see https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/edge-functions-logs.html
*/
resource "aws_lambda_function" "function" {
  provider = aws.us-east-1

  description = "Lambda@Edge function used for translating path parameters for OCPP WebSockets API Gateway"
  filename    = data.archive_file.lambda.output_path

  function_name = local.function_name
  runtime       = "nodejs18.x"
  handler       = "index.handler"
  role          = aws_iam_role.function.arn
  publish       = true

  source_code_hash = data.archive_file.lambda.output_base64sha256

  tags = local.tags
}

resource "aws_cloudfront_distribution" "gateway" {
  enabled = true

  default_root_object = "index.html"
  is_ipv6_enabled     = true
  price_class         = "PriceClass_100"

  origin {
    connection_attempts = 3
    connection_timeout  = 10
    origin_id           = "origin1"
    origin_path         = "/${var.environment}"
    domain_name         = var.api_gateway_domain_name

    custom_origin_config {
      http_port                = 80
      https_port               = 443
      origin_protocol_policy   = "https-only"
      origin_keepalive_timeout = 5
      origin_read_timeout      = 60
      origin_ssl_protocols     = ["TLSv1", "TLSv1.1", "TLSv1.2"]
    }
  }

  default_cache_behavior {
    target_origin_id       = "origin1"
    viewer_protocol_policy = "redirect-to-https"

    allowed_methods = ["GET", "HEAD"]
    cached_methods  = ["GET", "HEAD"]

    forwarded_values {
      headers                 = []
      query_string            = false
      query_string_cache_keys = []

      cookies {
        forward           = "none"
        whitelisted_names = []
      }
    }

    lambda_function_association {
      event_type   = "origin-request"
      lambda_arn   = aws_lambda_function.function.qualified_arn
      include_body = false
    }
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
      locations        = []
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = true
  }

  tags = local.tags
}
