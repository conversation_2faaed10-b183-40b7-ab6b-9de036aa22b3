output "cluster_identifier" {
  description = "The RDS Cluster Identifier."
  value       = aws_rds_cluster.this.cluster_identifier
}

output "cluster_resource_id" {
  value = aws_rds_cluster.this.cluster_resource_id
}

output "cluster_arn" {
  description = "Amazon Resource Name (ARN) of cluster."
  value       = aws_rds_cluster.this.arn
}

output "cluster_endpoint" {
  description = "The DNS address of the RDS instance."
  value       = aws_rds_cluster.this.endpoint
}

output "reader_endpoint" {
  description = "A read-only endpoint for the Aurora cluster, automatically load-balanced across replicas."
  value       = aws_rds_cluster.this.reader_endpoint
}

output "engine" {
  description = "The running version of the database."
  value       = aws_rds_cluster.this.engine_version_actual
}

output "port" {
  description = "The DNS address of the  instance."
  value       = aws_rds_cluster.this.endpoint
}

output "cluster_members" {
  description = "List of RDS Instances that are a part of this cluster"
  value       = aws_rds_cluster.this.cluster_members
}

output "storage_encrypted" {
  description = " Specifies whether the DB cluster is encrypted"
  value       = aws_rds_cluster.this.storage_encrypted
}

output "desired_write_instance_class" {
  description = "The RDS instance class."
  value       = var.aurora_write_instance_class
}

output "reader_instance_class" {
  description = "The RDS instance class."
  value       = var.aurora_read_instance_class
}
