resource "aws_sns_topic_subscription" "pod_unit_events_subscription" {
  topic_arn = var.unit_event_received_sns_topic_arn
  protocol  = "sqs"
  endpoint  = var.unit_events_sqs_queue_arn

  raw_message_delivery = true

  filter_policy = jsonencode(
    {
      // Filter out OCPP1.6 HEARTBEAT and POWERON events
      "$or" : [
        { "cs-protocol" : [{ "anything-but" : ["OCPP1.6"] }] },
        { "event" : [{ "anything-but" : ["HEARTBEAT", "POWERON"] }] },
      ],
    }
  )
}
