resource "aws_cloudwatch_metric_alarm" "logging_comms_os_cluster_red_status" {
  count = var.alarms_enabled ? 1 : 0

  alarm_name        = "opensearch-logging-comms-cluster-red"
  alarm_description = "At least one primary shard and its replicas are not allocated to a node."
  metric_name       = "ClusterStatus.red"
  namespace         = "AWS/ES"

  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "1"  # Alert on 1 or more
  period              = "60" # Look at each minute
  evaluation_periods  = "1"  # Look at the last minute
  datapoints_to_alarm = "1"  # One is enough

  dimensions = {
    DomainName = var.logging_comms_os
    ClientId   = data.aws_caller_identity.current.account_id
  }

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions
  tags          = local.tags
}

resource "aws_cloudwatch_metric_alarm" "logging_comms_os_cluster_yellow_status" {
  count = var.alarms_enabled ? 1 : 0

  alarm_name        = "opensearch-logging-comms-cluster-yellow"
  alarm_description = "At least one replica shard is not allocated to a node."
  metric_name       = "ClusterStatus.yellow"
  namespace         = "AWS/ES"

  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "1"  # Alert on 1 or more
  period              = "60" # Look at each minute
  evaluation_periods  = "5"  # Look at the last 5 minutes
  datapoints_to_alarm = "5"  # ... each minute needs to be above threshold

  dimensions = {
    DomainName = var.logging_comms_os
    ClientId   = data.aws_caller_identity.current.account_id
  }

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions
  tags          = local.tags
}

resource "aws_cloudwatch_metric_alarm" "logging_comms_os_cluster_index_writes_blocked" {
  count = var.alarms_enabled ? 1 : 0

  alarm_name        = "opensearch-logging-comms-cluster-index-writes-blocked"
  alarm_description = "The cluster is blocking write requests"
  metric_name       = "ClusterIndexWritesBlocked"
  namespace         = "AWS/ES"

  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "1"   # Alert on 1 or more
  period              = "300" # Check every 5 minutes
  evaluation_periods  = "1"   # Once is enough
  datapoints_to_alarm = "1"

  dimensions = {
    DomainName = var.logging_comms_os
    ClientId   = data.aws_caller_identity.current.account_id
  }

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions
  tags          = local.tags
}

resource "aws_cloudwatch_metric_alarm" "logging_comms_os_master_jvm_memory_pressure" {
  count = var.alarms_enabled ? 1 : 0

  alarm_name        = "opensearch-logging-comms-master-jvm-memory-pressure"
  alarm_description = "Alarm on master JVM pressure being consistently high (above 85%)"
  metric_name       = "MasterJVMMemoryPressure"
  namespace         = "AWS/ES"

  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "85" # Alert on above 85%
  period              = "60" # Check every minute
  evaluation_periods  = "3"  # Look at the last 3 minutes
  datapoints_to_alarm = "3"  # has the threshold been breached for 3 periods

  dimensions = {
    DomainName = var.logging_comms_os
    ClientId   = data.aws_caller_identity.current.account_id
  }

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions
  tags          = local.tags
}

resource "aws_cloudwatch_metric_alarm" "logging_comms_os_jvm_memory_pressure" {
  count = var.alarms_enabled ? 1 : 0

  alarm_name        = "opensearch-logging-comms-jvm-memory-pressure"
  alarm_description = "Alarm on data nodes JVM pressure being consistently high (above 95%)"
  metric_name       = "JVMMemoryPressure"
  namespace         = "AWS/ES"

  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "95" # Alert on above 95%
  period              = "60" # Check every minute
  evaluation_periods  = "3"  # Look at the last 3 minutes
  datapoints_to_alarm = "3"  # has the threshold been breached for 3 periods

  dimensions = {
    DomainName = var.logging_comms_os
    ClientId   = data.aws_caller_identity.current.account_id
  }

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions
  tags          = local.tags
}

resource "aws_cloudwatch_metric_alarm" "logging_comms_os_old_gen_jvm_memory_pressure" {
  count = var.alarms_enabled ? 1 : 0

  alarm_name        = "opensearch-logging-comms-old-gen-jvm-memory-pressure"
  alarm_description = "Alarm on data nodes Old Gen JVM pressure being consistently high (above 80%)"
  metric_name       = "OldGenJVMMemoryPressure"
  namespace         = "AWS/ES"

  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "80" # Alert on above 80%
  period              = "60" # Check every minute
  evaluation_periods  = "3"  # Look at the last 3 minutes
  datapoints_to_alarm = "3"  # has the threshold been breached for 3 periods

  dimensions = {
    DomainName = var.logging_comms_os
    ClientId   = data.aws_caller_identity.current.account_id
  }

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions
  tags          = local.tags
}

resource "aws_cloudwatch_metric_alarm" "logging_comms_os_automated_snapshot_failure" {
  count = var.alarms_enabled ? 1 : 0

  alarm_name        = "opensearch-logging-comms-automated-snapshot-failure"
  alarm_description = "An automated snapshot failed. This failure is often the result of a red cluster health status"
  metric_name       = "AutomatedSnapshotFailure"
  namespace         = "AWS/ES"

  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "1"  # Alert on 1 or more
  period              = "60" # Look at each minute
  evaluation_periods  = "1"  # Look at the last minute
  datapoints_to_alarm = "1"  # One is enough

  dimensions = {
    DomainName = var.logging_comms_os
    ClientId   = data.aws_caller_identity.current.account_id
  }

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions
  tags          = local.tags
}

resource "aws_cloudwatch_metric_alarm" "logging_comms_os_free_storage_space" {
  count = var.alarms_enabled ? 1 : 0

  alarm_name        = "opensearch-logging-comms-free-storage-space"
  alarm_description = "Low free storage. A node in the cluster is down to 150 GiB of free storage space (a days storage is ~7GiB)"
  metric_name       = "FreeStorageSpace"
  namespace         = "AWS/ES"

  statistic           = "Minimum"
  comparison_operator = "LessThanOrEqualToThreshold"
  threshold           = "150528" # Alert on less than 150 GiB free storage
  period              = "60"     # Look at each minute
  evaluation_periods  = "1"      # Look at the last minute
  datapoints_to_alarm = "1"      # One is enough

  dimensions = {
    DomainName = var.logging_comms_os
    ClientId   = data.aws_caller_identity.current.account_id
  }

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions
  tags          = local.tags
}

resource "aws_cloudwatch_metric_alarm" "logging_comms_os_dashboards_unhealthy_nodes" {
  count = var.alarms_enabled ? 1 : 0

  alarm_name        = "opensearch-logging-comms-dashboards-unhealthy-nodes"
  alarm_description = "Opensearch dashboards nodes are unhealthy"
  metric_name       = "OpenSearchDashboardsHealthyNodes"
  namespace         = "AWS/ES"

  statistic           = "Minimum"
  comparison_operator = "LessThanThreshold"
  threshold           = "1"  # Alert on less than 1 healthy node
  period              = "60" # Look at each minute
  evaluation_periods  = "15" # Over the last 15 minutes
  datapoints_to_alarm = "3"  # alarm on 3 unhealthy minutes

  dimensions = {
    DomainName = var.logging_comms_os
    ClientId   = data.aws_caller_identity.current.account_id
  }

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions
  tags          = local.tags
}


resource "aws_cloudwatch_metric_alarm" "logging_comms_os_cpu_utilization_80" {
  count = var.alarms_enabled ? 1 : 0

  alarm_name        = "opensearch-logging-comms-cpu-utilization-above-80"
  alarm_description = "Sustained CPU utilisation above 80% (for 15 mins)"
  metric_name       = "CPUUtilization"
  namespace         = "AWS/ES"

  statistic           = "Maximum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "80"  # Alert on >= 80%
  period              = "300" # Look every 5 minutes
  evaluation_periods  = "3"   # Over the last 15 minutes
  datapoints_to_alarm = "3"   # alarm on 3 consecutive periods

  dimensions = {
    DomainName = var.logging_comms_os
    ClientId   = data.aws_caller_identity.current.account_id
  }

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions
  tags          = local.tags
}

resource "aws_cloudwatch_metric_alarm" "logging_comms_os_threadpool_write_queue" {
  count = var.alarms_enabled ? 1 : 0

  alarm_name        = "opensearch-logging-comms-threadpool-write-queue"
  alarm_description = "The cluster is experiencing high indexing concurrency"
  metric_name       = "ThreadpoolWriteQueue"
  namespace         = "AWS/ES"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "100" # Alert on >= 100
  period              = "60"  # Look at each minute
  evaluation_periods  = "1"   # Look at the last minute
  datapoints_to_alarm = "1"   # Once is enough

  dimensions = {
    DomainName = var.logging_comms_os
    ClientId   = data.aws_caller_identity.current.account_id
  }

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions
  tags          = local.tags
}

resource "aws_cloudwatch_metric_alarm" "logging_comms_os_5xx_errors" {
  count = var.alarms_enabled ? 1 : 0

  alarm_name        = "opensearch-logging-comms-5xx-errors"
  alarm_description = "10% or more of search requests are failing. One or more data nodes might be overloaded, or requests are failing to complete within the idle timeout period"

  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "0.1" # If more than 10% of requests are 5xx
  evaluation_periods  = "1"

  alarm_actions = var.alarm_actions
  ok_actions    = var.alarm_actions
  tags          = local.tags

  metric_query {
    id          = "p1"
    expression  = "e1/t1"
    label       = "PercentageOf5xx"
    return_data = "true"
  }

  metric_query {
    id = "e1"
    metric {
      metric_name = "5xx"
      namespace   = "AWS/ES"
      period      = "300"
      stat        = "Sum"

      dimensions = {
        DomainName = var.logging_comms_os
        ClientId   = data.aws_caller_identity.current.account_id
      }
    }
  }

  metric_query {
    id = "t1"
    metric {
      metric_name = "OpenSearchRequests"
      namespace   = "AWS/ES"
      period      = "300"
      stat        = "Sum"

      dimensions = {
        DomainName = var.logging_comms_os
        ClientId   = data.aws_caller_identity.current.account_id
      }
    }
  }

}
