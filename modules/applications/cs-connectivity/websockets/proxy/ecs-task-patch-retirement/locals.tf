locals {
  event_target_input = jsonencode({
    environment           = var.environment,
    notificationsTopicArn = var.notifications_topic_arn,
    cluster               = var.ecs_cluster_name,
    services              = [var.ecs_service_name],
    containerName         = var.container_name,
    containerPort         = var.container_port,
    codeDeploy = {
      applicationName : var.code_deploy_application_name,
      deploymentGroupName : var.code_deploy_deployment_group_name,
    },
    lambda = {
      nextDeploymentTimeArn : aws_lambda_function.next_deployment_time_generator.arn,
    },
  })

  next_deployment_time_generator = {
    function_name = format("%s-next-deployment-time-generator", var.identifier)
  }
}
