variable "identifier" {
  type        = string
  description = "Identifier for the module"
}

variable "environment" {
  type        = string
  description = "The environment"
}

variable "notifications_topic_arn" {
  type        = string
  description = "ARN of the SNS topic to send notifications to"
}

variable "ecs_cluster_name" {
  type        = string
  description = "The name of the ECS cluster"
}

variable "ecs_service_arn" {
  type        = string
  description = "The ARN of the ECS service"
}

variable "ecs_service_name" {
  type        = string
  description = "The name of the ECS service"
}

variable "container_name" {
  type        = string
  description = "The name of the container"
}

variable "container_port" {
  type        = number
  description = "The port of the container"
}

variable "code_deploy_application_name" {
  type        = string
  description = "The name of the CodeDeploy application"
}

variable "code_deploy_application_arn" {
  type        = string
  description = "The ARN of the CodeDeploy application"
}

variable "code_deploy_deployment_group_name" {
  type        = string
  description = "The name of the CodeDeploy deployment group"
}

variable "code_deploy_deployment_group_arn" {
  type        = string
  description = "The ARN of the CodeDeploy deployment group"
}

variable "code_deploy_deployment_config_arn" {
  type        = string
  description = "The name of the CodeDeploy config"
}
