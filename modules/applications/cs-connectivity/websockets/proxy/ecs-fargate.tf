module "ecs" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "13.0.0"

  identifier         = local.identifier
  repo_names         = ["caddy-mtls"]
  container_insights = var.environment == "prod" ? "enabled" : "disabled"

  tags = local.tags
}

data "aws_iam_policy_document" "task_role" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
    ]
    resources = [
      aws_secretsmanager_secret.certificate.arn,
      aws_secretsmanager_secret.private_key.arn,
    ]
    effect = "Allow"
  }

  statement {
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
    ]
    resources = ["*"]
    effect    = "Allow"
  }
}

module "ecs_task_patch_retirement" {
  count = var.react_to_ecs_task_patch_retirement_events ? 1 : 0

  source = "./ecs-task-patch-retirement"

  environment = var.environment

  identifier              = local.identifier
  notifications_topic_arn = var.notifications_topic_arn

  ecs_cluster_name                  = module.ecs.name
  ecs_service_name                  = module.proxy.service_name
  ecs_service_arn                   = module.proxy.service_arn
  code_deploy_application_name      = aws_codedeploy_app.proxy.name
  code_deploy_application_arn       = aws_codedeploy_app.proxy.arn
  code_deploy_deployment_group_name = aws_codedeploy_deployment_group.proxy.deployment_group_name
  code_deploy_deployment_group_arn  = aws_codedeploy_deployment_group.proxy.arn
  code_deploy_deployment_config_arn = aws_codedeploy_deployment_config.proxy.arn
  container_name                    = local.container_definitions[0].name
  container_port                    = local.container_definitions[0].portMappings[0].containerPort
}

module "proxy" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "13.0.0"

  log_group_name         = local.log_group_name
  logs_retention_in_days = var.logs_retention_in_days

  service_type       = "blue-green"
  identifier         = local.identifier
  cluster_name       = module.ecs.name
  cluster_arn        = module.ecs.arn
  pipeline_role_name = module.ecs.github_role_name

  attach_custom_ecs_task_iam_policy = true
  ecs_task_custom_policy            = data.aws_iam_policy_document.task_role.json

  memory = var.memory
  cpu    = var.cpu

  capacity_fargate_base        = 1
  capacity_fargate_weight      = 1
  capacity_fargate_spot_base   = 0
  capacity_fargate_spot_weight = 0
  container_definitions        = jsonencode(local.container_definitions)

  vpc_id     = var.vpc_id
  subnet_ids = var.vpc_private_subnet_ids

  enable_auto_scaling      = true
  use_default_step_scaling = false
  scaling_min_capacity     = var.scaling_min_capacity
  scaling_max_capacity     = 100

  custom_step_scaling = [
    {
      name = "MemoryUtilizationHigh"
      alarm = {
        threshold = 70
        metrics = [
          {
            id          = "memoryUtilizationHigh"
            return_data = true
            metric = {
              metric_name = "MemoryUtilization"
              namespace   = "AWS/ECS"
              period      = 60
              stat        = "Average"
              dimensions = {
                ClusterName = module.ecs.name
                ServiceName = local.identifier
              }
            }
          }
        ]
      }

      scale_out_policy = {
        cooldown             = 120
        gradual_lower_bound  = 0
        gradual_upper_bound  = 20
        gradual_adjustment   = var.scale_out_adjustment
        critical_lower_bound = 20
        critical_adjustment  = var.scale_out_adjustment
      }

      scale_in_policy = null
    }
  ]

  load_balancing_configuration = [
    {
      target_group_arn = aws_lb_target_group.proxy_blue.arn
      container_name   = local.identifier
      container_port   = local.container_port
    }
  ]

  additional_kms_administrators = var.kms_admins
  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }]
  }]

  tags = local.tags
}

resource "aws_security_group_rule" "proxy_ingress_allow_lb" {
  security_group_id = module.proxy.security_group_id

  type     = "ingress"
  protocol = "tcp"

  description = "Access permitted from load balancer"

  from_port = 443
  to_port   = 443

  source_security_group_id = aws_security_group.proxy_lb.id
}

resource "aws_security_group_rule" "proxy_egress_all" {
  security_group_id = module.proxy.security_group_id

  type     = "egress"
  protocol = "tcp"

  description = "Permit all egress traffic"

  from_port = 0
  to_port   = 65535

  cidr_blocks      = ["0.0.0.0/0"]
  ipv6_cidr_blocks = ["::/0"]
}
