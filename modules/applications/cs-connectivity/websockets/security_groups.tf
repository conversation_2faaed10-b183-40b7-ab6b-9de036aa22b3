resource "aws_security_group_rule" "vpc_endpoint_allow_proxy_crt_renewer" {
  security_group_id = var.vpc_endpoint_execute_api_security_group_id
  type              = "ingress"

  description              = "Allow proxy-crt-renewer over https."
  protocol                 = "tcp"
  from_port                = 443
  to_port                  = 443
  source_security_group_id = module.proxy.crt_renewer.security_group_id
}

resource "aws_security_group_rule" "vpc_endpoint_assets_service_allow_pow_handlers" {
  security_group_id = var.vpc_endpoint_assets_service_security_group_id
  type              = "ingress"

  description              = "Allow POW handlers over http."
  protocol                 = "tcp"
  from_port                = 80
  to_port                  = 80
  source_security_group_id = module.pow_handlers.lambda_security_group_id
}
