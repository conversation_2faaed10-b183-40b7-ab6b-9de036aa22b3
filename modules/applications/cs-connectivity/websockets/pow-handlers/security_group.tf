resource "aws_security_group" "lambda" {
  name_prefix = local.identifier
  vpc_id      = var.vpc_id

  tags = local.tags
}

resource "aws_security_group_rule" "lambda_egress" {
  security_group_id = aws_security_group.lambda.id

  description = "Allow all egress traffic"

  type     = "egress"
  protocol = "tcp"

  from_port = 0
  to_port   = 65535

  cidr_blocks      = ["0.0.0.0/0"]
  ipv6_cidr_blocks = ["::/0"]
}
