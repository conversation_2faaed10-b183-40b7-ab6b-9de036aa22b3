resource "aws_sns_topic" "pod_unit_event_received" {
  name = format("pod-unit-event-received%s", var.environment != "prod" ? "-${var.environment}" : "")

  tags = local.tags
}

resource "aws_sns_topic_policy" "pod_unit_event_received_policy" {
  arn    = aws_sns_topic.pod_unit_event_received.arn
  policy = data.aws_iam_policy_document.pod_unit_event_received_policy.json
}

data "aws_iam_policy_document" "pod_unit_event_received_policy" {
  statement {
    sid    = "__default_statement_ID"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["*"]
    }
    actions = [
      "SNS:GetTopicAttributes",
      "SNS:SetTopicAttributes",
      "SNS:AddPermission",
      "SNS:RemovePermission",
      "SNS:DeleteTopic",
      "SNS:Subscribe",
      "SNS:ListSubscriptionsByTopic",
      "SNS:Publish",
      "SNS:Receive",
    ]
    condition {
      test     = "StringEquals"
      variable = "AWS:SourceOwner"

      values = [
        data.aws_caller_identity.current.account_id,
      ]
    }

    resources = [
      aws_sns_topic.pod_unit_event_received.arn,
    ]
  }

  statement {
    sid    = "allowSubscriptionsFromNetworkAccounts"
    effect = "Allow"
    actions = [
      "SNS:Subscribe",
    ]

    principals {
      type = "AWS"
      identifiers = [
        format("arn:aws:iam::%s:root", var.cs_state_account_id),
        format("arn:aws:iam::%s:root", var.cs_connectivity_account_id),
      ]
    }

    resources = [
      aws_sns_topic.pod_unit_event_received.arn,
    ]
  }

  dynamic "statement" {
    for_each = length(var.publishing_accounts) > 0 ? [1] : []
    content {
      sid    = "allowPublishingFromNetworkAccounts"
      effect = "Allow"
      actions = [
        "SNS:Publish"
      ]

      principals {
        type        = "AWS"
        identifiers = [for id in var.publishing_accounts : format("arn:aws:iam::%s:root", id)]
      }

      resources = [
        aws_sns_topic.pod_unit_event_received.arn,
      ]
    }
  }
}
