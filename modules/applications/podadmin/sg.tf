data "aws_security_groups" "destination_lambdas" {
  filter {
    name   = "group-name"
    values = var.destination_lambda_names
  }
}

resource "aws_security_group" "podadmin" {
  name        = "podadmin-db-${var.environment}"
  description = var.environment == "prod" ? "Security Group for the podadmin databases." : "Security group for podadmin ${var.environment} database"
  vpc_id      = var.vpc_id
}

resource "aws_security_group_rule" "sg_ingress_destination_lambdas" {
  for_each                 = toset(data.aws_security_groups.destination_lambdas.ids)
  description              = "Permit traffic from destination lambda."
  type                     = "ingress"
  from_port                = 3306
  to_port                  = 3306
  protocol                 = "TCP"
  source_security_group_id = each.value
  security_group_id        = aws_security_group.podadmin.id
}

resource "aws_security_group_rule" "sg_ingress_destination_vpc" {
  description       = "Permit traffic from destination account VPCs."
  type              = "ingress"
  from_port         = 3306
  to_port           = 3306
  protocol          = "TCP"
  cidr_blocks       = var.environment == "prod" ? ["*********/16"] : ["*********/16", "********/16"]
  security_group_id = aws_security_group.podadmin.id
}

resource "aws_security_group_rule" "sg_egress_all" {
  description = "Permit all egress traffic."
  type        = "egress"
  from_port   = 0
  to_port     = 0
  protocol    = "-1"
  cidr_blocks = ["0.0.0.0/0"]
  # ipv6_cidr_blocks  = ["::/0"]
  security_group_id = aws_security_group.podadmin.id
}