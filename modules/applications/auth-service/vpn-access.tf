// create routes from the dns to auth

resource "aws_route53_record" "auth_internal" {
  zone_id = var.route53_zone_experience_internal_zone_id
  name    = "auth.${var.route53_zone_experience_internal_name}"
  type    = "A"

  alias {
    name                   = data.aws_lb.auth_internal_alb.dns_name
    zone_id                = data.aws_lb.auth_internal_alb.zone_id
    evaluate_target_health = false
  }
}

resource "aws_security_group_rule" "auth_allow_vpn_access" {
  type              = "ingress"
  description       = "Access permitted to from the VPN"
  from_port         = 80
  to_port           = 80
  protocol          = "tcp"
  cidr_blocks       = local.vpn_cidr_blocks
  security_group_id = one(data.aws_lb.auth_internal_alb.security_groups)
}
