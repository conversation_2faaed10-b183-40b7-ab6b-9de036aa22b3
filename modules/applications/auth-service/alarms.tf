locals {
  alb_alarm_dimensions = {
    LoadBalancer = module.alb.load_balancer_arn_suffix
    TargetGroup  = module.alb.target_group_arn_suffix[0]
  }
  opsgenie_topic_arn = "arn:aws:sns:eu-west-1:${data.aws_caller_identity.current.account_id}:opsgenie-cw-experience-commercial"
}

resource "aws_cloudwatch_metric_alarm" "alb_unhealthy_host_count" {
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-alb-unhealthy-host-count", local.identifier)
  dimensions = local.alb_alarm_dimensions

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "UnHealthyHostCount"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 0

  actions_enabled = true
  alarm_actions   = [local.opsgenie_topic_arn]
  ok_actions      = [local.opsgenie_topic_arn]

  tags = local.tags
}

resource "aws_cloudwatch_metric_alarm" "alb_http_code_elb_4xx_count" {
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-alb-http-code-elb-4xx-count", local.identifier)
  dimensions = local.alb_alarm_dimensions

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "HTTPCode_ELB_4XX_Count"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 100

  actions_enabled = true
  alarm_actions   = [local.opsgenie_topic_arn]
  ok_actions      = [local.opsgenie_topic_arn]

  tags = local.tags
}

resource "aws_cloudwatch_metric_alarm" "alb_http_code_target_4xx_count" {
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-alb-http-code-target-4xx-count", local.identifier)
  dimensions = local.alb_alarm_dimensions

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "HTTPCode_Target_4XX_Count"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 100

  actions_enabled = true
  alarm_actions   = [local.opsgenie_topic_arn]
  ok_actions      = [local.opsgenie_topic_arn]

  tags = local.tags
}

resource "aws_cloudwatch_metric_alarm" "alb_http_code_elb_5xx_count" {
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-alb-http-code-elb-5xx-count", local.identifier)
  dimensions = local.alb_alarm_dimensions

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "HTTPCode_ELB_5XX_Count"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 0

  actions_enabled = true
  alarm_actions   = [local.opsgenie_topic_arn]
  ok_actions      = [local.opsgenie_topic_arn]

  tags = local.tags
}

resource "aws_cloudwatch_metric_alarm" "alb_http_code_target_5xx_count" {
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-alb-http-code-target-5xx-count", local.identifier)
  dimensions = local.alb_alarm_dimensions

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "HTTPCode_Target_5XX_Count"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 0

  actions_enabled = true
  alarm_actions   = [local.opsgenie_topic_arn]
  ok_actions      = [local.opsgenie_topic_arn]

  tags = local.tags
}

resource "aws_cloudwatch_metric_alarm" "alb_target_response_time" {
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-alb-target-response-time", local.identifier)
  dimensions = local.alb_alarm_dimensions

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "TargetResponseTime"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 10

  actions_enabled = true
  alarm_actions   = [local.opsgenie_topic_arn]
  ok_actions      = [local.opsgenie_topic_arn]

  tags = local.tags
}

resource "aws_sns_topic_subscription" "cloudwatch_opsgenie_notifications" {
  topic_arn = local.opsgenie_topic_arn
  protocol  = "https"
  endpoint  = var.cloudwatch_opsgenie_api_url
}
