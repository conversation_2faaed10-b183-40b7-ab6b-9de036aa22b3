data "aws_iam_policy_document" "custom_ecs_task_execution_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend.arn]
  }
  statement {
    sid     = "RetrieveSecretManagerSecretValues"
    actions = ["secretsmanager:GetSecretValue"]
    resources = [
      aws_secretsmanager_secret.this.arn,
    ]
  }
}

// TODO: Review and scope down to only what is required. These were copied from the OpsWorks stack.
data "aws_iam_policy_document" "custom_ecs_task_container_policy" {
  statement {
    sid       = "AllowKMSBackendKeyDecrypt"
    actions   = ["kms:Decrypt"]
    resources = [aws_kms_key.backend.arn]
  }
  statement {
    sid       = "AllowEventBusPutEvents"
    actions   = ["events:PutEvents"]
    resources = ["arn:aws:events:*:${data.aws_caller_identity.current.account_id}:event-bus/*"]
  }
  statement {
    sid     = "AllowSNSPublishEvents"
    actions = ["sns:Publish"]
    resources = [
      "arn:aws:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:accounts-service-user-events${var.broadcast_topic_arn_suffix}"
    ]
  }
  statement {
    sid = "AllowSessionManager"
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
    ]
    resources = ["*"]
  }
}

data "aws_iam_policy_document" "backend" {
  statement {
    sid       = "EnableAccountAdministration"
    actions   = ["kms:*"]
    resources = ["*"]

    principals {
      type = "AWS"
      identifiers = flatten([
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/terraform-ci",
        local.kms_admins
      ])
    }
  }

  statement {
    sid       = "AllowKMSDecrypt"
    actions   = ["kms:Decrypt"]
    resources = ["*"]

    principals {
      type = "AWS"
      identifiers = [
        module.db_migrate.execution_role_arn,
        module.request_handler.execution_role_arn,
        module.request_handler.task_role_arn,
        module.scheduled_task_worker.execution_role_arn,
        module.scheduled_task_worker.task_role_arn,
        module.queue_worker.execution_role_arn,
        module.queue_worker.task_role_arn
      ]
    }
  }

  statement {
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }
    condition {
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }
  }
}

// TODO: Review and scope down to only what is required. These were copied from the OpsWorks stack.
resource "aws_iam_role_policy_attachment" "request_handler_role_policy_attachments" {
  for_each = toset([
    "AmazonSQSFullAccess",
    "AmazonSSMManagedInstanceCore",
    "CloudWatchAgentServerPolicy"
  ])
  role       = module.request_handler.task_role_name
  policy_arn = format("arn:aws:iam::aws:policy/%s", each.key)
}

// TODO: Review and scope down to only what is required. These were copied from the OpsWorks stack.
resource "aws_iam_role_policy_attachment" "queue_worker_role_policy_attachments" {
  for_each = toset([
    "AmazonSQSFullAccess",
    "AmazonSSMManagedInstanceCore",
    "CloudWatchAgentServerPolicy"
  ])
  role       = module.queue_worker.task_role_name
  policy_arn = format("arn:aws:iam::aws:policy/%s", each.key)
}

// TODO: Review and scope down to only what is required. These were copied from the OpsWorks stack.
resource "aws_iam_role_policy_attachment" "scheduled_task_worker_role_policy_attachments" {
  for_each = toset([
    "AmazonSQSFullAccess",
    "AmazonSSMManagedInstanceCore",
    "CloudWatchAgentServerPolicy"
  ])
  role       = module.scheduled_task_worker.task_role_name
  policy_arn = format("arn:aws:iam::aws:policy/%s", each.key)
}
