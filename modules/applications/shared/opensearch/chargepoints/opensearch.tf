module "os_cluster" {
  source                                 = "terraform-enterprise.pod-point.com/technology/opensearch/aws"
  version                                = "1.1.1"
  cluster_type                           = "non-vpc"
  identifier                             = local.identifier
  os_version                             = var.environment == "production" ? "7.9" : "OpenSearch_1.3"
  instance_type                          = var.environment == "production" ? "t3.medium.elasticsearch" : "t3.small.elasticsearch"
  instance_number                        = var.environment == "production" ? 6 : 2
  multi_az_enabled                       = var.environment == "production" ? true : false
  az_number                              = var.environment == "production" ? 3 : null
  volume_size                            = var.environment == "production" ? 30 : 10
  volume_type                            = "gp3"
  master_enabled                         = var.environment == "production" ? true : false
  master_type                            = var.environment == "production" ? "t3.medium.elasticsearch" : null
  master_count                           = var.environment == "production" ? 3 : null
  custom_access_policy                   = data.aws_iam_policy_document.access_policy.json
  node_to_node_encryption_enabled        = true
  kms_encryption_key_arn                 = "arn:aws:kms:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:key/${local.encryption_key_id}"
  tls_security_policy                    = "Policy-Min-TLS-1-0-2019-07"
  custom_endpoint_enabled                = true
  custom_endpoint_domain                 = local.custom_endpoint_domain
  custom_endpoint_subdomain              = local.custom_endpoint_subdomain
  custom_endpoint_domain_certificate_arn = data.aws_acm_certificate.pod_point_cert.arn
  enable_old_formatting                  = true
  cloudwatch_logs                        = lookup(local.cloudwatch_logs, var.environment)
  tags = {
    "pp:environment"                      = lookup(local.tag_environment, var.environment)
    "pp:domain"                           = "network"
    "pp:owner"                            = "network:assets"
    "pp:service"                          = "chargepoints"
    "pp:terraformWorkspace"               = "modules/applications/shared/opensearch/chargepoints"
    "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
  }
}