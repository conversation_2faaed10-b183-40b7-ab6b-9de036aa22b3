data "aws_iam_policy_document" "access_policy" {
  statement {
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = var.access_policy_roles
    }

    actions = [
      "es:*"
    ]

    resources = ["arn:aws:es:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:domain/${local.identifier}/*"]
  }
  statement {
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    actions = [
      "es:*"
    ]

    resources = ["arn:aws:es:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:domain/${local.identifier}/*"]

    condition {
      test     = "IpAddress"
      variable = "aws:SourceIp"
      values   = var.access_policy_ips
    }
  }
}