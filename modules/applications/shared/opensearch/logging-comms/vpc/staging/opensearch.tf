locals {
  identifier                = "logging-comms-vpc-staging"
  firehose_identifer        = format("%s-firehose", local.identifier)
  pod_point_main_account_id = "************"
}

variable "endpoint_vpc_id" {
  type    = string
  default = null
}

variable "endpoint_subnet_ids" {
  type    = list(string)
  default = null
}

variable "endpoint_security_group_mis_web_app" {
  type    = string
  default = null
}
variable "endpoint_security_group_mis_scheduled_task" {
  type    = string
  default = null
}

variable "logging_comms_managed_grafana_cidr" {
  description = "Logging comms managed grafana CIDR."
  type        = string
  default     = null
}

module "os_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/opensearch/aws"
  version = "2.0.3"

  identifier = local.identifier

  vpc_id                  = module.logging_comms_vpc.vpc_id
  subnet_ids              = module.logging_comms_vpc.private_subnets
  availability_zone_count = 1
  engine_version          = "OpenSearch_2.13"
  instance_type           = "t3.small.search"
  instance_number         = 3
  master_enabled          = false
  ebs_options = {
    enabled     = true
    volume_type = "gp3"
    volume_size = 10
    iops        = 3000
    throughput  = 125
  }
  domain_endpoint_options = {
    custom_endpoint_enabled         = true
    custom_endpoint                 = "opensearch-logging-comms-staging.pod-point.com"
    custom_endpoint_certificate_arn = "arn:aws:acm:eu-west-1:************:certificate/d2916c8e-2f96-47ec-8107-2bb56f2e6b14"
    tls_security_policy             = "Policy-Min-TLS-1-2-2019-07"
  }
  auto_tune_options = {
    desired_state = "DISABLED"
  }

  saml_options = {
    enabled                 = true
    master_backend_role     = "e2652444-f031-708b-2035-3bdbcb7e7b6b" # Terraform Managed Group - AWS SSO Access Logging Comms VPN Users
    roles_key               = "Role"
    session_timeout_minutes = 120
    idp = {
      entity_id        = "https://portal.sso.eu-west-1.amazonaws.com/saml/assertion/NjI1OTAxMzE0NTQ2X2lucy1iMzU2ZWFiMDM4MzA1ZGU3"
      metadata_content = file("${path.module}/kibana-idp-metadata.xml")
    }
  }

  tags = {
    "pp:environment"                      = "staging"
    "pp:domain"                           = "network"
    "pp:owner"                            = "network:connectivity"
    "pp:service"                          = "logging-comms-opensearch"
    "pp:terraformWorkspace"               = "modules/applications/shared/opensearch/logging-comms"
    "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
  }
}

resource "aws_elasticsearch_vpc_endpoint" "endpoint" {
  domain_arn = module.os_cluster.arn
  vpc_options {
    security_group_ids = [var.endpoint_security_group_mis_web_app, var.endpoint_security_group_mis_scheduled_task]
    subnet_ids         = var.endpoint_subnet_ids
  }
}

output "vpc_endpoint" {
  description = "The VPC endpoint address for the staging logging comms opensearch cluster"
  value       = aws_elasticsearch_vpc_endpoint.endpoint
}

resource "aws_vpc_security_group_ingress_rule" "allow_internal_vpc" {
  security_group_id = module.os_cluster.security_group_id
  cidr_ipv4         = module.logging_comms_vpc.vpc_cidr_block
  from_port         = 443
  to_port           = 443
  ip_protocol       = "tcp"
}

resource "aws_vpc_security_group_ingress_rule" "allow_logging_comms_vpn" {
  security_group_id = module.os_cluster.security_group_id
  cidr_ipv4         = "**********/22" # AWS Logging Comms VPN
  from_port         = 443
  to_port           = 443
  ip_protocol       = "tcp"
}

resource "aws_vpc_security_group_ingress_rule" "allow_logging_comms_managed_grafana" {
  security_group_id = module.os_cluster.security_group_id
  cidr_ipv4         = var.logging_comms_managed_grafana_cidr
  from_port         = 443
  ip_protocol       = "tcp"
  to_port           = 443
}

resource "aws_vpc_security_group_ingress_rule" "allow_logging_comms_firehose" {
  security_group_id            = module.os_cluster.security_group_id
  from_port                    = 443
  ip_protocol                  = "tcp"
  to_port                      = 443
  referenced_security_group_id = aws_security_group.this_firehose.id
}

resource "aws_vpc_security_group_egress_rule" "allow_logging_comms_external" {
  security_group_id = module.os_cluster.security_group_id

  description = "Egress traffic required by Kinesis Data Firehose"
  cidr_ipv4   = "0.0.0.0/0"
  ip_protocol = "-1"
}

resource "aws_route53_record" "logging_comms_vpc" {
  zone_id = "ZI1YF8KE9MFAW"
  name    = "opensearch-logging-comms-staging"
  type    = "CNAME"
  ttl     = "60"
  records = [module.os_cluster.endpoint]
}

# Firehose stream

resource "aws_kinesis_firehose_delivery_stream" "logging_comms_firehose" {
  name        = local.firehose_identifer
  destination = "opensearch"

  kinesis_source_configuration {
    kinesis_stream_arn = "arn:aws:kinesis:eu-west-1:${local.pod_point_main_account_id}:stream/logging-comms-staging"
    role_arn           = aws_iam_role.this_firehose.arn
  }

  opensearch_configuration {
    domain_arn            = module.os_cluster.arn
    role_arn              = aws_iam_role.this_firehose.arn
    index_name            = "comms"
    index_rotation_period = "OneDay"
    s3_backup_mode        = "AllDocuments"

    document_id_options {
      default_document_id_format = "FIREHOSE_DEFAULT"
    }

    s3_configuration {
      role_arn           = aws_iam_role.this_firehose.arn
      bucket_arn         = module.this_firehose_destination.s3_bucket_arn
      compression_format = "GZIP"
      buffering_size     = 64


      cloudwatch_logging_options {
        enabled         = "true"
        log_group_name  = aws_cloudwatch_log_group.this_firehose.name
        log_stream_name = aws_cloudwatch_log_stream.this_firehose_backend_logs.name
      }
    }

    vpc_config {
      subnet_ids         = module.logging_comms_vpc.private_subnets
      security_group_ids = [aws_security_group.this_firehose.id]
      role_arn           = aws_iam_role.this_firehose.arn
    }
  }

  depends_on = [
    aws_iam_role_policy.this_firehose
  ]
}

resource "aws_security_group" "this_firehose" {
  name        = local.firehose_identifer
  description = "Security group for the ${local.firehose_identifer} kinesis data firehose."
  vpc_id      = module.logging_comms_vpc.vpc_id
}

resource "aws_security_group_rule" "this_firehose_self" {
  type              = "ingress"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  self              = true
  security_group_id = aws_security_group.this_firehose.id
}

resource "aws_security_group_rule" "this_firehose_vpn" {
  type              = "ingress"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  cidr_blocks       = ["**********/22"]
  security_group_id = aws_security_group.this_firehose.id
}

resource "aws_security_group_rule" "this_firehose_opensearch_egress" {
  type              = "egress"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  cidr_blocks       = ["********/24"]
  security_group_id = aws_security_group.this_firehose.id
}

resource "aws_cloudwatch_log_group" "this_firehose" {
  name              = "/aws/kinesisfirehose/${local.firehose_identifer}"
  retention_in_days = 7
}

resource "aws_cloudwatch_log_stream" "this_firehose_backend_logs" {
  log_group_name = aws_cloudwatch_log_group.this_firehose.name
  name           = "BackendLogs"
}

resource "aws_cloudwatch_log_stream" "this_firehose_error_logs" {
  log_group_name = aws_cloudwatch_log_group.this_firehose.name
  name           = "DestinationDelivery"
}

resource "aws_iam_role" "this_firehose" {
  name               = format("%s-firehose", local.identifier)
  assume_role_policy = data.aws_iam_policy_document.this_firehose_assume_role.json
}

data "aws_iam_policy_document" "this_firehose_assume_role" {
  statement {
    sid    = "Firehose"
    effect = "Allow"
    actions = [
      "sts:AssumeRole"
    ]

    principals {
      type        = "Service"
      identifiers = ["firehose.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "this_firehose" {
  statement {
    sid    = "RequiredDestinationBucketActions"
    effect = "Allow"
    actions = [
      "s3:AbortMultipartUpload",
      "s3:GetBucketLocation",
      "s3:GetObject",
      "s3:ListBucket",
      "s3:ListBucketMultipartUploads",
      "s3:PutObject"
    ]

    resources = [
      "${module.this_firehose_destination.s3_bucket_arn}",
      "${module.this_firehose_destination.s3_bucket_arn}/*"
    ]
  }

  statement {
    sid    = "RequiredKinesisActions"
    effect = "Allow"
    actions = [
      "kinesis:DescribeStream",
      "kinesis:GetShardIterator",
      "kinesis:GetRecords",
      "kinesis:ListShards"
    ]

    resources = [
      "arn:aws:kinesis:eu-west-1:${local.pod_point_main_account_id}:stream/logging-comms-staging"
    ]
  }
  statement {
    sid    = "RequiredVPCActions"
    effect = "Allow"
    actions = [
      "ec2:DescribeVpcs",
      "ec2:DescribeVpcAttribute",
      "ec2:DescribeSubnets",
      "ec2:DescribeSecurityGroups",
      "ec2:DescribeNetworkInterfaces",
      "ec2:CreateNetworkInterface",
      "ec2:CreateNetworkInterfacePermission",
      "ec2:DeleteNetworkInterface",
    ]

    resources = ["*"]
  }

  statement {
    sid    = "RequiredCloudWatchLogActions"
    effect = "Allow"
    actions = [
      "logs:PutLogEvents"
    ]

    resources = [
      aws_cloudwatch_log_stream.this_firehose_backend_logs.arn,
      aws_cloudwatch_log_stream.this_firehose_error_logs.arn
    ]
  }

  statement {
    sid    = "RequiredOSActions"
    effect = "Allow"
    actions = [
      "es:DescribeElasticsearchDomain",
      "es:DescribeElasticsearchDomains",
      "es:DescribeElasticsearchDomainConfig",
      "es:ESHttpPost",
      "es:ESHttpPut"
    ]

    resources = [
      "${module.os_cluster.arn}",
      "${module.os_cluster.arn}/*"
    ]
  }


  statement {
    sid    = "RequiredOSEndpointActions"
    effect = "Allow"
    actions = [
      "es:DescribeElasticsearchDomain",
      "es:DescribeElasticsearchDomains",
      "es:DescribeElasticsearchDomainConfig",
      "es:ESHttpPost",
      "es:ESHttpPut",
      "es:ESHttpGet"
    ]

    resources = [
      "${module.os_cluster.arn}/_all/_settings",
      "${module.os_cluster.arn}/_cluster/stats",
      "${module.os_cluster.arn}/comms/_mapping/%FIREHOSE_POLICY_TEMPLATE_PLACEHOLDER%",
      "${module.os_cluster.arn}/_nodes",
      "${module.os_cluster.arn}/_nodes/*/stats",
      "${module.os_cluster.arn}/_stats",
      "${module.os_cluster.arn}/comms/_stats"
    ]
  }
}

resource "aws_iam_role_policy" "this_firehose" {
  name   = "opensearch"
  role   = aws_iam_role.this_firehose.id
  policy = data.aws_iam_policy_document.this_firehose.json
}
