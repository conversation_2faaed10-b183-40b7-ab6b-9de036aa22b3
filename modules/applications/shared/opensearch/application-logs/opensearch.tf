module "os_cluster" {
  source                                 = "terraform-enterprise.pod-point.com/technology/opensearch/aws"
  version                                = "1.0.4"
  cluster_type                           = "non-vpc"
  identifier                             = local.identifier
  os_version                             = "7.10"
  instance_type                          = "m6g.xlarge.elasticsearch"
  instance_number                        = 3
  multi_az_enabled                       = true
  az_number                              = 3
  volume_size                            = 300
  volume_type                            = "gp3"
  master_enabled                         = true
  master_type                            = "r6g.large.elasticsearch"
  master_count                           = 3
  custom_access_policy                   = data.aws_iam_policy_document.access_policy.json
  node_to_node_encryption_enabled        = true
  kms_encryption_key_arn                 = "arn:aws:kms:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:key/${local.encryption_key_id}"
  tls_security_policy                    = "Policy-Min-TLS-1-0-2019-07"
  custom_endpoint_enabled                = true
  custom_endpoint_domain                 = local.custom_endpoint_domain
  custom_endpoint_subdomain              = local.custom_endpoint_subdomain
  custom_endpoint_domain_certificate_arn = data.aws_acm_certificate.pod_point_cert.arn
  cloudwatch_logs                        = []
  tags = {
    "pp:environment"                      = "production"
    "pp:domain"                           = "technology"
    "pp:owner"                            = "technology"
    "pp:service"                          = "application-logs"
    "pp:terraformWorkspace"               = "modules/applications/shared/opensearch/application-logs"
    "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
  }
}