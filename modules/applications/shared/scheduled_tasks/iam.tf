/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *     Scheduler execution role
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_iam_role" "scheduler_execution_role" {
  name = "${var.identifier}-scheduler-execution"

  assume_role_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "scheduler.amazonaws.com"
        },
        "Action" : "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_role_policy" "scheduled_tasks_policy" {
  name = "${var.identifier}-scheduled-tasks"
  role = aws_iam_role.scheduler_execution_role.id

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : "iam:PassRole",
        "Resource" : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : "ecs:RunTask",
        "Resource" : replace(aws_ecs_task_definition.scheduled_task.arn, "/:\\d+$/", ":*")
      }
    ]
  })
}


/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *      ECS Task Execution Role
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_iam_role" "scheduled_tasks_execution_role" {
  name        = "${var.identifier}-execution-role"
  description = "ECS task execution role for ${var.identifier}"
  # Using an aws_iam_policy_document data source was causing the assume policy to be remade on every run, hence putting jsonencode in directly
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      },
    ]
  })
  managed_policy_arns = ["arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"]
}

resource "aws_iam_role_policy" "scheduled_tasks_execution_role_policy" {
  count  = length(data.aws_iam_policy_document.scheduled_tasks_execution_role_policy_document) > 0 ? 1 : 0
  name   = "${var.identifier}-execution-role-policy"
  role   = aws_iam_role.scheduled_tasks_execution_role.id
  policy = data.aws_iam_policy_document.scheduled_tasks_execution_role_policy_document[0].json
}

data "aws_iam_policy_document" "scheduled_tasks_execution_role_policy_document" {
  count = length(var.secrets_manager_secrets_arns) > 0 ? 1 : 0

  dynamic "statement" {
    for_each = length(var.secrets_manager_secrets_arns) > 0 ? [1] : []

    content {
      sid = "RetrieveSecretManagerSecretValues"

      actions = ["secretsmanager:GetSecretValue"]

      resources = var.secrets_manager_secrets_arns
    }
  }

  dynamic "statement" {
    for_each = length(var.secrets_kms_key_arns) > 0 ? [1] : []

    content {
      sid       = "AllowKMSBackendKeyDecrypt"
      actions   = ["kms:Decrypt"]
      resources = var.secrets_kms_key_arns
    }
  }
}

resource "aws_iam_role_policy" "ecs_task_execution" {
  count  = var.attach_custom_ecs_task_execution_iam_policy ? 1 : 0
  name   = "${var.identifier}-custom-execution-role-policy"
  role   = aws_iam_role.scheduled_tasks_execution_role.id
  policy = var.ecs_task_execution_custom_policy
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *           ECS Task Role
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_iam_role" "scheduled_tasks_task_role" {
  name        = "${var.identifier}-task-role"
  description = "ECS task role for ${var.identifier}"
  # Using an aws_iam_policy_document data source was causing the assume policy to be remade on every run, hence putting jsonencode in directly
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      },
    ]
  })
}

resource "aws_iam_role_policy" "ecs_task" {
  count  = var.attach_custom_ecs_task_iam_policy ? 1 : 0
  name   = "${var.identifier}-ecs-task"
  role   = aws_iam_role.scheduled_tasks_task_role.id
  policy = var.ecs_task_custom_policy
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *         Github User Attach
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

data "aws_iam_policy_document" "iam_pass_ecs_roles" {
  statement {
    effect = "Allow"
    actions = [
      "iam:PassRole"
    ]
    resources = [
      aws_iam_role.scheduled_tasks_task_role.arn,
      aws_iam_role.scheduled_tasks_execution_role.arn,
      aws_iam_role.scheduler_execution_role.arn
    ]
  }
}

resource "aws_iam_policy" "iam_pass_ecs_policy" {
  name        = "iam-pass-role-${var.identifier}"
  description = "Policy to allow pass role permissions for the ecs task roles"

  policy = data.aws_iam_policy_document.iam_pass_ecs_roles.json
}

resource "aws_iam_role_policy_attachment" "policy_attachment" {
  role = var.pipeline_role_name

  policy_arn = aws_iam_policy.iam_pass_ecs_policy.arn
}
