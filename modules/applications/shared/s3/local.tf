locals {
  random_id = lower(random_id.suffix.id)
  default_s3_encryption_settings = {
    rule = {
      bucket_key_enabled = false
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }

  default_lifecycle_rules = [
    {
      id      = "noncurrent_object_expiration"
      enabled = true

      transitions = {
        first = {
          days          = 30
          storage_class = "STANDARD_IA"
        }
      }

      noncurrent_version_expiration = {
        days = 30
      }
    }
  ]
}
