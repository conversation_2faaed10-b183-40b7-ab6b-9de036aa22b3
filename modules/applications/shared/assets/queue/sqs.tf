resource "aws_sqs_queue" "this" {
  name = var.queue_name

  kms_master_key_id = var.kms_key_id

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.dlq.arn
    maxReceiveCount     = var.max_receive_count
  })

  delay_seconds = var.delay_seconds
}

resource "aws_sqs_queue" "dlq" {
  name                      = format("%s-dlq", var.queue_name)
  message_retention_seconds = 1209600 // 14 days (the maximum)

  kms_master_key_id = var.kms_key_id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = ["arn:aws:sqs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${var.queue_name}"]
  })
}
