# SNS topic for Opsgenie cloudwatch integration - different to Opsgenie cloudwatch events
resource "aws_sns_topic" "cs_state_cloudwatch_opsgenie_topic" {
  count = var.cs_state_cloudwatch_opsgenie_api != null ? 1 : 0

  provider = aws.pod-point
  name     = format("cs-state-cloudwatch-opsgenie-topic-%s", var.environment)
}

resource "aws_sns_topic_subscription" "cs_state_cloudwatch_opsgenie_notifications" {
  count = length(aws_sns_topic.cs_state_cloudwatch_opsgenie_topic)

  provider  = aws.pod-point
  topic_arn = aws_sns_topic.cs_state_cloudwatch_opsgenie_topic[count.index].arn
  protocol  = "https"
  endpoint  = var.cs_state_cloudwatch_opsgenie_api
}

# SNS topic in global region for r53 healthcheck alarms
resource "aws_sns_topic" "cs_state_cloudwatch_opsgenie_topic_us_east_1" {
  count = var.cs_state_cloudwatch_opsgenie_api != null ? 1 : 0

  provider = aws.pod-point-global
  name     = format("cs-state-cloudwatch-opsgenie-topic-%s", var.environment)
}

resource "aws_sns_topic_subscription" "cs_state_cloudwatch_opsgenie_notifications_us_east_1" {
  count = length(aws_sns_topic.cs_state_cloudwatch_opsgenie_topic_us_east_1)

  provider  = aws.pod-point-global
  topic_arn = aws_sns_topic.cs_state_cloudwatch_opsgenie_topic_us_east_1[count.index].arn
  protocol  = "https"
  endpoint  = var.cs_state_cloudwatch_opsgenie_api
}

# Opsgenie topics created by organisation/aws-management/stackset_templates/opsgenie-integrations.yaml
resource "aws_sns_topic_subscription" "cs_state_opsgenie_notifications" {
  topic_arn = var.grid_opsgenie_topic_arn
  protocol  = "https"
  endpoint  = format("%s?apiKey=%s", var.opsgenie_integration_url, var.grid_opsgenie_api_key)
}

# Opsgenie topic created by organisation/aws-management/stackset_templates/opsgenie-integrations.yaml
resource "aws_sns_topic_subscription" "assets_opsgenie_sns" {
  count = var.assets_opsgenie_sns_endpoint != null ? 1 : 0

  topic_arn = var.assets_opsgenie_topic_arn
  protocol  = "https"
  endpoint  = var.assets_opsgenie_sns_endpoint
}
