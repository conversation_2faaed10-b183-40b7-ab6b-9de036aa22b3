module "commissioning_service_db_user_dedicated_kms_key" {
  source      = "terraform-enterprise.pod-point.com/technology/kms/aws"
  version     = "1.0.0"
  alias_name  = format("%s-db-user", local.identifier)
  description = "Used to encrypt the database passwords stored in secrets managers and of which are used by the commissioning service."
  policy      = data.aws_iam_policy_document.commissioning_service_db_user_kms_policy.json
}

data "aws_iam_policy_document" "commissioning_service_db_user_kms_policy" {
  statement {
    sid = "KeyAdministrators"

    principals {
      type = "AWS"
      identifiers = concat([
        format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id),
      ], var.additional_kms_administrators)
    }

    actions   = ["kms:*"]
    resources = ["*"]
  }

  statement {
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }
    condition {
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }
  }

  statement {
    sid = "AllowECSTaskExecutionDecryptOperations"

    principals {
      type = "AWS"
      identifiers = [
        module.commissioning_api.execution_role_arn,
      ]
    }

    actions   = ["kms:Decrypt"]
    resources = ["*"]
  }

  statement {
    sid = "AllowRDSProxyDecryptOperations"

    principals {
      type = "AWS"
      identifiers = [
        data.aws_iam_role.podadmin_proxy.arn,
      ]
    }

    actions   = ["kms:Decrypt"]
    resources = ["*"]
  }
}

resource "aws_secretsmanager_secret" "database_service_user" {
  name                    = format("/aurora/podadmin-%s/commissioning-service/credentials", var.environment)
  kms_key_id              = module.commissioning_service_db_user_dedicated_kms_key.id
  description             = "Contains the secrets for authenticating to the podadmin database."
  recovery_window_in_days = 7

  tags = local.module_tags
}

resource "aws_secretsmanager_secret_version" "database_service_user" {
  secret_id = aws_secretsmanager_secret.database_service_user.id
  secret_string = jsonencode({
    username = "commissioning_service"
    password = "MANUAL_INTERACTION_REQUIRED_REPLACE_ME"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}

data "aws_iam_role" "podadmin_proxy" {
  name = format("podadmin_%s_rds_proxy", var.environment)
}

resource "aws_iam_role_policy" "podadmin_proxy_allow_encryption" {
  name   = "commissioning-service-permissions"
  role   = data.aws_iam_role.podadmin_proxy.id
  policy = data.aws_iam_policy_document.podadmin_proxy_allow_encryption.json
}

data "aws_iam_policy_document" "podadmin_proxy_allow_encryption" {
  statement {
    sid    = "AllowGetSecretForCommissioningServiceCredentials"
    effect = "Allow"
    resources = [
      aws_secretsmanager_secret.database_service_user.arn
    ]
    actions = ["secretsmanager:GetSecretValue"]
  }

  statement {
    sid    = "AllowKMSDecryptForCommissioningServiceCredentials"
    effect = "Allow"
    resources = [
      module.commissioning_service_db_user_dedicated_kms_key.arn
    ]
    actions = ["kms:Decrypt"]

    condition {
      test     = "StringEquals"
      variable = "kms:ViaService"
      values = [
        "secretsmanager.eu-west-1.amazonaws.com"
      ]
    }
  }
}
