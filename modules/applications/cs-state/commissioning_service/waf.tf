/**
 * IP Sets managed in the organisation/aws-security workspace.
 * Currently IP'sets are only provisioned in the Pod Point AWS account.
*/
data "aws_wafv2_ip_set" "allow_list" {
  provider = aws.pod-point-us-east-1

  count = var.enable_waf ? 1 : 0

  name  = "cf-allow-list"
  scope = "CLOUDFRONT"
}


resource "aws_wafv2_web_acl" "cloudfront" {
  provider = aws.pod-point-us-east-1
  count    = var.enable_waf ? 1 : 0

  name        = format("%s", local.identifier)
  description = format("WAF to monitor and protect traffic for the %s Cloudfront distribution", local.identifier)
  scope       = "CLOUDFRONT"

  default_action {
    block {}
  }

  visibility_config {
    metric_name                = local.identifier
    cloudwatch_metrics_enabled = true
    sampled_requests_enabled   = true
  }

  rule {
    name     = "AllowListedAddr"
    priority = 0

    action {
      allow {}
    }

    statement {
      ip_set_reference_statement {
        arn = data.aws_wafv2_ip_set.allow_list[0].arn
      }
    }

    visibility_config {
      metric_name                = format("%s-AllowListedAddr", local.identifier)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AllowPath-commissioning"
    priority = 1

    action {
      allow {}
    }

    statement {

      byte_match_statement {
        field_to_match {
          uri_path {}
        }
        positional_constraint = "STARTS_WITH"
        search_string         = "/pcbs"
        text_transformation {
          priority = 1
          type     = "NONE"
        }
      }
    }

    visibility_config {
      metric_name                = format("%s-AllowPath-commissioning", local.identifier)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  tags = local.module_tags
}

module "waf_cf_access_logs_database" {
  providers = {
    aws = aws.pod-point-us-east-1
  }

  source  = "terraform-enterprise.pod-point.com/technology/waf/aws//modules/waf_access_logs_database"
  version = "2.1.1"

  count = var.enable_waf ? 1 : 0

  acl_scope                  = "CLOUDFRONT"
  athena_database_identifier = replace(local.identifier, "-", "_")
  glue_catalogue_identifier  = format("%s_waf", replace(local.identifier, "-", "_"))

  query_execution_s3_bucket_identifier = var.environment == "prod" ? "commission-${var.environment}-query" : "commission-stg-query"
  log_bucket_s3_bucket_identifier      = var.environment == "prod" ? "commission-${var.environment}-waf-logs" : "commission-stg-waf-logs"

  waf_acl_arn                  = aws_wafv2_web_acl.cloudfront[0].arn
  waf_acl_name                 = aws_wafv2_web_acl.cloudfront[0].name
  athena_workgroup_identifier  = format("%s-waf", local.identifier)
  athena_workgroup_description = "WAF for the commisioning service cloudfront distribution."

  tags = local.module_tags
}
