data "aws_iam_policy_document" "custom_ecs_task_execution_policy" {
  statement {
    sid     = "AllowKMSBackendKeyDecrypt"
    actions = ["kms:Decrypt"]
    resources = [
      aws_kms_key.backend.arn,
      module.commissioning_service_db_user_dedicated_kms_key.arn
    ]
  }
  statement {
    sid     = "RetrieveSecretManagerSecretValues"
    actions = ["secretsmanager:GetSecretValue"]
    resources = [
      aws_secretsmanager_secret.commissioning_secrets.arn,
      aws_secretsmanager_secret.database_service_user.arn
    ]
  }
}

data "aws_iam_policy_document" "backend" {
  statement {
    sid     = "Enable Account Administration"
    actions = ["kms:*"]
    resources = [
      "*"
    ]
    principals {
      type = "AWS"
      identifiers = concat([
        format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id),
      ], var.additional_kms_administrators)
    }
  }

  statement {
    sid = "Permit ECS Service Task Execution KMS Decrypt"
    actions = [
      "kms:Decrypt"
    ]

    principals {
      type        = "AWS"
      identifiers = [module.commissioning_api.execution_role_arn]
    }
    resources = ["*"]
  }

  statement {
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }
    condition {
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }
  }
}
