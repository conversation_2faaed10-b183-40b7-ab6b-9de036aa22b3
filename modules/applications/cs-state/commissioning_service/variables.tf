variable "environment" {
  description = <<EOF
    The environment of the service.

    This needs to be declared while the service is in the old Pod Point account.

    Otherwise naming conflicts will be present in our terraform apply runs.
  EOF
  type        = string
}

variable "initial_task_def_environments" {
  description = "A List of objects used to set environment variables for the initial ECS task definition."
  type = list(object({
    name  = string
    value = string
  }))
}

variable "ecs_task_role_additional_policies" {
  description = "A list of additional policy ARNs to add to the ECS tasks"
  type        = list(string)
  default     = []
}

variable "vpc_id" {
  description = "The VPC ID."
  type        = string
}

variable "ecs_private_subnets_ids" {
  description = "The Private subnets the ECS Fargate service and tasks will use."
  type        = list(string)
}

variable "alb_public_subnets_ids" {
  description = "The Public subnets the ALB will use."
  type        = list(string)
}

variable "podadmin_db_security_group_id" {
  description = "Podadmin database security group ID"
  type        = string
}

variable "cloudwatch_alarm_topic_arn" {
  type        = string
  description = "The topic to which Cloudwatch Alarm events are published"
}

variable "aws_region" {
  type        = string
  description = "The AWS region for the service."
  default     = "eu-west-1"
}

variable "acm_arn" {
  description = "The arn of the ACM certificate that will be referenced by the load balancer. The value the ACM certificate must support the route53 record name."
  type        = string
}

variable "pod_point_com_domain_name" {
  type        = string
  description = "The domain name for the Commissioning API app"
  default     = "pod-point.com"
}

variable "pod_point_com_hosted_zone" {
  type        = string
  description = "The hosted zone for the Commissioning API app"
  default     = "ZI1YF8KE9MFAW"
}

variable "commissioning_api_domain_name" {
  type        = string
  description = "The name for the Commissioning API load balancer"
}

variable "api_scaling_min_capacity" {
  description = "The minimum number of api processes to maintain"
  type        = number
}

variable "api_scaling_max_capacity" {
  description = "The maximum number of api processes to maintain"
  type        = number
}

variable "api_deployment_minimum_healthy_percent" {
  description = "Lower limit (as a percentage of the service's desiredCount) of the number of running tasks that must remain running and healthy in a service during a deployment."
  type        = number
  default     = 0
}

variable "enable_waf" {
  type        = bool
  description = "Toggle to decide whether AWS WAF ACL resources will be provisioned."
  default     = false
}

variable "cloudfront_realtime_metrics" {
  description = <<EOF
    A flag that indicates whether additional CloudWatch metrics are enabled for a given CloudFront distribution.
    Valid values are `Enabled` and `Disabled`.
  EOF
  type        = string
  default     = "Disabled"
}

variable "acm_certificate_arn" {
  description = "ARN of the AWS Certificate Manager certificate that you wish to use with this distribution. The ACM certificate must be in US-EAST-1."
  type        = string
  default     = "arn:aws:acm:us-east-1:959744386191:certificate/f3d747b5-887c-4ffb-85d3-17e12b4140e8"
}

variable "cloudfront_enable_logging" {
  description = "Whether to enable logging for the cloudfront distribution."
  type        = bool
  default     = false
}

variable "additional_kms_administrators" {
  description = "The ARNs of IAM roles to allow to use the application's KMS key"
  type        = list(string)
  default     = []
}
