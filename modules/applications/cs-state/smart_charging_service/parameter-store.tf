
// EDF-Trading Config
resource "aws_ssm_parameter" "edf-trading-config" {
  name        = "/smart-charging/features/edf-trading"
  description = "EDF Trading Lambda Feature Flags. The following flags are available: createFlexRequests, submitBid. To enable a feature, write it out as a value below and to disable it, remove it from the value."
  type        = "StringList"
  value       = "MANUAL_INTERACTION_REQUIRED_REPLACE_ME"
  tags        = local.module_tags

  lifecycle {
    ignore_changes = [value]
  }

}
