module "smart_charging_ecs_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "10.2.0"

  identifier         = "smart-charging-service"
  repo_names         = ["smart-charging-service"]
  container_insights = var.environment == "prod" ? "enabled" : "disabled"
  additional_github_ci_policy_statements = [
    {
      sid       = "AllowEC2DescribeSecurityGroups"
      effect    = "Allow"
      actions   = ["ec2:DescribeSecurityGroups"]
      resources = ["*"]
    }
  ]

  tags = local.module_tags
}
