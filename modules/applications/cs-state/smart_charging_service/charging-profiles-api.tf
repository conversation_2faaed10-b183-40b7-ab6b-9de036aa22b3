module "charging_profiles_api" {
  source = "../shared/private-api"

  environment = var.environment
  region      = data.aws_region.current.name
  account_id  = data.aws_caller_identity.current.account_id
  subnet_ids  = var.subnet_ids
  vpc_id      = var.vpc_id
  name        = local.charging_profiles_api_ecs_task_name
  ecs_port    = local.api_ecs_port

  deploy_alb = true

  additional_kms_administrators = var.additional_kms_administrators

  # ECS Cluster Info
  ecs_cluster_arn              = module.smart_charging_ecs_cluster.arn
  ecs_cluster_name             = module.smart_charging_ecs_cluster.name
  ecs_cluster_github_role_name = module.smart_charging_ecs_cluster.github_role_name
  ecs_task_execution_policy    = data.aws_iam_policy_document.charging_profiles_api_execution_policy.json

  attach_custom_ecs_task_iam_policy = true
  ecs_task_custom_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : "cloudwatch:PutMetricData",
        "Resource" : "*"
      }
    ]
  })

  # ECS Task Info
  container_definition = templatefile("${path.module}/templates/charging-profiles-api-container.tftpl", {
    "name" : local.charging_profiles_api_ecs_task_name
    "region" : data.aws_region.current.name
    "port" : local.api_ecs_port
    "node_env" : var.environment
    "db_credentials" : aws_secretsmanager_secret.charging_profiles_api_db_credentials.arn
    "db_port" : 5432
    "db_read_endpoint" : module.aurora_smart_charge.reader_endpoint
    "db_write_endpoint" : module.aurora_smart_charge.cluster_endpoint
    "db_pool_acquire_ms" : 5000
    "db_pool_max" : 50
    "db_pool_min" : 1
    "asset_service_api_url" : "http://${module.asset_service_api_privatelink_client.dns_entry[0].dns_name}"
    "connectivity_commands_api_url" : "http://${module.commands_api_privatelink_client.dns_entry[0].dns_name}"
    "connectivity_service_api_url" : "http://${module.connectivity_status_api_privatelink_client.dns_entry[0].dns_name}"
    "smart_charging_service_api_url" : "http://${aws_route53_record.smart_charging_service_api.fqdn}"
    "configcat_sdk_key" : aws_secretsmanager_secret.smart_charging_service_configcat_sdk_key.arn
    "redis_enabled" : true
    "redis_host" : module.smart_charging_service_elasticache_redis.endpoint
    "redis_port" : module.smart_charging_service_elasticache_redis.port
    "redis_password" : module.smart_charging_service_elasticache_redis.auth_token_secret_manager_arn
  })

  cpu    = var.charging_profiles_api_ecs_cpu
  memory = var.charging_profiles_api_ecs_memory

  ecs_force_new_deployment   = true
  ecs_logs_retention_in_days = 30

  ecs_scaling_max_capacity = var.charging_profiles_api_ecs_scaling_max_capacity
  ecs_scaling_min_capacity = var.charging_profiles_api_ecs_scaling_min_capacity

  ecs_use_default_step_scaling = false
  ecs_custom_step_scaling = [
    {
      name = "CPUUtilization-high"
      alarm = {
        threshold = 60
        metrics = [
          {
            id = "cpu_utilization"
            metric = {
              metric_name = "CPUUtilization"
              namespace   = "AWS/ECS"
              period      = 60
              stat        = "Average"
              dimensions = {
                ClusterName = module.smart_charging_ecs_cluster.name
                ServiceName = local.charging_profiles_api_ecs_task_name
              }
            }
            return_data = true
          }
        ]
      }
      scale_out_policy = {
        cooldown             = 120
        gradual_lower_bound  = 0
        gradual_upper_bound  = 20
        gradual_adjustment   = 1
        critical_lower_bound = 20
        critical_adjustment  = 3
      }
      scale_in_policy = {
        cooldown    = 120
        upper_bound = -10
        adjustment  = -1
      }
    }
  ]

  # Api gateway
  api_gateway_ingress_rules   = var.api_gateway_ingress_rules
  execute_api_vpc_endpoint_id = var.execute_api_vpc_endpoint_id
  vpc_endpoint_ids            = var.vpc_endpoint_ids

  tags = local.module_tags
}

data "aws_iam_policy_document" "charging_profiles_api_execution_policy" {
  statement {
    sid     = "AllowKMSBackendKeyDecrypt"
    actions = ["kms:Decrypt"]

    resources = [
      module.kms.arn
    ]
  }
  statement {
    actions = ["secretsmanager:GetSecretValue"]
    resources = [
      aws_secretsmanager_secret.charging_profiles_api_db_credentials.arn,
      aws_secretsmanager_secret.smart_charging_service_configcat_sdk_key.arn,
      module.smart_charging_service_elasticache_redis.auth_token_secret_manager_arn,
    ]
  }
}

resource "aws_route53_record" "charging_profiles_api" {
  zone_id = var.route_53_zone_id
  name    = "${local.charging_profiles_api_ecs_task_name}.${var.route_53_zone_name}"
  type    = "A"

  alias {
    name                   = module.charging_profiles_api.nlb_dns_name
    zone_id                = module.charging_profiles_api.nlb_dns_zone_id
    evaluate_target_health = false
  }
}

resource "aws_security_group_rule" "charging_profiles_api_allow_vpn_ingress" {
  type        = "ingress"
  description = "Access permitted to from the VPN"
  from_port   = 80
  to_port     = 80
  protocol    = "tcp"
  cidr_blocks = [
    "**********/22"
  ]
  security_group_id = module.charging_profiles_api.alb_security_group_id
}

resource "aws_security_group_rule" "charging_profiles_api_allow_vpc_ingress" {
  type              = "ingress"
  description       = "Access permitted from the VPC"
  from_port         = 80
  to_port           = 80
  protocol          = "tcp"
  cidr_blocks       = [var.vpc_cidr_block]
  security_group_id = module.charging_profiles_api.alb_security_group_id
}
