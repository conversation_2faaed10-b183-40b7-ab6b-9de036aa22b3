resource "aws_cloudwatch_event_bus" "smart_charging" {
  name = "smart-charging"
  tags = local.module_tags
}

resource "aws_scheduler_schedule_group" "scheduled_flex_events" {
  name = "flex-events"
  tags = local.module_tags
}

resource "aws_scheduler_schedule_group" "scheduled_delegated_control_events" {
  name = "delegate-control"
  tags = local.module_tags
}

resource "aws_scheduler_schedule_group" "scheduled_charge_now_events" {
  name = "charge-now"
  tags = local.module_tags
}

resource "aws_scheduler_schedule_group" "tariffs_events" {
  name = "tariffs"
  tags = local.module_tags
}

resource "aws_schemas_discoverer" "smart_charging_discoverer" {
  source_arn  = aws_cloudwatch_event_bus.smart_charging.arn
  description = "Auto discover smart-charging events"
  tags        = local.module_tags
}

resource "aws_schemas_registry" "smart_charging" {
  name        = "smart-charging-registry"
  description = "smart-charging schema registry"
  tags        = local.module_tags
}

data "aws_iam_policy_document" "smart_charging_scheduler_role_assume_role_policy" {
  statement {
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["scheduler.amazonaws.com"]
    }

    condition {
      test     = "StringEquals"
      variable = "aws:SourceAccount"
      values   = [data.aws_caller_identity.current.account_id]
    }
  }
}

resource "aws_iam_role" "smart_charging_scheduler_role" {
  name               = "smart-charging-scheduler-role"
  assume_role_policy = data.aws_iam_policy_document.smart_charging_scheduler_role_assume_role_policy.json

  inline_policy {
    name = "allow-put-events"
    policy = jsonencode({
      Version = "2012-10-17"
      Statement = [
        {
          Effect = "Allow"
          Action = [
            "events:PutEvents"
          ]
          Resource = [aws_cloudwatch_event_bus.smart_charging.arn]
        }
      ]
    })
  }

  tags = local.module_tags
}

data "aws_iam_policy_document" "experience_forwarding_rule_assume_role_policy" {
  statement {
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "experience_forwarding_rule_role" {
  name               = "experience-forwarding-rule-role"
  assume_role_policy = data.aws_iam_policy_document.experience_forwarding_rule_assume_role_policy.json

  inline_policy {
    name = "allow-put-events"
    policy = jsonencode({
      Version = "2012-10-17"
      Statement = [
        {
          Effect = "Allow"
          Action = [
            "events:PutEvents"
          ]
          Resource = [var.experience_event_bus_arn]
        }
      ]
    })
  }

  tags = local.module_tags
}

resource "aws_cloudwatch_event_rule" "experience_forwarding_rule" {
  name           = "experience_forwarding_rule"
  event_bus_name = aws_cloudwatch_event_bus.smart_charging.name

  event_pattern = jsonencode({
    "detail-type" = ["UserEnodeVehicleCreated", "UserEnodeVehicleLinked", "ChargeByTimeTargetUnmet", "EnodeUserCredentialsInvalidated", "UserEnodeVehicleInterventions"]
  })
}

resource "aws_cloudwatch_event_target" "experience_event_bus" {
  rule           = aws_cloudwatch_event_rule.experience_forwarding_rule.name
  arn            = var.experience_event_bus_arn
  event_bus_name = aws_cloudwatch_event_bus.smart_charging.name

  role_arn = aws_iam_role.experience_forwarding_rule_role.arn
}
