module "aurora_smart_charge" {
  source  = "terraform-enterprise.pod-point.com/technology/aurora/aws"
  version = "5.2.2"

  identifier       = "smart-charging"
  vpc_id           = var.vpc_id
  subnet_group_ids = var.subnet_ids
  port             = local.database_port

  engine         = "aurora-postgresql"
  engine_version = var.db_engine_version

  admin_username = local.database_admin_username
  database_name  = "smart_charging"

  # CA cert rollover
  ca_cert_identifier                = "rds-ca-rsa2048-g1"
  apply_cluster_changes_immediately = var.environment != "prod" ? true : false

  # Added on upgrade from module version 3.1.1 to 4.1.1
  aurora_managed_admin_user = false

  cluster_preffered_maintenance_window        = "Mon:02:00-Mon:03:00"
  cluster_preffered_backup_window             = "00:30-02:00"
  enable_aws_backup                           = var.environment == "prod" ? true : false
  cluster_deletion_protection                 = true
  cluster_allow_major_version_upgrade         = true
  cluster_iam_database_authentication_enabled = true

  instance_enable_performance_insights = true

  # Parameters
  cluster_parameter_group_family = "aurora-postgresql14"
  cluster_parameters             = {}

  instance_parameter_group_family = "aurora-postgresql14"
  instance_parameters             = {}

  # Cluster instances
  cluster_instance_class = var.db_cluster_instance_class
  cluster_instance_count = var.db_cluster_instance_count

  # KMS encription
  secret_manager_admin_user_kms_encryption_key     = module.kms.arn
  storage_kms_encryption_key                       = module.kms.arn
  instance_performance_insights_kms_encryption_key = module.kms.arn

  # tags
  tags = local.module_tags
}
