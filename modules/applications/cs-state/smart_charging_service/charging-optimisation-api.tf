module "charging_optimisation_api" {
  source = "../shared/private-api"

  environment = var.environment
  region      = data.aws_region.current.name
  account_id  = data.aws_caller_identity.current.account_id
  subnet_ids  = var.subnet_ids
  vpc_id      = var.vpc_id
  name        = local.charging_optimisation_api_ecs_task_name
  ecs_port    = local.api_ecs_port

  deploy_alb = true

  additional_kms_administrators = var.additional_kms_administrators

  # ECS Cluster Info
  ecs_cluster_arn              = module.smart_charging_ecs_cluster.arn
  ecs_cluster_name             = module.smart_charging_ecs_cluster.name
  ecs_cluster_github_role_name = module.smart_charging_ecs_cluster.github_role_name
  ecs_task_execution_policy    = data.aws_iam_policy_document.charging_optimisation_api_execution_policy.json

  attach_custom_ecs_task_iam_policy = true
  ecs_task_custom_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : "cloudwatch:PutMetricData",
        "Resource" : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "sqs:SendMessage",
          "sqs:ReceiveMessage",
          "sqs:DeleteMessage",
          "sqs:ChangeMessageVisibility",
          "sqs:GetQueueAttributes"
        ],
        "Resource" : [
          aws_sqs_queue.forecast_requests.arn
        ]
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "kms:Decrypt"
        ],
        "Resource" : [
          module.kms.arn
        ]
      }
    ]
  })

  # ECS Task Info
  container_definition = templatefile("${path.module}/templates/charging-optimisation-api-container.tftpl", {
    "name" : local.charging_optimisation_api_ecs_task_name
    "region" : data.aws_region.current.name
    "port" : local.api_ecs_port
    "node_env" : var.environment
    "db_credentials" : aws_secretsmanager_secret.charging_optimisation_api_db_credentials.arn
    "db_port" : 5432
    "db_read_endpoint" : module.aurora_smart_charge.reader_endpoint
    "db_write_endpoint" : module.aurora_smart_charge.cluster_endpoint
    "db_pool_acquire_ms" : 5000
    "db_pool_max" : 20
    "db_pool_min" : 1
    "configcat_sdk_key" : aws_secretsmanager_secret.smart_charging_service_configcat_sdk_key.arn
    "urls" : {
      "smart_charging_service_api_url" : "http://${aws_route53_record.smart_charging_service_api.fqdn}"
      "energy_metrics_api_url" : module.energy_metrics_api.api_gateway_vpce_url
      "forecast_requests_queue_url" : aws_sqs_queue.forecast_requests.url
    }
  })

  ecs_force_new_deployment   = true
  ecs_logs_retention_in_days = 30

  ecs_scaling_min_capacity = var.charging_optimisation_api_ecs_scaling_min_capacity
  ecs_scaling_max_capacity = var.charging_optimisation_api_ecs_scaling_max_capacity

  # Api gateway
  api_gateway_ingress_rules   = var.api_gateway_ingress_rules
  execute_api_vpc_endpoint_id = var.execute_api_vpc_endpoint_id
  vpc_endpoint_ids            = var.vpc_endpoint_ids

  tags = local.module_tags
}

data "aws_iam_policy_document" "charging_optimisation_api_execution_policy" {
  statement {
    sid     = "AllowKMSBackendKeyDecrypt"
    actions = ["kms:Decrypt"]

    resources = [
      module.kms.arn
    ]
  }
  statement {
    actions = ["secretsmanager:GetSecretValue"]
    resources = [
      aws_secretsmanager_secret.charging_optimisation_api_db_credentials.arn,
      aws_secretsmanager_secret.smart_charging_service_configcat_sdk_key.arn
    ]
  }
}

resource "aws_route53_record" "charging_optimisation_api" {
  zone_id = var.route_53_zone_id
  name    = "${local.charging_optimisation_api_ecs_task_name}.${var.route_53_zone_name}"
  type    = "A"

  alias {
    name                   = module.charging_optimisation_api.nlb_dns_name
    zone_id                = module.charging_optimisation_api.nlb_dns_zone_id
    evaluate_target_health = false
  }
}

resource "aws_security_group_rule" "charging_optimisation_api_allow_vpn_ingress" {
  type        = "ingress"
  description = "Access permitted to from the VPN"
  from_port   = 80
  to_port     = 80
  protocol    = "tcp"
  cidr_blocks = [
    "**********/22",
    "**********/22"
  ]
  security_group_id = module.charging_optimisation_api.alb_security_group_id
}

resource "aws_security_group_rule" "charging_optimisation_api_allow_vpc_ingress" {
  type              = "ingress"
  description       = "Access permitted from the VPC"
  from_port         = 80
  to_port           = 80
  protocol          = "tcp"
  cidr_blocks       = [var.vpc_cidr_block]
  security_group_id = module.charging_optimisation_api.alb_security_group_id
}
