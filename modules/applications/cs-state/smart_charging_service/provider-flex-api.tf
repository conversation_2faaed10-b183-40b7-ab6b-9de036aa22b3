resource "aws_api_gateway_domain_name" "api_flex" {
  certificate_arn = aws_acm_certificate_validation.flex_star_us_east_1.certificate_arn
  domain_name     = "api.${var.flex_subdomain}.pod-point.com"

  tags = local.module_tags
}

resource "aws_route53_record" "api_flex" {
  provider = aws.us-east-1

  name    = aws_api_gateway_domain_name.api_flex.domain_name
  type    = "A"
  zone_id = aws_route53_zone.flex.id

  alias {
    evaluate_target_health = true
    name                   = aws_api_gateway_domain_name.api_flex.cloudfront_domain_name
    zone_id                = aws_api_gateway_domain_name.api_flex.cloudfront_zone_id
  }
}

resource "aws_security_group" "flex_provider_security_group" {
  name        = "flex-provider-lambdas"
  description = "Security group for the flex provider lambdas"
  vpc_id      = var.vpc_id

  tags = local.module_tags
}

resource "aws_security_group_rule" "flex_provider_security_group_egress" {
  type              = "egress"
  description       = "Allows all outbound traffic."
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  security_group_id = aws_security_group.flex_provider_security_group.id
  cidr_blocks       = ["0.0.0.0/0"]
}
