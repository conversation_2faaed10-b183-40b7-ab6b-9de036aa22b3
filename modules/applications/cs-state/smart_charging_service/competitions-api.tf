module "competitions_api" {
  source = "../shared/private-api"

  environment = var.environment
  region      = data.aws_region.current.name
  account_id  = data.aws_caller_identity.current.account_id
  subnet_ids  = var.subnet_ids
  vpc_id      = var.vpc_id
  name        = local.competitions_api_ecs_task_name
  ecs_port    = local.api_ecs_port

  deploy_alb = true

  additional_kms_administrators = var.additional_kms_administrators

  # ECS Cluster Info
  ecs_cluster_arn              = module.smart_charging_ecs_cluster.arn
  ecs_cluster_name             = module.smart_charging_ecs_cluster.name
  ecs_cluster_github_role_name = module.smart_charging_ecs_cluster.github_role_name
  ecs_task_execution_policy    = data.aws_iam_policy_document.competitions_api_execution_policy.json

  attach_custom_ecs_task_iam_policy = true
  ecs_task_custom_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : "cloudwatch:PutMetricData",
        "Resource" : "*"
      }
    ]
  })

  # ECS Task Info
  container_definition = templatefile("${path.module}/templates/competitions-api-container.tftpl", {
    name              = local.competitions_api_ecs_task_name
    region            = data.aws_region.current.name
    port              = local.api_ecs_port
    db_credentials    = aws_secretsmanager_secret.competitions_db_credentials.arn
    db_port           = 5432
    db_read_endpoint  = module.aurora_smart_charge.reader_endpoint
    db_write_endpoint = module.aurora_smart_charge.cluster_endpoint
    node_env          = var.environment
    axle_base_url     = var.axle_base_url
    flex_api_base_url = "https://${aws_api_gateway_domain_name.api_flex.domain_name}"
    axle_credentials  = aws_secretsmanager_secret.axle_credentials.arn
    mailchimp_api_key = aws_secretsmanager_secret.mailchimp_api_key.arn
    configcat_sdk_key = aws_secretsmanager_secret.smart_charging_service_configcat_sdk_key.arn
    "urls" : {
      "smart_charging_service_api_url" : "http://${aws_route53_record.smart_charging_service_api.fqdn}"
      "opt_out_url" : "https://${var.flex_subdomain}.pod-point.com/faq",
    }
  })

  ecs_force_new_deployment   = true
  ecs_logs_retention_in_days = 30

  ecs_scaling_min_capacity = var.competitions_api_ecs_scaling_min_capacity
  ecs_scaling_max_capacity = var.competitions_api_ecs_scaling_max_capacity

  # Api gateway
  api_gateway_ingress_rules   = var.api_gateway_ingress_rules
  execute_api_vpc_endpoint_id = var.execute_api_vpc_endpoint_id
  vpc_endpoint_ids            = var.vpc_endpoint_ids

  tags = local.module_tags
}

data "aws_iam_policy_document" "competitions_api_execution_policy" {
  statement {
    sid     = "RetrieveSecretManagerSecretValues"
    actions = ["secretsmanager:GetSecretValue"]

    resources = [
      aws_secretsmanager_secret.competitions_db_credentials.arn,
      aws_secretsmanager_secret.axle_credentials.arn,
      aws_secretsmanager_secret.mailchimp_api_key.arn,
      aws_secretsmanager_secret.smart_charging_service_configcat_sdk_key.arn
    ]
  }

  statement {
    sid     = "AllowKMSBackendKeyDecrypt"
    actions = ["kms:Decrypt"]

    resources = [
      module.kms.arn
    ]
  }
}

resource "aws_route53_record" "competitions_api" {
  zone_id = var.route_53_zone_id
  name    = "${local.competitions_api_ecs_task_name}.${var.route_53_zone_name}"
  type    = "A"

  alias {
    name                   = module.competitions_api.nlb_dns_name
    zone_id                = module.competitions_api.nlb_dns_zone_id
    evaluate_target_health = false
  }
}

resource "aws_security_group_rule" "competitions_api_allow_vpn_ingress" {
  type        = "ingress"
  description = "Access permitted to from the VPN"
  from_port   = 80
  to_port     = 80
  protocol    = "tcp"
  cidr_blocks = [
    "**********/22",
    "**********/22"
  ]
  security_group_id = module.competitions_api.alb_security_group_id
}

resource "aws_security_group_rule" "competitions_api_allow_vpc_ingress" {
  type              = "ingress"
  description       = "Access permitted from the VPC"
  from_port         = 80
  to_port           = 80
  protocol          = "tcp"
  cidr_blocks       = [var.vpc_cidr_block]
  security_group_id = module.competitions_api.alb_security_group_id
}
