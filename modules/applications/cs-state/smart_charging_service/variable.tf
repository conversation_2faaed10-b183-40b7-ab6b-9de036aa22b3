/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *       Module Parameters
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "environment" {
  description = "Environment name infrastructure is being deployed to."
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "subnet_ids" {
  type = list(any)
}

variable "vpc_link_sg_name" {
  description = "Name of the VPC link security group"
  type        = string
  default     = ""
}

variable "vpc_id" {
  type = string
}

variable "vpc_cidr_block" {
  type = string
}

variable "additional_kms_administrators" {
  description = "The ARNs of IAM roles to allow to use the application's KMS key"
  type        = list(string)
  default     = []
}

variable "flex_subdomain" {
  description = "The subdomain to use for this environment of the Flex API (e.g. 'staging-flex' or 'flex')"
  type        = string
}

variable "axle_base_url" {
  description = "The base URL of the Axle API"
  type        = string
  default     = "https://sandbox.axle.energy"
}

variable "set_mail_pod_point_com_dkim" {
  description = "If true, the mail.pod-point.com domain will be configured with DKIM"
  type        = bool
  default     = false
}

variable "commands_api_vpc_endpoint_service_name" {
  description = "The name of the VPC endpoint service for the commands API"
  type        = string
}

variable "asset_service_api_vpc_endpoint_service_name" {
  description = "The name of the VPC endpoint service for the asset service API"
  type        = string
}

variable "connectivity_status_api_vpc_endpoint_service_name" {
  description = "The name of the VPC endpoint service name for the connectivity status api"
  type        = string
}

variable "configuration_api_vpc_endpoint_service_name" {
  description = "The name of the VPC endpoint service name for the configuration api"
  type        = string
}

variable "driver_account_service_api_vpc_endpoint_service_name" {
  description = "The created VPC endpoint service name for the driver account service api"
  type        = string
}

variable "subscriptions_api_vpc_endpoint_service_name" {
  description = "The name of the VPC endpoint service for the subscriptions service API"
  type        = string
}

variable "dreev_api_url" {
  description = "The URL of the Dreev API"
  type        = string
  default     = "https://csms.staging.dreev.net"
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *         API Gateway
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "api_gateway_ingress_rules" {
  description = "A map containing Security Group Rules for the API Gateway VPC endpoint "
  type = map(object({
    from_port             = string
    to_port               = string
    protocol              = string
    description           = string
    ipv4_cidrs            = optional(list(string))
    ipv6_cidrs            = optional(list(string))
    source_security_group = optional(string)
  }))

  default = {}
}

variable "execute_api_vpc_endpoint_id" {
  description = "The VPC Endpoint for the execute-api service"
  type        = string
}

variable "vpc_endpoint_ids" {
  description = "List of VPC endpoints that should be allowed access to the API"
  type        = list(string)
}

variable "mobile_api_endpoint_service_names" {
  description = "A list of VPC endpoint service names to give the mobile vpc access to"
  type        = map(string)
}

variable "mobile_api_account_id" {
  description = "The account ID of the mobile API"
  type        = string
}

/*
 * arch2 lambda -> smart charging service
 */

variable "arch2_lambda_api_endpoint_service_names" {
  description = "A list of VPC endpoint service names to give arch2 lambda vpc access to"
  type        = map(string)
}

variable "arch2_lambda_api_account_id" {
  description = "The account ID of the arch2 lambdas"
  type        = string
}

variable "arch2_lambda_subnet_ids" {
  description = "The subnet ids of arch2 lambda's vpc configuration"
  type        = list(string)
}

variable "arch2_lambda_security_groups" {
  description = "The arch2 lambda security groups"
  type        = list(string)
}

variable "arch2_lambda_vpc_id" {
  description = "vpc id of that arch2 lambda connects to"
  type        = string
}


/*
 * POD ADMIN
 */

variable "pod_admin_database" {
  type    = string
  default = "podpoint"
}

variable "pod_admin_reader_endpoint" {
  type = string
}

variable "pod_admin_writer_endpoint" {
  type = string
}

variable "pod_admin_port" {
  type    = number
  default = 3306
}

variable "podadmin_cluster_name" {
  description = "The name of the podadmin Aurora cluster."
  type        = string
}

variable "podadmin_database_creds_kms_admins" {
  description = "The arns of owners to assign to the KMS key used for administering the podadmin database secret."
  type        = list(string)
}

/*
 * PPCP API (Arch2 Lambda)
 */

variable "ppcp_api_execution_roles" {
  type = list(string)
}

variable "rds_proxy_id" {
  type        = string
  description = "When terraform creates an RDS proxy it doesn't export the ID so we need to inject it into the security group here"
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *         Aurora
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "db_cluster_instance_class" {
  description = "The instance class to use for the database cluster"
  type        = string
  default     = "db.t4g.medium"
}

variable "db_cluster_instance_count" {
  description = "The number of instances to use for the database cluster"
  type        = number
  default     = 2
}

variable "db_engine_version" {
  description = "The version of the database engine to use"
  type        = string
  default     = "14.15"
}

/*
 * .cs-state.dev internal dns access
 */

variable "route_53_zone_id" {
  description = "The route 53 zone id for the pool api service."
  type        = string
}

variable "route_53_zone_name" {
  description = "The route 53 zone name for the pool api service."
  type        = string
}

/*
 * Redis cache
 */

variable "cache_replicas_per_node_group" {
  type        = number
  description = "The number of replica nodes per node group."
  default     = 1
}

variable "cache_node_group_count" {
  type        = number
  description = "The number of node groups in the cache cluster."
  default     = 1
}

variable "cache_instance_type" {
  type        = string
  description = "The instance type to use for the cache cluster."
  default     = "cache.t4g.micro"
}

# smart-charging-service-api

variable "smart_charging_service_api_ecs_scaling_max_capacity" {
  description = "The maximum number of tasks to run for the smart-charging-service-api ECS service"
  type        = number
  default     = 4
}

variable "smart_charging_service_api_ecs_scaling_min_capacity" {
  description = "The minimum number of tasks to run for the smart-charging-service-api ECS service"
  type        = number
  default     = 2
}

variable "smart_charging_service_api_ecs_scaling_statistic" {
  description = "The CPU metric to use for scaling the smart-charging-service-api ECS service (Average or Maximum)"
  type        = string
  default     = "Average"
}

variable "smart_charging_service_api_ecs_cpu" {
  description = "CPU allocated to fargate"
  type        = number
  default     = 1024
}

variable "smart_charging_service_api_ecs_memory" {
  description = "Memory allocated to fargate"
  type        = number
  default     = 2048
}

variable "smart_charging_service_api_log_level" {
  description = "The log level for the smart-charging-service-api"
  type        = string
  default     = "info"
}

# charging-profiles-api

variable "charging_profiles_api_ecs_scaling_max_capacity" {
  description = "The maximum number of tasks to run for the charging-profiles-api ECS service"
  type        = number
  default     = 4
}

variable "charging_profiles_api_ecs_scaling_min_capacity" {
  description = "The minimum number of tasks to run for the charging-profiles-api ECS service"
  type        = number
  default     = 2
}

variable "charging_profiles_api_ecs_cpu" {
  description = "CPU allocated to fargate"
  type        = number
  default     = 256
}

variable "charging_profiles_api_ecs_memory" {
  description = "Memory allocated to fargate"
  type        = number
  default     = 512
}

# energy metrics api

variable "energy_metrics_api_ecs_scaling_max_capacity" {
  description = "The maximum number of tasks to run for the energy-metrics-api ECS service"
  type        = number
  default     = 4
}

variable "energy_metrics_api_ecs_scaling_min_capacity" {
  description = "The minimum number of tasks to run for the energy-metrics-api ECS service"
  type        = number
  default     = 2
}

# competitions api

variable "competitions_api_ecs_scaling_max_capacity" {
  description = "The maximum number of tasks to run for the competitions-api ECS service"
  type        = number
  default     = 4
}

variable "competitions_api_ecs_scaling_min_capacity" {
  description = "The minimum number of tasks to run for the competitions-api ECS service"
  type        = number
  default     = 2
}

# charging optimisation api

variable "charging_optimisation_api_ecs_scaling_max_capacity" {
  description = "The maximum number of tasks to run for the charging-optimisation-api ECS service"
  type        = number
  default     = 4
}

variable "charging_optimisation_api_ecs_scaling_min_capacity" {
  description = "The minimum number of tasks to run for the charging-optimisation-api ECS service"
  type        = number
  default     = 2
}

# tariffs api

variable "tariffs_api_ecs_scaling_max_capacity" {
  description = "The maximum number of tasks to run for the tariffs-api ECS service"
  type        = number
  default     = 4
}

variable "tariffs_api_ecs_scaling_min_capacity" {
  description = "The minimum number of tasks to run for the tariffs-api ECS service"
  type        = number
  default     = 2
}

# vehicles api

variable "vehicles_api_ecs_scaling_max_capacity" {
  description = "The maximum number of tasks to run for the vehicles-api ECS service"
  type        = number
  default     = 4
}

variable "vehicles_api_ecs_scaling_min_capacity" {
  description = "The minimum number of tasks to run for the vehicles-api ECS service"
  type        = number
  default     = 2
}


/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *       Timestream Parameters
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "edf_energy_trades_magnetic_store_retention_period_in_days" {
  description = "The number of days to retain data in the smart-charging stream"
  type        = number
  default     = 365
}

variable "edf_energy_trades_memory_store_retention_period_in_hours" {
  description = "The longest delay (in hours) between now and when data is acceptable to be written to the smart-charging stream"
  type        = number
  default     = 24
}

variable "edf_price_forecasts_magnetic_store_retention_period_in_days" {
  description = "The number of days to retain data in the smart-charging stream"
  type        = number
  default     = 365
}

variable "edf_price_forecasts_memory_store_retention_period_in_hours" {
  description = "The longest delay (in hours) between now and when data is acceptable to be written to the smart-charging stream"
  type        = number
  default     = 24
}

variable "smart_charging_retention_period_in_days" {
  description = "The number of days to retain data in the smart-charging stream"
  type        = number
  default     = 365
}

variable "smart_charging_oldest_writable_data_in_hours" {
  description = "The longest delay (in hours) between now and when data is acceptable to be written to the smart-charging stream"
  type        = number
  default     = 24
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *       Network Assets
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "network_assets_account_id" {
  description = "The network assets account id"
  type        = string
}

variable "forecast_requests_max_receive_count" {
  description = <<EOF
        The number of times a message is delivered to the source queue before being moved to the dead-letter queue.
    EOF
  type        = number
  nullable    = true
  default     = 3
}

variable "charging_station_provisioning_max_receive_count" {
  description = <<EOF
        The number of times a message is delivered to the source queue before being moved to the dead-letter queue.
    EOF
  type        = number
  nullable    = true
  default     = 4
}

variable "experience_account_ids" {
  description = "The experience account ids matching the environment (i.e. dev = [], staging = [dev, staging], prod = [prod])"
  type        = map(string)
}

variable "experience_account_id" {
  description = "The experience account ids matching the environment"
  type        = string
}

variable "experience_event_bus_arn" {
  description = "The arn of the experience event bus"
  type        = string
}

variable "smart_charging_service_api_vpc_endpoint_service_name" {
  // note: this has to be hard coded because api-gateway creates it and does not expose it
  // in terraform; nor can it be persuaded to tag the created resource properly.
  description = "The created VPC endpoint service name for the smart charging service api"
  type        = string
}

variable "competitions_api_vpc_endpoint_service_name" {
  // note: this has to be hard coded because api-gateway creates it and does not expose it
  // in terraform; nor can it be persuaded to tag the created resource properly.
  description = "The created VPC endpoint service name for the competitions api"
  type        = string
}

variable "private_subnets_ids" {
  description = "The private subnet ids"
  type        = list(string)
}

variable "public_subnets_ids" {
  description = "The public subnet ids"
  type        = list(string)
}
