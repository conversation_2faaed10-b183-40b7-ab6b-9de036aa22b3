module "energy_metrics_api" {
  source = "../shared/private-api"

  environment = var.environment
  region      = data.aws_region.current.name
  account_id  = data.aws_caller_identity.current.account_id
  subnet_ids  = var.subnet_ids
  vpc_id      = var.vpc_id
  name        = local.energy_metrics_api_ecs_task_name
  ecs_port    = local.api_ecs_port

  deploy_alb = true

  # ECS Cluster
  ecs_cluster_arn              = module.smart_charging_ecs_cluster.arn
  ecs_cluster_name             = module.smart_charging_ecs_cluster.name
  ecs_cluster_github_role_name = module.smart_charging_ecs_cluster.github_role_name
  ecs_task_execution_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "kms:Decrypt"
        ],
        "Resource" : [
          "arn:aws:kms:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:key/*"
        ]
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "secretsmanager:GetSecretValue"
        ],
        "Resource" : [
          aws_secretsmanager_secret.smart_charging_service_configcat_sdk_key.arn,
          aws_secretsmanager_secret.energy_metrics_api_db_credentials.arn
        ]
      }
    ]
  })

  attach_custom_ecs_task_iam_policy = true
  ecs_task_custom_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "timestream:DescribeEndpoints",
          "timestream:SelectValues",
        ],
        "Resource" : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "timestream:Select",
          "timestream:Unload"
        ],
        "Resource" : "arn:aws:timestream:eu-west-1:${data.aws_caller_identity.current.account_id}:database/energy_metrics/table/energy_metrics*"
      },
      {
        "Effect" : "Allow",
        "Action" : "cloudwatch:PutMetricData",
        "Resource" : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "s3:ListBucket",
          "s3:GetBucketAcl"
        ],
        "Resource" : [
          module.energy_metrics_reports_bucket.s3_bucket_arn
        ]
      },
      {
        "Action" : [
          "s3:AbortMultipartUpload",
          "s3:GetBucketAcl",
          "s3:GetObject",
          "s3:GetObjectAcl",
          "s3:PutObject",
        ],
        "Effect" : "Allow",
        "Resource" : [
          "${module.energy_metrics_reports_bucket.s3_bucket_arn}/*"
        ]
      }
    ]
  })

  # ECS Task
  container_definition = templatefile("${path.module}/templates/energy-metrics-api-container.tftpl", {
    "configcat_sdk_key_arn" : aws_secretsmanager_secret.smart_charging_service_configcat_sdk_key.arn
    "db_credentials_arn" : aws_secretsmanager_secret.energy_metrics_api_db_credentials.arn
    "db_port" : 5432
    "db_read_endpoint" : module.aurora_smart_charge.reader_endpoint
    "db_write_endpoint" : module.aurora_smart_charge.cluster_endpoint
    "name" : local.energy_metrics_api_ecs_task_name
    "node_env" : var.environment
    "port" : local.api_ecs_port
    "region" : data.aws_region.current.name
    "reports_bucket" : module.energy_metrics_reports_bucket.s3_bucket_id
  })

  ecs_force_new_deployment   = true
  ecs_logs_retention_in_days = 30

  ecs_scaling_max_capacity = var.energy_metrics_api_ecs_scaling_max_capacity
  ecs_scaling_min_capacity = var.energy_metrics_api_ecs_scaling_min_capacity

  # API Gateway
  api_gateway_ingress_rules   = var.api_gateway_ingress_rules
  execute_api_vpc_endpoint_id = var.execute_api_vpc_endpoint_id
  vpc_endpoint_ids            = var.vpc_endpoint_ids

  additional_kms_administrators = var.additional_kms_administrators

  tags = local.module_tags
}

resource "aws_route53_record" "energy_metrics_api" {
  zone_id = var.route_53_zone_id
  name    = "${local.energy_metrics_api_ecs_task_name}.${var.route_53_zone_name}"
  type    = "A"

  alias {
    name                   = module.energy_metrics_api.nlb_dns_name
    zone_id                = module.energy_metrics_api.nlb_dns_zone_id
    evaluate_target_health = false
  }
}

resource "aws_security_group_rule" "energy_metrics_api_allow_vpn_ingress" {
  type        = "ingress"
  description = "Access permitted to from the VPN"
  from_port   = 80
  to_port     = 80
  protocol    = "tcp"
  cidr_blocks = [
    "**********/22",
    "**********/22"
  ]
  security_group_id = module.energy_metrics_api.alb_security_group_id
}

resource "aws_security_group_rule" "energy_metrics_api_allow_vpc_ingress" {
  type              = "ingress"
  description       = "Access permitted from the VPC"
  from_port         = 80
  to_port           = 80
  protocol          = "tcp"
  cidr_blocks       = [var.vpc_cidr_block]
  security_group_id = module.energy_metrics_api.alb_security_group_id
}

module "energy_metrics_reports_bucket" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.1.2"

  bucket_prefix = "energy-metrics-reports-${data.aws_caller_identity.current.account_id}"

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
      bucket_key_enabled = true
    }
  }

  lifecycle_rule = [
    {
      id         = "DefaultLifecycle"
      enabled    = false
      expiration = {}
    }
  ]

  tags = local.module_tags
}
