resource "aws_iam_role" "database_access_role" {
  name               = format("%s-db-access-role", local.identifier)
  assume_role_policy = data.aws_iam_policy_document.database_access_assume_role_policy.json

  tags = local.module_tags
}

data "aws_iam_policy_document" "database_access_assume_role_policy" {
  statement {
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "AWS"
      identifiers = var.ppcp_api_execution_roles
    }
  }
}

resource "aws_iam_policy" "database_access_policy" {
  name        = format("%s-database-access-policy", local.identifier)
  description = format("Grants connect access to the %s database", local.identifier)
  policy      = data.aws_iam_policy_document.database_access_policy.json

  tags = local.module_tags
}

data "aws_iam_policy_document" "database_access_policy" {
  statement {
    effect  = "Allow"
    actions = ["rds-db:connect"]

    resources = [
      format(
        "arn:aws:rds-db:%s:%s:dbuser:%s/%s",
        local.region,
        local.account_id,
        module.aurora_smart_charge.cluster_resource_id,
        local.database_ppcp_api_username
      ),
      format(
        "arn:aws:rds-db:%s:%s:dbuser:%s/%s",
        local.region,
        local.account_id,
        var.rds_proxy_id,
        local.rds_proxy_arch2_lambda_user
      )
    ]
  }
}

resource "aws_iam_role_policy_attachment" "database_access_policy_attachment" {
  role       = aws_iam_role.database_access_role.name
  policy_arn = aws_iam_policy.database_access_policy.arn
}
