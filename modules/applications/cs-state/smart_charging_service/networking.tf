moved {
  from = module.database_egress_rules["ports_all_open"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_egress_rules["ports_all_open"]
}

resource "aws_security_group_rule" "database_egress_rules" {
  for_each = local.database_egress_rules

  type                     = "egress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = lookup(each.value, "security_group_id", module.aurora_smart_charge.security_group_id)
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)
}

moved {
  from = module.database_ingress_rules["permit_vpn_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_vpn_access"]
}

moved {
  from = module.database_ingress_rules["permit_smart_charging_service_api_ecs_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_smart_charging_service_api_ecs_access"]
}

moved {
  from = module.database_ingress_rules["permit_grafana_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_grafana_access"]
}

moved {
  from = module.database_ingress_rules["permit_main_vpc_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_main_vpc_access"]
}

moved {
  from = module.database_ingress_rules["permit_rds_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_rds_access"]
}

moved {
  from = module.database_ingress_rules["permit_data_prod_vpc_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_data_prod_vpc_access"]
}

moved {
  from = module.database_ingress_rules["permit_data_staging_vpc_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_data_staging_vpc_access"]
}

moved {
  from = module.database_ingress_rules["permit_data_dev_vpc_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_data_dev_vpc_access"]
}

moved {
  from = module.database_ingress_rules["permit_competitions_api_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_competitions_api_access"]
}

resource "aws_security_group_rule" "database_ingress_rules" {
  for_each = local.database_ingress_rules

  type                     = "ingress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = lookup(each.value, "security_group_id", module.aurora_smart_charge.security_group_id)
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)
}

module "commands_api_privatelink_client" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws//modules/privatelink-client"
  version = "2.4.0"

  client_ingress_rules = {
    smart_charging_service_api = {
      description           = "Connectivity permitted from smart charging service API (cs-state) to commands API (cs-connectivity)"
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.smart_charging_service_api.security_group_id
    }
    charging_profiles = {
      description           = "Connectivity permitted from the charging profiles API (cs-state) to commands API (cs-connectivity)"
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.charging_profiles_api.security_group_id
    }
  }

  target = {
    name         = "commands-api"
    service_name = var.commands_api_vpc_endpoint_service_name
  }

  vpc_id     = var.vpc_id
  subnet_ids = var.subnet_ids

  tags = local.module_tags
}

module "asset_service_api_privatelink_client" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws//modules/privatelink-client"
  version = "2.4.0"

  client_ingress_rules = {
    firmware_upgrade_api = {
      description           = "Connectivity permitted from smart charging service API (cs-state) to asset service api (network-assets)"
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.smart_charging_service_api.security_group_id
    },
    flex_provider_notifier = {
      description           = "Connectivity permitted from the flex provider lambdas to asset service api (network-assets)"
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = aws_security_group.flex_provider_security_group.id
    },
    charging_profiles = {
      description           = "Connectivity permitted from the charging profiles API to asset service api (network-assets)"
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.charging_profiles_api.security_group_id
    }
    tariffs_api = {
      description           = "Connectivity permitted from the tariffs API to asset service api (network-assets)"
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.tariffs_api.security_group_id
    }
  }

  target = {
    name         = "asset-service-api"
    service_name = var.asset_service_api_vpc_endpoint_service_name
  }

  vpc_id     = var.vpc_id
  subnet_ids = var.subnet_ids

  tags = local.module_tags
}

module "connectivity_status_api_privatelink_client" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws//modules/privatelink-client"
  version = "2.4.0"

  client_ingress_rules = {
    flex_provider_security_group = {
      description           = "Connectivity permitted from the flex provider lambdas"
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = aws_security_group.flex_provider_security_group.id
    }
    smart_charging_service_api_security_group = {
      description           = "Connectivity permitted from the smart charging service API"
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.smart_charging_service_api.security_group_id
    }

    charging_profiles_api_security_group = {
      description           = "Connectivity permitted from the charging profiles API"
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.charging_profiles_api.security_group_id
    }

    tariffs_api_security_group = {
      description           = "Connectivity permitted from the tariffs API"
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.tariffs_api.security_group_id
    }
  }

  target = {
    name         = "connectivity-status-api"
    service_name = var.connectivity_status_api_vpc_endpoint_service_name
  }

  vpc_id     = var.vpc_id
  subnet_ids = var.subnet_ids

  tags = local.module_tags
}

module "configuration_api_privatelink_client" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws//modules/privatelink-client"
  version = "2.4.0"

  client_ingress_rules = {
    smart_charging_service_api = {
      description           = "Connectivity permitted from smart charging service API (cs-state) to configuration API (network-assets)"
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.smart_charging_service_api.security_group_id
    },
  }

  target = {
    name         = "configuration-api"
    service_name = var.configuration_api_vpc_endpoint_service_name
  }

  vpc_id     = var.vpc_id
  subnet_ids = var.subnet_ids

  tags = local.module_tags
}

module "subscriptions_api_privatelink_client" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws//modules/privatelink-client"
  version = "2.4.0"

  client_ingress_rules = {
    smart_charging_service_api = {
      description           = "Connectivity permitted from smart charging service API (cs-state) to subscriptions service API (experience-mobile)"
      from_port             = 5120
      to_port               = 5120
      protocol              = "TCP"
      source_security_group = module.smart_charging_service_api.security_group_id
    },
  }

  target = {
    name         = "subscriptions-service-api"
    service_name = var.subscriptions_api_vpc_endpoint_service_name
  }

  vpc_id     = var.vpc_id
  subnet_ids = var.subnet_ids

  tags = local.module_tags
}
