// websites:

module "flex_dot_com" {
  count = var.environment == "prod" ? 1 : 0

  source  = "terraform-enterprise.pod-point.com/technology/s3-with-cloudfront-and-r53-validation/aws"
  version = "2.0.0"

  providers = {
    aws         = aws,
    aws.acm     = aws.us-east-1,
    aws.route53 = aws.us-east-1,
  }

  acm_cert_arn = aws_acm_certificate.flex_us_east_1.arn

  cf_comment = "Static (SPA) for Pod Point flex website"
  cf_custom_error_response = [{
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
    error_caching_min_ttl = 10
    }
  ]
  cf_default_root_object = "index.html"

  cf_primary_origin_id = "${var.flex_subdomain}.pod-point.com"

  r53_hosted_zone_id = aws_route53_zone.flex.zone_id
  r53_record_name    = "${var.flex_subdomain}.pod-point.com"

  s3_bucket_name = "${var.flex_subdomain}.pod-point.com"

  default_cache_behaviour = {
    cache_policy_id = data.aws_cloudfront_cache_policy.default.id
  }

  tags = local.module_tags
}

module "signup_dot_flex_dot_com" {
  count = var.environment == "prod" ? 1 : 0

  source  = "terraform-enterprise.pod-point.com/technology/s3-with-cloudfront-and-r53-validation/aws"
  version = "2.0.0"

  providers = {
    aws         = aws,
    aws.acm     = aws.us-east-1,
    aws.route53 = aws.us-east-1,
  }

  acm_cert_arn = aws_acm_certificate.flex_star_us_east_1.arn

  cf_comment = "Static (SPA) for Pod Point flex sign-up flow"
  cf_custom_error_response = [{
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
    error_caching_min_ttl = 10
    }
  ]
  cf_default_root_object = "index.html"

  cf_primary_origin_id = "signup.${var.flex_subdomain}.pod-point.com"

  r53_hosted_zone_id = aws_route53_zone.flex.zone_id
  r53_record_name    = "signup.${var.flex_subdomain}.pod-point.com"

  s3_bucket_name = "signup.${var.flex_subdomain}.pod-point.com"

  default_cache_behaviour = {
    cache_policy_id = data.aws_cloudfront_cache_policy.default.id
  }

  tags = local.module_tags
}

// deployment / config:

data "aws_cloudfront_cache_policy" "default" {
  name = "Managed-CachingOptimized"
}

resource "aws_iam_role" "github_signup_flex_pod_point_com_deploy_role" {
  name               = "github-signup-flex-pod-point-com-deploy-role"
  assume_role_policy = data.aws_iam_policy_document.assume_github_signup_flex_pod_point_com_deploy_role_policy.json
  inline_policy {
    name = "sync-to-s3"
    policy = jsonencode({
      Version = "2012-10-17"
      Statement = concat([
        {
          "Effect" : "Allow",
          "Action" : [
            "s3:ListAllMyBuckets"
          ],
          "Resource" : [
            "arn:aws:s3:::*"
          ]
        }
        ], var.environment == "prod" ? [
        {
          "Effect" : "Allow",
          "Action" : [
            "s3:ListBucket",
            "s3:ListBucketMultipartUploads",
            "s3:GetBucketLocation",
            "s3:AbortMultipartUpload",
            "s3:GetObjectAcl",
            "s3:GetObjectVersion",
            "s3:DeleteObject",
            "s3:DeleteObjectVersion",
            "s3:GetObject",
            "s3:PutObjectAcl",
            "s3:PutObject",
            "s3:GetObjectVersionAcl"
          ],
          "Resource" : [
            "arn:aws:s3:::${module.flex_dot_com[0].s3_id}",
            "arn:aws:s3:::${module.flex_dot_com[0].s3_id}/*",
            "arn:aws:s3:::${module.signup_dot_flex_dot_com[0].s3_id}",
            "arn:aws:s3:::${module.signup_dot_flex_dot_com[0].s3_id}/*"
          ]
        },
        {
          "Effect" : "Allow",
          "Action" : [
            "cloudfront:CreateInvalidation"
          ],
          "Resource" : [
            "${module.flex_dot_com[0].cloudfront_arn}",
            "${module.signup_dot_flex_dot_com[0].cloudfront_arn}"
          ]
        }
        ]
      : [])
    })
  }
  tags = local.module_tags
}

data "aws_iam_policy_document" "assume_github_signup_flex_pod_point_com_deploy_role_policy" {
  statement {
    sid    = "AllowOpenIDConnect"
    effect = "Allow"

    actions = [
      "sts:AssumeRoleWithWebIdentity"
    ]

    principals {
      type        = "Federated"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/token.actions.githubusercontent.com"]
    }

    condition {
      test     = "StringEquals"
      variable = "token.actions.githubusercontent.com:aud"
      values   = ["sts.amazonaws.com"]
    }

    condition {
      test     = "ForAnyValue:StringLike"
      variable = "token.actions.githubusercontent.com:sub"
      values = [
        "repo:Pod-Point/smart-charging-service:*"
      ]
    }
  }
}

moved {
  from = module.flex_dot_com
  to   = module.flex_dot_com[0]
}

moved {
  from = module.signup_dot_flex_dot_com
  to   = module.signup_dot_flex_dot_com[0]
}
