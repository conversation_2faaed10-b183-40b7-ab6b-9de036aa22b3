[
  {
    "environment": [
      {
        "name": "DB_READ_ENDPOINT",
        "value": "${db_read_endpoint}"
      },
      {
        "name": "DB_WRITE_ENDPOINT",
        "value": "${db_write_endpoint}"
      },
      {
        "name": "DB_PORT",
        "value": "${db_port}"
      },
      {
        "name": "NODE_ENV",
        "value": "${node_env}"
      },
      {
        "name": "TZ",
        "value": "Etc/UTC"
      },
      {
        "name": "APP_DB_USERNAME",
        "value": "competitions"
      },
      {
        "name": "PORT",
        "value": "${port}"
      },
      {
        "name": "AXLE_BASE_URL",
        "value": "${axle_base_url}"
      },
      {
        "name": "FLEX_API_AXLE_BASE_URL",
        "value": "${flex_api_base_url}"
      }
      %{ for env_name, env_value in urls }
        ,
        {
          "name": "${upper(env_name)}",
          "value": "${env_value}"
        }
      %{ endfor ~}
    ],
    "secrets": [
      {
        "name": "DB_CREDENTIALS",
        "valueFrom": "${db_credentials}"
      },
      {
        "name": "AXLE_CREDENTIALS",
        "valueFrom": "${axle_credentials}"
      },
      {
        "name": "MAILCHIMP_API_KEY",
        "valueFrom": "${mailchimp_api_key}"
      },
      {
        "name": "CONFIGCAT_SDK_KEY",
        "valueFrom": "${configcat_sdk_key}"
      }
    ],
    "essential": true,
    "image": "terraform",
    "linuxParameters": {
      "initProcessEnabled": true
    },
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${name}",
        "awslogs-region": "${region}",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "name": "${name}",
    "networkMode": "awsvpc",
    "portMappings": [
      {
        "containerPort": ${port},
        "hostPort": ${port}
      }
    ],
    "healthCheck": {
      "command": [ "CMD-SHELL", "curl -f http://localhost:${port}/health || exit 1" ],
      "interval": 30,
      "retries": 5,
      "timeout": 10
    }
  }
]
