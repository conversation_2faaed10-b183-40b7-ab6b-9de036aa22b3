[
  {
    "environment": [
      {
        "name": "NODE_ENV",
        "value": "${node_env}"
      },
      {
        "name": "TZ",
        "value": "Etc/UTC"
      },
      {
        "name": "PORT",
        "value": "${port}"
      },
      {
        "name": "DB_READ_ENDPOINT",
        "value": "${db_read_endpoint}"
      },
      {
        "name": "DB_WRITE_ENDPOINT",
        "value": "${db_write_endpoint}"
      },
      {
        "name": "DB_PORT",
        "value": "${db_port}"
      },
      {
        "name": "DB_POOL_ACQUIRE_MS",
        "value": "${db_pool_acquire_ms}"
      },
      {
        "name": "DB_POOL_MAX",
        "value": "${db_pool_max}"
      },
      {
        "name": "DB_POOL_MIN",
        "value": "${db_pool_min}"
      }
      %{ for env_name, env_value in urls }
        ,
        {
          "name": "${upper(env_name)}",
          "value": "${env_value}"
        }
      %{ endfor ~}
    ],
    "secrets": [
      {
        "name": "DB_CREDENTIALS",
        "valueFrom": "${db_credentials}"
      },
      {
        "name": "CONFIGCAT_SDK_KEY",
        "valueFrom": "${configcat_sdk_key}"
      }
    ],
    "essential": true,
    "image": "terraform",
    "linuxParameters": {
      "initProcessEnabled": true
    },
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${name}",
        "awslogs-region": "${region}",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "name": "${name}",
    "networkMode": "awsvpc",
    "portMappings": [
      {
        "containerPort": ${port},
        "hostPort": ${port}
      }
    ],
    "healthCheck": {
      "command": [ "CMD-SHELL", "curl -f http://localhost:${port}/health || exit 1" ],
      "interval": 30,
      "retries": 5,
      "timeout": 10
    }
  }
]
