[{"environment": [{"name": "DB_READ_ENDPOINT", "value": "${db_read_endpoint}"}, {"name": "DB_WRITE_ENDPOINT", "value": "${db_write_endpoint}"}, {"name": "DB_PORT", "value": "${db_port}"}, {"name": "NODE_ENV", "value": "${node_env}"}, {"name": "NO_COLOR", "value": "true"}, {"name": "TZ", "value": "Etc/UTC"}], "secrets": [{"name": "DB_CREDENTIALS", "valueFrom": "${db_credentials}"}], "essential": true, "image": "terraform", "linuxParameters": {"initProcessEnabled": true}, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/${name}", "awslogs-region": "${region}", "awslogs-stream-prefix": "ecs"}}, "name": "${name}", "networkMode": "awsvpc"}]