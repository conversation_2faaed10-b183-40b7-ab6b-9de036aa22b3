[
  {
    "environment": [
      {
        "name": "NODE_ENV",
        "value": "${node_env}"
      },
      {
        "name": "TZ",
        "value": "Etc/UTC"
      },
      {
        "name": "PORT",
        "value": "${port}"
      },
      {
        "name": "DB_PORT",
        "value": "${db_port}"
      },
      {
        "name": "DB_READ_ENDPOINT",
        "value": "${db_read_endpoint}"
      },
      {
        "name": "DB_WRITE_ENDPOINT",
        "value": "${db_write_endpoint}"
      },
      {
        "name": "REPORTS_BUCKET",
        "value": "${reports_bucket}"
      }
    ],
    "secrets": [
      {
        "name": "CONFIGCAT_SDK_KEY",
        "valueFrom": "${configcat_sdk_key_arn}"
      },
      {
        "name": "DB_CREDENTIALS",
        "valueFrom": "${db_credentials_arn}"
      }
    ],
    "essential": true,
    "image": "terraform",
    "linuxParameters": {
      "initProcessEnabled": true
    },
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${name}",
        "awslogs-region": "${region}",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "name": "${name}",
    "networkMode": "awsvpc",
    "portMappings": [
      {
        "containerPort": ${port},
        "hostPort": ${port},
        "protocol": "tcp"
      }
    ],
    "healthCheck": {
      "command": [ "CMD-SHELL", "curl -f http://localhost:${port}/health || exit 1" ],
      "interval": 30,
      "retries": 5,
      "timeout": 10
    }
  }
]
