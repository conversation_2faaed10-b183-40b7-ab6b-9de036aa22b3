data "aws_caller_identity" "current" {}

module "ecs_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "10.2.0"

  identifier         = local.identifier
  repo_names         = ["admin-tool"]
  container_insights = "enabled"
  additional_github_ci_policy_statements = [
    {
      sid    = "AllowDescribeSecurityGroupsForECSTaskRunGitHubAction"
      effect = "Allow"
      actions = [
        "ec2:DescribeSecurityGroups"
      ]
      resources = [
        "*"
      ]
    }
  ]
}
