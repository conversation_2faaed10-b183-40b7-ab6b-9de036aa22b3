variable "environment" {
  description = <<EOF
    The environment of the service.

    This needs to be declared while the service is in the old Pod Point account.

    Otherwise naming conflicts will be present in our terraform apply runs.
  EOF
  type        = string
}

variable "additional_kms_administrators" {
  description = "The ARNs of IAM roles to allow to use the application's KMS key"
  type        = list(string)
  default     = []
}

variable "initial_task_def_environments" {
  description = "A List of objects used to set environment variables for the initial ECS task definition."
  type = list(object({
    name  = string
    value = string
  }))
}

variable "vpc_id" {
  description = "The VPC ID."
  type        = string
}

variable "podadmin_db_security_group_id" {
  description = "Podadmin database security group ID"
  type        = string
}

variable "aws_region" {
  type        = string
  description = "The AWS region for the service."
  default     = "eu-west-1"
}

variable "elasticsearch_charge_points_cluster_domain_arn" {
  type        = string
  description = "The chargepoints elasticsearch cluster domain arn"
}
