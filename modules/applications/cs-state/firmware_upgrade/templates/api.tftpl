[
  {
    "environment": [
      {
        "name": "DB_READ_ENDPOINT",
        "value": "${db_read_endpoint}"
      },
      {
        "name": "DB_WRITE_ENDPOINT",
        "value": "${db_write_endpoint}"
      },
      {
        "name": "DB_PORT",
        "value": "${db_port}"
      },
      {
        "name": "NODE_ENV",
        "value": "${node_env}"
      },
      {
        "name": "NO_COLOR",
        "value": "true"
      },
      {
        "name": "TZ",
        "value": "Etc/UTC"
      },
      {
        "name": "FIRMWARE_UPDATES_SQS_QUEUE_URL",
        "value": "${firmware_updates_sqs_queue_url}"
      },
      {
        "name": "FIRMWARE_UPDATES_SQS_QUEUE_REGION",
        "value": "${region}"
      },
      {
        "name": "LOG_LEVEL",
        "value": "${log_level}"
      },
      {
        "name": "LOG_FORMAT",
        "value": "json"
      },
      {
        "name": "FIRMWARE_BUNDLES_S3_BUCKET_NAME",
        "value": "${firmware_bundles_s3_bucket_name}"
      },
      {
        "name": "EMBEDDED_FIRMWARE_BUNDLES_S3_BUCKET_NAME",
        "value": "${embedded_firmware_bundles_s3_bucket_name}"
      },
      {
        "name": "REGION",
        "value": "${region}"
      },
      {
        "name": "FIRMWARE_BUNDLES_SIGNING_EXPIRES_IN",
        "value": "43200"
      },
      {
        "name": "ASSET_SERVICE_API_URL",
        "value": "${asset_service_api_url}"
      },
      {
        "name" : "COMMANDS_API_URL",
        "value" : "${commands_api_url}"
      },
      {
        "name": "SHORT_URL_API_URL",
        "value": "${short_url_api_endpoint}"
      },
      {
        "name" : "CERTIFICATE_SERVICE_URL",
        "value" : "${certificate_service_url}"
      }
      %{ if arch_5_target_manifest_version != "" }
        ,
        {
          "name": "ARCH_5_TARGET_MANIFEST_VERSION",
          "value": "${arch_5_target_manifest_version}"
        }
      %{ endif ~}
    ],
    "secrets": [
      {
        "name": "DB_CREDENTIALS",
        "valueFrom": "${db_credentials}"
      },
      {
        "name": "CONFIGCAT_SDK_KEY",
        "valueFrom": "${configcat_sdk_key}"
      }
    ],
    "essential": true,
    "image": "terraform",
    "linuxParameters": {
      "initProcessEnabled": true
    },
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${name}",
        "awslogs-region": "${region}",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "name": "${name}",
    "networkMode": "awsvpc",
    "portMappings": [
      {
        "containerPort": ${port},
        "hostPort": ${port}
      }
    ]
  }
]
