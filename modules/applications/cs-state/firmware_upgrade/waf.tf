// IP sets copied from https://github.com/Pod-Point/terraform/blob/f50ac19c208aa098114940e9b76dbe2a5946f7b3/organisation/aws-security/waf_rules.tf
locals {
  block_list = [
    "*************/32", "*************/32", "**************/32"
  ]
}

resource "aws_wafv2_ip_set" "allow_list_cf" {
  count = var.enable_waf ? 1 : 0

  name               = "cf-allow-list"
  description        = "Trusted IP addresses"
  scope              = "REGIONAL"
  ip_address_version = "IPV4"
  addresses = [
    "**************/32", # AWS Client VPN, NAT Gateway public IP
    "************/32",   # AWS Client VPN, NAT Gateway public IP
    "**************/32", # AWS Client VPN, NAT Gateway public IP
    "***********/32",    # NEW AWS Client VPN - Public NAT 1
    "************/32",   # NEW AWS Client VPN - Public NAT 2
    "*************/32"   # NEW AWS Client VPN - Public NAT 3
  ]

  tags = local.module_tags
}

resource "aws_wafv2_ip_set" "block_list_cf" {
  count = var.enable_waf ? 1 : 0

  name               = "cf-block-list"
  description        = "Untrusted IP addresses."
  scope              = "REGIONAL"
  ip_address_version = "IPV4"
  addresses          = local.block_list

  tags = local.module_tags
}

resource "aws_wafv2_web_acl" "regional" {
  count = var.enable_waf ? 1 : 0

  name        = format("%s", local.api_public_name)
  description = format("WAF to monitor and protect traffic for %s", local.api_public_name)
  scope       = "REGIONAL"

  default_action {
    allow {}
  }

  rule {
    name     = "BlockListedAddr"
    priority = 0

    action {
      block {}
    }

    statement {
      ip_set_reference_statement {
        arn = aws_wafv2_ip_set.block_list_cf[0].arn
      }
    }

    visibility_config {
      metric_name                = format("%sBlockListedAddr", local.api_public_name)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AllowListedAddr"
    priority = 5

    action {
      allow {}
    }

    statement {
      ip_set_reference_statement {
        arn = aws_wafv2_ip_set.allow_list_cf[0].arn
      }
    }

    visibility_config {
      metric_name                = format("%sAllowListedAddr", local.api_public_name)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesAmazonIpReputationList"
    priority = 10

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAmazonIpReputationList"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      metric_name                = format("%sAmazonIpReputationList", local.api_public_name)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 15

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
        version     = "Version_1.17"
      }
    }

    visibility_config {
      metric_name                = format("%sKnownBadInputsRuleSet", local.api_public_name)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesLinuxRuleSet"
    priority = 20

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesLinuxRuleSet"
        vendor_name = "AWS"
        version     = "Version_2.1"
      }
    }

    visibility_config {
      metric_name                = format("%sLinuxRuleSet", local.api_public_name)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesUnixRuleSet"
    priority = 25

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesUnixRuleSet"
        vendor_name = "AWS"
        version     = "Version_1.1"
      }
    }

    visibility_config {
      metric_name                = format("%sUnixRuleSet", local.api_public_name)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesCommonRuleSet"
    priority = 35

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
        version     = "Version_1.6"
      }
    }

    visibility_config {
      metric_name                = format("%sCommonRuleSet", local.api_public_name)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesSQLiRuleSet"
    priority = 40

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesSQLiRuleSet"
        vendor_name = "AWS"
        version     = "Version_2.0"
      }
    }

    visibility_config {
      metric_name                = format("%sSQLiRuleSet", local.api_public_name)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    metric_name                = local.api_public_name
    cloudwatch_metrics_enabled = true
    sampled_requests_enabled   = true
  }

  rule {
    name     = "blanket-rate-limit-ip"
    priority = 50

    action {
      count {}
    }

    statement {
      rate_based_statement {
        aggregate_key_type = "IP"
        limit              = 200
      }
    }

    visibility_config {
      metric_name                = format("%sBlanketRateLimit", local.api_public_name)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  tags = local.module_tags
}

module "waf_access_logs_database" {
  source  = "terraform-enterprise.pod-point.com/technology/waf/aws//modules/waf_access_logs_database"
  version = "2.1.1"

  count = var.enable_waf ? 1 : 0

  acl_scope                  = "REGIONAL"
  athena_database_identifier = replace(local.api_public_name, "-", "_")
  glue_catalogue_identifier  = format("%s_waf", replace(local.api_public_name, "-", "_"))

  query_execution_s3_bucket_identifier = format("%s-query", local.api_public_name)

  // Cannot use `local.api_public_name` here as this makes the bucket prefix longer than the 37 character maximum.
  log_bucket_s3_bucket_identifier = "fus-api-public"

  waf_acl_arn                  = aws_wafv2_web_acl.regional[0].arn
  waf_acl_name                 = aws_wafv2_web_acl.regional[0].name
  athena_workgroup_identifier  = format("%s-waf", local.api_public_name)
  athena_workgroup_description = format("WAF for %s.", local.api_public_name)

  tags = local.module_tags
}

resource "aws_wafv2_web_acl_association" "api_gateway" {
  count        = var.enable_waf ? 1 : 0
  resource_arn = aws_api_gateway_stage.api_gateway.arn
  web_acl_arn  = aws_wafv2_web_acl.regional[0].arn
}
