/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *       Module Parameters
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "environment" {
  description = "Environment name infrastructure is being deployed to."
  type        = string
}

variable "region" {
  type = string
}

variable "private_subnet_ids" {
  type = list(any)
}

variable "public_subnet_ids" {
  type = list(any)
}

variable "vpc_id" {
  type = string
}

variable "vpc_cidr_block" {
  description = "The CIDR block that will be granted access to Podadmin DB"
  type        = string
}

variable "execute_api_vpc_endpoint_id" {
  description = "The VPC Endpoint for the execute-api service"
  type        = string
}

variable "vpc_endpoint_ids" {
  description = "List of VPC endpoints that should be allowed access to the API"
  type        = list(string)
}

variable "vpc_link_sg_name" {
  description = "Name of the VPC link security group"
  type        = string
  default     = ""
}

variable "should_deploy_fusspot" {
  description = "Whether to deploy FUSSPOT"
  type        = bool
  default     = false
}

variable "build_account_id" {
  description = "Build account for cs-state account"
  type        = string
  default     = ""
}

variable "additional_kms_administrators" {
  description = "The ARNs of IAM roles to allow to use the application's KMS key"
  type        = list(string)
  default     = []
}

variable "short_url_api" {
  description = "The short url api"
  type = object({
    rest_api_id = string
    endpoint    = string
  })
}

variable "certificate_service_api" {
  description = "The certificate service api"
  type = object({
    rest_api_id = string
    endpoint    = string
  })
}

variable "firmware_upgrade_api_cpu" {
  description = "The cpu for each firmware upgrade api task"
  type        = number
  default     = 256
}

variable "firmware_upgrade_api_memory" {
  description = "The memory for each firmware upgrade api task"
  type        = number
  default     = 512
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *     Pod unit updates SQS
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/
variable "pod_unit_updates_visibility_timeout_seconds" {
  description = "The visibility timeout for the queue. An integer from 0 to 43200 (12 hours)."
  type        = number
  default     = 180
}

variable "pod_unit_updates_message_retention_seconds" {
  description = <<EOF
        The number of seconds Amazon SQS retains a message.

        Integer representing seconds, from 60 (1 minute) to 1209600 (14 days).
    EOF
  type        = number
  default     = 345600 # 4 days
}

variable "pod_unit_updates_max_message_size" {
  description = <<EOF
        The limit of how many bytes a message can contain before Amazon SQS rejects it.

        An integer from 1024 bytes (1 KiB) up to 262144 bytes (256 KiB).
    EOF
  type        = number
  default     = 262144 # 256 KiB
}

variable "pod_unit_updates_delay_in_seconds" {
  description = "The time in seconds that the delivery of all messages in the queue will be delayed. An integer from 0 to 900 (15 minutes)"
  type        = number
  default     = 0
}

variable "pod_unit_updates_receive_wait_time_seconds" {
  description = <<EOF
        The time for which a ReceiveMessage call will wait for a message to arrive (long polling) before returning.

        An integer from 0 to 20 (seconds).

        with 0 meaning that the call will return immediately.
    EOF
  type        = number
  default     = 0
}

variable "pod_unit_updates_kms_data_key_reuse_period_seconds" {
  description = <<EOF
        The length of time, in seconds, for which Amazon SQS can reuse a data key to encrypt or decrypt messages before calling AWS KMS again.
        An integer representing seconds, between 60 seconds (1 minute) and 86,400 seconds (24 hours)
    EOF
  type        = number
  default     = 86400 # 24 hours
}

variable "pod_unit_updates_fifo_queue" {
  description = "Boolean designating a FIFO queue"
  type        = bool
  default     = false
}

variable "pod_unit_updates_fifo_throughput_limit" {
  description = <<EOF
        Specifies whether the FIFO queue throughput quota applies to the entire queue or per message group.
    EOF
  type        = string
  default     = "perQueue"

  validation {
    condition     = contains(["perQueue", "perMessageGroupId"], var.pod_unit_updates_fifo_throughput_limit)
    error_message = "The value must be one of 'perQueue' or 'perMessageGroupId'."
  }
}

variable "pod_unit_updates_deduplication_scope" {
  description = <<EOF
        Specifies whether message deduplication occurs at the message group or queue level.

        Valid values are messageGroup and queue (default).
    EOF
  nullable    = true
  default     = null

  validation {
    condition     = var.pod_unit_updates_deduplication_scope == null || try(contains(["messageGroup", "queue", null], var.pod_unit_updates_deduplication_scope), false)
    error_message = "The value must be one of 'messageGroup' or 'queue'."
  }
}

variable "pod_unit_updates_redrive_policy" {
  description = <<EOF
        The JSON policy to set up the Dead Letter Queue.

        Use jsonencode to pass the value down.
    EOF
  type        = string
  nullable    = true
  default     = null
}

variable "pod_unit_updates_max_receive_count" {
  description = <<EOF
        The number of times a message is delivered to the source queue before being moved to the dead-letter queue.
    EOF
  type        = number
  nullable    = true
  default     = 4
}

variable "pod_unit_updates_redrive_allow_policy" {
  description = <<EOF
        The JSON policy to set up the Dead Letter Queue redrive permission.

        Use jsonencode to pass the value down.
    EOF
  type        = string
  nullable    = true
  default     = null
}

variable "pod_unit_updates_queue_consumer_scaling_min_capacity" {
  description = "The minimum number of pod_unit_updates queue consumer processes to maintain"
  type        = number
}

variable "pod_unit_updates_queue_consumer_scaling_max_capacity" {
  description = "The maximum number of pod_unit_updates queue consumer processes to maintain"
  type        = number
}

variable "update_queue_consumer_scaling_min_capacity" {
  description = "The minimum number of update_queue_consumer queue consumer processes to maintain"
  type        = number
}

variable "update_queue_consumer_scaling_max_capacity" {
  description = "The maximum number of update_queue_consumer queue consumer processes to maintain"
  type        = number
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 * Firmware update requests SQS
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/
variable "firmware_updates_visibility_timeout_seconds" {
  description = "The visibility timeout for the queue. An integer from 0 to 43200 (12 hours)."
  type        = number
  default     = 180
}

variable "firmware_updates_message_retention_seconds" {
  description = <<EOF
        The number of seconds Amazon SQS retains a message.

        Integer representing seconds, from 60 (1 minute) to 1209600 (14 days).
    EOF
  type        = number
  default     = 345600 # 4 days
}

variable "firmware_updates_max_message_size" {
  description = <<EOF
        The limit of how many bytes a message can contain before Amazon SQS rejects it.

        An integer from 1024 bytes (1 KiB) up to 262144 bytes (256 KiB).
    EOF
  type        = number
  default     = 262144 # 256 KiB
}

variable "firmware_updates_delay_in_seconds" {
  description = "The time in seconds that the delivery of all messages in the queue will be delayed. An integer from 0 to 900 (15 minutes)"
  type        = number
  default     = 0
}

variable "firmware_updates_receive_wait_time_seconds" {
  description = <<EOF
        The time for which a ReceiveMessage call will wait for a message to arrive (long polling) before returning.

        An integer from 0 to 20 (seconds).

        with 0 meaning that the call will return immediately.
    EOF
  type        = number
  default     = 0
}

variable "firmware_updates_kms_data_key_reuse_period_seconds" {
  description = <<EOF
        The length of time, in seconds, for which Amazon SQS can reuse a data key to encrypt or decrypt messages before calling AWS KMS again.
        An integer representing seconds, between 60 seconds (1 minute) and 86,400 seconds (24 hours)
    EOF
  type        = number
  default     = 86400 # 24 hours
}

variable "firmware_updates_fifo_queue" {
  description = "Boolean designating a FIFO queue"
  type        = bool
  default     = false
}

variable "firmware_updates_fifo_throughput_limit" {
  description = <<EOF
        Specifies whether the FIFO queue throughput quota applies to the entire queue or per message group.
    EOF
  type        = string
  default     = "perQueue"

  validation {
    condition     = contains(["perQueue", "perMessageGroupId"], var.firmware_updates_fifo_throughput_limit)
    error_message = "The value must be one of 'perQueue' or 'perMessageGroupId'."
  }
}

variable "firmware_updates_deduplication_scope" {
  description = <<EOF
        Specifies whether message deduplication occurs at the message group or queue level.

        Valid values are messageGroup and queue (default).
    EOF
  nullable    = true
  default     = null

  validation {
    condition     = var.firmware_updates_deduplication_scope == null || try(contains(["messageGroup", "queue", null], var.firmware_updates_deduplication_scope), false)
    error_message = "The value must be one of 'messageGroup' or 'queue'."
  }
}

variable "firmware_updates_max_receive_count" {
  description = <<EOF
        The number of times a message is delivered to the source queue before being moved to the dead-letter queue.
    EOF
  type        = number
  nullable    = true
  default     = 4
}

variable "firmware_updates_redrive_allow_policy" {
  description = <<EOF
        The JSON policy to set up the Dead Letter Queue redrive permission.

        Use jsonencode to pass the value down.
    EOF
  type        = string
  nullable    = true
  default     = null
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 * Firmware update events SQS
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/
variable "firmware_update_events_visibility_timeout_seconds" {
  description = "The visibility timeout for the queue. An integer from 0 to 43200 (12 hours)."
  type        = number
  default     = 180
}

variable "firmware_update_events_message_retention_seconds" {
  description = <<EOF
        The number of seconds Amazon SQS retains a message.

        Integer representing seconds, from 60 (1 minute) to 1209600 (14 days).
    EOF
  type        = number
  default     = 345600 # 4 days
}

variable "firmware_update_events_max_message_size" {
  description = <<EOF
        The limit of how many bytes a message can contain before Amazon SQS rejects it.

        An integer from 1024 bytes (1 KiB) up to 262144 bytes (256 KiB).
    EOF
  type        = number
  default     = 262144 # 256 KiB
}

variable "firmware_update_events_delay_in_seconds" {
  description = "The time in seconds that the delivery of all messages in the queue will be delayed. An integer from 0 to 900 (15 minutes)"
  type        = number
  default     = 0
}

variable "firmware_update_events_receive_wait_time_seconds" {
  description = <<EOF
        The time for which a ReceiveMessage call will wait for a message to arrive (long polling) before returning.

        An integer from 0 to 20 (seconds).

        with 0 meaning that the call will return immediately.
    EOF
  type        = number
  default     = 0
}

variable "firmware_update_events_kms_data_key_reuse_period_seconds" {
  description = <<EOF
        The length of time, in seconds, for which Amazon SQS can reuse a data key to encrypt or decrypt messages before calling AWS KMS again.
        An integer representing seconds, between 60 seconds (1 minute) and 86,400 seconds (24 hours)
    EOF
  type        = number
  default     = 86400 # 24 hours
}

variable "firmware_update_events_fifo_queue" {
  description = "Boolean designating a FIFO queue"
  type        = bool
  default     = false
}

variable "firmware_update_events_fifo_throughput_limit" {
  description = <<EOF
        Specifies whether the FIFO queue throughput quota applies to the entire queue or per message group.
    EOF
  type        = string
  default     = "perQueue"

  validation {
    condition     = contains(["perQueue", "perMessageGroupId"], var.firmware_update_events_fifo_throughput_limit)
    error_message = "The value must be one of 'perQueue' or 'perMessageGroupId'."
  }
}

variable "firmware_update_events_deduplication_scope" {
  description = <<EOF
        Specifies whether message deduplication occurs at the message group or queue level.

        Valid values are messageGroup and queue (default).
    EOF
  nullable    = true
  default     = null

  validation {
    condition     = var.firmware_update_events_deduplication_scope == null || try(contains(["messageGroup", "queue", null], var.firmware_update_events_deduplication_scope), false)
    error_message = "The value must be one of 'messageGroup' or 'queue'."
  }
}

variable "firmware_update_events_max_receive_count" {
  description = <<EOF
        The number of times a message is delivered to the source queue before being moved to the dead-letter queue.
    EOF
  type        = number
  nullable    = true
  default     = 4
}

variable "firmware_update_events_redrive_allow_policy" {
  description = <<EOF
        The JSON policy to set up the Dead Letter Queue redrive permission.

        Use jsonencode to pass the value down.
    EOF
  type        = string
  nullable    = true
  default     = null
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 * FUSSPOT
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/
variable "fusspot_commissioning_service_url" {
  description = "The URL of the commissioning service"
  type        = string
  nullable    = true
  default     = null
}

variable "fusspot_commissioning_service_username" {
  description = "The username for the commissioning service"
  type        = string
  nullable    = true
  default     = null
}

variable "fusspot_arch2_lambda_url" {
  description = "The Arch2 Lambda endpoint used to simulate Update Started events"
  type        = string
  nullable    = true
  default     = null
}


/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 * Controller firmware update started events SQS
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/
variable "controller_firmware_update_started_max_receive_count" {
  description = <<EOF
        The number of times a message is delivered to the source queue before being moved to the dead-letter queue.
    EOF
  type        = number
  nullable    = true
  default     = 4
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *    Arch 5 Firmware Events
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/
variable "arch5_firmware_bundle_events_max_receive_count" {
  description = "The number of times a message is delivered to the source queue before being moved to the dead-letter queue."
  type        = number
  nullable    = true
  default     = 4
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *        PodAdmin
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "podadmin_user_password" {
  description = "The password for the firmware_upgrade user in podadmin"
  type        = string
  sensitive   = true
}

variable "podadmin_read_endpoint" {
  description = "The read endpoint for the podadmin db"
  type        = string
}

variable "podadmin_read_write_endpoint" {
  description = "The read/write endpoint for the podadmin db"
  type        = string
}

variable "podadmin_port" {
  description = "The TCP port the Podadmin DB listens on"
  type        = number
  default     = 3306
}

variable "podadmin_security_group_id" {
  description = "The security group id for the podadmin database"
  type        = string
}


// SNS

variable "pod_unit_event_received_topic_arn" {
  description = "The arn for the topic that the pod unit updates queue subscribes to"
  type        = string
}

variable "arch5_firmware_bundle_events_topic_arn" {
  description = "The arn for the topic that arch 5 firmware events are published to"
  type        = string
}

// API Gateway

variable "api_gateway_ingress_rules" {
  description = "A map containing Security Group Rules for the API Gateway VPC endpoint "
  type = map(object({
    from_port             = string
    to_port               = string
    protocol              = string
    description           = string
    ipv4_cidrs            = optional(list(string))
    ipv6_cidrs            = optional(list(string))
    source_security_group = optional(string, null)
  }))

  default = {}
}

// Fleet deployer

variable "fleet_deployer_enabled" {
  description = "Enable the fleet deployer scheduled task"
  type        = bool
  default     = false
}

variable "fleet_deployer_schedule_expression" {
  description = "The schedule expression for the fleet deployer scheduled task"
  type        = string
  default     = "rate(5 minutes)"
}

// Rollout Batcher

variable "rollout_batcher_enabled" {
  description = "The state of the rollout batcher trigger"
  type        = string
}

variable "rollout_batcher_schedule_expression" {
  description = "The schedule expression for the fleet deployer scheduled task"
  type        = string
  default     = "rate(1 minutes)"
}

// General

variable "pod_point_main_account_id" {
  description = "The main podpoint account"
  type        = string
}

variable "embedded_build_account_id" {
  description = "The embedded build account"
  type        = string
}

variable "cs_connectivity_account_id" {
  default = "The connectivity account"
  type    = string
}

variable "experience_account_ids" {
  description = "The experience account ids matching the environment (i.e. dev = [], staging = [dev, staging], prod = [prod])"
  type        = map(string)
}

variable "firmware_upgrade_vpc_endpoint_service_name" {
  // note: this has to be hard coded because api-gateway creates it and does not expose it
  // in terraform; nor can it be persuaded to tag the created resource properly.
  description = "The created VPC endpoint service name for the firmware upgrade service"
  type        = string
}

variable "tags" {
  description = "tags to apply to the resources."
  type        = map(any)
  default     = {}
}

variable "log_level" {
  description = "maximum level to generate logs at - debug, warn or error"
  type        = string
  default     = "info"
  validation {
    condition     = contains(["debug", "info", "warn", "error"], var.log_level)
    error_message = "The value must be one of 'debug', 'info', 'warn' or 'error'"
  }
}

variable "cloudwatch_alarm_topic_arn" {
  type        = string
  nullable    = true
  description = "The ARN of the SNS topic to which Cloudwatch alarms should be published"
}

variable "enable_waf" {
  type        = bool
  description = "Toggle to decide whether AWS WAF ACL resources will be provisioned."
  default     = false
}

variable "asset_service_api_vpc_endpoint_service_name" {
  type        = string
  description = "The name of the VPC endpoint service for the asset service API"
}

variable "ocpp_cs_requests_topic_arn" {
  description = "The arn for the topic that the ocpp requests queue subscribes to"
  type        = string
}

variable "ocpp_requests_queue_consumer_scaling_min_capacity" {
  description = "The minimum number of ocpp_requests queue consumer processes to maintain"
  type        = number
}

variable "ocpp_requests_queue_consumer_scaling_max_capacity" {
  description = "The maximum number of ocpp_requests queue consumer processes to maintain"
  type        = number
}

variable "firmware_upgrade_database_cluster_instance_class" {
  description = "The instance class for the firmware upgrade database cluster"
  type        = string
  default     = "db.t4g.medium"
}

variable "aurora_cluster_instance_count" {
  description = "Number of instances within the aurora cluster"
  type        = string
  default     = 2
}

variable "embedded_firmware_arch_5_bucket_arn" {
  description = "The arn of the bucket where the embedded firmware for arch 5 is stored"
  type        = string
}

variable "embedded_firmware_arch_5_bucket_name" {
  description = "The name of the bucket where the embedded firmware for arch 5 is stored"
  type        = string
}

variable "embedded_firmware_arch_5_bucket_kms_key_arn" {
  description = "The arn of the kms key used to encrypt the embedded firmware for arch 5"
  type        = string
}

variable "arch_5_target_manifest_version" {
  description = "The arch 5 target manifest version for devices with implicit group"
  type        = string
  default     = ""
}

variable "commands_api_vpc_endpoint_service_name" {
  description = "The name of the VPC endpoint service for the commands API"
  type        = string
}

/*
* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*  Installation Completed SQS
* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

variable "installation_completed_queue_consumer_scaling_min_capacity" {
  description = "The minimum number of installation_completed_queue_consumer queue consumer processes to maintain"
  type        = number
}

variable "installation_completed_queue_consumer_scaling_max_capacity" {
  description = "The maximum number of installation_completed_queue_consumer queue consumer processes to maintain"
  type        = number
}

variable "installation_completed_events_topic_arn" {
  description = "The arn for the topic that the installation completed queue subscribes to"
  type        = string
}

variable "logging_comms_grafana_cidr_block" {
  description = "CIDR block for logging comms Grafana access."
  type        = string
  default     = ""
}
