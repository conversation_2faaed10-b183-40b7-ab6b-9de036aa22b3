module "ocpp_requests_consumer" {
  source = "./ocpp-requests-consumer"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
  }

  environment = var.environment
  region      = var.region

  private_subnet_ids = var.private_subnet_ids
  vpc_id             = var.vpc_id

  kms_key_id                 = module.kms.id
  kms_key_arn                = module.kms.arn
  ocpp_cs_requests_topic_arn = var.ocpp_cs_requests_topic_arn

  firmware_upgrade_ecs_cluster_name             = module.firmware_upgrade_ecs_cluster.name
  firmware_upgrade_ecs_cluster_arn              = module.firmware_upgrade_ecs_cluster.arn
  firmware_upgrade_ecs_cluster_github_role_name = module.firmware_upgrade_ecs_cluster.github_role_name

  ocpp_requests_queue_consumer_scaling_max_capacity = var.ocpp_requests_queue_consumer_scaling_max_capacity
  ocpp_requests_queue_consumer_scaling_min_capacity = var.ocpp_requests_queue_consumer_scaling_min_capacity

  firmware_upgrade_aurora_cluster_endpoint          = module.aurora.cluster_endpoint
  firmware_upgrade_aurora_reader_endpoint           = module.aurora.reader_endpoint
  firmware_upgrade_aurora_cluster_resource_id       = module.aurora.cluster_resource_id
  firmware_upgrade_aurora_cluster_security_group_id = module.aurora.security_group_id

  module_kms_administrators = var.additional_kms_administrators

  enable_alarms              = local.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.firmware_upgrade_configcat_sdk_key.arn

  secrets_kms_key_arn = module.kms.arn
}

module "management_ui" {
  source = "./management-ui"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
    aws.shared-services     = aws.shared-services
  }

  environment = var.environment
  region      = var.region

  private_subnet_ids = var.private_subnet_ids
  public_subnet_ids  = var.public_subnet_ids
  vpc_id             = var.vpc_id

  kms_key_id  = module.kms.id
  kms_key_arn = module.kms.arn

  firmware_upgrade_ecs_cluster_name             = module.firmware_upgrade_ecs_cluster.name
  firmware_upgrade_ecs_cluster_arn              = module.firmware_upgrade_ecs_cluster.arn
  firmware_upgrade_ecs_cluster_github_role_name = module.firmware_upgrade_ecs_cluster.github_role_name

  additional_kms_administrators = var.additional_kms_administrators

  acm_arn                       = module.acm.arn
  firmware_upgrade_api_dns_name = module.api.api_gateway_vpce_url

  enable_alarms              = local.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.firmware_upgrade_configcat_sdk_key.arn

  secrets_kms_key_arn = module.kms.arn
}

module "fleet_deployer" {
  source = "./fleet-deployer"

  firmware_update_candidates_queue_arn = module.firmware_update_candidates_queue.queue_arn
  firmware_update_candidates_queue_url = module.firmware_update_candidates_queue.queue_url
  ecs_cluster_name                     = module.firmware_upgrade_ecs_cluster.name
  ecs_cluster_github_role_name         = module.firmware_upgrade_ecs_cluster.github_role_name
  queue_consumer_scaling_min_capacity  = 1
  queue_consumer_scaling_max_capacity  = 2

  aurora_reader_endpoint           = module.aurora.reader_endpoint
  aurora_cluster_endpoint          = module.aurora.cluster_endpoint
  aurora_cluster_resource_id       = module.aurora.cluster_resource_id
  aurora_cluster_security_group_id = module.aurora.security_group_id

  firmware_upgrade_api_url                = local.firmware_upgrade_api_gateway_vpce_url
  firmware_upgrade_api_gateway_id         = module.api.api_gateway_id
  firmware_upgrade_api_gateway_stage_name = module.api.api_gateway_stage_name

  asset_service_api_url = "http://${module.privatelink_client.dns_entry[0].dns_name}"

  log_level = var.log_level

  kms_key_arn                   = module.kms.arn
  secrets_kms_key_arn           = module.kms.arn
  additional_kms_administrators = var.additional_kms_administrators

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.firmware_upgrade_configcat_sdk_key.arn

  vpc_id             = var.vpc_id
  private_subnet_ids = var.private_subnet_ids

  enable_alarms              = local.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  ecs_cluster_arn = module.firmware_upgrade_ecs_cluster.arn

  environment = var.environment
}

module "rollout_batcher" {
  source = "./rollout-batcher"

  environment = var.environment

  vpc_id             = var.vpc_id
  private_subnet_ids = var.private_subnet_ids

  kms_key_arn      = module.kms.arn
  build_account_id = var.build_account_id

  rollout_batcher_enabled             = var.rollout_batcher_enabled
  rollout_batcher_schedule_expression = var.rollout_batcher_schedule_expression

  firmware_upgrade_aurora_cluster_endpoint          = module.aurora.cluster_endpoint
  firmware_upgrade_aurora_reader_endpoint           = module.aurora.reader_endpoint
  firmware_upgrade_aurora_cluster_resource_id       = module.aurora.cluster_resource_id
  firmware_upgrade_aurora_cluster_security_group_id = module.aurora.security_group_id

  asset_service_api_url = "http://${module.privatelink_client.dns_entry[0].dns_name}"

  firmware_update_candidates_queue_arn = module.firmware_update_candidates_queue.queue_arn
  firmware_update_candidates_queue_url = module.firmware_update_candidates_queue.queue_url

  tags = local.module_tags
}

module "installation_completed_consumer" {
  source = "./installation-completed-consumer"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
  }

  environment = var.environment

  log_level = var.log_level

  vpc_id             = var.vpc_id
  private_subnet_ids = var.private_subnet_ids

  ecs_cluster_name             = module.firmware_upgrade_ecs_cluster.name
  ecs_cluster_arn              = module.firmware_upgrade_ecs_cluster.arn
  ecs_cluster_github_role_name = module.firmware_upgrade_ecs_cluster.github_role_name

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.firmware_upgrade_configcat_sdk_key.arn

  kms_key_id  = module.kms.id
  kms_key_arn = module.kms.arn

  secrets_kms_key_arn           = module.kms.arn
  additional_kms_administrators = var.additional_kms_administrators

  queue_consumer_scaling_min_capacity = var.installation_completed_queue_consumer_scaling_min_capacity
  queue_consumer_scaling_max_capacity = var.installation_completed_queue_consumer_scaling_max_capacity

  enable_alarms              = local.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  installation_completed_events_topic_arn = var.installation_completed_events_topic_arn
  firmware_upgrade_api_url                = local.firmware_upgrade_api_gateway_vpce_url
}

moved {
  from = module.new_fleet_deployer
  to   = module.fleet_deployer
}
