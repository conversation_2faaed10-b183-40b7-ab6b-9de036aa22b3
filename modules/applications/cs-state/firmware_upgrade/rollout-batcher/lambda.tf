module "lambda" {
  source  = "terraform-enterprise.pod-point.com/technology/containerised-lambda/aws"
  version = "1.1.0"

  identifier                          = local.identifier
  ecr_repo_account_id                 = var.build_account_id
  ecr_repo_name                       = local.identifier
  repo_names                          = ["firmware-upgrade"]
  environment_variables               = local.environment_variables
  additional_lambda_policy_statements = local.additional_policy_statements
  memory_size                         = var.memory_size
  timeout                             = var.timeout
  application_log_level               = "INFO"
  tags                                = var.tags
  vpc_security_group_ids              = [aws_security_group.rollout_batcher_lambda.id]
  vpc_subnet_ids                      = var.private_subnet_ids
}

resource "aws_lambda_permission" "permit_execution_from_eventbridge_scheduler" {
  statement_id  = "AllowExecutionFromScheduler"
  action        = "lambda:InvokeFunction"
  function_name = module.lambda.lambda_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_scheduler_schedule.rollout-batcher-trigger.arn
}
