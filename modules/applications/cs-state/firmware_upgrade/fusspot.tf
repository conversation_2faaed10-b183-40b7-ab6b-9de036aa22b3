resource "aws_route53_record" "fusspot_load_balancer" {
  count = var.should_deploy_fusspot ? 1 : 0

  provider = aws.pod-point-eu-west-1

  zone_id         = local.pod_point_com_hosted_zone
  type            = "A"
  name            = format("fusspot-%s.%s", var.environment, local.pod_point_com_domain_name)
  allow_overwrite = false

  alias {
    name                   = module.fusspot_load_balancer[0].load_balancer_dns_name
    zone_id                = module.fusspot_load_balancer[0].load_balancer_zone_id
    evaluate_target_health = true
  }
}

module "fusspot_load_balancer" {
  count = var.should_deploy_fusspot ? 1 : 0

  source  = "terraform-enterprise.pod-point.com/technology/lb/aws"
  version = "3.0.1"

  enable_internal_lb = false

  http_tcp_listeners = [
    {
      port        = 80
      protocol    = "HTTP"
      action_type = "redirect"
      redirect = {
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  ]

  https_listeners = [
    {
      port               = 443
      protocol           = "HTTPS"
      certificate_arn    = module.acm.arn
      target_group_index = 0
    }
  ]

  load_balancer_name    = local.fusspot_task_name
  load_balancer_subnets = var.public_subnet_ids
  load_balancer_type    = "application"

  security_group_name        = "${local.fusspot_task_name}-lb"
  security_group_description = "Security Group for the Firmware Upgrade FUSSPOT load balancer."
  security_group_ingress_rules = {
    "port_http_all" = {
      description = "HTTP permitted from the Internet."
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }

    "port_https_all" = {
      description = "HTTPS permitted from the Internet."
      from_port   = 443
      to_port     = 443
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }

  target_groups = [
    {
      backend_port         = 3000
      backend_protocol     = "HTTP"
      deregistration_delay = 60
      name                 = local.fusspot_task_name
      target_type          = "ip"
      vpc_id               = var.vpc_id

      health_check = {
        enabled             = true
        healthy_threshold   = 2
        interval            = 5
        matcher             = "200"
        path                = "/health"
        port                = "traffic-port"
        protocol            = "HTTP"
        timeout             = 3
        unhealthy_threshold = 2
      }
    }
  ]

  vpc_id = var.vpc_id

  tags = local.module_tags
}

resource "aws_lb_listener_rule" "fusspot_load_balancer_oidc" {
  count        = local.environment != "prod" && var.should_deploy_fusspot ? 1 : 0
  listener_arn = module.fusspot_load_balancer[0].https_listener_arns[0]
  priority     = 1

  action {
    type = "authenticate-oidc"

    authenticate_oidc {
      authorization_endpoint = jsondecode(data.aws_secretsmanager_secret_version.assets_embedded_squads_oidc_config[0].secret_string)["auth_endpoint"]
      client_id              = jsondecode(data.aws_secretsmanager_secret_version.assets_embedded_squads_oidc_config[0].secret_string)["client_id"]
      client_secret          = jsondecode(data.aws_secretsmanager_secret_version.assets_embedded_squads_oidc_config[0].secret_string)["client_secret"]
      issuer                 = jsondecode(data.aws_secretsmanager_secret_version.assets_embedded_squads_oidc_config[0].secret_string)["issuer"]
      token_endpoint         = jsondecode(data.aws_secretsmanager_secret_version.assets_embedded_squads_oidc_config[0].secret_string)["token_endpoint"]
      user_info_endpoint     = jsondecode(data.aws_secretsmanager_secret_version.assets_embedded_squads_oidc_config[0].secret_string)["user_info_endpoint"]
    }
  }

  action {
    type             = "forward"
    target_group_arn = module.fusspot_load_balancer[0].target_group_arns[0]
  }

  condition {
    path_pattern {
      values = ["/*"]
    }
  }

  tags = local.module_tags

  lifecycle {
    ignore_changes = [
      action[0].authenticate_oidc[0].client_secret
    ]
  }
}

module "fusspot" {
  count = var.should_deploy_fusspot ? 1 : 0

  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "10.2.0"

  service_type                                = "rolling"
  cluster_arn                                 = module.firmware_upgrade_ecs_cluster.arn
  cluster_name                                = module.firmware_upgrade_ecs_cluster.name
  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = jsonencode(local.ecs_task_execution_custom_policy)
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.fusspot_task_policy.json
  identifier                                  = local.fusspot_task_name
  load_balancing_configuration = [{
    target_group_arn = module.fusspot_load_balancer[0].target_group_arns[0]
    container_name   = local.fusspot_task_name
    container_port   = 3000
  }]
  pipeline_role_name = module.firmware_upgrade_ecs_cluster.github_role_name
  subnet_ids         = var.private_subnet_ids
  vpc_id             = var.vpc_id

  scaling_min_capacity = 1
  scaling_max_capacity = 1

  container_definitions = templatefile("${path.module}/templates/fusspot.tftpl", {
    "name" : local.fusspot_task_name
    "region" : data.aws_region.current.name
    "port" : 3000
    "db_port" : module.aurora.port
    "db_read_endpoint" : module.aurora.reader_endpoint
    "db_write_endpoint" : module.aurora.cluster_endpoint
    "node_env" : var.environment
    "log_level" : var.log_level
    "db_credentials" : aws_secretsmanager_secret.firmware_upgrade_fusspot_db_credentials[0].arn
    "configcat_sdk_key" : aws_secretsmanager_secret.firmware_upgrade_configcat_sdk_key.arn
    "commissioning_service_url" : var.fusspot_commissioning_service_url,
    "commissioning_service_username" : var.fusspot_commissioning_service_username,
    "commissioning_service_password" : aws_secretsmanager_secret.fusspot_commissioning_service_password[0].arn,
    "session_secret" : aws_secretsmanager_secret.fusspot_session_secret[0].arn
    "firmware_upgrade_api_url" : format("https://%s-%s.execute-api.%s.amazonaws.com/%s/",
      module.api.api_gateway_id,
      var.execute_api_vpc_endpoint_id,
      var.region,
      module.api.api_gateway_stage_name
    ),
    "arch2_lambda_url" : var.fusspot_arch2_lambda_url,
    "arch2_lambda_access_key" : aws_secretsmanager_secret.fusspot_arch2_lambda_access_key[0].arn,
    "arch2_lambda_secret_key" : aws_secretsmanager_secret.fusspot_arch2_lambda_secret_key[0].arn,
    "pod_unit_updates_sqs_queue_url" : aws_sqs_queue.pod_unit_updates.url
  })

  additional_kms_administrators = var.additional_kms_administrators
  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }]
  }]

  tags = local.module_tags
}

moved {
  from = module.fusspot_ingress[0].aws_security_group_rule.this
  to   = aws_security_group_rule.fusspot_ingress[0]
}

resource "aws_security_group_rule" "fusspot_ingress" {
  count = var.should_deploy_fusspot ? 1 : 0

  type                     = "ingress"
  from_port                = 3000
  to_port                  = 3000
  protocol                 = "TCP"
  description              = "Access permitted from load balancer."
  security_group_id        = module.fusspot[0].security_group_id
  source_security_group_id = module.fusspot_load_balancer[0].security_group_id
}

moved {
  from = module.fusspot_egress[0].aws_security_group_rule.this
  to   = aws_security_group_rule.fusspot_egress[0]
}

resource "aws_security_group_rule" "fusspot_egress" {
  count = var.should_deploy_fusspot ? 1 : 0

  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  description       = "Permit all egress traffic."
  security_group_id = module.fusspot[0].security_group_id
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
}

data "aws_iam_policy_document" "fusspot_task_policy" {
  statement {
    actions = [
      "sqs:SendMessage",
      "sqs:ChangeMessageVisibility",
      "sqs:GetQueueAttributes"
    ]
    effect    = "Allow"
    resources = [aws_sqs_queue.pod_unit_updates.arn]
  }

  statement {
    actions = [
      "kms:Decrypt*",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:Describe*"
    ]
    effect    = "Allow"
    resources = [module.kms.arn]
  }

  statement {
    actions = ["execute-api:Invoke"]
    effect  = "Allow"
    resources = [
      format("arn:aws:execute-api:%s:%s:%s/%s/GET/firmware/device",
        var.region,
        data.aws_caller_identity.current.account_id,
        module.api.api_gateway_id,
        module.api.api_gateway_stage_name,
      ),
      format("arn:aws:execute-api:%s:%s:%s/%s/GET/firmware/device/*",
        var.region,
        data.aws_caller_identity.current.account_id,
        module.api.api_gateway_id,
        module.api.api_gateway_stage_name,
    )]
  }

  statement {
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel"
    ]
    resources = ["*"]
  }
}

resource "aws_cloudwatch_metric_alarm" "fusspot_cpu_usage" {
  count = var.should_deploy_fusspot && var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name  = format("%s-cpu-usage", local.fusspot_task_name)
  namespace   = "AWS/ECS"
  metric_name = "CPUUtilization"
  dimensions = {
    ClusterName = module.firmware_upgrade_ecs_cluster.name
    ServiceName = local.fusspot_task_name
  }
  alarm_description   = "Alerts on CPU usage of 80% or higher over a window of 15 minutes consistently in each minute."
  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "80" # Alert on 80% or higher
  period              = "60" # Look at each minute
  evaluation_periods  = "15" # Look at the last 15 minutes wherein ...
  datapoints_to_alarm = "15" # ... each minute needs to be above threshold
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "fusspot_memory_usage" {
  count = var.should_deploy_fusspot && var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name  = format("%s-memory-usage", local.fusspot_task_name)
  namespace   = "AWS/ECS"
  metric_name = "MemoryUtilization"
  dimensions = {
    ClusterName = module.firmware_upgrade_ecs_cluster.name
    ServiceName = local.fusspot_task_name
  }
  alarm_description   = "Alerts on memory usage of 90% or higher over a window of 15 minutes consistently in each minute."
  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "90" # Alert on 90% or higher
  period              = "60" # Look at each minute
  evaluation_periods  = "15" # Look at the last 15 minutes wherein ...
  datapoints_to_alarm = "15" # ... each minute needs to be above threshold
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "fusspot_5XX_errors" {
  count = var.should_deploy_fusspot && var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name = format("%s-5XX-errors", local.fusspot_task_name)
  dimensions = {
    LoadBalancer = module.fusspot_load_balancer[0].load_balancer_arn_suffix
  }
  metric_name         = "HTTPCode_Target_5XX_Count"
  namespace           = "AWS/ApplicationELB"
  comparison_operator = "GreaterThanThreshold"
  period              = "300"
  evaluation_periods  = "1"
  statistic           = "Sum"
  threshold           = "1"
  alarm_description   = "All 5XX errors"
  actions_enabled     = "true"
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "fusspot_alb_response_time" {
  count = var.should_deploy_fusspot && var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name                = format("%s-response-time", local.fusspot_task_name)
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  alarm_description         = "50% or more of responses take longer than 500ms within 5 minutes"
  evaluation_periods        = 1
  treat_missing_data        = "notBreaching"
  insufficient_data_actions = []
  metric_name               = "TargetResponseTime"
  namespace                 = "AWS/ApplicationELB"
  period                    = 300 // 5 minutes
  statistic                 = "Average"
  threshold                 = 0.5 // Half a second
  alarm_actions             = [var.cloudwatch_alarm_topic_arn]
  ok_actions                = [var.cloudwatch_alarm_topic_arn]

  dimensions = {
    LoadBalancer = module.fusspot_load_balancer[0].load_balancer_arn_suffix
  }

  tags = local.module_tags
}
