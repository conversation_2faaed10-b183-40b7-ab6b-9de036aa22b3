module "fargate_service" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "10.2.0"

  service_type       = "rolling"
  cluster_arn        = var.firmware_upgrade_ecs_cluster_arn
  cluster_name       = var.firmware_upgrade_ecs_cluster_name
  identifier         = local.api_name
  pipeline_role_name = var.firmware_upgrade_ecs_cluster_github_role_name

  subnet_ids = var.private_subnet_ids

  vpc_id = var.vpc_id

  cpu    = 512
  memory = 1024

  scale_in_adjustment  = -1
  scaling_min_capacity = var.ocpp_requests_queue_consumer_scaling_min_capacity
  scaling_max_capacity = var.ocpp_requests_queue_consumer_scaling_max_capacity
  scaling_statistic    = "Average"

  attach_custom_ecs_task_iam_policy = true
  ecs_task_custom_policy            = data.aws_iam_policy_document.ocpp_requests_consumer_task_policy.json

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.ocpp_requests_consumer_task_execution_policy.json

  container_definitions = jsonencode([{
    "environment" : [
      {
        "name" : "DB_READ_ENDPOINT",
        "value" : "${var.firmware_upgrade_aurora_reader_endpoint}"
      },
      {
        "name" : "DB_READ_WRITE_ENDPOINT",
        "value" : "${var.firmware_upgrade_aurora_cluster_endpoint}"
      },
      {
        "name" : "DB_PORT",
        "value" : "${tostring(local.database_port)}"
      },
      {
        "name" : "NODE_ENV",
        "value" : "${local.environment}"
      },
      {
        "name" : "TZ",
        "value" : "Etc/UTC"
      },
      {
        "name" : "NO_COLOR",
        "value" : "true"
      },
      {
        "name" : "DB_CREDENTIALS",
        "value" : "{\"username\":\"ocpp_requests_consumer\"}"
      },
      {
        "name" : "LOG_LEVEL",
        "value" : "${var.log_level}"
      },
      {
        "name" : "LOG_FORMAT",
        "value" : "json"
      },
      {
        "name" : "REGION",
        "value" : "${data.aws_region.current.name}"
      },
      {
        "name" : "OCPP_REQUESTS_QUEUE_URL",
        "value" : "${aws_sqs_queue.ocpp_requests.url}"
      },
    ],
    "secrets" : [
      {
        "name" : "CONFIGCAT_SDK_KEY",
        "valueFrom" : "${var.configcat_sdk_key_secret_arn}"
      }
    ],
    "essential" : true,
    "image" : "terraform",
    "linuxParameters" : {
      "initProcessEnabled" : true
    },
    "logConfiguration" : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-group" : "/ecs/${local.api_name}",
        "awslogs-region" : "${var.region}",
        "awslogs-stream-prefix" : "ecs"
      }
    },
    "name" : "${local.api_name}",
    "networkMode" : "awsvpc",
    "portMappings" : [
      {
        "containerPort" : local.container_port,
        "hostPort" : local.container_port
      }
    ]
  }])

  additional_kms_administrators = var.module_kms_administrators
  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }]
  }]

  tags = local.module_tags
}
