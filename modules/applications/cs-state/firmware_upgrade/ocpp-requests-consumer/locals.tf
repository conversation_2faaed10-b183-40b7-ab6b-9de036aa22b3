locals {
  environment = var.environment

  api_name = "firmware-upgrade-ocpp-requests-consumer"

  container_port = 3000
  database_port  = 5432

  database_ingress_rules = {
    permit_api = {
      description           = "API permission"
      from_port             = local.database_port
      to_port               = local.database_port
      protocol              = "TCP"
      source_security_group = module.fargate_service.security_group_id
    },
  }

  module_tags = {
    "pp:owner"   = "network:assets"
    "pp:service" = "firmware-upgrade"
  }
}
