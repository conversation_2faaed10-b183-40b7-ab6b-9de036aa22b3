/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *          KMS policy
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

data "aws_iam_policy_document" "custom_kms_policy" {
  statement {
    sid     = "Enable Account Access"
    actions = ["kms:*"] # Later scope this down to allow aws backup for aurora
    resources = [
      "*"
    ]
    principals {
      type        = "AWS"
      identifiers = [format("arn:aws:iam::%s:root", data.aws_caller_identity.current.account_id)]
    }
  }

  statement {
    sid = "EnableSNS"
    actions = [
      "kms:GenerateDataKey",
      "kms:Decrypt"
    ]

    resources = ["*"] # Scope this down later on and point to the source sns.

    principals {
      type        = "Service"
      identifiers = ["sns.amazonaws.com"]
    }
  }
}
