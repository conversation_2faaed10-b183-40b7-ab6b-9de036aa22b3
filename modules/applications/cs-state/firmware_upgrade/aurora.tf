module "aurora" {
  source  = "terraform-enterprise.pod-point.com/technology/aurora/aws"
  version = "5.2.2"

  identifier       = local.identifier
  vpc_id           = var.vpc_id
  subnet_group_ids = var.private_subnet_ids
  port             = local.database_port

  engine         = "aurora-postgresql"
  engine_version = "14.15"

  admin_username = local.database_admin_username
  database_name  = "charging_stations"

  // Added on upgrade from module version 3.1.1 to 5.2.0 - kept false to keep same behaviour for now
  aurora_managed_admin_user = false

  cluster_preffered_maintenance_window = "Mon:02:00-Mon:03:00"
  cluster_preffered_backup_window      = "00:30-02:00"
  enable_aws_backup                    = var.environment == "prod" ? true : false
  cluster_deletion_protection          = true
  cluster_allow_major_version_upgrade  = true

  // Enable IAM authentication
  cluster_iam_database_authentication_enabled = true

  instance_enable_performance_insights = true

  # Parameters
  cluster_parameter_group_family = "aurora-postgresql14"
  cluster_parameters             = {}

  instance_parameter_group_family = "aurora-postgresql14"
  instance_parameters             = {}

  # Cluster instances
  cluster_instance_class = var.firmware_upgrade_database_cluster_instance_class
  cluster_instance_count = var.aurora_cluster_instance_count

  # KMS Encryption.
  secret_manager_admin_user_kms_encryption_key     = module.kms.arn
  storage_kms_encryption_key                       = module.kms.arn
  instance_performance_insights_kms_encryption_key = module.kms.arn

  tags = local.module_tags
}

module "aurora_alarms" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  source  = "terraform-enterprise.pod-point.com/technology/aurora/aws//modules/aurora_alarms"
  version = "4.0.0"

  cluster_id           = module.aurora.cluster_identifier
  cluster_instance_ids = module.aurora.cluster_instance_ids

  enable_freeable_memory_alarm = false

  instance_cpu_utilization_gt_60 = {
    ok_actions    = [var.cloudwatch_alarm_topic_arn]
    alarm_actions = [var.cloudwatch_alarm_topic_arn]
  }
  instance_cpu_utilization_gt_80 = {
    ok_actions    = [var.cloudwatch_alarm_topic_arn]
    alarm_actions = [var.cloudwatch_alarm_topic_arn]
  }
  instance_database_connections = {
    threshold     = 120
    ok_actions    = [var.cloudwatch_alarm_topic_arn]
    alarm_actions = [var.cloudwatch_alarm_topic_arn]
  }
  replica_lag_gt_5 = {
    ok_actions    = [var.cloudwatch_alarm_topic_arn]
    alarm_actions = [var.cloudwatch_alarm_topic_arn]
  }
  replica_lag_gt_30 = {
    ok_actions    = [var.cloudwatch_alarm_topic_arn]
    alarm_actions = [var.cloudwatch_alarm_topic_arn]
  }
}
