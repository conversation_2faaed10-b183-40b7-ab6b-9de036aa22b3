module "queue_consumer" {
  source = "../../../shared/assets/queue-consumer"

  name = "installation-completed-consumer"

  environment = var.environment

  queue_arns = [
    module.installation_completed_events.queue_arn
  ]

  write_queue_arns = []

  ecs_cluster_arn              = var.ecs_cluster_arn
  ecs_cluster_github_role_name = var.ecs_cluster_github_role_name
  ecs_cluster_name             = var.ecs_cluster_name

  scaling_min_capacity = var.queue_consumer_scaling_min_capacity
  scaling_max_capacity = var.queue_consumer_scaling_max_capacity

  environment_variables_plain = {
    "INSTALLATION_COMPLETED_EVENTS_QUEUE_URL" = module.installation_completed_events.queue_url
    "FIRMWARE_UPGRADE_API_URL"                = var.firmware_upgrade_api_url
  }

  log_level = var.log_level

  kms_key_arn                   = var.kms_key_arn
  secrets_kms_key_arn           = var.secrets_kms_key_arn
  additional_kms_administrators = var.additional_kms_administrators

  configcat_sdk_key_secret_arn = var.configcat_sdk_key_secret_arn

  vpc_id             = var.vpc_id
  private_subnet_ids = var.private_subnet_ids

  enable_alarms              = var.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn
}
