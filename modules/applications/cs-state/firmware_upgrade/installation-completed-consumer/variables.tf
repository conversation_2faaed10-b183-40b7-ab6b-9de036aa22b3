variable "ecs_cluster_arn" {
  description = "The arn of the ECS cluster for the fargate service."
}

variable "ecs_cluster_name" {
  description = "The name of the ECS cluster for the fargate service."
}

variable "ecs_cluster_github_role_name" {
  description = "The github role name for the ECS cluster."
}

variable "environment" {
  description = "Environment name infrastructure is being deployed to."
  type        = string
}

variable "queue_consumer_scaling_min_capacity" {
  description = "Lower limit for queue consumer autoscaling"
  type        = number
}

variable "queue_consumer_scaling_max_capacity" {
  description = "Upper limit for queue consumer autoscaling"
  type        = number
}

variable "log_level" {
  description = "maximum level to generate logs at - debug, warn or error"
  type        = string
  default     = "info"
  validation {
    condition     = contains(["debug", "info", "warn", "error"], var.log_level)
    error_message = "The value must be one of 'debug', 'info', 'warn' or 'error'"
  }
}

variable "kms_key_arn" {
  description = "The arn of the KMS key to be used by services"
  type        = string
}

variable "kms_key_id" {
  description = "The id of the KMS key to be used by services"
  type        = string
}

variable "secrets_kms_key_arn" {
  type        = string
  description = "ARN of the KMS key used to encrypt secrets"
}

variable "configcat_sdk_key_secret_arn" {
  description = "Config Cat Feature Flag SDK Key"
  type        = string
}

variable "vpc_id" {
  description = "The VPC ID."
  type        = string
}

variable "additional_kms_administrators" {
  description = "IAM ARN of a trusted entity for decrypting the module owned encrypted resources like CloudWatch Logs"
  type        = list(string)
  default     = []
}

variable "private_subnet_ids" {
  type        = list(string)
  description = "A list of private subnet ids to assigned to the fargate service"
}


variable "enable_alarms" {
  type        = bool
  description = "Set to true to enable CloudWatch Metric Alarms. cloudwatch_alarm_topic_arn must also be set"
}

variable "cloudwatch_alarm_topic_arn" {
  type        = string
  description = "The ARN of the SNS topic to which Cloudwatch alarms should be published"
}

variable "installation_completed_events_topic_arn" {
  description = "The ARN of the SNS topic to subscribe to for installation completed events"
  type        = string
}

variable "firmware_upgrade_api_url" {
  description = "The URL for the firmware upgrade API."
}
