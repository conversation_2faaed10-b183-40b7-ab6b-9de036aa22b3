module "kms" {
  source  = "terraform-enterprise.pod-point.com/technology/kms/aws"
  version = "1.1.0"

  description              = format("KMS key for the %s upgrade process", local.identifier)
  alias_name               = local.identifier
  policy                   = data.aws_iam_policy_document.custom_kms_policy.json
  enable_aws_backup_access = var.environment == "prod" ? true : false

  tags = local.module_tags
}
