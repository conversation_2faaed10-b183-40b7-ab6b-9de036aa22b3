module "api" {
  source = "../shared/private-api"

  environment      = var.environment
  region           = data.aws_region.current.name
  account_id       = data.aws_caller_identity.current.account_id
  subnet_ids       = var.private_subnet_ids
  vpc_id           = var.vpc_id
  name             = local.api_task_name
  nlb_name         = "firmware-upgrade-nlb"
  vpc_link_sg_name = var.vpc_link_sg_name
  ecs_port         = 3000
  deploy_alb       = true

  custom_description            = "security group for the firmware-upgrade-${var.environment} vpc link function."
  additional_kms_administrators = var.additional_kms_administrators

  # ECS Cluster info
  ecs_cluster_arn                   = module.firmware_upgrade_ecs_cluster.arn
  ecs_cluster_name                  = module.firmware_upgrade_ecs_cluster.name
  ecs_cluster_github_role_name      = module.firmware_upgrade_ecs_cluster.github_role_name
  ecs_task_execution_policy         = jsonencode(local.ecs_task_execution_custom_policy)
  attach_custom_ecs_task_iam_policy = true
  ecs_task_custom_policy            = jsonencode(local.ecs_task_custom_policy)

  # ECS Task info
  cpu    = var.firmware_upgrade_api_cpu
  memory = var.firmware_upgrade_api_memory

  container_definition = templatefile("${path.module}/templates/api.tftpl", {
    "name" : local.api_task_name
    "region" : data.aws_region.current.name
    "port" : local.ecs_port
    "db_port" : module.aurora.port
    "db_read_endpoint" : module.aurora.reader_endpoint
    "db_write_endpoint" : module.aurora.cluster_endpoint
    "db_credentials" : aws_secretsmanager_secret.firmware_upgrade_api_db_credentials.arn
    "node_env" : var.environment
    "firmware_updates_sqs_queue_url" : aws_sqs_queue.firmware_updates.url
    "log_level" : var.log_level
    "configcat_sdk_key" : aws_secretsmanager_secret.firmware_upgrade_configcat_sdk_key.arn
    "firmware_bundles_s3_bucket_name" : module.firmware_bundles_bucket.s3_bucket_id
    "embedded_firmware_bundles_s3_bucket_name" : var.embedded_firmware_arch_5_bucket_name
    "asset_service_api_url" : "http://${module.privatelink_client.dns_entry[0].dns_name}"
    "arch_5_target_manifest_version" : var.arch_5_target_manifest_version
    "commands_api_url" : "http://${module.commands_api_privatelink_client.dns_entry[0].dns_name}"
    "short_url_api_endpoint" : var.short_url_api.endpoint,
    "certificate_service_url" : var.certificate_service_api.endpoint,
  })

  # Api gateway
  api_gateway_ingress_rules   = var.api_gateway_ingress_rules
  execute_api_vpc_endpoint_id = var.execute_api_vpc_endpoint_id
  vpc_endpoint_ids            = var.vpc_endpoint_ids

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "api_cpu_usage" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name  = format("%s-cpu-usage", local.api_task_name)
  namespace   = "AWS/ECS"
  metric_name = "CPUUtilization"
  dimensions = {
    ClusterName = module.firmware_upgrade_ecs_cluster.name
    ServiceName = local.api_task_name
  }
  alarm_description   = "Alerts on CPU usage of 80% or higher over a window of 15 minutes consistently in each minute."
  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "80" # Alert on 80% or higher
  period              = "60" # Look at each minute
  evaluation_periods  = "15" # Look at the last 15 minutes wherein ...
  datapoints_to_alarm = "15" # ... each minute needs to be above threshold
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "api_memory_usage" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name  = format("%s-memory-usage", local.api_task_name)
  namespace   = "AWS/ECS"
  metric_name = "MemoryUtilization"
  dimensions = {
    ClusterName = module.firmware_upgrade_ecs_cluster.name
    ServiceName = local.api_task_name
  }
  alarm_description   = "Alerts on memory usage of 90% or higher over a window of 15 minutes consistently in each minute."
  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "90" # Alert on 90% or higher
  period              = "60" # Look at each minute
  evaluation_periods  = "15" # Look at the last 15 minutes wherein ...
  datapoints_to_alarm = "15" # ... each minute needs to be above threshold
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "alb_response_time_alarm" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name                = format("%s-response-time", local.api_task_name)
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  alarm_description         = "50% or more of responses take longer than 500ms within 5 minutes"
  evaluation_periods        = 1
  treat_missing_data        = "notBreaching"
  insufficient_data_actions = []
  metric_name               = "TargetResponseTime"
  namespace                 = "AWS/ApplicationELB"
  period                    = 300 // 5 minutes
  statistic                 = "Average"
  threshold                 = 0.5 // Half a second
  alarm_actions             = [var.cloudwatch_alarm_topic_arn]
  ok_actions                = [var.cloudwatch_alarm_topic_arn]

  dimensions = {
    LoadBalancer = module.api.load_balancer_arn_suffix
  }

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "elb_502" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name                = format("%s-elb-502", local.api_task_name)
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  alarm_description         = "1 or more 502 errors from ELB within 5 minutes"
  evaluation_periods        = 1
  treat_missing_data        = "notBreaching"
  insufficient_data_actions = []
  metric_name               = "HTTPCode_ELB_502_Count"
  namespace                 = "AWS/ApplicationELB"
  period                    = 300 // 5 minutes
  statistic                 = "Maximum"
  threshold                 = 1
  alarm_actions             = [var.cloudwatch_alarm_topic_arn]
  ok_actions                = [var.cloudwatch_alarm_topic_arn]

  dimensions = {
    LoadBalancer = module.api.load_balancer_arn_suffix
  }

  tags = local.module_tags
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *         Public API Gateway
 * Adapted from ../shared/private-api
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/
resource "aws_api_gateway_rest_api" "public" {
  name = local.api_public_name
  endpoint_configuration {
    types = ["EDGE"]
  }

  tags = local.module_tags
}

resource "aws_api_gateway_domain_name" "firmware_api" {
  certificate_arn = module.acm_cloudfront.arn
  domain_name     = local.environment == "prod" ? format("%s.%s", local.public_subdomain, local.pod_point_com_domain_name) : format("%s-%s.%s", local.public_subdomain, local.environment, local.pod_point_com_domain_name)

  tags = local.module_tags
}

resource "aws_route53_record" "firmware_api" {
  name    = aws_api_gateway_domain_name.firmware_api.domain_name
  type    = "A"
  zone_id = local.pod_point_com_hosted_zone

  provider = aws.pod-point-eu-west-1

  alias {
    evaluate_target_health = true
    name                   = aws_api_gateway_domain_name.firmware_api.cloudfront_domain_name
    zone_id                = aws_api_gateway_domain_name.firmware_api.cloudfront_zone_id
  }
}

resource "aws_api_gateway_base_path_mapping" "firmware_api" {
  api_id      = aws_api_gateway_rest_api.public.id
  stage_name  = aws_api_gateway_stage.api_gateway.stage_name
  domain_name = aws_api_gateway_domain_name.firmware_api.domain_name
}

resource "aws_api_gateway_resource" "firmware" {
  rest_api_id = aws_api_gateway_rest_api.public.id
  parent_id   = aws_api_gateway_rest_api.public.root_resource_id
  path_part   = "firmware"
}

resource "aws_api_gateway_resource" "firmware_check" {
  rest_api_id = aws_api_gateway_rest_api.public.id
  parent_id   = aws_api_gateway_resource.firmware.id
  path_part   = "check"
}

resource "aws_api_gateway_method" "firmware_check_post" {
  rest_api_id   = aws_api_gateway_rest_api.public.id
  authorization = "NONE"
  http_method   = "POST"
  resource_id   = aws_api_gateway_resource.firmware_check.id
}

resource "aws_api_gateway_integration" "vpc_link_integration" {
  rest_api_id             = aws_api_gateway_rest_api.public.id
  http_method             = aws_api_gateway_method.firmware_check_post.http_method
  resource_id             = aws_api_gateway_resource.firmware_check.id
  type                    = "HTTP_PROXY"
  uri                     = format("http://%s/firmware/check", module.api.nlb_dns_name)
  integration_http_method = aws_api_gateway_method.firmware_check_post.http_method
  connection_type         = "VPC_LINK"
  connection_id           = module.api.api_gateway_vpc_link_id
  passthrough_behavior    = "WHEN_NO_MATCH"
}

resource "aws_api_gateway_deployment" "deployment" {
  rest_api_id = aws_api_gateway_rest_api.public.id

  triggers = {
    # NOTE: The configuration below will satisfy ordering considerations,
    #       but not pick up all future REST API changes. More advanced patterns
    #       are possible, such as using the filesha1() function against the
    #       Terraform configuration file(s) or removing the .id references to
    #       calculate a hash against whole resources. Be aware that using whole
    #       resources will show a difference after the initial implementation.
    #       It will stabilize to only change when resources change afterwards.
    redeployment = sha1(jsonencode([
      aws_api_gateway_resource.firmware.id,
      aws_api_gateway_resource.firmware_check.id,
      aws_api_gateway_method.firmware_check_post.id,
      aws_api_gateway_integration.vpc_link_integration.id,
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_api_gateway_stage" "api_gateway" {
  rest_api_id   = aws_api_gateway_rest_api.public.id
  deployment_id = aws_api_gateway_deployment.deployment.id
  stage_name    = var.environment

  lifecycle {
    ignore_changes = [
      cache_cluster_size,
    ]
  }
}

resource "aws_api_gateway_method_settings" "all" {
  rest_api_id = aws_api_gateway_rest_api.public.id
  stage_name  = aws_api_gateway_stage.api_gateway.stage_name
  method_path = "*/*"

  settings {
    # Enable CloudWatch logging and metrics
    metrics_enabled    = true
    data_trace_enabled = true
    logging_level      = "INFO"
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 * Public API Gateway Alarms
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_cloudwatch_metric_alarm" "latency" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  namespace  = "AWS/ApiGateway"
  alarm_name = format("%s-gateway-latency", local.api_public_name)
  dimensions = {
    ApiName = aws_api_gateway_rest_api.public.name
  }
  period              = 300
  evaluation_periods  = 1
  statistic           = "Maximum"
  metric_name         = "Latency"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = 2000
  actions_enabled     = "true"
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "http_5XX_count" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  namespace  = "AWS/ApiGateway"
  alarm_name = format("%s-gateway-5XX-count", local.api_public_name)
  dimensions = {
    ApiName = aws_api_gateway_rest_api.public.name
  }
  period              = 300
  evaluation_periods  = 1
  statistic           = "Sum"
  metric_name         = "5XXError"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 1
  alarm_description   = "All 5XX errors - Firmware Upgrade Public API Gateway"
  actions_enabled     = "true"
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 * Private API Gateway Alarms
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/
resource "aws_cloudwatch_metric_alarm" "private_latency" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  namespace  = "AWS/ApiGateway"
  alarm_name = format("%s-gateway-latency", local.api_task_name)
  dimensions = {
    ApiName = module.api.api_gateway_name
  }
  period              = 300
  evaluation_periods  = 1
  statistic           = "Maximum"
  metric_name         = "Latency"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = 2000
  actions_enabled     = "true"
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "private_http_5XX_count" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  namespace  = "AWS/ApiGateway"
  alarm_name = format("%s-gateway-5XX-count", local.api_task_name)
  dimensions = {
    ApiName = module.api.api_gateway_name
  }
  period              = 300
  evaluation_periods  = 1
  statistic           = "Sum"
  metric_name         = "5XXError"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 1
  alarm_description   = "All 5XX errors - Firmware Upgrade Private API Gateway"
  actions_enabled     = "true"
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

/**
* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
* Privatelink client FUS -> Asset API
* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */
module "privatelink_client" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws//modules/privatelink-client"
  version = "2.4.0"

  client_ingress_rules = {
    firmware_upgrade_api = {
      description           = "Connectivity permitted from firmware upgrade api to asset service api."
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.api.security_group_id
    },
    fleet_deployer_queue_consumer = {
      description           = "Connectivity permitted from fleet deployer queue consumer to asset service api."
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.fleet_deployer.queue_consumer_security_group_id
    }
  }

  target = {
    name         = "asset-service-api-${local.environment}"
    service_name = var.asset_service_api_vpc_endpoint_service_name
  }

  vpc_id     = var.vpc_id
  subnet_ids = var.private_subnet_ids
}


/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 * Private VPC link service
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/
data "aws_vpc_endpoint_service" "firmware_upgrade_vpc_service_endpoint" {
  count = length(var.experience_account_ids) > 0 ? 1 : 0

  filter {
    name   = "service-name"
    values = [var.firmware_upgrade_vpc_endpoint_service_name]
  }
}

resource "aws_vpc_endpoint_service_allowed_principal" "allow_experience_firmware_upgrade_vpc_service_endpoint" {
  for_each = var.experience_account_ids

  vpc_endpoint_service_id = data.aws_vpc_endpoint_service.firmware_upgrade_vpc_service_endpoint[0].service_id
  principal_arn           = "arn:aws:iam::${each.value}:root"
}
