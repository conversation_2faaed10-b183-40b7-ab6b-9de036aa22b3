variable "environment" {
  description = "Environment name infrastructure is being deployed to."
  type        = string
}

variable "region" {
  type = string
}

variable "private_subnet_ids" {
  type = list(any)
}

variable "public_subnet_ids" {
  type = list(any)
}

variable "vpc_id" {
  type = string
}

variable "firmware_upgrade_ecs_cluster_arn" {
  description = "The arn for the firmware upgrade ecs cluster."
}

variable "firmware_upgrade_ecs_cluster_name" {
  description = "The name for the firmware upgrade ecs cluster."
}

variable "firmware_upgrade_ecs_cluster_github_role_name" {
  description = "The github role name for the firmware upgrade service ecs cluster."
}

variable "additional_kms_administrators" {
  description = "The ARNs of IAM roles to allow to use the application's KMS key"
  type        = list(string)
}

variable "cloudwatch_alarm_topic_arn" {
  type        = string
  description = "The ARN of the SNS topic to which Cloudwatch alarms should be published"
  default     = ""
}

variable "enable_alarms" {
  type        = bool
  description = "Set to true to enable CloudWatch Metric Alarms. cloudwatch_alarm_topic_arn must also be set"
  default     = false
}

variable "log_level" {
  description = "maximum level to generate logs at - debug, warn or error"
  type        = string
  default     = "debug"
  validation {
    condition     = contains(["debug", "info", "warn", "error"], var.log_level)
    error_message = "The value must be one of 'debug', 'info', 'warn' or 'error'"
  }
}

variable "kms_key_id" {
  description = "The id of the kms key"
  type        = string
}

variable "kms_key_arn" {
  description = "The arn of the kms key"
  type        = string
}

variable "acm_arn" {
  description = "The arn of the acm"
  type        = string
}

variable "firmware_upgrade_api_dns_name" {
  description = "The dns name of the firmware upgrade api"
  type        = string
}

variable "configcat_sdk_key_secret_arn" {
  description = "Config Cat Feature Flag SDK Key"
  type        = string
}

variable "secrets_kms_key_arn" {
  type        = string
  description = "ARN of the KMS key used to encrypt secrets"
}

variable "tags" {
  description = "key, value pair of tags to associate to your resources."
  type        = map(any)
  default     = {}
}
