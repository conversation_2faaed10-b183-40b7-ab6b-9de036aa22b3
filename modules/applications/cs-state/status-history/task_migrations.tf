locals {
  migrations_container_definition = merge(local.default_container_definition, {
    name : format("%s-migrations", local.identifier),
    logConfiguration : merge(local.default_container_definition["logConfiguration"], {
      options : merge(local.default_container_definition["logConfiguration"]["options"], {
        awslogs-group : "/ecs/${local.identifier}/migrations",
      })
    }),
    secrets : concat(local.default_container_definition["secrets"], [
      {
        name : "DB_USERNAME",
        valueFrom : format("%s:username::", module.status_history_aurora.admin_user_secret_manager_arn)
      },
      {
        name : "DB_PASSWORD",
        valueFrom : format("%s:password::", module.status_history_aurora.admin_user_secret_manager_arn)
      },
    ])
  })
}

module "migrations" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/standalone-task"
  version = "12.2.0"

  identifier         = format("%s-migrations", local.identifier)
  pipeline_role_name = module.ecs_cluster.github_role_name
  vpc_id             = var.vpc_id

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.task_execution_common.json

  container_definition = jsonencode([local.migrations_container_definition])

  log_group_name         = local.migrations_container_definition["logConfiguration"]["options"]["awslogs-group"]
  logs_retention_in_days = local.logs_retention_in_days

  depends_on = [
    module.ecs_cluster,
  ]

  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"
      ]
    }]
  }]

  tags = merge(local.tags, {
    "pp:service" = "status-history:migrations"
  })
}

resource "aws_security_group_rule" "fargate_migrations_egress" {
  for_each = tomap({
    "ports_all_open" = local.security_group_egress_rules.ports_all_open
  })

  type              = "egress"
  from_port         = each.value.from_port
  to_port           = each.value.to_port
  protocol          = each.value.protocol
  description       = each.value.description
  cidr_blocks       = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks  = lookup(each.value, "ipv6_cidrs", null)
  security_group_id = module.migrations.security_group_id
}
