variable "environment" {
  description = "The environment (e.g. dev, staging, prod)"
  type        = string
}

variable "tags" {
  description = "The key, value pairs of tags to associate to the resources within this module"
  type        = map(any)
  default     = {}
}

/*-------------------*\
|*        VPC        *|
\*-------------------*/

variable "vpc_id" {
  description = "The ID of the VPC in which the Status History database exists"
  type        = string
}

variable "vpc_cidr_block" {
  description = "The CIDR block of the VPC in which the Status History database exists"
  type        = string
}

variable "vpc_private_subnet_ids" {
  type        = list(string)
  description = "The IDs of the private subnets of the VPC in which the Status History database exists"
}

/*-------------------*\
|*        KMS        *|
\*-------------------*/

variable "kms_admins" {
  description = "The ARNs of the IAM roles that should have administrative access to the KMS key"
  type        = list(string)
  default     = []
}

/*-------------------*\
|*        SNS        *|
\*-------------------*/

variable "status_messages_topic_arn" {
  description = "The ARN of the connectivity status messages SNS topic for the consumer to subscribe to"
  type        = string
}

/*--------------------*\
|*     Monitoring     *|
\*--------------------*/

variable "alarm_actions" {
  type        = list(string)
  default     = []
  description = "Define the actions to perform when the alarms change state."
}

variable "support_grafana_cidr_block" {
  description = "CIDR block for Support Grafana Access."
  type        = string
  default     = ""
}
