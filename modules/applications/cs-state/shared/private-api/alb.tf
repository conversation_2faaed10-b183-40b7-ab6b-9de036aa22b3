module "api_alb" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws"
  version = "4.0.0"

  count = var.deploy_alb ? 1 : 0

  enable_internal_lb = true

  http_tcp_listeners = [
    {
      port               = 80
      protocol           = "HTTP"
      target_group_index = 0
    }
  ]

  load_balancer_name    = "${var.name}-alb"
  load_balancer_subnets = var.subnet_ids
  load_balancer_type    = "application"
  idle_timeout          = 30

  security_group_name        = "${var.name}-api-lb"
  security_group_description = "Security Group for the ${var.name}-api load balancer."
  security_group_ingress_rules = {
    "port_http_all" = {
      description = "HTTP permitted from the NLB."
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cidrs  = formatlist("%s/32", data.aws_network_interface.nlb.*.private_ip)
      ipv6_cidrs  = null
    }
  }

  target_groups = [
    {
      backend_port         = 3000
      backend_protocol     = "HTTP"
      deregistration_delay = 5
      name                 = var.name
      target_type          = "ip"
      vpc_id               = var.vpc_id

      health_check = {
        enabled             = true
        healthy_threshold   = 2
        interval            = 6
        matcher             = "200"
        path                = var.alb_health_check_path
        port                = "traffic-port"
        protocol            = "HTTP"
        timeout             = 5
        unhealthy_threshold = 2
      }
    }
  ]

  vpc_id = var.vpc_id

  tags = var.tags
}

resource "aws_lb_target_group_attachment" "nlb_alb_target" {
  count = var.deploy_alb ? 1 : 0

  target_group_arn = module.api_nlb.target_group_arns[0]
  target_id        = module.api_alb[0].load_balancer_id
  port             = 80

  # adding an explicit dependency on the Application Loadbalancer Outputs.
  # This should prevent creating the target group before the ALB listener is available
  depends_on = [
    module.api_alb[0].http_tcp_listener_arns
  ]
}
