resource "aws_ecs_task_definition" "this" {

  family = var.task_name

  task_role_arn            = aws_iam_role.ecs_task.arn
  requires_compatibilities = ["EC2", "FARGATE"]
  memory                   = "1024"
  cpu                      = "512"
  execution_role_arn       = aws_iam_role.ecs_task_execution.arn
  network_mode             = "awsvpc"
  container_definitions    = var.container_definition

  lifecycle {
    ignore_changes = [container_definitions]
  }

  tags = var.tags
}

# We need to define a cloudwatch group for the task as it's not created by the fargate service module
# (due to it being a manually run task rather than a long-lived service)

resource "aws_cloudwatch_log_group" "diagnostics_smoke_test_log_group" {
  name              = "/ecs/${var.task_name}"
  retention_in_days = 30

  tags = var.tags
}

