resource "aws_iam_role" "ecs_task_execution" {
  name        = "${var.task_name}-task-execution-role"
  description = "ECS task execution role for ${var.task_name}"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      },
    ]
  })
  managed_policy_arns = ["arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"]

  tags = var.tags
}

data "aws_iam_policy_document" "ecs_task_execution_policy" {
  count = length(var.ecs_task_execution_policy_statements) > 0 ? 1 : 0
  dynamic "statement" {
    for_each = var.ecs_task_execution_policy_statements
    content {
      sid       = statement.value.sid
      actions   = statement.value.actions
      effect    = statement.value.effect
      resources = statement.value.resources
    }
  }
}

resource "aws_iam_role_policy" "ecs_task_execution_policy_attachment" {
  count  = length(var.ecs_task_execution_policy_statements) > 0 ? 1 : 0
  name   = "${var.task_name}-ecs-task-execution"
  role   = aws_iam_role.ecs_task_execution.id
  policy = data.aws_iam_policy_document.ecs_task_execution_policy[0].json
}


resource "aws_iam_role" "ecs_task" {
  name        = "${var.task_name}-ecs-task-role"
  description = "ECS task role for ${var.task_name}"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      },
    ]
  })

  tags = var.tags
}

data "aws_iam_policy_document" "ecs_task_policy" {
  count = length(var.ecs_task_policy_statements) > 0 ? 1 : 0
  dynamic "statement" {
    for_each = var.ecs_task_policy_statements
    content {
      sid       = statement.value.sid
      actions   = statement.value.actions
      effect    = statement.value.effect
      resources = statement.value.resources
    }
  }
}

resource "aws_iam_role_policy" "ecs_task_policy_attachment" {
  count  = length(var.ecs_task_policy_statements) > 0 ? 1 : 0
  name   = "${var.task_name}-ecs-task"
  role   = aws_iam_role.ecs_task.id
  policy = data.aws_iam_policy_document.ecs_task_policy[0].json
}

data "aws_iam_policy_document" "iam_pass_ecs_roles" {
  statement {
    effect = "Allow"
    actions = [
      "iam:PassRole"
    ]
    resources = [
      aws_iam_role.ecs_task.arn,
      aws_iam_role.ecs_task_execution.arn
    ]
  }
}

resource "aws_iam_policy" "iam_pass_ecs_policy" {
  name        = "iam-pass-role-${var.task_name}"
  description = "Policy to allow pass role permissions for the ecs task roles"

  policy = data.aws_iam_policy_document.iam_pass_ecs_roles.json

  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "policy_attachment" {
  role       = var.ecs_cluster_github_role_name
  policy_arn = aws_iam_policy.iam_pass_ecs_policy.arn
}


data "aws_iam_policy_document" "tag_task_policy_document" {
  statement {
    effect = "Allow"
    actions = [
      "ecs:TagResource",
      "ecs:UntagResource"
    ]
    resources = [
      "arn:aws:ecs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:task-definition/${var.task_name}:*"
    ]
  }
}

resource "aws_iam_policy" "tag_task_definition_policy" {
  name        = "iam-tag-task-definition-${var.task_name}"
  description = "Policy to allow tagging task definition permissions for the ecs task github action"

  policy = data.aws_iam_policy_document.tag_task_policy_document.json

  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "tag_task_policy_attachment" {
  role       = var.ecs_cluster_github_role_name
  policy_arn = aws_iam_policy.tag_task_definition_policy.arn
}
