variable "task_name" {
  description = "Name of the task being deployed"
  type        = string
}

variable "container_definition" {
  description = "Initial container definition to be used"
  type        = string
}


variable "ecs_task_execution_policy_statements" {
  description = "A list containing policy statements for ecs task execution policy"
  type = list(object({
    sid       = string
    actions   = list(string)
    effect    = string
    resources = list(string)
  }))

  default = []
}

variable "ecs_task_policy_statements" {
  description = "A list containing policy statements for ecs task policy"
  type = list(object({
    sid       = string
    actions   = list(string)
    effect    = string
    resources = list(string)
  }))

  default = []
}

variable "ecs_cluster_github_role_name" {
  description = "Github role name to deploy task on"
  type        = string
}

variable "tags" {
  description = "Tags to be applied to the resources"
  type        = map(string)
  default     = {}
}
