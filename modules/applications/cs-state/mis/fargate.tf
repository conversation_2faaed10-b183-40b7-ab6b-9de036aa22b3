data "aws_caller_identity" "current" {}

module "ecs_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "10.2.0"

  identifier         = local.identifier
  repo_names         = ["admin-tool"]
  container_insights = "enabled"
  additional_github_ci_policy_statements = [
    {
      sid     = "AllowSchedulerGetUpdateSchedule"
      actions = ["scheduler:GetSchedule", "scheduler:UpdateSchedule"]
      resources = [
        module.scheduled_task["queue-worker-heartbeat"].schedule_arn,
        module.scheduled_task["clear-api-nonces"].schedule_arn,
        module.scheduled_task["reset-out-of-service-units"].schedule_arn,
        module.scheduled_task["update-last-contact"].schedule_arn,
      ]
    },
    {
      sid    = "AllowDescribeSecurityGroupsForECSTaskRunGitHubAction"
      effect = "Allow"
      actions = [
        "ec2:DescribeSecurityGroups"
      ]
      resources = [
        "*"
      ]
    }
  ]

  tags = local.module_tags
}
