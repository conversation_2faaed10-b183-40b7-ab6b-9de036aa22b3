module "database_migrations" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/standalone-task"
  version = "10.2.0"

  identifier         = local.database_migrations_identifier
  pipeline_role_name = module.ecs_cluster.github_role_name
  vpc_id             = var.vpc_id

  attach_custom_ecs_task_execution_iam_policy = true
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.mis_database_migrations_ecs_task_policy.json
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json

  container_definition = jsonencode([
    {
      "name" : "php",
      "image" : "MANUAL_INTERACTION_REQUIRED",
      "command" : ["php", "artisan", "migrate", "--force", "-n"],
      "essential" : true,
      "networkMode" : "awsvpc",
      "readonly_root_filesystem" : false,
      "linuxParameters" : {
        "initProcessEnabled" : false
      },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/standalone/${local.database_migrations_identifier}",
          "awslogs-region" : var.aws_region,
          "awslogs-stream-prefix" : "ecs"
        }
      }
      "environment" : concat(local.base_env_vars, var.initial_task_def_environments)
      "secrets" : local.ecs_secrets
    }
  ])

  additional_kms_administrators = var.additional_kms_administrators
  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }]
  }]

  tags = local.module_tags
}

resource "aws_security_group_rule" "database_migrations_egress" {
  description       = "Permit all egress traffic."
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = module.database_migrations.security_group_id
}

resource "aws_security_group_rule" "database_migrations_db_security_group_ingress" {
  description              = "Permit 3306 from ${local.database_migrations_identifier}."
  type                     = "ingress"
  from_port                = 3306
  to_port                  = 3306
  protocol                 = "tcp"
  source_security_group_id = module.database_migrations.security_group_id
  security_group_id        = var.podadmin_db_security_group_id
}

# This contains permissions copied directly from OpsWorks role
# and some additional permissions to enable ECS Exec
# TODO: Minimise scope to least privilege
data "aws_iam_policy_document" "mis_database_migrations_ecs_task_policy" {
  statement {
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel"
    ]
    resources = ["*"]
  }
}
