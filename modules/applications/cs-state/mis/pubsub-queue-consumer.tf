module "pubsub_queue_consumer" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "10.2.0"

  cluster_arn  = module.ecs_cluster.arn
  cluster_name = module.ecs_cluster.name
  identifier   = format("%s-pubsub-consumer", local.identifier)

  container_definitions = jsonencode([
    {
      "name" : "php",
      "image" : "MANUAL_INTERACTION_REQUIRED",
      "command" : ["php", "artisan", "queue:work", "pub_sub"],
      "essential" : true,
      "networkMode" : "awsvpc",
      "readonly_root_filesystem" : false,
      "linuxParameters" : {
        "initProcessEnabled" : false
      },
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/${local.identifier}-pubsub-consumer",
          "awslogs-region" : var.aws_region,
          "awslogs-stream-prefix" : "ecs"
        }
      }
      "environment" : concat(local.base_env_vars, var.initial_task_def_environments)
      "secrets" : local.ecs_secrets
    }
  ])
  pipeline_role_name                          = module.ecs_cluster.github_role_name
  service_type                                = "rolling"
  subnet_ids                                  = var.ecs_private_subnets_ids
  vpc_id                                      = var.vpc_id
  attach_custom_ecs_task_execution_iam_policy = true
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.mis_pubsub_queue_consumer_ecs_task_policy.json
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  force_new_deployment                        = false
  deployment_maximum_percent                  = 100
  deployment_minimum_healthy_percent          = 0
  scaling_min_capacity                        = 1 # sets desired count
  enable_auto_scaling                         = false

  additional_kms_administrators = var.additional_kms_administrators
  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }]
  }]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "mis_pubsub_queue_consumer_age_of_oldest_message" {
  alarm_name = "${local.identifier}-pubsub-queue-consumer-age-of-oldest-message"
  dimensions = {
    QueueName = aws_sqs_queue.mis_pubsub_queue.name
  }
  metric_name         = "ApproximateAgeOfOldestMessage"
  namespace           = "AWS/SQS"
  comparison_operator = "GreaterThanThreshold"
  period              = "300"
  evaluation_periods  = "1"
  statistic           = "Maximum"
  threshold           = "900"
  alarm_description   = "MIS pub-sub queue age of oldest message"
  actions_enabled     = "true"
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "mis_pubsub_queue_consumer_cpu_alarm" {
  alarm_name = "${local.identifier}-pubsub-queue-consumer-cpu"
  dimensions = {
    ClusterName = module.ecs_cluster.name
    ServiceName = module.pubsub_queue_consumer.service_name
  }
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  comparison_operator = "GreaterThanThreshold"
  period              = "300"
  evaluation_periods  = "1"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "MIS pub-sub queue consumer CPU Utilisation Average > 80%"
  actions_enabled     = "true"
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "mis_pubsub_queue_consumer_memory_alarm" {
  alarm_name = "${local.identifier}-pubsub-queue-consumer-memory"
  dimensions = {
    ClusterName = module.ecs_cluster.name
    ServiceName = module.pubsub_queue_consumer.service_name
  }
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  comparison_operator = "GreaterThanThreshold"
  period              = "300"
  evaluation_periods  = "1"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "MIS pub-sub queue consumer Memory Utilisation Average > 80%"
  actions_enabled     = "true"
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_security_group_rule" "pubsub_queue_consumer_egress" {
  description       = "Permit all egress traffic."
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = module.pubsub_queue_consumer.security_group_id
}

resource "aws_security_group_rule" "pubsub_queue_consumer_db_security_group_ingress" {
  description              = "Permit 3306 from mis-pubsub-queue-consumer-${local.environment}."
  type                     = "ingress"
  from_port                = 3306
  to_port                  = 3306
  protocol                 = "tcp"
  source_security_group_id = module.pubsub_queue_consumer.security_group_id
  security_group_id        = var.podadmin_db_security_group_id
}

resource "aws_security_group_rule" "pubsub_queue_consumer_redis_cache_security_group_ingress" {
  description              = "Permit 6379 from the mis-pubsub-queue-consumer-${local.environment}."
  type                     = "ingress"
  from_port                = 6379
  to_port                  = 6379
  protocol                 = "tcp"
  source_security_group_id = module.pubsub_queue_consumer.security_group_id
  security_group_id        = var.redis_cache_security_group_id
}

# This contains permissions copied directly from OpsWorks role
# and some additional permissions to enable ECS Exec
# TODO: Minimise scope to least privilege
data "aws_iam_policy_document" "mis_pubsub_queue_consumer_ecs_task_policy" {
  statement {
    actions   = ["s3:*"]
    resources = ["*"]
  }
  statement {
    actions   = ["sqs:*"]
    resources = ["*"]
  }
  statement {
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_role_policy_attachment" "pubsub_queue_consumer_task_role_policy_attachments" {
  for_each   = toset(var.ecs_task_role_additional_policies)
  role       = module.pubsub_queue_consumer.task_role_name
  policy_arn = each.key
}
