moved {
  from = module.database_egress_rules["ports_all_open"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_egress_rules["ports_all_open"]
}

resource "aws_security_group_rule" "database_egress_rules" {
  for_each = local.database_egress_rules

  type                     = "egress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = lookup(each.value, "security_group_id", module.aurora_diagnostics.security_group_id)
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)
}

moved {
  from = module.database_ingress_rules["permit_vpn_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_vpn_access"]
}

moved {
  from = module.database_ingress_rules["permit_event_processor_ecs_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_event_processor_ecs_access"]
}

moved {
  from = module.database_ingress_rules["permit_api_ecs_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_api_ecs_access"]
}

moved {
  from = module.database_ingress_rules["permit_migrations_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_migrations_access"]
}

moved {
  from = module.database_ingress_rules["permit_grafana_access"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_grafana_access"]
}

resource "aws_security_group_rule" "database_ingress_rules" {
  for_each = local.database_ingress_rules

  type                     = "ingress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = lookup(each.value, "security_group_id", module.aurora_diagnostics.security_group_id)
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)
}

module "commands_api_privatelink_client" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws//modules/privatelink-client"
  version = "2.4.0"

  client_ingress_rules = {
    diagnostics_api = {
      description           = "Connectivity permitted from diagnostics API (cs-state) to commands API (cs-connectivity)"
      from_port             = 80
      to_port               = 80
      protocol              = "TCP"
      source_security_group = module.diagnostics_api.security_group_id
    }
  }

  target = {
    name         = "diagnostics-commands-api"
    service_name = var.commands_api_vpc_endpoint_service_name
  }

  vpc_id     = var.vpc_id
  subnet_ids = var.subnet_ids

  tags = local.module_tags
}
