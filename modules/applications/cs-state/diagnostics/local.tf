locals {
  name       = "diagnostics-service"
  identifier = "${local.name}-${var.environment}"

  event_processor_task_name = "${local.name}-event-processor"
  migrations_task_name      = "${local.name}-database-migrations"
  api_task_name             = "${local.name}-api"
  smoke_test_task_name      = "${local.name}-smoke-test"

  database_admin_username = "podpoint"
  database_port           = 5432

  podadmin_port = 3306

  database_ingress_rules = {
    permit_vpn_access = {
      description = "AWS Client VPN"
      from_port   = local.database_port
      to_port     = local.database_port
      protocol    = "TCP"
      ipv4_cidrs = [
        "**********/22",
        "**********/22"
      ]
    },
    permit_event_processor_ecs_access = {
      description           = "Event processor permission"
      from_port             = local.database_port
      to_port               = local.database_port
      protocol              = "TCP"
      source_security_group = module.diagnostics_event_processor.security_group_id
    },
    permit_api_ecs_access = {
      description           = "API permission"
      from_port             = local.database_port
      to_port               = local.database_port
      protocol              = "TCP"
      source_security_group = module.diagnostics_api.security_group_id
    },
    permit_migrations_access = {
      description           = "Migrations permission"
      from_port             = local.database_port
      to_port               = local.database_port
      protocol              = "TCP"
      source_security_group = module.migrations_task_definition.security_group_id
    },
    permit_grafana_access = {
      description = "Allow Grafana - Pod Point"
      from_port   = local.database_port
      to_port     = local.database_port
      protocol    = "TCP"
      ipv4_cidrs  = ["*********/32"]
    },
  }

  database_egress_rules = {
    ports_all_open = {
      description = "Permit all egress traffic."
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }

  ecs_task_execution_custom_policy = {
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "kms:Decrypt"
        ],
        "Resource" : [
          module.kms.arn,
        ]
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "secretsmanager:GetSecretValue"
        ],
        "Resource" : [
          "${module.aurora_diagnostics.admin_user_secret_manager_arn}",
          "${aws_secretsmanager_secret.diagnostics_service.arn}",
          "${aws_secretsmanager_secret.configcat_sdk_key.arn}",
        ]
      }
    ]
  }

  ecs_egress_rules = {
    permit_database = {
      description           = "Permit ${local.name} ecs calls to database"
      from_port             = local.database_port
      to_port               = local.database_port
      protocol              = "TCP"
      source_security_group = module.aurora_diagnostics.security_group_id
    },
  }

  ecs_security_group_name = format("%s-ecs", local.identifier)

  ecs_port = 3000

  module_tags = {
    "pp:owner"   = "network:assets"
    "pp:service" = "diagnostics"
  }
}
