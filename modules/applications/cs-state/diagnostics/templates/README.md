Normally changes to the task definition are ignored by the lifecycle attribute in the Fargate module. This is to avoid creating a new task definition every time the Terraform scripts are applied.

If you need to make changes to the task definition, for example to add or modify environment variables, you must force Terraform to `replace` the task definition.

To do this in Terraform Enterprise, add the following environment variable:

Name: `TF_CLI_ARGS_plan`
Value: `-replace=module.diagnostics.module.diagnostics_event_processor.aws_ecs_task_definition.this`

Note that you will need to subsequently run a deployment to deploy a task using the new definition (it will not be applied to existing tasks)

Don't forget to apply this environment variable in all environments, and remove it when you are finished deploying!
