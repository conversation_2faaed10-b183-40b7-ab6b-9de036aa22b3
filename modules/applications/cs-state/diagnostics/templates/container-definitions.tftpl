[
  {
    "environment": [
      {
        "name": "COMMAND_RESPONSES_SQS_QUEUE_NAME",
        "value": "${command_responses_sqs_queue_name}"
      },
      {
        "name": "COMMAND_RESPONSES_QUEUE_URL",
        "value": "${command_responses_queue_url}"
      },
      {
        "name": "CS_REQUESTS_QUEUE_NAME",
        "value": "${cs_requests_queue_name}"
      },
      {
        "name": "CS_REQUESTS_QUEUE_URL",
        "value": "${cs_requests_queue_url}"
      },
      {
        "name": "DB_READ_ENDPOINT",
        "value": "${db_read_endpoint}"
      },
      {
        "name": "DB_WRITE_ENDPOINT",
        "value": "${db_write_endpoint}"
      },
      {
        "name": "DB_PORT",
        "value": "${db_port}"
      },
      {
        "name": "PODADMIN_READ_ENDPOINT",
        "value": "${podadmin_endpoint}"
      },
      {
        "name": "PODADMIN_PORT",
        "value": "${podadmin_port}"
      },
      {
        "name": "SECURITY_EVENT_SQS_QUEUE_NAME",
        "value": "${sqs_queue_name}"
      },
      {
        "name": "SECURITY_EVENT_SQS_QUEUE_URL",
        "value": "${sqs_queue_url}"
      },
      {
        "name": "SECURITY_EVENT_SQS_QUEUE_REGION",
        "value": "${sqs_queue_region}"
      },
      {
        "name": "NODE_ENV",
        "value": "${node_env}"
      },
      {
        "name": "NO_COLOR",
        "value": "true"
      },
      {
        "name": "TZ",
        "value": "Etc/UTC"
      },
      {
        "name": "LOG_FORMAT",
        "value": "json"
      },
      {
        "name": "LOG_LEVEL",
        "value": "${log_level}"
      }
    ],
    "secrets": [
      {
        "name": "DB_CREDENTIALS",
        "valueFrom": "${db_credentials}"
      },
      {
        "name": "PODADMIN_CREDENTIALS",
        "valueFrom": "${podadmin_credentials}"
      },
      {
        "name": "CONFIGCAT_SDK_KEY",
        "valueFrom": "${configcat_sdk_key}"
      }
    ],
    "essential": true,
    "image": "terraform",
    "linuxParameters": {
      "initProcessEnabled": true
    },
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${name}",
        "awslogs-region": "${region}",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "name": "${name}",
    "networkMode": "awsvpc",
    "portMappings": [
      {
        "containerPort": ${port},
        "hostPort": ${port}
      }
    ]
  }
]
