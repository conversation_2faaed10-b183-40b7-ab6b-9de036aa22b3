module "diagnostics_api" {
  source = "../shared/private-api"

  environment = var.environment
  region      = data.aws_region.current.name
  account_id  = data.aws_caller_identity.current.account_id
  subnet_ids  = var.subnet_ids
  vpc_id      = var.vpc_id
  name        = local.api_task_name
  ecs_port    = 3000

  deploy_alb = true

  additional_kms_administrators = var.additional_kms_administrators

  ecs_cluster_arn              = module.diagnostics_ecs_cluster.arn
  ecs_cluster_name             = module.diagnostics_ecs_cluster.name
  ecs_cluster_github_role_name = module.diagnostics_ecs_cluster.github_role_name
  ecs_task_execution_policy    = jsonencode(local.ecs_task_execution_custom_policy)

  attach_custom_ecs_task_iam_policy = true
  ecs_task_custom_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "execute-api:Invoke"
        ],
        "Resource" : [
          "arn:aws:execute-api:${var.region}:*:${var.short_url_api.rest_api_id}/*/POST/create"
        ]
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "s3:putObject",
          "s3:getObject",
        ],
        "Resource" : [
          "${module.charging_station_logs_bucket.s3_bucket_arn}/*"
        ]
      }
    ]
  })

  container_definition = templatefile("${path.module}/templates/api-container.tftpl", {
    "name" : local.api_task_name
    "region" : data.aws_region.current.name
    "port" : 3000
    "aws_default_region" : data.aws_region.current.name
    "aws_log_bucket_name" : module.charging_station_logs_bucket.s3_bucket_id
    "db_credentials" : module.aurora_diagnostics.admin_user_secret_manager_arn
    "db_port" : 5432
    "db_read_endpoint" : module.aurora_diagnostics.reader_endpoint
    "db_write_endpoint" : module.aurora_diagnostics.cluster_endpoint
    "node_env" : var.environment
    "short_url_api_endpoint" : var.short_url_api.endpoint
    "commands_api_endpoint" : "http://${module.commands_api_privatelink_client.dns_entry[0].dns_name}"
    "configcat_sdk_key" : aws_secretsmanager_secret.configcat_sdk_key.arn
    "log_level" : var.log_level
  })

  api_gateway_ingress_rules   = var.api_gateway_ingress_rules
  execute_api_vpc_endpoint_id = var.execute_api_vpc_endpoint_id
  vpc_endpoint_ids            = var.vpc_endpoint_ids

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "api_cpu_usage" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name  = format("%s-cpu-usage", local.api_task_name)
  namespace   = "AWS/ECS"
  metric_name = "CPUUtilization"
  dimensions = {
    ClusterName = module.diagnostics_ecs_cluster.name
    ServiceName = local.api_task_name
  }
  alarm_description   = "Alerts on CPU usage of 80% or higher over a window of 15 minutes consistently in each minute."
  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "80" # Alert on 80% or higher
  period              = "60" # Look at each minute
  evaluation_periods  = "15" # Look at the last 15 minutes wherein ...
  datapoints_to_alarm = "15" # ... each minute needs to be above threshold
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "api_memory_usage" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name  = format("%s-memory-usage", local.api_task_name)
  namespace   = "AWS/ECS"
  metric_name = "MemoryUtilization"
  dimensions = {
    ClusterName = module.diagnostics_ecs_cluster.name
    ServiceName = local.api_task_name
  }
  alarm_description   = "Alerts on memory usage of 90% or higher over a window of 15 minutes consistently in each minute."
  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = "90" # Alert on 90% or higher
  period              = "60" # Look at each minute
  evaluation_periods  = "15" # Look at the last 15 minutes wherein ...
  datapoints_to_alarm = "15" # ... each minute needs to be above threshold
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "api_5XX_errors" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  namespace  = "AWS/ApiGateway"
  alarm_name = format("%s-gateway-5XX-count", local.api_task_name)
  dimensions = {
    ApiName = local.api_task_name
  }
  period              = 300
  evaluation_periods  = 1
  statistic           = "Sum"
  metric_name         = "5XXError"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 1
  alarm_description   = "All 5XX errors - Diagnostics API gateway"
  actions_enabled     = "true"
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "api_alb_response_time" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name                = format("%s-response-time", local.api_task_name)
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  alarm_description         = "50% or more of responses take longer than 500ms within 5 minutes"
  evaluation_periods        = 1
  treat_missing_data        = "notBreaching"
  insufficient_data_actions = []
  metric_name               = "TargetResponseTime"
  namespace                 = "AWS/ApplicationELB"
  period                    = 300 // 5 minutes
  statistic                 = "Average"
  threshold                 = 0.5 // Half a second
  alarm_actions             = [var.cloudwatch_alarm_topic_arn]
  ok_actions                = [var.cloudwatch_alarm_topic_arn]

  dimensions = {
    LoadBalancer = module.diagnostics_api.load_balancer_arn_suffix
  }

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "elb_502" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name                = format("%s-elb-502", local.api_task_name)
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  alarm_description         = "1 or more 502 errors from ELB within 5 minutes"
  evaluation_periods        = 1
  treat_missing_data        = "notBreaching"
  insufficient_data_actions = []
  metric_name               = "HTTPCode_ELB_502_Count"
  namespace                 = "AWS/ApplicationELB"
  period                    = 300 // 5 minutes
  statistic                 = "Maximum"
  threshold                 = 1
  alarm_actions             = [var.cloudwatch_alarm_topic_arn]
  ok_actions                = [var.cloudwatch_alarm_topic_arn]

  dimensions = {
    LoadBalancer = module.diagnostics_api.load_balancer_arn_suffix
  }

  tags = local.module_tags
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 * Private VPC link service
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/
data "aws_vpc_endpoint_service" "diagnostics_vpc_service_endpoint" {
  count = length(var.experience_account_ids) > 0 ? 1 : 0

  filter {
    name   = "service-name"
    values = [var.diagnostics_vpc_endpoint_service_name]
  }
}

resource "aws_vpc_endpoint_service_allowed_principal" "allow_experience_diagnostics_vpc_service_endpoint" {
  for_each = var.experience_account_ids

  vpc_endpoint_service_id = data.aws_vpc_endpoint_service.diagnostics_vpc_service_endpoint[0].service_id
  principal_arn           = "arn:aws:iam::${each.value}:root"
}
