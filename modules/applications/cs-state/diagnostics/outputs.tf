output "aurora_diagnostics" {
  description = "Aurora cluster attributes for diagnostics service."
  value = {
    cluster_id        = module.aurora_diagnostics.cluster_identifier
    cluster_endpoint  = module.aurora_diagnostics.cluster_endpoint
    reader_endpoint   = module.aurora_diagnostics.reader_endpoint
    engine            = module.aurora_diagnostics.engine
    port              = module.aurora_diagnostics.port
    cluster_instances = module.aurora_diagnostics.cluster_members
  }
}

output "sqs" {
  description = "The SQS queue attributes"
  value = {
    id  = aws_sqs_queue.security_event_updates_dlq.id
    arn = aws_sqs_queue.security_event_updates_dlq.arn
  }
}

output "api_gateway_vpce_url" {
  description = "The API endpoints"

  value = {
    api_endpoint = module.diagnostics_api.api_gateway_vpce_url
  }
}

output "api_gateway_id" {
  description = "The ID of the API Gateway"
  value       = module.diagnostics_api.api_gateway_id
}

output "api_gateway_stage_name" {
  description = "The stage name of the API Gateway"
  value       = module.diagnostics_api.api_gateway_stage_name
}

output "diagnostics_servie_credentials" {
  description = "The secrets manager attributes of the username and password for the diagnostics service user for podadmin"
  value = {
    arn  = aws_secretsmanager_secret.diagnostics_service.arn
    name = aws_secretsmanager_secret.diagnostics_service.name
  }
}

output "execute_api_vpc_endpoint_sec_group_id" {
  value = module.diagnostics_api.vpc_security_group_id
}

output "kms_diagnostics" {
  description = "KMS module outputs."
  value       = module.kms[*]
}

output "diagnostics_aurora_security_group" {
  description = "The security group for the Aurora cluster"
  value       = module.aurora_diagnostics.security_group_id
}
