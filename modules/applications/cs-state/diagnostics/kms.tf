module "kms" {
  source  = "terraform-enterprise.pod-point.com/technology/kms/aws"
  version = "1.1.0"

  description              = format("KMS key for the %s service", local.identifier)
  alias_name               = local.identifier
  policy                   = data.aws_iam_policy_document.diagnostics_kms_policy.json
  enable_aws_backup_access = var.environment == "prod" ? true : false

  tags = local.module_tags
}

data "aws_iam_policy_document" "diagnostics_kms_policy" {
  statement {
    sid     = "Enable Account Access"
    actions = ["kms:*"] # Later scope this down to allow aws backup for aurora
    resources = [
      "*"
    ]
    principals {
      type        = "AWS"
      identifiers = [format("arn:aws:iam::%s:root", data.aws_caller_identity.current.account_id)]
    }
  }

  statement {
    sid = "EnableSNS"
    actions = [
      "kms:GenerateDataKey",
      "kms:Decrypt"
    ]

    resources = ["*"] # Scope this down later on and point to the source sns.

    principals {
      type        = "Service"
      identifiers = ["sns.amazonaws.com"]
    }
  }
}
