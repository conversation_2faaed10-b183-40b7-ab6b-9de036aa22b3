resource "aws_sqs_queue" "diagnostics_command_responses" {
  name = "diagnostics-command-responses"

  kms_master_key_id = module.kms.id

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.diagnostics_command_responses_dlq.arn
    maxReceiveCount     = var.diagnostics_command_responses_max_receive_count
  })

  tags = local.module_tags
}

resource "aws_cloudwatch_metric_alarm" "diagnostics_command_responses_messages_visible" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name = format("diagnostics_command_responses-%s-messages-visible", var.environment)

  dimensions = {
    QueueName = aws_sqs_queue.security_event_updates_dlq.name
  }
  metric_name         = "ApproximateNumberOfMessagesVisible"
  namespace           = "AWS/SQS"
  comparison_operator = "GreaterThanThreshold"
  period              = "300"
  evaluation_periods  = "1"
  statistic           = "Maximum"
  threshold           = "10"
  alarm_description   = "Diagnostics Command Responses approximate number of messages visible > 10"
  actions_enabled     = "true"
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}

resource "aws_sqs_queue" "diagnostics_command_responses_dlq" {
  name                      = "diagnostics-command-responses-dlq"
  message_retention_seconds = 1209600 // 14 days (the maximum)

  kms_master_key_id = module.kms.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = ["arn:aws:sqs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:diagnostics-command-responses"]
  })

  tags = local.module_tags
}

resource "aws_sns_topic_subscription" "diagnostics_command_responses_sub" {
  endpoint             = aws_sqs_queue.diagnostics_command_responses.arn
  protocol             = "sqs"
  raw_message_delivery = true
  topic_arn            = var.diagnostics_command_responses_topic_arn
  filter_policy = jsonencode(
    {
      "Type" : ["GetLogs-DiagnosticsLog", "GetLogs-SecurityLog"],
    }
  )
}

resource "aws_sqs_queue_policy" "diagnostics_command_responses_queue_policy" {
  queue_url = aws_sqs_queue.diagnostics_command_responses.id
  policy    = <<POLICY
{
  "Version": "2012-10-17",
  "Id": "${aws_sqs_queue.diagnostics_command_responses.arn}/SQSDefaultPolicy",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "sqs:SendMessage",
      "Resource": "${aws_sqs_queue.diagnostics_command_responses.arn}",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "${var.diagnostics_command_responses_topic_arn}"
        }
      }
    }
  ]
}
POLICY
}

resource "aws_cloudwatch_metric_alarm" "diagnostics_command_responses_dlq_messages_visible" {
  count = var.cloudwatch_alarm_topic_arn != null ? 1 : 0

  alarm_name = format("diagnostics_command_responses-%s-dlq-messages-visible", var.environment)

  dimensions = {
    QueueName = aws_sqs_queue.security_event_updates_dlq.name
  }
  metric_name         = "ApproximateNumberOfMessagesVisible"
  namespace           = "AWS/SQS"
  comparison_operator = "GreaterThanThreshold"
  period              = "300"
  evaluation_periods  = "1"
  statistic           = "Maximum"
  threshold           = "0"
  alarm_description   = "Diagnostics Command Responses DLQ approximate number of messages visible > 0"
  actions_enabled     = "true"
  treat_missing_data  = "notBreaching"
  alarm_actions       = [var.cloudwatch_alarm_topic_arn]
  ok_actions          = [var.cloudwatch_alarm_topic_arn]

  tags = local.module_tags
}
