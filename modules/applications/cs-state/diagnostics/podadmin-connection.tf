resource "aws_secretsmanager_secret" "diagnostics_service" {
  name                    = "aws/database/podadmin/diagnostics_service"
  description             = "User the diagnostic_service will use when authenticating to podadmin"
  kms_key_id              = module.kms.id
  recovery_window_in_days = 0

  tags = local.module_tags
}

resource "aws_secretsmanager_secret_version" "diagnostics_service" {
  secret_id = aws_secretsmanager_secret.diagnostics_service.id
  secret_string = jsonencode({
    username = "diagnostics_service"
    password = "MANUAL_INTERACTION_REQUIRED_REPLACE_ME"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}
