module "diagnostics_ecs_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "10.2.0"

  identifier         = "diagnostics"
  repo_names         = ["diagnostics-service"]
  container_insights = var.environment == "prod" ? "enabled" : "disabled"
  additional_github_ci_policy_statements = [
    {
      sid       = "AllowEC2DescribeSecurityGroups"
      actions   = ["ec2:DescribeSecurityGroups"]
      resources = ["*"]
      effect    = "Allow"
    }
  ]

  tags = local.module_tags
}
