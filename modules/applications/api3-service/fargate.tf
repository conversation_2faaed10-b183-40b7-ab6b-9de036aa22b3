module "ecs_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "11.2.4"

  identifier         = local.identifier
  repo_names         = [local.service_name]
  container_insights = "enabled"

  tags = local.tags
}

module "ecs_service" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "11.2.4"

  identifier   = local.identifier
  service_type = "rolling"

  vpc_id                            = var.vpc_id
  subnet_ids                        = var.ecs_private_subnets_ids
  cluster_name                      = module.ecs_cluster.name
  cluster_arn                       = module.ecs_cluster.arn
  pipeline_role_name                = module.ecs_cluster.github_role_name
  force_new_deployment              = true
  health_check_grace_period_seconds = 30

  cpu    = var.fargate_task_level_cpu
  memory = var.fargate_task_level_memory

  scaling_min_capacity = var.fargate_scaling_min_capacity
  scaling_max_capacity = var.fargate_scaling_max_capacity

  enable_auto_scaling      = true
  use_default_step_scaling = false

  custom_step_scaling = [
    {
      name = "CPUUtilization-high"
      alarm = {
        threshold = var.fargate_scaling_cpu_threshold
        metrics = [
          {
            id = "metric_high"
            metric = {
              metric_name = "CPUUtilization"
              namespace   = "AWS/ECS"
              period      = "60"
              stat        = "Maximum"
              dimensions = {
                ClusterName = module.ecs_cluster.name
                ServiceName = local.identifier
              }
            }
            return_data = true
          }
        ]
      }

      scale_out_policy = {
        cooldown             = 60
        gradual_lower_bound  = 0
        gradual_upper_bound  = 20
        gradual_adjustment   = 1
        critical_lower_bound = 20
        critical_adjustment  = 3
      }

      scale_in_policy = {
        cooldown    = 300
        upper_bound = 0
        adjustment  = -1
      }
    }
  ]

  capacity_fargate_base        = var.environment == "dev" ? 0 : var.fargate_scaling_min_capacity
  capacity_fargate_weight      = 0
  capacity_fargate_spot_base   = var.environment == "dev" ? var.fargate_scaling_min_capacity : 0
  capacity_fargate_spot_weight = 1

  load_balancing_configuration = [{
    target_group_arn = var.privatelink_target_api3_alb_arn
    container_name   = local.identifier
    container_port   = local.container_port
  }]

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.custom_ecs_task_container_policy.json

  container_definitions = templatefile("${path.module}/templates/container-definitions-service.tftpl", {
    "name" : local.identifier
    "service_name" : local.service_name
    "region" : data.aws_region.current.name
    "port" : tonumber(local.container_port)
    "secrets" : local.container_secrets
    "environment" : local.container_environment
    "health_check_path" : local.health_check_path
    "account_id" : data.aws_caller_identity.current.account_id
  })

  additional_kms_administrators = local.kms_admins
  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }]
  }]

  tags = local.tags
}

module "ecs_queue_worker" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "11.2.4"

  identifier   = format("%s-queue-worker", local.identifier)
  service_type = "rolling"

  vpc_id               = var.vpc_id
  subnet_ids           = var.ecs_private_subnets_ids
  cluster_name         = module.ecs_cluster.name
  cluster_arn          = module.ecs_cluster.arn
  pipeline_role_name   = module.ecs_cluster.github_role_name
  force_new_deployment = false

  cpu    = var.fargate_queue_worker_task_level_cpu
  memory = var.fargate_queue_worker_task_level_memory

  enable_auto_scaling      = true
  use_default_step_scaling = false

  scaling_min_capacity = var.fargate_queue_worker_desired_task_count
  scaling_max_capacity = var.fargate_queue_worker_maximum_task_count

  custom_step_scaling = [
    {
      name = "MessageToTaskRatio"
      alarm = {
        threshold = 10
        metrics = [
          {
            id    = "queue_visible_count"
            label = "ApproximateNumberOfMessagesVisible"
            metric = {
              namespace   = "AWS/SQS"
              metric_name = "ApproximateNumberOfMessagesVisible"
              stat        = "p95"
              period      = 60
              dimensions = {
                QueueName = data.aws_sqs_queue.sqs_sns_queue.name
              }
            }
          },
          {
            id    = "ecs_worker_task_count"
            label = "RunningTaskCount"
            metric = {
              namespace   = "ECS/ContainerInsights"
              metric_name = "RunningTaskCount"
              stat        = "Maximum"
              period      = 60
              dimensions = {
                ClusterName = module.ecs_cluster.name
                ServiceName = module.ecs_queue_worker.service_name
              }
            }
          },
          {
            id          = "message_to_task_ratio"
            expression  = "queue_visible_count / ecs_worker_task_count"
            label       = "MessageToTaskRatio"
            return_data = true
          }
        ]
      }

      scale_out_policy = {
        cooldown             = 60
        gradual_lower_bound  = 0
        gradual_upper_bound  = 5
        gradual_adjustment   = 1
        critical_lower_bound = 5
        critical_adjustment  = 3
      }

      scale_in_policy = {
        cooldown    = 120
        upper_bound = 0
        adjustment  = -1
      }
    }
  ]

  capacity_fargate_base        = var.environment == "dev" ? 0 : var.fargate_queue_worker_desired_task_count
  capacity_fargate_weight      = 0
  capacity_fargate_spot_base   = var.environment == "dev" ? var.fargate_queue_worker_desired_task_count : 0
  capacity_fargate_spot_weight = 1

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.custom_ecs_task_container_policy.json

  container_definitions = templatefile("${path.module}/templates/container-definitions-queue-worker.tftpl", {
    "environment" : local.container_environment
    "name" : format("%s-queue-worker", local.identifier)
    "region" : data.aws_region.current.name
    "secrets" : local.container_secrets
  })

  additional_kms_administrators = local.kms_admins
  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }]
  }]

  tags = local.tags
}

module "ecs_scheduled_task_worker" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "11.2.4"

  service_type = "rolling"
  identifier   = format("%s-scheduled-task-worker", local.identifier)

  vpc_id               = var.vpc_id
  subnet_ids           = var.ecs_private_subnets_ids
  cluster_name         = module.ecs_cluster.name
  cluster_arn          = module.ecs_cluster.arn
  pipeline_role_name   = module.ecs_cluster.github_role_name
  force_new_deployment = false

  cpu    = var.fargate_scheduled_task_worker_task_level_cpu
  memory = var.fargate_scheduled_task_worker_task_level_memory

  enable_auto_scaling = false

  scaling_min_capacity         = var.fargate_scheduled_task_worker_desired_task_count
  scaling_max_capacity         = var.fargate_scheduled_task_worker_desired_task_count
  capacity_fargate_base        = var.environment == "dev" ? 0 : var.fargate_scheduled_task_worker_desired_task_count
  capacity_fargate_weight      = 0
  capacity_fargate_spot_base   = var.environment == "dev" ? var.fargate_scheduled_task_worker_desired_task_count : 0
  capacity_fargate_spot_weight = 1

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.custom_ecs_task_container_policy.json

  container_definitions = templatefile("${path.module}/templates/container-definitions-scheduled-task-worker.tftpl", {
    "environment" : local.container_environment
    "name" : format("%s-scheduled-task-worker", local.identifier)
    "region" : data.aws_region.current.name
    "secrets" : local.container_secrets
  })

  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }]
  }]

  tags = local.tags
}
