{"variables": [{"type": "pattern", "pattern": "{{customerId}}", "inputType": "input", "id": "customerId", "label": "BigCommerce Customer ID", "defaultValue": "[0-9]*", "visible": true}, {"type": "pattern", "pattern": "{{apigwRequestId}}", "inputType": "input", "id": "apigwRequestId", "label": "API Gateway Request ID", "defaultValue": "[0-9A-Za-z=]*", "visible": true}, {"type": "pattern", "pattern": "{{sentrySessionId}}", "inputType": "input", "id": "sentrySessionId", "label": "Sentry Session ID", "defaultValue": "[0-9A-Za-z]*", "visible": true}, {"type": "property", "property": "FunctionName", "inputType": "select", "id": "handler", "label": "Lambda Handler", "defaultValue": "", "visible": true, "values": [{"value": "", "label": "All"}, {"value": "${app_slug}-customerCreated", "label": "Customer Created <PERSON><PERSON> Handler"}, {"value": "${app_slug}-checkoutRedirect", "label": "Checkout Redirect Web Handler"}, {"value": "${app_slug}-requestForwarder", "label": "Request Forward (Proxy) Web Handler"}, {"value": "${app_slug}-resetPassword", "label": "Reset Password Web Handler"}, {"value": "${app_slug}-showUser", "label": "Show User Web Handler"}, {"value": "${app_slug}-lambdaAuthorizer", "label": "Driver Account Authorizer"}]}], "widgets": [{"height": 6, "width": 24, "y": 15, "x": 0, "type": "log", "properties": {"query": "SOURCE '/aws/lambda/${app_slug}-requestForwarder' | fields @timestamp, @message, @logStream, @log\n| filter @type not in [\"START\", \"END\", \"REPORT\"]\n| filter level not in [\"DEBUG\"]\n| filter customer_id like /{{customerId}}/\n| filter apigw_requestid like /{{apigwRequestId}}/\n| filter sentry_session_id like /{{sentrySessionId}}/\n| display timestamp, level, customer_id, apigw_requestid, message\n| sort @timestamp desc", "region": "${region}", "stacked": false, "title": "Request Forwarder (Proxy) Logs", "view": "table"}}, {"height": 3, "width": 24, "y": 0, "x": 0, "type": "alarm", "properties": {"title": "Alarms", "alarms": ["arn:aws:cloudwatch:${region}:${account_id}:alarm:${service_name} requestForwarder timeout errors - ${environment}", "arn:aws:cloudwatch:${region}:${account_id}:alarm:${service_name} requestForwarder 404 errors - ${environment}", "arn:aws:cloudwatch:${region}:${account_id}:alarm:${service_name} requestForwarder 429 errors - ${environment}", "arn:aws:cloudwatch:${region}:${account_id}:alarm:${service_name} requestForwarder 422 errors - ${environment}", "arn:aws:cloudwatch:${region}:${account_id}:alarm:${service_name} requestForwarder 5xx errors - ${environment}", "arn:aws:cloudwatch:${region}:${account_id}:alarm:${service_name} customer.created event DLQ depth - ${environment}", "arn:aws:cloudwatch:${region}:${account_id}:alarm:${service_name} customer.updated event DLQ depth - ${environment}"]}}, {"height": 6, "width": 24, "y": 21, "x": 0, "type": "log", "properties": {"query": "SOURCE '/aws/lambda/${app_slug}-customerCreated' | parse @message /(?<errorTimestamp>.*?)[\\t](?<errorRequestId>.*?)[\\t]/\n| fields COALESCE(errorTimestamp, timestamp) as TIMES<PERSON>MP, COALESCE(errorRequestId, function_request_id) as REQUEST_ID, toupper(COALESCE(errorType, level)) as L<PERSON><PERSON><PERSON>, COALESCE(errorMessage, message) as MESSAGE\n| display TIMESTAMP, REQUEST_ID, LEVEL, MESSAGE\n| filter @type not in [\"START\", \"END\", \"REPORT\"]\n| filter commerceId like /{{customerId}}/\n| sort @timestamp desc", "region": "${region}", "stacked": false, "title": "Created Customers Queue Worker Logs", "view": "table"}}, {"height": 6, "width": 12, "y": 3, "x": 0, "type": "metric", "properties": {"metrics": [["AWS/Lambda", "Errors", "FunctionName", "${app_slug}-checkoutRedirect", {"region": "${region}", "label": "checkoutRedirect"}], ["AWS/Lambda", "Errors", "FunctionName", "${app_slug}-customerCreated", {"region": "${region}", "label": "customerCreated"}], ["AWS/Lambda", "Errors", "FunctionName", "${app_slug}-lambdaAuthorizer", {"region": "${region}", "label": "lambdaAuthorizer"}], ["AWS/Lambda", "Errors", "FunctionName", "${app_slug}-requestForwarder", {"region": "${region}", "label": "requestF<PERSON><PERSON><PERSON>"}], ["AWS/Lambda", "Errors", "FunctionName", "${app_slug}-resetPassword", {"region": "${region}", "label": "resetPassword"}], ["AWS/Lambda", "Errors", "FunctionName", "${app_slug}-showUser", {"region": "${region}", "label": "showUser"}]], "view": "timeSeries", "stacked": false, "region": "${region}", "period": 300, "stat": "Sum", "legend": {"position": "bottom"}}}, {"height": 6, "width": 12, "y": 9, "x": 0, "type": "metric", "properties": {"metrics": [["AWS/Lambda", "Duration", "FunctionName", "${app_slug}-checkoutRedirect", {"region": "${region}", "label": "checkoutRedirect"}], ["...", "${app_slug}-customerCreated", {"region": "${region}", "label": "customerCreated"}], ["...", "${app_slug}-lambdaAuthorizer", {"region": "${region}", "label": "lambdaAuthorizer"}], ["...", "${app_slug}-requestForwarder", {"region": "${region}", "label": "requestF<PERSON><PERSON><PERSON>"}], ["...", "${app_slug}-resetPassword", {"region": "${region}", "label": "resetPassword"}], ["...", "${app_slug}-showUser", {"region": "${region}", "label": "showUser"}]], "view": "timeSeries", "stacked": false, "region": "${region}", "period": 300, "stat": "Average", "title": "Average Duration"}}, {"height": 6, "width": 12, "y": 9, "x": 12, "type": "metric", "properties": {"metrics": [["AWS/Lambda", "ConcurrentExecutions", "FunctionName", "${app_slug}-checkoutRedirect", {"region": "${region}", "label": "checkoutRedirect"}], ["...", "${app_slug}-customerCreated", {"region": "${region}", "label": "customerCreated"}], ["...", "${app_slug}-lambdaAuthorizer", {"region": "${region}", "label": "lambdaAuthorizer"}], ["...", "${app_slug}-requestForwarder", {"region": "${region}", "label": "requestF<PERSON><PERSON><PERSON>"}], ["...", "${app_slug}-resetPassword", {"region": "${region}", "label": "resetPassword"}], ["...", "${app_slug}-showUser", {"region": "${region}", "label": "showUser"}]], "view": "timeSeries", "stacked": false, "region": "${region}", "title": "Average Concurrent Executions", "period": 300, "stat": "Average"}}, {"height": 6, "width": 12, "y": 3, "x": 12, "type": "metric", "properties": {"metrics": [["${namespace}", "2xx-response-count", {"region": "${region}", "label": "2xx-response-count"}], ["${namespace}", "404-response-count", {"region": "${region}", "label": "404-response-count"}], ["${namespace}", "422-response-count", {"region": "${region}", "label": "422-response-count"}], ["${namespace}", "429-response-count", {"region": "${region}", "label": "429-response-count"}], ["${namespace}", "5xx-response-count", {"region": "${region}", "label": "5xx-response-count"}]], "view": "timeSeries", "stacked": false, "region": "${region}", "stat": "Sum", "period": 300, "title": "requestForwarder (Proxy) HTTP Responses"}}]}