data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

data "template_file" "dashboard" {
  template = file("${path.module}/cloudwatch/dashboard.tpl")
  vars = {
    account_id   = data.aws_caller_identity.current.account_id
    app_slug     = local.app_slug
    environment  = var.environment
    namespace    = "ownership/${local.service_name}"
    region       = data.aws_region.current.name
    service_name = local.service_name
  }
}

resource "aws_cloudwatch_dashboard" "dashboard" {
  count = var.environment == "prod" ? 1 : 0

  dashboard_name = local.app_slug
  dashboard_body = data.template_file.dashboard.rendered
}

resource "aws_cloudwatch_metric_alarm" "timeout" {
  count = var.environment == "prod" ? 1 : 0

  alarm_name          = "${local.service_name} requestForwarder timeout errors - ${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "timeout"
  namespace           = "ownership/${local.service_name}"
  period              = 300
  statistic           = "Sum"
  threshold           = 0
  datapoints_to_alarm = 1

  actions_enabled = "true"
  alarm_actions   = [data.aws_sns_topic.opsgenie.arn]
  ok_actions      = [data.aws_sns_topic.opsgenie.arn]

  tags = local.service_tags
}

resource "aws_cloudwatch_metric_alarm" "five_hundred" {
  count = var.environment == "prod" ? 1 : 0

  alarm_name          = "${local.service_name} requestForwarder 5xx errors - ${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "5xx-response-count"
  namespace           = "ownership/${local.service_name}"
  period              = 300
  statistic           = "Sum"
  threshold           = 0
  datapoints_to_alarm = 1

  actions_enabled = "true"
  alarm_actions   = [data.aws_sns_topic.opsgenie.arn]
  ok_actions      = [data.aws_sns_topic.opsgenie.arn]

  tags = local.service_tags
}

resource "aws_cloudwatch_metric_alarm" "four_two_nine" {
  count = var.environment == "prod" ? 1 : 0

  alarm_name          = "${local.service_name} requestForwarder 429 errors - ${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "429-response-count"
  namespace           = "ownership/${local.service_name}"
  period              = 300
  statistic           = "Sum"
  threshold           = 10
  datapoints_to_alarm = 1

  actions_enabled = "true"
  alarm_actions   = [data.aws_sns_topic.opsgenie.arn]
  ok_actions      = [data.aws_sns_topic.opsgenie.arn]

  tags = local.service_tags
}

resource "aws_cloudwatch_metric_alarm" "four_o_four" {
  count = var.environment == "prod" ? 1 : 0

  alarm_name          = "${local.service_name} requestForwarder 404 errors - ${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "404-response-count"
  namespace           = "ownership/${local.service_name}"
  period              = 300
  statistic           = "Sum"
  threshold           = 10
  datapoints_to_alarm = 1

  actions_enabled = "true"
  alarm_actions   = [data.aws_sns_topic.opsgenie.arn]
  ok_actions      = [data.aws_sns_topic.opsgenie.arn]

  tags = local.service_tags
}

resource "aws_cloudwatch_metric_alarm" "four_two_two" {
  count = var.environment == "prod" ? 1 : 0

  alarm_name          = "${local.service_name} requestForwarder 422 errors - ${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "422-response-count"
  namespace           = "ownership/${local.service_name}"
  period              = 300
  statistic           = "Sum"
  threshold           = 10
  datapoints_to_alarm = 1

  actions_enabled = "true"
  alarm_actions   = [data.aws_sns_topic.opsgenie.arn]
  ok_actions      = [data.aws_sns_topic.opsgenie.arn]

  tags = local.service_tags
}

resource "aws_cloudwatch_log_metric_filter" "timeout" {
  count = var.environment == "prod" ? 1 : 0

  name           = "${local.service_name}-requestForwarder-timeout"
  pattern        = "Task timed out after"
  log_group_name = "/aws/lambda/${local.app_slug}-requestForwarder"

  metric_transformation {
    name          = "timeout"
    namespace     = "ownership/ecommerce-service"
    value         = "1"
    default_value = "0"
    unit          = "Count"
  }
}

resource "aws_cloudwatch_log_metric_filter" "five_hundred" {
  count = var.environment == "prod" ? 1 : 0

  name           = "${local.service_name}-requestForwarder-5xx-response-count"
  pattern        = "\"[handlers/requestForwarder] response (5\""
  log_group_name = "/aws/lambda/${local.app_slug}-requestForwarder"

  metric_transformation {
    name          = "5xx-response-count"
    namespace     = "ownership/ecommerce-service"
    value         = "1"
    default_value = "0"
    unit          = "Count"
  }
}

resource "aws_cloudwatch_log_metric_filter" "four_two_nine" {
  count = var.environment == "prod" ? 1 : 0

  name           = "${local.service_name}-requestForwarder-429-response-count"
  pattern        = "\"[handlers/requestForwarder] response (429)\""
  log_group_name = "/aws/lambda/${local.app_slug}-requestForwarder"

  metric_transformation {
    name          = "429-response-count"
    namespace     = "ownership/ecommerce-service"
    value         = "1"
    default_value = "0"
    unit          = "Count"
  }
}

resource "aws_cloudwatch_log_metric_filter" "four_two_two" {
  count = var.environment == "prod" ? 1 : 0

  name           = "${local.service_name}-requestForwarder-422-response-count"
  pattern        = "\"[handlers/requestForwarder] response (422)\""
  log_group_name = "/aws/lambda/${local.app_slug}-requestForwarder"

  metric_transformation {
    name          = "422-response-count"
    namespace     = "ownership/ecommerce-service"
    value         = "1"
    default_value = "0"
    unit          = "Count"
  }
}

resource "aws_cloudwatch_log_metric_filter" "four_o_four" {
  count = var.environment == "prod" ? 1 : 0

  name           = "${local.service_name}-requestForwarder-404-response-count"
  pattern        = "\"[handlers/requestForwarder] response (404)\""
  log_group_name = "/aws/lambda/${local.app_slug}-requestForwarder"

  metric_transformation {
    name          = "404-response-count"
    namespace     = "ownership/ecommerce-service"
    value         = "1"
    default_value = "0"
    unit          = "Count"
  }
}

resource "aws_cloudwatch_log_metric_filter" "two_hundred" {
  count = var.environment == "prod" ? 1 : 0

  name           = "${local.service_name}-requestForwarder-2xx-response-count"
  pattern        = "\"[handlers/requestForwarder] response (2\""
  log_group_name = "/aws/lambda/${local.app_slug}-requestForwarder"

  metric_transformation {
    name          = "2xx-response-count"
    namespace     = "ownership/ecommerce-service"
    value         = "1"
    default_value = "0"
    unit          = "Count"
  }
}
