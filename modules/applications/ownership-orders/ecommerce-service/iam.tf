resource "aws_iam_role" "github_ci" {
  name = format("github-actions-role-%s", local.app_slug)
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = "arn:aws:iam::959744386191:oidc-provider/token.actions.githubusercontent.com"
        },
        Action = "sts:AssumeRoleWithWebIdentity",
        Condition = {
          StringEquals = {
            "token.actions.githubusercontent.com:aud" = "sts.amazonaws.com"
          },
          StringLike = {
            "token.actions.githubusercontent.com:sub" = "repo:Pod-Point/ecommerce-service:*"
          }
        }
      }
    ]
  })
  managed_policy_arns = [
    aws_iam_policy.deploy.arn
  ]

  tags = local.service_tags
}

moved {
  from = aws_iam_role.github_ci[0]
  to   = aws_iam_role.github_ci
}

resource "aws_iam_policy" "deploy" {
  name = format("deployment-policy-%s", local.app_slug)
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "cloudformation:List*",
          "cloudformation:Get*",
          "cloudformation:PreviewStackUpdate",
          "cloudformation:ValidateTemplate",
        ]
        Effect = "Allow"
        Resource = [
          "*",
        ]
      },
      {
        Action = [
          "ssm:GetParameter",
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:ssm:*:*:parameter/ecommerce-service/*",
        ]
      },
      {
        Action = [
          "cloudformation:CreateStack",
          "cloudformation:CreateUploadBucket",
          "cloudformation:DescribeStackEvents",
          "cloudformation:DescribeStackResource",
          "cloudformation:DescribeStackResources",
          "cloudformation:UpdateStack",
          "cloudformation:DescribeStacks",
          "cloudformation:CreateChangeSet",
          "cloudformation:DeleteChangeSet",
          "cloudformation:DescribeChangeSet",
          "cloudformation:ExecuteChangeSet",
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:cloudformation:*:*:stack/ecommerce-service*/*",
        ]
      },
      {
        Action = [
          "lambda:Get*",
          "lambda:List*",
          "lambda:CreateFunction",
          "lambda:CreateEventSourceMapping",
          "lambda:UpdateEventSourceMapping",
          "lambda:DeleteEventSourceMapping",
          "lambda:PutFunctionConcurrency",
        ]
        Effect = "Allow"
        Resource = [
          "*",
        ]
      },
      {
        Action = [
          "s3:CreateBucket",
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:DeleteObject",
          "s3:DeleteBucket",
          "s3:ListBucketVersions",
          "s3:PutBucketTagging",
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:s3:::ecommerce-service-*-serverlessdeployment*",
        ]
      },
      {
        Action = [
          "lambda:AddPermission",
          "lambda:CreateAlias",
          "lambda:DeleteFunction",
          "lambda:InvokeFunction",
          "lambda:PublishVersion",
          "lambda:RemovePermission",
          "lambda:TagResource",
          "lambda:Update*",
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:lambda:*:*:function:ecommerce-service-*-*",
        ]
      },
      {
        Action = [
          "apigateway:GET",
          "apigateway:POST",
          "apigateway:PUT",
          "apigateway:DELETE",
          "apigateway:PATCH",
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:apigateway:*::/restapis*",
          "arn:aws:apigateway:*::/tags*",
          "arn:aws:apigateway:*::/apis*",
          "arn:aws:apigateway:*::/domainnames*",
        ]
      },
      {
        Action = [
          "iam:PassRole",
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:iam::*:role/*",
        ]
      },
      {
        Action = "iam:*"
        Effect = "Allow"
        Resource = [
          "arn:aws:iam::*:role/ecommerce-service-*-*lambdaRole",
        ]
      },
      {
        Action = [
          "cloudwatch:GetMetricStatistics",
        ]
        Effect = "Allow"
        Resource = [
          "*",
        ]
      },
      {
        Action = [
          "cloudwatch:*",
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:cloudwatch:*:*:alarm:ecommerce-service*",
        ]
      },
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:DeleteLogGroup",
          "logs:PutLogEvents",
          "logs:ListTagsForResource",
          "logs:TagResource",
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:logs:*:*:*",
        ]
      },
      {
        Action = [
          "logs:DescribeLogStreams",
          "logs:DescribeLogGroups",
          "logs:FilterLogEvents",
        ]
        Effect = "Allow"
        Resource = [
          "*",
        ]
      },
      {
        Action = [
          "events:DescribeRule",
          "events:Put*",
          "events:Remove*",
          "events:Delete*",
          "events:Update*",
          "events:Create*",
          "events:DescribeArchive",
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:events:*:*:rule/ecommerce-service-*-*",
          "arn:aws:events:*:*:archive/ecommerce-service-*",
        ]
      },
      {
        Action = [
          "sqs:*",
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:sqs:*:*:EcommerceService*",
          "arn:aws:sqs:*:*:ecommerce-service*",
        ]
      },
      {
        Action = [
          "s3:PutEncryptionConfiguration",
          "s3:GetEncryptionConfiguration",
          "s3:PutBucketPolicy",
        ]
        Effect   = "Allow"
        Resource = "*"
      },
      {
        Action = [
          "s3:PutEncryptionConfiguration",
          "s3:GetEncryptionConfiguration",
          "s3:PutBucketPolicy",
        ]
        Effect   = "Allow"
        Resource = "*"
      },
      {
        Action = [
          "route53:ChangeResourceRecordSets",
          "route53:ListResourceRecordSets",
          "route53:GetHostedZone",
          "route53:GetChange",
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:route53:::hostedzone/ZI1YF8KE9MFAW",
          "arn:aws:route53:::change/*",
        ]
      },
      {
        Action = [
          "dynamodb:*"
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:dynamodb:*:*:table/ecommerce-*"
        ]
      },
      {
        Action = [
          "ec2:DescribeSecurityGroups",
          "ec2:DescribeSubnets",
          "ec2:DescribeVpcs",
        ]
        Effect = "Allow"
        Resource = [
          "*",
        ]
      },
    ]
  })

  tags = local.service_tags
}

moved {
  from = aws_iam_policy.deploy[0]
  to   = aws_iam_policy.deploy
}
// TODO: these might need associating when we do the full account migration
