resource "aws_ssm_parameter" "hosted_zone_id_uk" {
  name  = format("/%s/%s/hosted-zone-id/uk", local.service_name, var.environment)
  type  = "String"
  value = data.aws_route53_zone.uk.zone_id

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.hosted_zone_id_uk[0]
  to   = aws_ssm_parameter.hosted_zone_id_uk
}

resource "aws_ssm_parameter" "acm_certificate_arn_uk" {
  name  = format("/%s/%s/acm-cert-arn/uk", local.service_name, var.environment)
  type  = "String"
  value = data.aws_acm_certificate.latest_wildcard_uk.arn

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.acm_certificate_arn_uk[0]
  to   = aws_ssm_parameter.acm_certificate_arn_uk
}

resource "aws_ssm_parameter" "acm_certificate_arn_ie" {
  name  = format("/%s/%s/acm-cert-arn/ie", local.service_name, var.environment)
  type  = "String"
  value = data.aws_acm_certificate.latest_wildcard_ie.arn

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.acm_certificate_arn_ie[0]
  to   = aws_ssm_parameter.acm_certificate_arn_ie
}

resource "aws_ssm_parameter" "egress_security_group_ids" {
  name  = format("/%s/%s/egress-security-group-ids", local.service_name, var.environment)
  type  = "StringList"
  value = aws_security_group.ecommerce_service_sg.id

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.egress_security_group_ids[0]
  to   = aws_ssm_parameter.egress_security_group_ids
}

resource "aws_ssm_parameter" "egress_subnet_ids" {
  name  = format("/%s/%s/egress-subnet-ids", local.service_name, var.environment)
  type  = "StringList"
  value = join(",", data.aws_subnets.vpc_private_subnets.ids)

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.egress_subnet_ids[0]
  to   = aws_ssm_parameter.egress_subnet_ids
}

resource "aws_ssm_parameter" "app_key" {
  name  = format("/%s/%s/app-key", local.service_name, var.environment)
  type  = "SecureString"
  value = "changeme" // 32 character key
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.app_key[0]
  to   = aws_ssm_parameter.app_key
}

/** Big Commerce **/

resource "aws_ssm_parameter" "bc_store_token" {
  name  = format("/%s/%s/x-token", local.service_name, var.environment)
  type  = "SecureString"
  value = "changeme" // http://login.bigcommerce.com/deep-links/settings/auth/api-accounts
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.bc_store_token[0]
  to   = aws_ssm_parameter.bc_store_token
}

resource "aws_ssm_parameter" "bc_store_hash" {
  name  = format("/%s/%s/store-hash", local.service_name, var.environment)
  type  = "String"
  value = var.store_hash

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.bc_store_hash[0]
  to   = aws_ssm_parameter.bc_store_hash
}

resource "aws_ssm_parameter" "bc_store_url" {
  name  = format("/%s/%s/store-url", local.service_name, var.environment)
  type  = "String"
  value = var.store_url

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.bc_store_url[0]
  to   = aws_ssm_parameter.bc_store_url
}

resource "aws_ssm_parameter" "bc_client_id" {
  name  = format("/%s/%s/client-id", local.service_name, var.environment)
  type  = "SecureString"
  value = "changeme" // http://login.bigcommerce.com/deep-links/settings/auth/api-accounts
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.bc_client_id[0]
  to   = aws_ssm_parameter.bc_client_id
}

resource "aws_ssm_parameter" "bc_client_secret" {
  name  = format("/%s/%s/client-secret", local.service_name, var.environment)
  type  = "SecureString"
  value = "changeme" // http://login.bigcommerce.com/deep-links/settings/auth/api-accounts
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.bc_client_secret[0]
  to   = aws_ssm_parameter.bc_client_secret
}

resource "aws_ssm_parameter" "bc_access_token" {
  name  = format("/%s/%s/access-token", local.service_name, var.environment)
  type  = "SecureString"
  value = "changeme" // http://login.bigcommerce.com/deep-links/settings/auth/api-accounts
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.bc_access_token[0]
  to   = aws_ssm_parameter.bc_access_token
}

resource "aws_ssm_parameter" "bc_store_hash_ie" {
  name  = format("/%s/%s/ie/store-hash", local.service_name, var.environment)
  type  = "String"
  value = var.store_hash_ie

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.bc_store_hash_ie[0]
  to   = aws_ssm_parameter.bc_store_hash_ie
}

resource "aws_ssm_parameter" "bc_store_url_ie" {
  name  = format("/%s/%s/ie/store-url", local.service_name, var.environment)
  type  = "String"
  value = var.store_url_ie

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.bc_store_url_ie[0]
  to   = aws_ssm_parameter.bc_store_url_ie
}

resource "aws_ssm_parameter" "bc_client_id_ie" {
  name  = format("/%s/%s/ie/client-id", local.service_name, var.environment)
  type  = "SecureString"
  value = "changeme" // http://login.bigcommerce.com/deep-links/settings/auth/api-accounts
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.bc_client_id_ie[0]
  to   = aws_ssm_parameter.bc_client_id_ie
}

resource "aws_ssm_parameter" "bc_client_secret_ie" {
  name  = format("/%s/%s/ie/client-secret", local.service_name, var.environment)
  type  = "SecureString"
  value = "changeme" // http://login.bigcommerce.com/deep-links/settings/auth/api-accounts
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.bc_client_secret_ie[0]
  to   = aws_ssm_parameter.bc_client_secret_ie
}

resource "aws_ssm_parameter" "bc_access_token_ie" {
  name  = format("/%s/%s/ie/access-token", local.service_name, var.environment)
  type  = "SecureString"
  value = "changeme" // http://login.bigcommerce.com/deep-links/settings/auth/api-accounts
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.bc_access_token_ie[0]
  to   = aws_ssm_parameter.bc_access_token_ie
}

/** ConfigCat */

resource "aws_ssm_parameter" "config_cat_key" {
  name  = format("/%s/%s/configcat-key", local.service_name, var.environment)
  type  = "SecureString"
  value = "value-managed-in-console"
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.config_cat_key[0]
  to   = aws_ssm_parameter.config_cat_key
}

/** Driver Account API */

resource "aws_ssm_parameter" "driver_account_api_base_url" {
  name  = format("/%s/%s/driver-account-api-base-url", local.service_name, var.environment)
  type  = "String"
  value = "http://${module.driver_account_api_privatelink_client.dns_entry[0].dns_name}:5104"

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.driver_account_api_base_url[0]
  to   = aws_ssm_parameter.driver_account_api_base_url
}

/* Storefront */

resource "aws_ssm_parameter" "storefront_url" {
  name  = format("/%s/%s/storefront-url", local.service_name, var.environment)
  type  = "String"
  value = var.storefront_url

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.storefront_url[0]
  to   = aws_ssm_parameter.storefront_url
}

resource "aws_ssm_parameter" "storefront_url_ie" {
  name  = format("/%s/%s/ie/storefront-url", local.service_name, var.environment)
  type  = "String"
  value = var.storefront_url_ie

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.storefront_url_ie[0]
  to   = aws_ssm_parameter.storefront_url_ie
}

/** Google Identity Platform */

resource "aws_ssm_parameter" "google_identity_provider_project_id" {
  name  = format("/%s/%s/gip-project-id", local.service_name, var.environment)
  type  = "String"
  value = var.google_admin_project_id

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.google_identity_provider_project_id[0]
  to   = aws_ssm_parameter.google_identity_provider_project_id
}

resource "aws_ssm_parameter" "google_identity_provider_private_key" {
  name  = format("/%s/%s/gip-private-key", local.service_name, var.environment)
  type  = "SecureString"
  value = "value-managed-in-console"
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.google_identity_provider_private_key[0]
  to   = aws_ssm_parameter.google_identity_provider_private_key
}

resource "aws_ssm_parameter" "google_identity_provider_client_email" {
  name  = format("/%s/%s/gip-client-email", local.service_name, var.environment)
  type  = "String"
  value = var.google_admin_client_email

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.google_identity_provider_client_email[0]
  to   = aws_ssm_parameter.google_identity_provider_client_email
}

/** Salesforce **/

resource "aws_ssm_parameter" "salesforce_instance_url" {
  name  = format("/%s/%s/salesforce-instance-url", local.service_name, var.environment)
  type  = "String"
  value = var.salesforce_instance_url

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.salesforce_instance_url[0]
  to   = aws_ssm_parameter.salesforce_instance_url
}

resource "aws_ssm_parameter" "salesforce_client_id" {
  name  = format("/%s/%s/salesforce-client-id", local.service_name, var.environment)
  type  = "SecureString"
  value = "changeme" // Secrets Manager: ecommerce-service-{stage}/salesforce-{stage}
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.salesforce_client_id[0]
  to   = aws_ssm_parameter.salesforce_client_id
}

resource "aws_ssm_parameter" "salesforce_username" {
  name  = format("/%s/%s/salesforce-username", local.service_name, var.environment)
  type  = "String"
  value = "changeme" // Secrets Manager: ecommerce-service-{stage}/salesforce-{stage}
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.salesforce_username[0]
  to   = aws_ssm_parameter.salesforce_username
}

resource "aws_ssm_parameter" "salesforce_audience" {
  name  = format("/%s/%s/salesforce-audience", local.service_name, var.environment)
  type  = "String"
  value = var.salesforce_audience

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.salesforce_audience[0]
  to   = aws_ssm_parameter.salesforce_audience
}

resource "aws_ssm_parameter" "salesforce_oauth_private_key" {
  name  = format("/%s/%s/salesforce-oauth-private-key", local.service_name, var.environment)
  type  = "SecureString"
  value = "changeme" // Secrets Manager: ecommerce-service-{stage}/salesforce-{stage}/certificate
  lifecycle {
    ignore_changes = [value]
  }

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.salesforce_oauth_private_key[0]
  to   = aws_ssm_parameter.salesforce_oauth_private_key
}

/** Sentry */

resource "aws_ssm_parameter" "sentry_dsn" {
  name  = format("/%s/%s/sentry-dsn", local.service_name, var.environment)
  type  = "String"
  value = var.sentry_dsn

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.sentry_dsn[0]
  to   = aws_ssm_parameter.sentry_dsn
}

/** DLQ Alarm Topics */

resource "aws_ssm_parameter" "customer_created_dlq_age_alarm_topics" {
  name  = format("/%s/%s/customer-created-dlq-max-age-alarm-topics", local.service_name, var.environment)
  type  = "StringList"
  value = data.aws_sns_topic.opsgenie.arn

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.customer_created_dlq_age_alarm_topics[0]
  to   = aws_ssm_parameter.customer_created_dlq_age_alarm_topics
}

resource "aws_ssm_parameter" "customer_created_dlq_depth_alarm_topics" {
  name  = format("/%s/%s/customer-created-dlq-depth-alarm-topics", local.service_name, var.environment)
  type  = "StringList"
  value = data.aws_sns_topic.opsgenie.arn

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.customer_created_dlq_depth_alarm_topics[0]
  to   = aws_ssm_parameter.customer_created_dlq_depth_alarm_topics
}

resource "aws_ssm_parameter" "customer_updated_dlq_age_alarm_topics" {
  name  = format("/%s/%s/customer-updated-dlq-max-age-alarm-topics", local.service_name, var.environment)
  type  = "StringList"
  value = data.aws_sns_topic.opsgenie.arn

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.customer_updated_dlq_age_alarm_topics[0]
  to   = aws_ssm_parameter.customer_updated_dlq_age_alarm_topics
}

resource "aws_ssm_parameter" "customer_updated_dlq_depth_alarm_topics" {
  name  = format("/%s/%s/customer-updated-dlq-depth-alarm-topics", local.service_name, var.environment)
  type  = "StringList"
  value = data.aws_sns_topic.opsgenie.arn

  tags = local.service_tags
}

moved {
  from = aws_ssm_parameter.customer_updated_dlq_depth_alarm_topics[0]
  to   = aws_ssm_parameter.customer_updated_dlq_depth_alarm_topics
}
