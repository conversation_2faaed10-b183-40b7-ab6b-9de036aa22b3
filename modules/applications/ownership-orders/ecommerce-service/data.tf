data "aws_route53_zone" "uk" {
  provider = aws.global

  name = var.domain_uk
}

data "aws_acm_certificate" "latest_wildcard_uk" {
  domain      = format("*.%s", data.aws_route53_zone.uk.name)
  types       = ["AMAZON_ISSUED"]
  most_recent = true
}

data "aws_acm_certificate" "latest_wildcard_ie" {
  // The .ie domain isn't an AWS hosted zone, so we're fetching this
  // using the domain string variable instead.
  domain      = format("*.%s", var.domain_ie)
  types       = ["AMAZON_ISSUED"]
  most_recent = true
}

data "aws_sns_topic" "opsgenie" {
  name = "opsgenie-cw-ownership-orders"
}

data "aws_subnets" "vpc_private_subnets" {
  filter {
    name   = "vpc-id"
    values = [var.vpc_id]
  }

  filter {
    name   = "tag:Name"
    values = ["pod-point-legacy-private-*"]
  }
}
