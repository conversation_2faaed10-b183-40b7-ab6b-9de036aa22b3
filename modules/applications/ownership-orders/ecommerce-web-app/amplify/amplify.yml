version: 1
frontend:
  phases:
    preBuild:
      commands:
        - git show --oneline -s HEAD | cat
        - npm ci --no-audit
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: public
    files:
      - "**/*"
test:
  phases:
    preTest:
      commands:
        - echo "127.0.0.1       ecommerce-web-app.pod-point.localhost" >> /etc/hosts
        - npm ci --no-audit
        - npm install -g pm2 wait-on --no-audit
        - npm install mocha@10.8.2 mochawesome@7.1.3 mochawesome-merge@4.3.0 mochawesome-report-generator@6.1.1 --no-audit
        - pm2 start amplify.e2e.config.js
        - wait-on -t 10m https://ecommerce-web-app.pod-point.localhost:3000
        # Set COMMIT_INFO variables to send Git specifics to Cypress Cloud when recording
        # https://docs.cypress.io/guides/continuous-integration/introduction#Git-information
        - export COMMIT_INFO_BRANCH="$(git rev-parse HEAD | xargs git name-rev | cut -d' ' -f2 | sed 's/remotes\/origin\///g')"
        - export COMMIT_INFO_MESSAGE="$(git log -1 --pretty=%B)"
        - export COMMIT_INFO_EMAIL="$(git log -1 --pretty=%ae)"
        - export COMMIT_INFO_AUTHOR="$(git log -1 --pretty=%an)"
        - export COMMIT_INFO_SHA="$(git log -1 --pretty=%H)"
        - export COMMIT_INFO_REMOTE="$(git config --get remote.origin.url)"
    test:
      commands:
        - export CYPRESS_BASE_URL=https://ecommerce-web-app.pod-point.localhost:3000
        - export CYPRESS_ENVIRONMENT="${GATSBY_ENVIRONMENT}"
        - if [ "${AWS_BRANCH}" = "main" ] || [ "${AWS_BRANCH}" = "staging" ] || [ "${AWS_BRANCH}" = "dev" ]; then npm run cy:amplify -- --record; else npm run cy:amplify; fi
    postTest:
      commands:
        - npx mochawesome-merge cypress/report/mochawesome-report/mochawesome*.json > cypress/report/mochawesome.json
        - pm2 kill
  artifacts:
    baseDirectory: cypress
    configFilePath: "**/mochawesome.json"
    files:
      - "**/*.png"
      - "**/*.mp4"
      - "out.log"
      - "error.log"
