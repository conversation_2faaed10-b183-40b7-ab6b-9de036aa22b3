customHeaders:
  - pattern: "**"
    headers:
      - key: "Content-Security-Policy"
        value: "default-src 'self' blob: https: data: ws: 'unsafe-eval' 'unsafe-inline'; frame-ancestors 'none'; block-all-mixed-content; upgrade-insecure-requests;"
      - key: "Permissions-Policy"
        value: "autoplay=(), camera=(), encrypted-media=(), fullscreen=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), midi=(), payment=(), sync-xhr=(self), usb=(), browsing-topics=()"
      - key: "Referrer-Policy"
        value: "strict-origin-when-cross-origin"
      - key: "Strict-Transport-Security"
        value: "max-age=31536000"
      - key: "X-Content-Type-Options"
        value: "nosniff"
      - key: "X-Frame-Options"
        value: "DENY"
      - key: "X-XSS-Protection"
        value: "1; mode=block"
