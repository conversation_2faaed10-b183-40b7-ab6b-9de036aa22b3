/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Distribution resources
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "random_string" "random_cf_header" {
  length  = 16
  special = false
  numeric = true
  upper   = true
  lower   = true
}

resource "aws_cloudfront_distribution" "this" {
  provider = aws.global

  enabled = true
  comment = "Cloudfront distribution for ecommerce-web-app-${var.country}."
  aliases = [
    "${var.migrate_domain == null ? var.subdomain : (var.migrate_domain.active ? var.subdomain : "*")}.${var.domain}",
  ]

  origin {
    origin_id   = var.subdomain
    domain_name = "${aws_amplify_branch.this.branch_name}.${aws_amplify_app.this.default_domain}"

    # Used to restrict access via listener if rule condition.
    custom_header {
      name  = "X-CF-Auth-Secret"
      value = random_string.random_cf_header.result
    }

    custom_origin_config {
      http_port              = "80"
      https_port             = "443"
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1", "TLSv1.1", "TLSv1.2", "SSLv3"]
    }
  }

  default_cache_behavior {
    compress                 = true
    allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods           = ["GET", "HEAD"]
    target_origin_id         = var.subdomain
    cache_policy_id          = aws_cloudfront_cache_policy.default.id
    origin_request_policy_id = aws_cloudfront_origin_request_policy.default.id
    viewer_protocol_policy   = "redirect-to-https"

    function_association {
      event_type   = "viewer-response"
      function_arn = aws_cloudfront_function.viewer_country.arn
    }
  }

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  logging_config {
    bucket          = module.cloudfront_log_bucket.s3_bucket_bucket_domain_name
    include_cookies = false
  }

  viewer_certificate {
    cloudfront_default_certificate = false
    acm_certificate_arn            = var.manage_domain ? module.managed_acm[0].acm_certificate_arn : module.external_acm[0].acm_certificate_arn
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }

  tags = {
    "pp:service" = "ecommerce-web-app"
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Cache Monitoring
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "aws_cloudfront_monitoring_subscription" "this" {
  provider = aws.global

  distribution_id = aws_cloudfront_distribution.this.id
  monitoring_subscription {
    realtime_metrics_subscription_config {
      realtime_metrics_subscription_status = "Enabled"
    }
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Caching and Origin Policies
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "aws_cloudfront_cache_policy" "default" {
  name        = "ecommerce-web-app-${var.country}-default"
  default_ttl = 86400
  max_ttl     = 31536000
  min_ttl     = 1

  parameters_in_cache_key_and_forwarded_to_origin {
    enable_accept_encoding_brotli = true
    enable_accept_encoding_gzip   = true

    cookies_config {
      cookie_behavior = "none"
    }

    headers_config {
      header_behavior = "whitelist"
      headers {
        items = [
          "Authorization"
        ]
      }
    }

    query_strings_config {
      query_string_behavior = "all"
    }
  }
}

resource "aws_cloudfront_origin_request_policy" "default" {
  name = "ecommerce-web-app-${var.country}-default"

  cookies_config {
    cookie_behavior = "none"
  }

  headers_config {
    header_behavior = "whitelist"
    headers {
      items = [
        "CloudFront-Viewer-Country"
      ]
    }
  }

  query_strings_config {
    query_string_behavior = "all"
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Functions
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

/**
 * This function is used in order to return the country CloudFront
 * would have guessed for the viewer in the HTTP headers so that
 * the application can suggest a customer to visit the store
 * associated to their country of origin and prevent for example
 * Irish customers to order from the UK store.
 */
resource "aws_cloudfront_function" "viewer_country" {
  name    = "ecommerce-web-app-${var.country}-viewer-country"
  runtime = "cloudfront-js-1.0"
  publish = true
  code    = file("${path.module}/cloudfront/viewer-country.js")
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Logging
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "random_string" "logs_bucket" {
  length  = 10
  special = false
  numeric = false
  upper   = false
  lower   = true
}

module "cloudfront_log_bucket" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "3.13.0"

  bucket = "ecommerce-web-app-${var.country}-cf-accesslogs-${random_string.logs_bucket.result}"

  versioning = {
    enabled = false
  }

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    "pp:service" = "ecommerce-web-app"
  }
}
