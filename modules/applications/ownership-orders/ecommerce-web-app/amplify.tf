/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  AWS Amplify application + main branch configuration
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */
data "local_file" "amplify_yml" {
  filename = "${path.module}/amplify/amplify.yml"
}

data "local_file" "customHttp_yml" {
  filename = "${path.module}/amplify/customHttp.yml"
}

resource "aws_amplify_app" "this" {
  access_token                = var.access_token
  basic_auth_credentials      = base64encode(local.basic_auth_credentials)
  enable_auto_branch_creation = false
  enable_basic_auth           = var.enable_basic_auth
  enable_branch_auto_build    = true
  enable_branch_auto_deletion = true
  iam_service_role_arn        = aws_iam_role.amplify_role.arn
  name                        = "ecommerce-web-app-${var.country}"
  repository                  = "https://github.com/Pod-Point/ecommerce-web-app"

  auto_branch_creation_patterns = [
    "*",
    "*/**"
  ]

  environment_variables = merge(
    local.environment_variables,
    jsondecode(data.aws_secretsmanager_secret_version.environment_variables.secret_string)
  )

  build_spec = <<-EOT
    ${data.local_file.amplify_yml.content}
  EOT

  custom_headers = <<-EOT
    ${data.local_file.customHttp_yml.content}
  EOT

  custom_rule {
    source = "/<*>"
    status = "404-200"
    target = "/404/"
  }

  lifecycle {
    ignore_changes = [
      access_token,
    ]
  }

  tags = {
    "pp:service" = "ecommerce-web-app"
  }
}

resource "aws_amplify_branch" "this" {
  app_id                      = aws_amplify_app.this.id
  branch_name                 = var.branch
  enable_auto_build           = var.enable_auto_build
  enable_notification         = true
  enable_pull_request_preview = var.enable_pull_request_preview
  framework                   = "Gatsby"
  stage                       = "PRODUCTION"

  tags = {
    "pp:service" = "ecommerce-web-app"
  }
}
