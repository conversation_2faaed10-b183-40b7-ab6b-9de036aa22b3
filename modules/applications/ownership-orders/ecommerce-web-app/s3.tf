/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  AWS S3 bucket used to store GitHub CI pipeline assets such
 *  as code coverage reports for example.
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

module "ci_pipeline_assets_bucket" {
  count = var.environment == "dev" && var.country == "uk" ? 1 : 0

  source = "terraform-aws-modules/s3-bucket/aws"

  bucket = "ecommerce-web-app-ci-assets"

  tags = {
    "pp:service" = "ecommerce-web-app"
  }
}

data "aws_iam_policy_document" "ci_pipeline_assets_bucket_policy_document" {
  count = var.environment == "dev" && var.country == "uk" ? 1 : 0

  statement {
    principals {
      type = "AWS"
      identifiers = [
        aws_iam_role.gha_role[0].arn
      ]
    }

    actions = [
      "s3:ListBucket",
      "s3:GetBucketAcl",
      "s3:GetBucketPolicy"
    ]

    resources = [
      module.ci_pipeline_assets_bucket[0].s3_bucket_arn,
    ]
  }

  statement {
    principals {
      type = "AWS"
      identifiers = [
        aws_iam_role.gha_role[0].arn
      ]
    }

    actions = [
      "s3:PutObject",
      "s3:GetObject",
    ]

    resources = [
      format("%s/*", module.ci_pipeline_assets_bucket[0].s3_bucket_arn)
    ]
  }
}

resource "aws_s3_bucket_policy" "ci_pipeline_assets_bucket_policy" {
  count = var.environment == "dev" && var.country == "uk" ? 1 : 0

  bucket = module.ci_pipeline_assets_bucket[0].s3_bucket_id
  policy = data.aws_iam_policy_document.ci_pipeline_assets_bucket_policy_document[0].json
}
