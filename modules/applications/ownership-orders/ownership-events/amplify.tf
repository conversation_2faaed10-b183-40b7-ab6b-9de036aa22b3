resource "aws_amplify_app" "this" {
  name                        = local.identifier
  access_token                = var.access_token
  repository                  = "https://github.com/Pod-Point/ownership-events"
  build_spec                  = file("${path.module}/buildspec.yml")
  enable_basic_auth           = true
  basic_auth_credentials      = base64encode(local.basic_auth_credentials)
  enable_branch_auto_build    = true
  enable_auto_branch_creation = false
  enable_branch_auto_deletion = false
  iam_service_role_arn        = aws_iam_role.amplify_role.arn
  auto_branch_creation_patterns = [
    "*",
    "*/**"
  ]
  custom_rule {
    source = "/<*>"
    target = "/index.html"
    status = "404-200"
  }
  tags = local.tags

  lifecycle {
    ignore_changes = [access_token]
  }
}

resource "aws_amplify_branch" "main" {
  app_id                      = aws_amplify_app.this.id
  branch_name                 = "main"
  stage                       = "PRODUCTION"
  enable_auto_build           = true
  enable_notification         = false
  enable_pull_request_preview = false
  tags                        = local.tags
}
