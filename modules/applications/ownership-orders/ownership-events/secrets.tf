resource "aws_secretsmanager_secret" "basic_auth_password" {
  name                    = "aws/amplify/${local.identifier}/basic-auth-password"
  description             = "The password used to protect the ${local.identifier} Amplify application with basic HTTP auth."
  kms_key_id              = aws_kms_key.ownership-events.id
  recovery_window_in_days = 7

  tags = local.tags
}

resource "aws_secretsmanager_secret_version" "basic_auth_password" {
  secret_id     = aws_secretsmanager_secret.basic_auth_password.id
  secret_string = "placeholder"

  lifecycle {
    ignore_changes = [secret_string]
  }
}

data "aws_secretsmanager_secret_version" "basic_auth_password" {
  secret_id  = aws_secretsmanager_secret.basic_auth_password.id
  depends_on = [aws_secretsmanager_secret_version.basic_auth_password]
}
