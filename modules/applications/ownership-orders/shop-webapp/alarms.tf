resource "aws_cloudwatch_metric_alarm" "unhealthy_hosts_alarm" {
  alarm_name        = format("%s_unhealthy_hosts", local.identifier)
  alarm_description = "Triggers when the target group has unhealthy hosts."

  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  datapoints_to_alarm = 2
  period              = 120

  metric_name        = "UnHealthyHostCount"
  namespace          = "AWS/ApplicationELB"
  statistic          = "Maximum"
  threshold          = 0
  actions_enabled    = true
  alarm_actions      = [data.aws_sns_topic.opsgenie.arn]
  ok_actions         = [data.aws_sns_topic.opsgenie.arn]
  treat_missing_data = "missing"

  dimensions = {
    TargetGroup  = module.alb.target_group_arn_suffix[0]
    LoadBalancer = module.alb.load_balancer_arn_suffix
  }
}

resource "aws_cloudwatch_metric_alarm" "httpcode_target_4xx_count" {

  alarm_name        = format("%s_spike_in_4xx_errors", local.identifier)
  alarm_description = "A Spike in the number of 4xx HTTP response codes generated by the targets. This does not include any response codes generated by the load balancer."

  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  datapoints_to_alarm = 1
  period              = 120

  metric_name        = "HTTPCode_Target_4XX_Count"
  namespace          = "AWS/ApplicationELB"
  statistic          = "Sum"
  threshold          = 10 // to refine once we have a baseline
  actions_enabled    = true
  alarm_actions      = [data.aws_sns_topic.opsgenie.arn]
  ok_actions         = [data.aws_sns_topic.opsgenie.arn]
  treat_missing_data = "notBreaching"

  dimensions = {
    TargetGroup  = module.alb.target_group_arn_suffix[0]
    LoadBalancer = module.alb.load_balancer_arn_suffix
  }
}

resource "aws_cloudwatch_metric_alarm" "httpcode_target_5xx_count" {
  alarm_name        = format("%s_spike_in_5xx_errors", local.identifier)
  alarm_description = "A Spike in the number of 5xx HTTP response codes generated by the targets. This does not include any response codes generated by the load balancer."

  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  datapoints_to_alarm = 1
  period              = 120

  metric_name        = "HTTPCode_Target_5XX_Count"
  namespace          = "AWS/ApplicationELB"
  statistic          = "Sum"
  threshold          = 10 // to refine once we have a baseline
  actions_enabled    = true
  alarm_actions      = [data.aws_sns_topic.opsgenie.arn]
  ok_actions         = [data.aws_sns_topic.opsgenie.arn]
  treat_missing_data = "notBreaching"

  dimensions = {
    TargetGroup  = module.alb.target_group_arn_suffix[0]
    LoadBalancer = module.alb.load_balancer_arn_suffix
  }
}

resource "aws_cloudwatch_metric_alarm" "target_latency_high" {
  alarm_name        = format("%s_high_p99_latency", local.identifier)
  alarm_description = "A spike in the target latency (p99) of the target group."

  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 3
  datapoints_to_alarm = 3
  period              = 60

  metric_name        = "TargetResponseTime"
  namespace          = "AWS/ApplicationELB"
  extended_statistic = "p99"
  threshold          = 2 // to refine once we have a baseline
  actions_enabled    = true
  alarm_actions      = [data.aws_sns_topic.opsgenie.arn]
  ok_actions         = [data.aws_sns_topic.opsgenie.arn]
  treat_missing_data = "notBreaching"

  dimensions = {
    TargetGroup  = module.alb.target_group_arn_suffix[0]
    LoadBalancer = module.alb.load_balancer_arn_suffix
  }
}
