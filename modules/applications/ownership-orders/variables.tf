variable "environment" {
  type     = string
  nullable = false
}

variable "vpc_cidr_block" {
  type     = string
  nullable = false
}

variable "bigcommerce_store_hash" {
  type     = string
  nullable = false
}

variable "bigcommerce_channel_id" {
  type     = string
  nullable = false
}

variable "bigcommerce_secondary_channel_id" {
  type     = string
  nullable = false
}

variable "bigcommerce_payment_url" {
  type     = string
  nullable = false
}

variable "axle_base_url" {
  type     = string
  nullable = false
}

variable "gtm_id" {
  type     = string
  nullable = false
  default  = "GTM-TC653C3"
}

variable "tesco_oidc_auth_url" {
  type = string
}

variable "tesco_oidc_token_endpoint" {
  type = string
}

variable "shop_webapp_subdomain" {
  type     = string
  nullable = false
}

variable "single_nat_gateway" {
  type     = bool
  default  = true
  nullable = false
}

variable "driver_account_api_vpc_endpoint" {
  type     = string
  nullable = false
}

variable "hardware_shop_url" {
  type     = string
  nullable = false
}

variable "partner_portal_api_vpc_endpoint" {
  type     = string
  nullable = false
}
