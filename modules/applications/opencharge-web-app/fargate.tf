module "ecs_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "11.1.1"

  identifier = local.identifier
  repo_names = ["opencharge-web-app"]
}

module "request_handler" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "11.1.1"

  cluster_arn                                 = module.ecs_cluster.arn
  cluster_name                                = module.ecs_cluster.name
  attach_custom_ecs_task_execution_iam_policy = true
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.custom_ecs_task_container_policy.json
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  identifier                                  = format("%s-request-handler", local.identifier)
  pipeline_role_name                          = module.ecs_cluster.github_role_name
  service_type                                = "rolling"
  subnet_ids                                  = var.ecs_private_subnets_ids
  vpc_id                                      = var.vpc_id
  cpu                                         = var.request_handler_cpu
  memory                                      = var.request_handler_memory
  scaling_min_capacity                        = var.request_handler_scaling_min_capacity
  scaling_max_capacity                        = var.request_handler_scaling_max_capacity

  container_definitions = jsonencode([
    {
      name : format("%s-request-handler", local.identifier),
      image : "TERRAFORM_IMAGE_PLACEHOLDER",
      essential : true,
      networkMode : "awsvpc",
      portMappings : [
        {
          containerPort : tonumber(local.container_port),
          hostPort : tonumber(local.container_port)
        }
      ],
      linuxParameters : {
        initProcessEnabled : false
      },
      healthCheck : {
        command : ["CMD-SHELL", "curl -f http://localhost/health-check || exit 1"]
      },
      logConfiguration : {
        logDriver : "awslogs",
        options : {
          awslogs-group : "/ecs/${format("%s-request-handler", local.identifier)}",
          awslogs-region : data.aws_region.current.name,
          awslogs-stream-prefix : "ecs"
        }
      }
      environment : local.container_environment,
      secrets : local.container_secrets
    }
  ])

  load_balancing_configuration = [
    {
      target_group_arn = module.alb.target_group_arns[0]
      container_name   = format("%s-request-handler", local.identifier)
      container_port   = local.container_port
    }
  ]

  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }]
  }]
}
