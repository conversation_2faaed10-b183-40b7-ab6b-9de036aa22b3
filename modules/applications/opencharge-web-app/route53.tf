resource "aws_route53_record" "ecs" {
  zone_id         = var.pod_point_com_hosted_zone
  name            = var.route53_record_name
  type            = "A"
  allow_overwrite = false

  alias {
    name                   = aws_cloudfront_distribution.this.domain_name
    zone_id                = aws_cloudfront_distribution.this.hosted_zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "legacy_domains" {
  for_each = toset(var.legacy_redirect_domains)

  name            = each.key
  type            = "A"
  zone_id         = var.pod_point_com_hosted_zone
  allow_overwrite = false

  alias {
    name                   = aws_cloudfront_distribution.this.domain_name
    zone_id                = aws_cloudfront_distribution.this.hosted_zone_id
    evaluate_target_health = true
  }
}
