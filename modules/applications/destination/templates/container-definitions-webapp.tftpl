[
  {
    "environment": ${jsonencode(environment)},
    "essential": true,
    "image": "TERRAFORM_IMAGE_PLACEHOLDER",
    "cpu": 0,
    "mountPoints": [],
    "volumesFrom": [],
    "linuxParameters": {
      "initProcessEnabled": false
    },
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${name}",
        "awslogs-region": "${region}",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "name": "${name}",
    "networkMode": "awsvpc",
    "portMappings": [
      {
        "containerPort": ${port},
        "hostPort": ${port}
      }
    ],
    "secrets": ${jsonencode(secrets)},
    "healthCheck": {
      "command": ["CMD-SHELL", "wget -qO - http://localhost:${port} || exit 1"],
      "interval": 30,
      "retries": 3,
      "startPeriod": 60,
      "timeout": 5
    }
  }
]
