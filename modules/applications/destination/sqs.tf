resource "aws_sqs_queue" "data_platform_sqs_charge_events" {
  tags              = local.tags
  name              = local.charge_events_sqs_name
  kms_master_key_id = module.data_platform_kms_sqs.arn
}

data "aws_iam_policy_document" "charge_notifications_events_allowed" {
  statement {
    sid     = "AllowChargeNotificationsTopicToSendToChargeEventsQ"
    actions = ["SQS:SendMessage"]

    resources = [aws_sqs_queue.data_platform_sqs_charge_events.arn]

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    condition {
      test     = "ArnEquals"
      variable = "aws:SourceArn"

      values = [
        local.data_platform_charge_notifications_sns_topic_arn,
      ]
    }
  }

  statement {
    sid = "AllowSubscriptionToChargeNotificationsSnsTopic"
    actions = [
      "SNS:Subscribe",
    ]
    effect = "Allow"
    resources = [
      local.data_platform_charge_notifications_sns_topic_arn,
    ]
  }
}

resource "aws_sqs_queue_policy" "data_platform_sqs_charge_events" {
  count = var.data_platform_charge_notifications_sns_topic_subscription_enabled ? 1 : 0

  queue_url = aws_sqs_queue.data_platform_sqs_charge_events.id
  policy    = data.aws_iam_policy_document.charge_notifications_events_allowed.json
}

resource "aws_sqs_queue" "data_platform_sqs_charge_events_dlq" {
  tags                      = local.tags
  name                      = format("%s-dlq", local.charge_events_sqs_name)
  kms_master_key_id         = module.data_platform_kms_sqs.arn
  message_retention_seconds = 604800 // 7 days

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.data_platform_sqs_charge_events.arn]
  })
}

resource "aws_sqs_queue_redrive_policy" "data_platform_sqs_charge_events_dlq_redrive_policy" {
  queue_url = aws_sqs_queue.data_platform_sqs_charge_events.id
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.data_platform_sqs_charge_events_dlq.arn
    maxReceiveCount     = 3
  })
}

resource "aws_sqs_queue" "migrate_users_events" {
  tags                      = local.tags
  name                      = local.migrate_users_sqs_name
  receive_wait_time_seconds = 20
}

resource "aws_sqs_queue_policy" "migrate_users_events_queue_policy" {
  queue_url = aws_sqs_queue.migrate_users_events.id
  policy    = data.aws_iam_policy_document.migrate_users_events_queue_policy_document.json
}

data "aws_iam_policy_document" "migrate_users_events_queue_policy_document" {
  statement {
    effect  = "Allow"
    actions = ["SQS:*"]
    principals {
      type        = "Service"
      identifiers = ["sns.amazonaws.com"]
    }
    resources = [aws_sqs_queue.migrate_users_events.arn]
  }
}

resource "aws_sqs_queue" "migrate_users_events_dlq" {
  tags                      = local.tags
  name                      = format("%s-dlq", local.migrate_users_sqs_name)
  message_retention_seconds = 604800 // 7 days

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.migrate_users_events.arn]
  })
}

resource "aws_sqs_queue_redrive_policy" "migrate_users_events_dlq_redrive_policy" {
  queue_url = aws_sqs_queue.migrate_users_events.id
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.migrate_users_events_dlq.arn
    maxReceiveCount     = 3
  })
}

resource "aws_sqs_queue" "data_platform_sqs_charge_commands" {
  tags              = local.tags
  name              = local.charge_commands_sqs_name
  kms_master_key_id = module.data_platform_kms_sqs.arn
}

resource "aws_sqs_queue" "data_platform_sqs_charge_commands_dlq" {
  tags                      = local.tags
  name                      = format("%s-dlq", local.charge_commands_sqs_name)
  kms_master_key_id         = module.data_platform_kms_sqs.arn
  message_retention_seconds = 604800 // 7 days

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.data_platform_sqs_charge_commands.arn]
  })
}

resource "aws_sqs_queue_redrive_policy" "data_platform_sqs_charge_commands_dlq_redrive_policy" {
  queue_url = aws_sqs_queue.data_platform_sqs_charge_commands.id
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.data_platform_sqs_charge_commands_dlq.arn
    maxReceiveCount     = 3
  })
}

resource "aws_sqs_queue" "data_platform_charge_completed_test_target" {
  count = var.environment != "prod" ? 1 : 0

  tags = local.tags
  name = local.charge_completed_test_target_sqs_name
}

resource "aws_sqs_queue_policy" "data_platform_charge_completed_test_target_queue_policy" {
  count = var.environment != "prod" ? 1 : 0

  queue_url = aws_sqs_queue.data_platform_charge_completed_test_target[0].id
  policy    = data.aws_iam_policy_document.data_platform_charge_completed_test_target_policy[0].json
}


data "aws_iam_policy_document" "data_platform_charge_completed_test_target_policy" {
  count = var.environment != "prod" ? 1 : 0
  statement {
    sid    = "PermitEventBridgeSendMessage"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }

    actions   = ["sqs:SendMessage"]
    resources = [aws_sqs_queue.data_platform_charge_completed_test_target[0].arn]

    condition {
      test     = "ArnEquals"
      variable = "aws:SourceArn"
      values = [
        aws_cloudwatch_event_rule.send-completed-event-to-test-queue[0].arn,
      ]
    }
  }
}

resource "aws_sqs_queue" "expense_reports_to_generate" {
  tags = local.tags
  name = "mobile-api-expense-reports-to-generate"
}

resource "aws_sqs_queue" "expense_reports_to_generate_dlq" {
  tags                      = local.tags
  name                      = "mobile-api-expense-reports-to-generate-dlq"
  message_retention_seconds = 604800 // 7 days

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.expense_reports_to_generate.arn]
  })
}

resource "aws_sqs_queue_redrive_policy" "expense_reports_to_generate_dlq_redrive_policy" {
  queue_url = aws_sqs_queue.expense_reports_to_generate.id

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.expense_reports_to_generate_dlq.arn
    maxReceiveCount     = 4
  })
}

resource "aws_sqs_queue" "billing_events" {
  tags = local.tags
  name = "billing-api-billing-events"
}

resource "aws_sqs_queue" "billing_events_dlq" {
  tags                      = local.tags
  name                      = "billing-api-billing-events-dlq"
  message_retention_seconds = 604800 // 7 days

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.billing_events.arn]
  })
}

resource "aws_sqs_queue_redrive_policy" "billing_events_dlq_redrive_policy" {
  queue_url = aws_sqs_queue.billing_events.id

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.billing_events_dlq.arn
    maxReceiveCount     = 4
  })
}
