{"rules": [{"rule-type": "selection", "rule-id": "1", "rule-name": "1", "object-locator": {"schema-name": "podpoint", "table-name": "pod_addresses"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "2", "rule-name": "2", "object-locator": {"schema-name": "podpoint", "table-name": "pod_locations"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "3", "rule-name": "3", "object-locator": {"schema-name": "podpoint", "table-name": "pod_units"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "4", "rule-name": "4", "object-locator": {"schema-name": "podpoint", "table-name": "charges"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "5", "rule-name": "5", "object-locator": {"schema-name": "podpoint", "table-name": "billing_events"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "6", "rule-name": "6", "object-locator": {"schema-name": "podpoint", "table-name": "users"}, "rule-action": "include"}, {"rule-type": "selection", "rule-id": "7", "rule-name": "7", "object-locator": {"schema-name": "podpoint", "table-name": "groups"}, "rule-action": "include"}]}