locals {
  driver_account_app_identifier                    = "driver-account-webapp"
  driver_account_app_pod_point_com_r53_record_name = var.environment == "prod" ? format("identity.%s", local.pod_point_com_domain_name) : format("identity-%s.%s", var.environment, local.pod_point_com_domain_name)
  driver_account_app_podenergy_com_r53_record_name = var.environment == "prod" ? format("identity.%s", local.podenergy_com_domain_name) : format("identity-%s.%s", var.environment, local.podenergy_com_domain_name)
  driver_account_app_internal_toggle               = var.environment == "dev"
  driver_account_app_is_prod                       = var.environment == "prod"
}

resource "aws_route53_record" "driver_account_webapp_load_balancer" {
  provider = aws.pod-point-eu-west-1

  zone_id         = local.pod_point_com_hosted_zone
  type            = "A"
  name            = local.driver_account_app_pod_point_com_r53_record_name
  allow_overwrite = false

  alias {
    name                   = local.driver_account_app_internal_toggle == false ? aws_cloudfront_distribution.driver_account_webapp[0].domain_name : module.driver_account_webapp_load_balancer.load_balancer_dns_name
    zone_id                = local.driver_account_app_internal_toggle == false ? aws_cloudfront_distribution.driver_account_webapp[0].hosted_zone_id : module.driver_account_webapp_load_balancer.load_balancer_zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "driver_account_webapp_load_balancer_podenergy_com" {
  provider = aws.pod-point-eu-west-1

  zone_id         = local.podenergy_com_hosted_zone
  type            = "A"
  name            = local.driver_account_app_podenergy_com_r53_record_name
  allow_overwrite = false

  alias {
    name                   = local.driver_account_app_internal_toggle == false ? aws_cloudfront_distribution.driver_account_webapp[0].domain_name : module.driver_account_webapp_load_balancer.load_balancer_dns_name
    zone_id                = local.driver_account_app_internal_toggle == false ? aws_cloudfront_distribution.driver_account_webapp[0].hosted_zone_id : module.driver_account_webapp_load_balancer.load_balancer_zone_id
    evaluate_target_health = true
  }
}

module "driver_account_webapp_load_balancer" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws"
  version = "3.0.0"
  tags    = local.tags

  access_logs = {
    create_bucket_policy = true
    enable_logs          = true
  }

  enable_internal_lb = local.driver_account_app_internal_toggle

  http_tcp_listeners = [
    {
      port        = 80
      protocol    = "HTTP"
      action_type = "redirect"
      redirect = {
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  ]

  https_listeners = concat(local.driver_account_app_internal_toggle == false ? [
    {
      port            = 443
      protocol        = "HTTPS"
      certificate_arn = tostring(module.pod_point_and_energy_acm.acm_certificate_arn)
      action_type     = "fixed-response"
      fixed_response = {
        content_type = "text/plain"
        message_body = "Access denied"
        status_code  = "403"
      }
    }
    ] : [], local.driver_account_app_internal_toggle ? [
    {
      port               = 443
      protocol           = "HTTPS"
      certificate_arn    = tostring(module.pod_point_and_energy_acm.acm_certificate_arn)
      target_group_index = 0
    }
  ] : [])

  https_listener_rules = local.driver_account_app_internal_toggle == false ? [
    {
      https_listener_index = 0
      priority             = 1
      actions = [{
        type               = "forward"
        target_group_index = 0
      }]

      conditions = [{
        http_headers = [{
          http_header_name = "X-CF-Auth-Secret"
          values           = [one(random_string.random_cf_header_val_driver_account_webapp[*].result)]
        }]
      }]
    }
  ] : []

  load_balancer_name    = local.driver_account_app_identifier
  load_balancer_subnets = local.driver_account_app_internal_toggle ? module.vpc.private_subnets_ids : module.vpc.public_subnets_ids
  load_balancer_type    = "application"

  security_group_name        = format("%s-lb", local.driver_account_app_identifier)
  security_group_description = format("Security Group for the %s load balancer.", local.driver_account_app_identifier)
  security_group_ingress_rules = {
    "port_http_all" = {
      description = "HTTP permitted from the Internet."
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }

    "port_https_all" = {
      description = "HTTPS permitted from the Internet."
      from_port   = 443
      to_port     = 443
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }

  target_groups = [
    {
      backend_port         = local.driver_account_webapp_port
      backend_protocol     = "HTTP"
      deregistration_delay = 60
      name                 = local.driver_account_app_identifier
      target_type          = "ip"
      vpc_id               = module.vpc.vpc_id

      health_check = {
        enabled             = true
        healthy_threshold   = 2
        interval            = 5
        matcher             = "200"
        path                = "/api/health"
        port                = "traffic-port"
        protocol            = "HTTP"
        timeout             = 3
        unhealthy_threshold = 2
      }
    }
  ]

  vpc_id = module.vpc.vpc_id
}

resource "aws_service_discovery_service" "driver_account_webapp" {
  tags = local.tags
  name = "driver-account-webapp"
  dns_config {
    namespace_id = aws_service_discovery_private_dns_namespace.this.id
    dns_records {
      ttl  = 0
      type = "A"
    }
  }
}

module "driver_account_webapp" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "11.3.1"
  tags    = local.tags

  service_type                                = "rolling"
  cluster_arn                                 = module.cluster.arn
  cluster_name                                = module.cluster.name
  cpu                                         = local.driver_account_app_is_prod ? "512" : "256"
  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = jsonencode(local.ecs_task_execution_custom_policy)
  identifier                                  = local.driver_account_app_identifier
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = jsonencode(local.ecs_task_custom_policy_driver_account_webapp)
  memory                                      = local.driver_account_app_is_prod ? "1024" : "512"
  pipeline_role_name                          = module.cluster.github_role_name
  load_balancing_configuration = [
    {
      target_group_arn = module.driver_account_webapp_load_balancer.target_group_arns[0]
      container_name   = local.driver_account_app_identifier
      container_port   = local.driver_account_webapp_port
    }
  ]
  service_registry = {
    registry_arn : aws_service_discovery_service.driver_account_webapp.arn
  }
  subnet_ids = module.vpc.private_subnets_ids
  vpc_id     = module.vpc.vpc_id

  container_definitions = templatefile("${path.module}/templates/container-definitions.tftpl", {
    "build_account_id" : var.build_account_id
    "environment" : var.environment
    "name" : local.driver_account_app_identifier
    "region" : data.aws_region.current.name
    "port" : local.driver_account_webapp_port
  })

  scheduled_scaling = var.environment != "prod" ? {
    scale_in_schedules = [{
      schedule     = "cron(0 18 ? * MON-FRI *)"
      min_capacity = 0
      max_capacity = 0
    }]
    scale_out_schedules = [{
      schedule     = "cron(0 8 ? * MON-FRI *)"
      min_capacity = 2
      max_capacity = 2
    }]
  } : null
}

resource "aws_security_group_rule" "driver_account_webapp_ingress" {
  type                     = "ingress"
  from_port                = local.driver_account_webapp_port
  to_port                  = local.driver_account_webapp_port
  protocol                 = "TCP"
  description              = "Access permitted from load balancer."
  security_group_id        = module.driver_account_webapp.security_group_id
  source_security_group_id = module.driver_account_webapp_load_balancer.security_group_id
}

resource "aws_security_group_rule" "driver_account_webapp_egress" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  description       = "Permit all egress traffic."
  security_group_id = module.driver_account_webapp.security_group_id
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
}

resource "aws_secretsmanager_secret" "driver_account_webapp" {
  tags                    = local.tags
  name                    = local.driver_account_app_identifier
  description             = "Secrets for the driver-account-webapp service."
  kms_key_id              = module.kms.arn
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "driver_account_webapp" {
  secret_id = aws_secretsmanager_secret.driver_account_webapp.id
  secret_string = jsonencode({
    firebase_private_key = var.mobile_firebase_private_key
  })
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Distribution Resources
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "random_string" "random_cf_header_val_driver_account_webapp" {
  count = local.driver_account_app_internal_toggle == false ? 1 : 0

  length  = 16
  special = false
  numeric = true
  upper   = true
  lower   = true
}

resource "aws_cloudfront_distribution" "driver_account_webapp" {
  tags  = local.tags
  count = local.driver_account_app_internal_toggle == false ? 1 : 0

  provider = aws.us-east-1

  enabled    = true
  comment    = "Cloudfront distribution for the ${local.driver_account_app_identifier} service."
  aliases    = [local.driver_account_app_pod_point_com_r53_record_name, local.driver_account_app_podenergy_com_r53_record_name]
  web_acl_id = var.enable_waf_on_driver_account_webapp ? one(aws_wafv2_web_acl.driver_account_webapp_cloudfront[*].arn) : null

  origin {
    origin_id   = local.driver_account_app_identifier
    domain_name = module.driver_account_webapp_load_balancer.load_balancer_dns_name

    custom_header {
      name  = "X-CF-Auth-Secret"
      value = random_string.random_cf_header_val_driver_account_webapp[0].result
    }

    custom_origin_config {
      http_port              = "80"
      https_port             = "443"
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1", "TLSv1.1", "TLSv1.2", "SSLv3"]
    }
  }

  default_cache_behavior {
    compress                 = true
    allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods           = ["GET", "HEAD"]
    target_origin_id         = local.driver_account_app_identifier
    cache_policy_id          = data.aws_cloudfront_cache_policy.driver_account_webapp_no_cache_aws_managed_policy[0].id
    origin_request_policy_id = aws_cloudfront_origin_request_policy.driver_account_webapp[0].id
    viewer_protocol_policy   = "redirect-to-https"
  }

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }


  dynamic "logging_config" {
    for_each = var.driver_account_webapp_cloudfront_enable_logging ? [1] : []

    content {
      bucket          = one(module.driver_account_webapp_cloudfront_log_bucket[*].s3_bucket_bucket_domain_name)
      include_cookies = false
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = false
    acm_certificate_arn            = module.pod_point_and_energy_cf_acm.acm_certificate_arn
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront cache monitoring
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_cloudfront_monitoring_subscription" "driver_account_webapp" {
  count = var.driver_account_webapp_cloudfront_realtime_metrics == "Enabled" && local.driver_account_app_identifier == false ? 1 : 0

  provider = aws.us-east-1

  distribution_id = aws_cloudfront_distribution.driver_account_webapp[0].id
  monitoring_subscription {
    realtime_metrics_subscription_config {
      realtime_metrics_subscription_status = "Enabled"
    }
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Cache Policy Resources
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

data "aws_cloudfront_cache_policy" "driver_account_webapp_no_cache_aws_managed_policy" {
  count = local.driver_account_app_internal_toggle == false ? 1 : 0
  name  = "Managed-CachingDisabled"
}

resource "aws_cloudfront_origin_request_policy" "driver_account_webapp" {
  count = local.driver_account_app_internal_toggle == false ? 1 : 0

  provider = aws.us-east-1

  name = local.driver_account_app_identifier

  cookies_config {
    cookie_behavior = "all"
  }

  headers_config {
    header_behavior = "allViewerAndWhitelistCloudFront"
    headers {
      items = [
        "CloudFront-Viewer-Address",
        "CloudFront-Viewer-Country-Region",
        "CloudFront-Viewer-JA3-Fingerprint",
      ]
    }
  }
  query_strings_config {
    query_string_behavior = "all"
  }
}


/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Logging
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

module "driver_account_webapp_cloudfront_log_bucket" {
  count = local.driver_account_app_internal_toggle == false && var.driver_account_webapp_cloudfront_enable_logging ? 1 : 0

  providers = {
    aws = aws.us-east-1
  }

  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.1.0"
  tags    = local.tags

  bucket = var.environment == "prod" ? format("%s-cf-accesslogs", local.driver_account_app_identifier) : format("%s-cf-accesslogs-%s", local.driver_account_app_identifier, random_string.driver_account_webapp_cloudfront_logs_bucket[0].result)

  versioning = {
    enabled = false
  }

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
}

resource "random_string" "driver_account_webapp_cloudfront_logs_bucket" {
  count = local.driver_account_app_internal_toggle == false ? 1 : 0

  length  = 10
  special = false
  numeric = true
  upper   = false
  lower   = true
}


/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *           WAF Resources
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *
 * IP Sets managed in the organisation/aws-security workspace.
 * Currently IP'sets are only provisioned in the Pod Point AWS account.
*/

resource "aws_wafv2_web_acl" "driver_account_webapp_cloudfront" {
  provider = aws.us-east-1
  tags     = local.tags
  count    = local.driver_account_app_internal_toggle == false && var.environment == "prod" && var.enable_waf_on_driver_account_webapp ? 1 : 0

  name        = format("%s", local.driver_account_app_identifier)
  description = format("WAF to monitor and protect traffic for the %s Cloudfront distribution", local.driver_account_app_identifier)
  scope       = "CLOUDFRONT"

  default_action {
    allow {}
  }

  rule {
    name     = "AWS-AWSManagedRulesAmazonIpReputationList"
    priority = 5

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAmazonIpReputationList"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      metric_name                = format("%sAmazonIpReputationList", local.driver_account_app_identifier)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 10

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
        version     = "Version_1.17"
      }
    }

    visibility_config {
      metric_name                = format("%sKnownBadInputsRuleSet", local.driver_account_app_identifier)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesLinuxRuleSet"
    priority = 15

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesLinuxRuleSet"
        vendor_name = "AWS"
        version     = "Version_2.1"
      }
    }

    visibility_config {
      metric_name                = format("%sLinuxRuleSet", local.driver_account_app_identifier)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesUnixRuleSet"
    priority = 20

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesUnixRuleSet"
        vendor_name = "AWS"
        version     = "Version_1.1"
      }
    }

    visibility_config {
      metric_name                = format("%sUnixRuleSet", local.driver_account_app_identifier)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesCommonRuleSet"
    priority = 30

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
        version     = "Version_1.6"
      }
    }

    visibility_config {
      metric_name                = format("%sCommonRuleSet", local.driver_account_app_identifier)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesSQLiRuleSet"
    priority = 35

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesSQLiRuleSet"
        vendor_name = "AWS"
        version     = "Version_2.0"
      }
    }

    visibility_config {
      metric_name                = format("%sSQLiRuleSet", local.driver_account_app_identifier)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    metric_name                = local.driver_account_app_identifier
    cloudwatch_metrics_enabled = true
    sampled_requests_enabled   = true
  }

  rule {
    name     = "blanket-rate-limit-ip"
    priority = 40

    action {
      count {}
    }

    statement {
      rate_based_statement {
        aggregate_key_type = "IP"
        limit              = 200
      }
    }

    visibility_config {
      metric_name                = format("%sBlanketRateLimit", local.driver_account_app_identifier)
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }
}
