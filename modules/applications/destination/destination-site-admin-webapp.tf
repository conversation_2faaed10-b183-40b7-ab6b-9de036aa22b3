locals {
  destination_site_admin_r53_record_name           = var.environment == "prod" ? format("sites.%s", local.pod_point_com_domain_name) : format("sites-%s.%s", var.environment, local.pod_point_com_domain_name)
  destination_site_admin_podenergy_r53_record_name = var.environment == "prod" ? format("sites.%s", local.podenergy_com_domain_name) : format("sites-%s.%s", var.environment, local.podenergy_com_domain_name)
  destination_site_admin_webapp_service_name       = format("destination-site-admin-webapp-%s", var.environment)

  destination_site_admin_webapp_environment = [
    {
      "name" : "CLARITY_TRACKING_CODE",
      "value" : var.site_admin_webapp_clarity_tracking_code
    },
    {
      "name" : "ENVIRONMENT",
      "value" : var.environment
    },
    {
      "name" : "FEEDBACK_FORM_URL",
      "value" : var.site_admin_webapp_feedback_form_url
    },
    {
      "name" : "FIREBASE_API_KEY",
      "value" : var.commercial_admin_firebase_api_key
    },
    {
      "name" : "FIREBASE_APP_ID",
      "value" : var.destination_site_admin_webapp_firebase_app_id
    },
    {
      "name" : "FIREBASE_AUTH_DOMAIN",
      "value" : var.commercial_admin_firebase_auth_domain
    },
    {
      "name" : "FIREBASE_CLIENT_EMAIL",
      "value" : var.commercial_admin_firebase_client_email
    },
    {
      "name" : "FIREBASE_MESSAGING_SENDER_ID",
      "value" : var.commercial_admin_firebase_messaging_sender_id
    },
    {
      "name" : "FIREBASE_PROJECT_ID",
      "value" : var.commercial_admin_firebase_project_id
    },
    {
      "name" : "FIREBASE_STORAGE_BUCKET",
      "value" : var.commercial_admin_firebase_storage_bucket
    },
    {
      "name" : "GOOGLE_ANALYTICS_TAG_ID",
      "value" : var.site_admin_webapp_google_analytics_tag_id
    },
    {
      "name" : "NEXT_PUBLIC_MAINTENANCE_BANNER_MESSAGE",
      "value" : var.site_admin_webapp_maintenance_banner_message
    },
    {
      "name" : "NEXTAUTH_URL",
      "value" : "https://${local.destination_site_admin_r53_record_name}"
    },
    {
      "name" : "RECAPTCHA_SITE_KEY",
      "value" : var.destination_site_admin_webapp_recaptcha_site_key
    },
    {
      "name" : "SITE_ADMIN_API_URL",
      "value" : "http://destination-site-admin-api.${aws_service_discovery_private_dns_namespace.this.name}:4202"
    }
  ]
  destination_site_admin_webapp_secrets = [
    {
      "name" : "CONFIGCAT_SDK_KEY",
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.destination_site_admin_webapp.arn, "configcat_sdk_key")
    },
    {
      "name" : "FIREBASE_PRIVATE_KEY",
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.destination_site_admin_webapp.arn, "firebase_private_key")
    },
    {
      "name" : "GOOGLE_CLOUD_API_KEY",
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.destination_site_admin_webapp.arn, "google_cloud_api_key")
    },
    {
      "name" : "NEXTAUTH_SECRET",
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.destination_site_admin_webapp.arn, "nextauth_secret")
    }
  ]
}

resource "aws_route53_record" "destination_site_admin_webapp_load_balancer" {
  provider = aws.pod-point-eu-west-1

  zone_id         = local.pod_point_com_hosted_zone
  type            = "A"
  name            = local.destination_site_admin_r53_record_name
  allow_overwrite = false

  alias {
    name                   = var.environment == "prod" ? aws_cloudfront_distribution.destination_site_admin[0].domain_name : module.destination_site_admin_webapp_load_balancer.load_balancer_dns_name
    zone_id                = var.environment == "prod" ? aws_cloudfront_distribution.destination_site_admin[0].hosted_zone_id : module.destination_site_admin_webapp_load_balancer.load_balancer_zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "destination_site_admin_webapp_load_balancer_podenergy_com" {
  provider = aws.pod-point-eu-west-1

  zone_id         = local.podenergy_com_hosted_zone
  type            = "A"
  name            = local.destination_site_admin_podenergy_r53_record_name
  allow_overwrite = false

  alias {
    name                   = var.environment == "prod" ? aws_cloudfront_distribution.destination_site_admin[0].domain_name : module.destination_site_admin_webapp_load_balancer.load_balancer_dns_name
    zone_id                = var.environment == "prod" ? aws_cloudfront_distribution.destination_site_admin[0].hosted_zone_id : module.destination_site_admin_webapp_load_balancer.load_balancer_zone_id
    evaluate_target_health = true
  }
}

module "destination_site_admin_webapp_load_balancer" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws"
  version = "2.2.1"
  tags    = local.tags

  access_logs = {
    create_bucket_policy = true
    enable_logs          = true
  }

  enable_internal_lb = var.environment != "prod"

  http_tcp_listeners = [
    {
      port        = 80
      protocol    = "HTTP"
      action_type = "redirect"
      redirect = {
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  ]

  https_listeners = concat(var.environment == "prod" ? [
    {
      port            = 443
      protocol        = "HTTPS"
      certificate_arn = module.acm.arn
      action_type     = "fixed-response"
      fixed_response = {
        content_type = "text/plain"
        message_body = "Access denied"
        status_code  = "403"
      }
    }
    ] : [], var.environment != "prod" ? [
    {
      port               = 443
      protocol           = "HTTPS"
      certificate_arn    = module.acm.arn
      target_group_index = 0
    }
  ] : [])

  https_listener_rules = var.environment == "prod" ? [
    {
      https_listener_index = 0
      priority             = 1
      actions = [{
        type               = "forward"
        target_group_index = 0
      }]

      conditions = [{
        http_headers = [{
          http_header_name = "X-CF-Auth-Secret"
          values           = [one(random_string.random_cf_header_val_site_admin[*].result)]
        }]
      }]
    }
  ] : []

  load_balancer_name    = "destination-site-admin-webapp"
  load_balancer_subnets = var.environment == "prod" ? module.vpc.public_subnets_ids : module.vpc.private_subnets_ids
  load_balancer_type    = "application"

  security_group_name        = "destination-site-admin-webapp-lb"
  security_group_description = "Security Group for the destination-site-admin-webapp load balancer."
  security_group_ingress_rules = {
    "port_http_all" = {
      description = "HTTP permitted from the Internet."
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }

    "port_https_all" = {
      description = "HTTPS permitted from the Internet."
      from_port   = 443
      to_port     = 443
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }

  target_groups = [
    {
      backend_port         = 4201
      backend_protocol     = "HTTP"
      deregistration_delay = 60
      name                 = "destination-site-admin-webapp"
      target_type          = "ip"
      vpc_id               = module.vpc.vpc_id

      health_check = {
        enabled             = true
        healthy_threshold   = 2
        interval            = 5
        matcher             = "200"
        path                = "/api/auth/session"
        port                = "traffic-port"
        protocol            = "HTTP"
        timeout             = 3
        unhealthy_threshold = 2
      }
    }
  ]

  vpc_id = module.vpc.vpc_id
}

resource "aws_service_discovery_service" "destination_site_admin_webapp" {
  tags = local.tags
  name = "destination-site-admin-webapp"
  dns_config {
    namespace_id = aws_service_discovery_private_dns_namespace.this.id
    dns_records {
      ttl  = 0
      type = "A"
    }
  }
  health_check_custom_config {
    failure_threshold = 1
  }
}

module "destination_site_admin_webapp" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "11.3.1"
  tags    = local.tags

  additional_kms_administrators               = var.kms_admins
  attach_custom_ecs_task_execution_iam_policy = true
  attach_custom_ecs_task_iam_policy           = true
  capacity_fargate_base                       = var.environment == "prod" ? 2 : 0
  capacity_fargate_spot_base                  = var.environment == "prod" ? 0 : 2
  capacity_fargate_spot_weight                = 1
  capacity_fargate_weight                     = 0
  cluster_arn                                 = module.cluster.arn
  cluster_name                                = module.cluster.name
  cpu                                         = "512"
  ecs_task_custom_policy                      = jsonencode(local.ecs_task_custom_policy_site_admin_webapp)
  ecs_task_execution_custom_policy            = jsonencode(local.ecs_task_execution_destination_site_admin_webapp_custom_policy)
  enable_auto_scaling                         = true
  identifier                                  = "destination-site-admin-webapp"
  memory                                      = "1024"
  pipeline_role_name                          = module.cluster.github_role_name
  scaling_max_capacity                        = 4
  scaling_min_capacity                        = 2
  service_registry                            = { registry_arn : aws_service_discovery_service.destination_site_admin_webapp.arn }
  service_type                                = "rolling"
  subnet_ids                                  = module.vpc.private_subnets_ids
  vpc_id                                      = module.vpc.vpc_id

  load_balancing_configuration = [
    {
      target_group_arn = module.destination_site_admin_webapp_load_balancer.target_group_arns[0]
      container_name   = "destination-site-admin-webapp"
      container_port   = 4201
    }
  ]

  container_definitions = templatefile("${path.module}/templates/container-definitions-site-admin.tftpl", {
    "build_account_id" : var.build_account_id
    "environment" : local.destination_site_admin_webapp_environment
    "healthcheck_path" : ""
    "name" : "destination-site-admin-webapp"
    "region" : data.aws_region.current.name
    "port" : 4201
    "secrets" : local.destination_site_admin_webapp_secrets
  })

  scheduled_scaling = var.environment != "prod" ? {
    scale_in_schedules = [{
      schedule     = "cron(0 18 ? * MON-FRI *)"
      min_capacity = 0
      max_capacity = 0
    }]
    scale_out_schedules = [{
      schedule     = "cron(0 8 ? * MON-FRI *)"
      min_capacity = 2
      max_capacity = 4
    }]
  } : null
}

resource "aws_security_group_rule" "destination_site_admin_webapp_ingress" {
  type                     = "ingress"
  from_port                = 4201
  to_port                  = 4201
  protocol                 = "TCP"
  description              = "Access permitted from load balancer."
  security_group_id        = module.destination_site_admin_webapp.security_group_id
  source_security_group_id = module.destination_site_admin_webapp_load_balancer.security_group_id
}

resource "aws_security_group_rule" "destination_site_admin_webapp_egress" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  description       = "Permit all egress traffic."
  security_group_id = module.destination_site_admin_webapp.security_group_id
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
}

resource "aws_secretsmanager_secret" "destination_site_admin_webapp" {
  tags                    = local.tags
  name                    = "destination-site-admin-webapp"
  description             = "Secrets for the destination-site-admin-webapp service."
  kms_key_id              = module.kms.arn
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "destination_site_admin_webapp" {
  secret_id = aws_secretsmanager_secret.destination_site_admin_webapp.id
  secret_string = jsonencode({
    configcat_sdk_key    = var.destination_site_admin_webapp_configcat_sdk_key
    firebase_private_key = var.destination_site_admin_webapp_firebase_private_key
    google_cloud_api_key = var.destination_site_admin_webapp_google_cloud_api_key
    nextauth_secret      = var.destination_site_admin_webapp_nextauth_secret
  })
}

resource "aws_cloudwatch_metric_alarm" "destination_site_admin_webapp_alb_unhealthy_host_count" {
  tags       = local.tags
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-alb-unhealthy-host-count", local.destination_site_admin_webapp_service_name)
  dimensions = {
    LoadBalancer = module.destination_site_admin_webapp_load_balancer.load_balancer_arn_suffix
    TargetGroup  = module.destination_site_admin_webapp_load_balancer.target_group_arn_suffix[0]
  }

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "UnHealthyHostCount"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 0

  actions_enabled = true
  alarm_actions   = [local.opsgenie_topic_arn]
  ok_actions      = [local.opsgenie_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "destination_site_admin_webapp_alb_http_code_elb_4xx_count" {
  tags       = local.tags
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-alb-http-code-elb-4xx-count", local.destination_site_admin_webapp_service_name)
  dimensions = {
    LoadBalancer = module.destination_site_admin_webapp_load_balancer.load_balancer_arn_suffix
    TargetGroup  = module.destination_site_admin_webapp_load_balancer.target_group_arn_suffix[0]
  }

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "HTTPCode_ELB_4XX_Count"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 100

  actions_enabled = true
  alarm_actions   = [local.opsgenie_topic_arn]
  ok_actions      = [local.opsgenie_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "destination_site_admin_webapp_alb_http_code_target_4xx_count" {
  tags       = local.tags
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-alb-http-code-target-4xx-count", local.destination_site_admin_webapp_service_name)
  dimensions = {
    LoadBalancer = module.destination_site_admin_webapp_load_balancer.load_balancer_arn_suffix
    TargetGroup  = module.destination_site_admin_webapp_load_balancer.target_group_arn_suffix[0]
  }

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "HTTPCode_Target_4XX_Count"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 100

  actions_enabled = true
  alarm_actions   = [local.opsgenie_topic_arn]
  ok_actions      = [local.opsgenie_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "destination_site_admin_webapp_alb_http_code_elb_5xx_count" {
  tags       = local.tags
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-alb-http-code-elb-5xx-count", local.destination_site_admin_webapp_service_name)
  dimensions = {
    LoadBalancer = module.destination_site_admin_webapp_load_balancer.load_balancer_arn_suffix
    TargetGroup  = module.destination_site_admin_webapp_load_balancer.target_group_arn_suffix[0]
  }

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "HTTPCode_ELB_5XX_Count"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 0

  actions_enabled = true
  alarm_actions   = [local.opsgenie_topic_arn]
  ok_actions      = [local.opsgenie_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "destination_site_admin_webapp_alb_http_code_target_5xx_count" {
  tags       = local.tags
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-alb-http-code-target-5xx-count", local.destination_site_admin_webapp_service_name)
  dimensions = {
    LoadBalancer = module.destination_site_admin_webapp_load_balancer.load_balancer_arn_suffix
    TargetGroup  = module.destination_site_admin_webapp_load_balancer.target_group_arn_suffix[0]
  }

  period              = 60
  evaluation_periods  = 5
  statistic           = "Average"
  metric_name         = "HTTPCode_Target_5XX_Count"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 0

  actions_enabled = true
  alarm_actions   = [local.opsgenie_topic_arn]
  ok_actions      = [local.opsgenie_topic_arn]
}

resource "aws_cloudwatch_metric_alarm" "destination_site_admin_webapp_alb_target_response_time" {
  tags       = local.tags
  namespace  = "AWS/ApplicationELB"
  alarm_name = format("%s-alb-target-response-time", local.destination_site_admin_webapp_service_name)
  dimensions = {
    LoadBalancer = module.destination_site_admin_webapp_load_balancer.load_balancer_arn_suffix
    TargetGroup  = module.destination_site_admin_webapp_load_balancer.target_group_arn_suffix[0]
  }

  period              = 60
  evaluation_periods  = 10
  statistic           = "Average"
  metric_name         = "TargetResponseTime"
  comparison_operator = "GreaterThanThreshold"
  threshold           = 1
  treat_missing_data  = "ignore"

  actions_enabled = true
  alarm_actions   = [local.opsgenie_topic_arn]
  ok_actions      = [local.opsgenie_topic_arn]
}
