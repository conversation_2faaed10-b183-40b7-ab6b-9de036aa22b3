locals {
  fleet_site_admin_r53_record_name     = var.environment == "prod" ? format("fleet.%s", local.pod_point_com_domain_name) : format("fleet-%s.%s", var.environment, local.pod_point_com_domain_name)
  fleet_site_admin_webapp_service_name = format("fleet-site-admin-webapp-%s", var.environment)
}

resource "aws_route53_record" "fleet_site_admin_webapp_load_balancer" {
  provider = aws.pod-point-eu-west-1

  zone_id         = local.pod_point_com_hosted_zone
  type            = "A"
  name            = local.fleet_site_admin_r53_record_name
  allow_overwrite = false

  alias {
    name                   = var.environment == "prod" ? aws_cloudfront_distribution.fleet_site_admin[0].domain_name : module.fleet_site_admin_webapp_load_balancer.load_balancer_dns_name
    zone_id                = var.environment == "prod" ? aws_cloudfront_distribution.fleet_site_admin[0].hosted_zone_id : module.fleet_site_admin_webapp_load_balancer.load_balancer_zone_id
    evaluate_target_health = true
  }
}

module "fleet_site_admin_webapp_load_balancer" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws"
  version = "2.2.1"
  tags    = local.tags

  access_logs = {
    create_bucket_policy = true
    enable_logs          = true
  }

  enable_internal_lb = var.environment != "prod"

  http_tcp_listeners = [
    {
      port        = 80
      protocol    = "HTTP"
      action_type = "redirect"
      redirect = {
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  ]

  https_listeners = concat(var.environment == "prod" ? [
    {
      port            = 443
      protocol        = "HTTPS"
      certificate_arn = module.acm.arn
      action_type     = "fixed-response"
      fixed_response = {
        content_type = "text/plain"
        message_body = "Access denied"
        status_code  = "403"
      }
    }
    ] : [], var.environment != "prod" ? [
    {
      port            = 443
      protocol        = "HTTPS"
      certificate_arn = module.acm.arn
      action_type     = "redirect"
      redirect = {
        host        = local.destination_site_admin_r53_record_name
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  ] : [])

  https_listener_rules = var.environment == "prod" ? [
    {
      https_listener_index = 0
      priority             = 1
      actions = [{
        type        = "redirect"
        host        = local.destination_site_admin_r53_record_name
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }]
      conditions = [{
        http_headers = [{
          http_header_name = "X-CF-Auth-Secret"
          values           = [one(random_string.random_cf_header_val_fleet_site_admin[*].result)]
        }]
      }]
    }
  ] : []

  load_balancer_name    = "fleet-site-admin-webapp"
  load_balancer_subnets = var.environment == "prod" ? module.vpc.public_subnets_ids : module.vpc.private_subnets_ids
  load_balancer_type    = "application"

  security_group_name        = "fleet-site-admin-webapp-lb"
  security_group_description = "Security Group for the fleet-site-admin-webapp load balancer."
  security_group_ingress_rules = {
    "port_http_all" = {
      description = "HTTP permitted from the Internet."
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }

    "port_https_all" = {
      description = "HTTPS permitted from the Internet."
      from_port   = 443
      to_port     = 443
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }

  target_groups = [
    {
      backend_port         = 4301
      backend_protocol     = "HTTP"
      deregistration_delay = 60
      name                 = "fleet-site-admin-webapp"
      target_type          = "ip"
      vpc_id               = module.vpc.vpc_id

      health_check = {
        enabled             = true
        healthy_threshold   = 2
        interval            = 5
        matcher             = "200"
        path                = "/api/auth/session"
        port                = "traffic-port"
        protocol            = "HTTP"
        timeout             = 3
        unhealthy_threshold = 2
      }
    }
  ]

  vpc_id = module.vpc.vpc_id
}
