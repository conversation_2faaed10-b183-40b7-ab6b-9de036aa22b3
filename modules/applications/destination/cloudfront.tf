/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Distribution Resources for destination site admin
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "random_string" "random_cf_header_val_site_admin" {
  count = var.environment == "prod" ? 1 : 0

  length  = 16
  special = false
  numeric = true
  upper   = true
  lower   = true
}

resource "aws_cloudfront_distribution" "destination_site_admin" {
  count    = var.environment == "prod" ? 1 : 0
  provider = aws.us-east-1
  tags     = local.tags

  enabled    = true
  comment    = "Cloudfront distribution for the destination site admin service."
  aliases    = [local.destination_site_admin_r53_record_name, local.destination_site_admin_podenergy_r53_record_name]
  web_acl_id = var.enable_waf_site_admin ? one(aws_wafv2_web_acl.cloudfront_site_admin[*].arn) : null

  origin {
    origin_id   = "destination-site-admin"
    domain_name = module.destination_site_admin_webapp_load_balancer.load_balancer_dns_name

    # Used to restrict access on the application load balancer via listener if rule condition.
    custom_header {
      name  = "X-CF-Auth-Secret"
      value = random_string.random_cf_header_val_site_admin[0].result
    }

    custom_origin_config {
      http_port              = "80"
      https_port             = "443"
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1", "TLSv1.1", "TLSv1.2", "SSLv3"]
    }
  }

  default_cache_behavior {
    compress                 = true
    allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods           = ["GET", "HEAD"]
    target_origin_id         = "destination-site-admin"
    cache_policy_id          = data.aws_cloudfront_cache_policy.no_cache_aws_managed_policy.id
    origin_request_policy_id = aws_cloudfront_origin_request_policy.destination_site_admin_default[0].id
    viewer_protocol_policy   = "redirect-to-https"
  }

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  dynamic "logging_config" {
    for_each = var.cloudfront_enable_logging ? [1] : []

    content {
      bucket          = one(module.cloudfront_log_bucket_destination_site_admin[*].s3_bucket_bucket_domain_name)
      include_cookies = false
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = false
    acm_certificate_arn            = module.pod_point_and_energy_cf_acm.acm_certificate_arn
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }
}


/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront cache monitoring
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_cloudfront_monitoring_subscription" "destination_site_admin" {
  provider = aws.us-east-1

  count = var.mobile_api_cloudfront_realtime_metrics == "Enabled" && var.environment == "prod" ? 1 : 0

  distribution_id = aws_cloudfront_distribution.destination_site_admin[0].id
  monitoring_subscription {
    realtime_metrics_subscription_config {
      realtime_metrics_subscription_status = var.destination_site_admin_realtime_metrics
    }
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Origin Policy
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_cloudfront_origin_request_policy" "destination_site_admin_default" {
  count    = var.environment == "prod" ? 1 : 0
  provider = aws.us-east-1

  name = "destination-site-admin-request-policy"

  cookies_config {
    cookie_behavior = "all"
  }

  headers_config {
    header_behavior = "allViewerAndWhitelistCloudFront"

    headers {
      items = [
        "CloudFront-Viewer-Address",
        "CloudFront-Viewer-Country-Region",
        "CloudFront-Viewer-JA3-Fingerprint",
      ]
    }
  }

  query_strings_config {
    query_string_behavior = "all"
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Logging
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

module "cloudfront_log_bucket_destination_site_admin" {
  count = var.cloudfront_enable_logging && var.environment == "prod" ? 1 : 0

  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.1.0"
  tags    = local.tags

  bucket = "destination-site-admin-${var.environment}-cf-accesslogs"

  versioning = {
    enabled = false
  }

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
}


/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Distribution Resources for internal site admin
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "random_string" "random_cf_header_val_internal_site_admin" {
  length  = 16
  special = false
  numeric = true
  upper   = true
  lower   = true
}

resource "aws_cloudfront_distribution" "internal_site_admin" {
  provider = aws.us-east-1
  tags     = local.tags

  enabled    = true
  comment    = "Cloudfront distribution for the internal site admin service."
  aliases    = [local.internal_site_admin_r53_record_name, local.internal_site_admin_podenergy_r53_record_name]
  web_acl_id = var.enable_waf_internal_site_admin ? one(aws_wafv2_web_acl.cloudfront_internal_site_admin[*].arn) : null

  origin {
    origin_id   = "internal-site-admin"
    domain_name = module.internal_site_admin_webapp_load_balancer_sso.load_balancer_dns_name

    # Used to restrict access on the application load balancer via listener if rule condition.
    custom_header {
      name  = "X-CF-Auth-Secret"
      value = random_string.random_cf_header_val_internal_site_admin.result
    }

    custom_origin_config {
      http_port              = "80"
      https_port             = "443"
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1", "TLSv1.1", "TLSv1.2", "SSLv3"]
    }
  }

  default_cache_behavior {
    compress                 = true
    allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods           = ["GET", "HEAD"]
    target_origin_id         = "internal-site-admin"
    cache_policy_id          = data.aws_cloudfront_cache_policy.no_cache_aws_managed_policy.id
    origin_request_policy_id = aws_cloudfront_origin_request_policy.internal_site_admin_default.id
    viewer_protocol_policy   = "redirect-to-https"
  }

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  dynamic "logging_config" {
    for_each = var.cloudfront_enable_logging ? [1] : []

    content {
      bucket          = one(module.cloudfront_log_bucket_internal_site_admin[*].s3_bucket_bucket_domain_name)
      include_cookies = false
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = false
    acm_certificate_arn            = module.pod_point_and_energy_cf_acm.acm_certificate_arn
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront cache monitoring
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_cloudfront_monitoring_subscription" "internal_site_admin" {
  provider = aws.us-east-1

  count = var.internal_site_admin_realtime_metrics == "Enabled" ? 1 : 0

  distribution_id = aws_cloudfront_distribution.internal_site_admin.id
  monitoring_subscription {
    realtime_metrics_subscription_config {
      realtime_metrics_subscription_status = var.internal_site_admin_realtime_metrics
    }
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Origin Policy
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_cloudfront_origin_request_policy" "internal_site_admin_default" {
  provider = aws.us-east-1

  name = "internal-site-admin-request-policy"

  cookies_config {
    cookie_behavior = "all"
  }

  headers_config {
    header_behavior = "allViewerAndWhitelistCloudFront"

    headers {
      items = [
        "CloudFront-Viewer-Address",
        "CloudFront-Viewer-Country-Region",
        "CloudFront-Viewer-JA3-Fingerprint",
      ]
    }
  }

  query_strings_config {
    query_string_behavior = "all"
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Logging
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

module "cloudfront_log_bucket_internal_site_admin" {
  count = var.cloudfront_enable_logging ? 1 : 0

  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.1.0"
  tags    = local.tags

  bucket = "internal-site-admin-${var.environment}-cf-accesslogs"

  versioning = {
    enabled = false
  }

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
}


/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Distribution Resources for fleet site admin
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "random_string" "random_cf_header_val_fleet_site_admin" {
  count = var.environment == "prod" ? 1 : 0

  length  = 16
  special = false
  numeric = true
  upper   = true
  lower   = true
}

resource "aws_cloudfront_distribution" "fleet_site_admin" {
  count    = var.environment == "prod" ? 1 : 0
  provider = aws.us-east-1
  tags     = local.tags

  enabled    = true
  comment    = "Cloudfront distribution for the fleet site admin service."
  aliases    = [local.fleet_site_admin_r53_record_name]
  web_acl_id = var.enable_waf_fleet_site_admin ? one(aws_wafv2_web_acl.cloudfront_fleet_site_admin[*].arn) : null

  origin {
    origin_id   = "fleet-site-admin"
    domain_name = module.fleet_site_admin_webapp_load_balancer.load_balancer_dns_name

    # Used to restrict access on the application load balancer via listener if rule condition.
    custom_header {
      name  = "X-CF-Auth-Secret"
      value = random_string.random_cf_header_val_fleet_site_admin[0].result
    }

    custom_origin_config {
      http_port              = "80"
      https_port             = "443"
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1", "TLSv1.1", "TLSv1.2", "SSLv3"]
    }
  }

  default_cache_behavior {
    compress                 = true
    allowed_methods          = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods           = ["GET", "HEAD"]
    target_origin_id         = "fleet-site-admin"
    cache_policy_id          = data.aws_cloudfront_cache_policy.no_cache_aws_managed_policy.id
    origin_request_policy_id = aws_cloudfront_origin_request_policy.fleet_site_admin_default[0].id
    viewer_protocol_policy   = "redirect-to-https"
  }

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  dynamic "logging_config" {
    for_each = var.cloudfront_enable_logging ? [1] : []

    content {
      bucket          = one(module.cloudfront_log_bucket_fleet_site_admin[*].s3_bucket_bucket_domain_name)
      include_cookies = false
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = false
    acm_certificate_arn            = module.acm_us_east_1.arn
    minimum_protocol_version       = "TLSv1.2_2021"
    ssl_support_method             = "sni-only"
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Origin Policy
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_cloudfront_origin_request_policy" "fleet_site_admin_default" {
  count    = var.environment == "prod" ? 1 : 0
  provider = aws.us-east-1

  name = "fleet-site-admin-request-policy"

  cookies_config {
    cookie_behavior = "all"
  }

  headers_config {
    header_behavior = "allViewerAndWhitelistCloudFront"

    headers {
      items = [
        "CloudFront-Viewer-Address",
        "CloudFront-Viewer-Country-Region",
        "CloudFront-Viewer-JA3-Fingerprint",
      ]
    }
  }

  query_strings_config {
    query_string_behavior = "all"
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Logging
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

module "cloudfront_log_bucket_fleet_site_admin" {
  count = var.cloudfront_enable_logging && var.environment == "prod" ? 1 : 0

  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.1.0"
  tags    = local.tags

  bucket = "fleet-site-admin-${var.environment}-cf-accesslogs"

  versioning = {
    enabled = false
  }

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *  CloudFront Cache Policy Resources
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

data "aws_cloudfront_cache_policy" "no_cache_aws_managed_policy" {
  name = "Managed-CachingDisabled"
}
