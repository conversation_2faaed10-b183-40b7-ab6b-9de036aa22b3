resource "aws_security_group" "dms_security_group" {
  name        = "podamin-to-experience-data-feed"
  description = "Security Group for data feed of podadmin for use by Experience."
  vpc_id      = module.vpc.vpc_id

  tags = local.tags
}

module "dms" {
  source  = "terraform-aws-modules/dms/aws"
  version = "2.1.0"
  tags    = local.tags

  # Subnet group
  repl_subnet_group_name        = "podamin-to-experience-data-feed"
  repl_subnet_group_description = "Subnet group to support data feed of podadmin for use by Experience."
  repl_subnet_group_subnet_ids  = module.vpc.private_subnets_ids

  # Instance
  repl_instance_id = var.data_platform_podadmin_data_feed_repl_instance_name

  repl_instance_class             = var.data_platform_podadmin_data_feed_repl_instance_class
  repl_instance_multi_az          = var.data_platform_podadmin_data_feed_repl_instance_multi_az
  repl_instance_apply_immediately = var.data_platform_podadmin_data_feed_repl_instance_apply_immediately

  repl_instance_kms_key_arn            = module.kms.arn
  repl_instance_publicly_accessible    = false
  repl_instance_vpc_security_group_ids = [aws_security_group.dms_security_group.id]

  repl_instance_auto_minor_version_upgrade  = true
  repl_instance_allow_major_version_upgrade = false

  # Create ssl certs for verify-full endpoints
  # eu-west-1 pem file obtained from
  # https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/UsingWithRDS.SSL.html#UsingWithRDS.SSL.RegionCertificates
  certificates = {
    data-platform-eu-west-1-dms-cert : {
      certificate_id : "data-platform-eu-west-1-dms-cert",
      certificate_pem : aws_ssm_parameter.data-platform-dms-eu-west-1-certs.value
    }
  }

  # Source and target endpoints
  endpoints = {
    source = {
      database_name                   = var.data_platform_podadmin_db_database
      endpoint_id                     = "podadmin-source"
      endpoint_type                   = "source"
      engine_name                     = "aurora"
      extra_connection_attributes     = "heartbeatFrequency=1;secretsManagerEndpointOverride=${module.vpc_endpoints.endpoints["secretsmanager"]["dns_entry"][0]["dns_name"]}"
      secrets_manager_arn             = aws_secretsmanager_secret.dms_source_podadmin_creds.arn
      secrets_manager_access_role_arn = aws_iam_role.dms_secrets_manager_role_podadmin.arn
      ssl_mode                        = "verify-full"
      tags                            = { EndpointType = "source" }
      certificate_key                 = "data-platform-eu-west-1-dms-cert"
    }

    target = {
      database_name                   = local.aurora_db_xdp_name
      endpoint_id                     = "experience-target"
      endpoint_type                   = "target"
      engine_name                     = local.aurora_postgres_engine
      extra_connection_attributes     = "secretsManagerEndpointOverride=${module.vpc_endpoints.endpoints["secretsmanager"]["dns_entry"][0]["dns_name"]}"
      secrets_manager_arn             = aws_secretsmanager_secret.dms_target_data_platform_creds.arn
      secrets_manager_access_role_arn = aws_iam_role.dms_secrets_manager_role_data_platform.arn
      ssl_mode                        = "verify-full"
      tags                            = { EndpointType = "target" }
      certificate_key                 = "data-platform-eu-west-1-dms-cert"
    }
  }

  # Replication tasks
  replication_tasks = {
    podadmin_to_experience = {
      replication_task_id       = "podadmin-to-experience"
      migration_type            = "full-load-and-cdc"
      replication_task_settings = file("${path.module}/config/task_settings.json")
      table_mappings            = file("${path.module}/config/table_mappings.json")
      source_endpoint_key       = "source"
      target_endpoint_key       = "target"
      tags = var.environment == "prod" ? { Task = "podadmin-to-experience", } : {
        Task               = "podadmin-to-experience",
        AutoSchedulerStop  = "6PM"
        AutoSchedulerStart = "8AM"
      }
    }
    podadmin_to_experience_2 = {
      replication_task_id       = "podadmin-to-experience-2"
      migration_type            = "full-load-and-cdc"
      replication_task_settings = file("${path.module}/config/task_settings.json")
      table_mappings            = file("${path.module}/config/table_mappings_2.json")
      source_endpoint_key       = "source"
      target_endpoint_key       = "target"
      tags = var.environment == "prod" ? { Task = "podadmin-to-experience-2", } : {
        Task               = "podadmin-to-experience-2"
        AutoSchedulerStop  = "6PM"
        AutoSchedulerStart = "8AM"
      }
    }
  }

  create_access_iam_role = false
}

resource "aws_security_group_rule" "dms_egress_podadmin_security_rule" {
  type              = "egress"
  from_port         = var.data_platform_podadmin_db_port
  to_port           = var.data_platform_podadmin_db_port
  protocol          = "TCP"
  description       = "Access permitted to podadmin endpoint."
  security_group_id = aws_security_group.dms_security_group.id
  cidr_blocks       = [local.podpoint_main_account_ipv4_cidrs]
  ipv6_cidr_blocks  = [local.podpoint_main_account_ipv6_cidrs]
}

resource "aws_security_group_rule" "dms_egress_experience_cluster_security_rule" {
  type                     = "egress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  description              = "Access permitted to experience cluster endpoint."
  security_group_id        = aws_security_group.dms_security_group.id
  source_security_group_id = module.aurora.security_group_id
}

resource "aws_security_group_rule" "dms_tcp_ingress" {
  type                     = "ingress"
  from_port                = 443
  to_port                  = 443
  protocol                 = "TCP"
  description              = "Ingress rule allow TCP 443 from same security group"
  security_group_id        = aws_security_group.dms_security_group.id
  source_security_group_id = aws_security_group.dms_security_group.id
}

resource "aws_security_group_rule" "dms_tcp_egress" {
  type                     = "egress"
  from_port                = 443
  to_port                  = 443
  protocol                 = "TCP"
  description              = "Egress rule allow TCP 443 to same security group"
  security_group_id        = aws_security_group.dms_security_group.id
  source_security_group_id = aws_security_group.dms_security_group.id
}

# Latest cert obtained from:
# https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/UsingWithRDS.SSL.html
resource "aws_ssm_parameter" "data-platform-dms-eu-west-1-certs" {
  tags  = local.tags
  name  = "data-platform-dms-eu-west-1-certs"
  type  = "String"
  tier  = "Advanced"
  value = file("${path.module}/config/eu-west-1-bundle.pem")
}

# sns topic for dms replication task event notifications
resource "aws_sns_topic" "data-platform-dms-replication-task-topic" {
  tags = local.tags
  name = "data-platform-dms-replication-task-topic"
}

# subscription to dms replication task events - producer to sns topic
resource "aws_dms_event_subscription" "data-platform-replication-task-events-subscription" {
  tags = var.environment == "prod" ? local.tags : merge(local.tags, {
    AutoSchedulerStop                    = "6PM"
    AutoSchedulerStart                   = "8AM"
    AutoSchedulerEventSubscriptionToggle = "True"
  })
  enabled          = true
  event_categories = ["state change", "failure"]
  name             = "data-platform-replication-task-events-subscription"
  sns_topic_arn    = aws_sns_topic.data-platform-dms-replication-task-topic.arn
  source_ids = [
    for item in module.dms.replication_tasks :
    item.replication_task_id
  ]
  source_type = "replication-task"
}

# subscription to create opsgenie alerts via https - consumer of sns topic
resource "aws_sns_topic_subscription" "data-platform-opsgenie-notifications" {
  topic_arn = aws_sns_topic.data-platform-dms-replication-task-topic.arn
  protocol  = "https"
  endpoint  = var.data_platform_replication_task_opsgenie_api

  filter_policy = var.environment == "prod" ? null : jsonencode({
    "Event Message" = [
      {
        "anything-but" = [
          "DMS-Modify-Notification"
        ]
      }
    ]
  })
  filter_policy_scope = var.environment == "prod" ? null : "MessageBody"
}
