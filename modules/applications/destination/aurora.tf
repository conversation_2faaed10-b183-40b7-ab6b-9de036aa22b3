module "aurora" {
  source  = "terraform-enterprise.pod-point.com/technology/aurora/aws"
  version = "6.2.1"
  tags = var.environment == "prod" ? local.tags : merge(local.tags, {
    "AutoSchedulerStop"  = "6PM"
    "AutoSchedulerStart" = "7AM"
  })

  identifier       = local.aurora_cluster_xdp_identifier
  vpc_id           = module.vpc.vpc_id
  subnet_group_ids = module.vpc.private_subnets_ids
  port             = 5432

  engine         = local.aurora_postgres_engine
  engine_version = local.aurora_postgres_engine_version

  admin_username            = local.aurora_db_admin_username
  database_name             = local.aurora_db_xdp_name
  aurora_managed_admin_user = false

  cluster_preffered_maintenance_window        = "Mon:02:00-Mon:03:00"
  cluster_preffered_backup_window             = "00:30-02:00"
  cluster_deletion_protection                 = true
  cluster_backup_retention_period             = 7
  enable_aws_backup                           = var.environment == "prod" ? true : false
  cluster_iam_database_authentication_enabled = true

  # KMS Encryption.
  secret_manager_admin_user_kms_encryption_key     = module.kms.arn
  storage_kms_encryption_key                       = module.kms.arn
  instance_performance_insights_kms_encryption_key = module.kms.arn

  # Parameters
  cluster_parameter_group_family = local.aurora_postgres_parameter_group_family
  cluster_parameters             = local.aurora_cluster_parameters

  instance_enable_performance_insights = true
  instance_parameter_group_family      = local.aurora_postgres_parameter_group_family
  instance_parameters                  = {}

  # Cluster instances
  apply_instance_changes_immediately = var.experience_db_cluster_apply_immediately
  cluster_instance_class             = var.experience_db_cluster_instance_class
  cluster_instance_count             = var.experience_db_cluster_instance_count

  instance_auto_minor_version_upgrade = false
  cluster_allow_major_version_upgrade = false
}

module "aurora_cloning_runner" {
  source  = "terraform-enterprise.pod-point.com/technology/aurora/aws//modules/aurora_cloning_step_function"
  version = "6.3.1"
}

module "aurora_cloning_scheduler" {
  source  = "terraform-enterprise.pod-point.com/technology/aurora/aws//modules/aurora_cloning_step_function_scheduler"
  version = "6.3.1"

  step_function_arn       = module.aurora_cloning_runner.step_function_arn
  step_function_role_name = module.aurora_cloning_runner.step_function_role_name
  target_cluster_id       = module.aurora.cluster_identifier
}

resource "aws_security_group_rule" "aurora_cluster_egress" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  description       = "Permit all egress traffic."
  security_group_id = module.aurora.security_group_id
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
}

resource "aws_security_group_rule" "aurora_cluster_data_platform_api_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  description              = "Access permitted from data-platform-api."
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = module.data_platform_api.security_group_id
}

resource "aws_security_group_rule" "aurora_cluster_installer_api_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  description              = "Access permitted from installer-api."
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = module.installer_api.security_group_id
}

resource "aws_security_group_rule" "aurora_cluster_dms_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  description              = "Access permitted from DMS replication server."
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = aws_security_group.dms_security_group.id
}

resource "aws_security_group_rule" "aurora_cluster_vpn_ingress" {
  type              = "ingress"
  from_port         = module.aurora.port
  to_port           = module.aurora.port
  cidr_blocks       = ["**********/22"]
  protocol          = "TCP"
  description       = "Access permitted from AWS Client VPN."
  security_group_id = module.aurora.security_group_id
}

resource "aws_security_group_rule" "aurora_cluster_data_team_vpc_ingress" {
  description       = "Access permitted from data ${var.environment} VPC."
  type              = "ingress"
  from_port         = module.aurora.port
  to_port           = module.aurora.port
  protocol          = "tcp"
  cidr_blocks       = [var.data_vpc_cidr]
  security_group_id = module.aurora.security_group_id
}

resource "aws_security_group_rule" "aurora_cluster_carbon_intensity_store_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  description              = "Access permitted from carbon-intensity-store."
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = aws_security_group.carbon_intensity_store_sg.id
}

resource "aws_security_group_rule" "aurora_cluster_data_platform_task_runner_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  description              = "Access permitted from data-platform-task-runner."
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = aws_security_group.data_platform_task_runner_sg.id
}

resource "aws_security_group_rule" "aurora_cluster_data_platform_events_queue_worker_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  description              = "Access permitted from data-platform-events-queue-worker."
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = module.data_platform_events_queue_worker.security_group_id
}

resource "aws_security_group_rule" "aurora_cluster_data_platform_async_processor_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  description              = "Access permitted from data-platform-async-processor."
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = module.data_platform_async_processor.security_group_id
}

resource "aws_security_group_rule" "aurora_cluster_internal_site_admin_api_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  description              = "Access permitted from internal-site-admin-api."
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = module.internal_site_admin_api.security_group_id
}

resource "aws_security_group_rule" "aurora_cluster_managed_grafana_ingress" {
  description              = "Access permitted from managed grafana."
  type                     = "ingress"
  protocol                 = "tcp"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = var.managed_grafana_sg
}

resource "aws_security_group_rule" "aurora_cluster_driver_account_api_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = module.driver_account_api.security_group_id
}

resource "aws_security_group_rule" "aurora_cluster_billing_api_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = module.billing_api.security_group_id
}

resource "aws_security_group_rule" "aurora_cluster_rewards_api_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = var.rewards_api_security_group_id
}

resource "aws_security_group_rule" "aurora_cluster_subscriptions_api_ingress" {
  description              = "Allow access from Subscriptions API"
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = var.subscriptions_api_security_group_id
}

resource "aws_security_group_rule" "aurora_cluster_payments_api_ingress" {
  type                     = "ingress"
  from_port                = module.aurora.port
  to_port                  = module.aurora.port
  protocol                 = "TCP"
  security_group_id        = module.aurora.security_group_id
  source_security_group_id = var.payments_api_security_group_id
}

resource "aws_security_group_rule" "aurora_cluster_ownership_acquisitions_ingress" {
  description       = "Access permitted from data ${var.environment} ownership-acquisitions"
  type              = "ingress"
  from_port         = module.aurora.port
  to_port           = module.aurora.port
  protocol          = "tcp"
  cidr_blocks       = [var.ownership_acquisitions_vpc_cidr]
  security_group_id = module.aurora.security_group_id
}


# Create an IAM role allowing the GitHub Actions user in the shared account the ability to manipulate RDS in this account
data "aws_iam_policy_document" "assume_github_actions_rds_role" {
  statement {
    sid    = "AllowOpenIDConnect"
    effect = "Allow"

    actions = [
      "sts:AssumeRoleWithWebIdentity"
    ]

    principals {
      type        = "Federated"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/token.actions.githubusercontent.com"]
    }

    condition {
      test     = "StringEquals"
      variable = "token.actions.githubusercontent.com:aud"
      values   = ["sts.amazonaws.com"]
    }

    condition {
      test     = "ForAnyValue:StringLike"
      variable = "token.actions.githubusercontent.com:sub"
      values = [
        for repoName in var.repo_names : "repo:Pod-Point/${repoName}:*"
      ]
    }
  }
}

data "aws_iam_policy_document" "github_actions_rds_role_policy" {
  statement {
    effect = "Allow"
    actions = [
      "rds:DescribeDBClusters",
      "rds:ListTagsForResource",
      "rds:StartDBCluster",
      "rds:StartDBInstance",
      "rds:StopDBCluster",
      "rds:StopDBInstance",
    ]
    resources = [
      module.aurora.cluster_arn
    ]
  }
}

resource "aws_iam_role" "github_actions_rds_role" {
  tags               = local.tags
  name               = "github-rds-${local.aurora_cluster_xdp_identifier}"
  assume_role_policy = data.aws_iam_policy_document.assume_github_actions_rds_role.json
}

resource "aws_iam_policy" "github_actions_rds_policy" {
  name        = "github-rds-${local.aurora_cluster_xdp_identifier}"
  description = "Policy for GitHub Actions to manipulate RDS"

  policy = data.aws_iam_policy_document.github_actions_rds_role_policy.json
}

resource "aws_iam_role_policy_attachment" "github_actions_rds_policy_attachment" {
  role       = aws_iam_role.github_actions_rds_role.name
  policy_arn = aws_iam_policy.github_actions_rds_policy.arn
}
