data "aws_iam_policy_document" "custom_ecs_task_execution_policy" {
  statement {
    sid     = "RetrieveSecretManagerSecretValues"
    actions = ["secretsmanager:GetSecretValue"]
    resources = [
      aws_secretsmanager_secret.unit_link_verification.arn,
    ]
  }

  statement {
    sid     = "AllowKMSBackendKeyDecrypt"
    actions = ["kms:Decrypt"]
    resources = [
      aws_kms_key.secrets.arn
    ]
  }
}

resource "aws_iam_role" "allow_read_ecommerce_dynamo" {
  provider = aws.pod-point

  assume_role_policy = data.aws_iam_policy_document.assume_role.json
}

data "aws_iam_policy_document" "assume_role" {
  provider = aws.pod-point

  statement {
    actions = ["sts:AssumeRole"]
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
  }
}

resource "aws_iam_policy_attachment" "allow_read_ecommerce_dynamo" {
  provider = aws.pod-point

  name       = "allow-read-ecommerce-dynamo"
  policy_arn = aws_iam_policy.allow_read_ecommerce_dynamo.arn
  roles      = [aws_iam_role.allow_read_ecommerce_dynamo.name]
}

resource "aws_iam_policy" "allow_read_ecommerce_dynamo" {
  provider = aws.pod-point

  policy = data.aws_iam_policy_document.allow_read_ecommerce_dynamo.json
}

data "aws_iam_policy_document" "allow_read_ecommerce_dynamo" {
  provider = aws.pod-point

  statement {
    actions = ["dynamodb:Query"]
    effect  = "Allow"
    resources = [
      "arn:aws:dynamodb:eu-west-1:${data.aws_caller_identity.pod_point.account_id}:table/${var.ecommerce_dynamo_table_name}",
      "arn:aws:dynamodb:eu-west-1:${data.aws_caller_identity.pod_point.account_id}:table/${var.ecommerce_dynamo_table_name}/index/gsi1",
    ]
  }
}
