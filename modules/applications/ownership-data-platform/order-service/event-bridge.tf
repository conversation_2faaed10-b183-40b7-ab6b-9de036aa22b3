resource "aws_cloudwatch_event_rule" "order_status_events_bmw" {
  name           = "order-status-events-bmw"
  event_bus_name = var.odp_sf_event_bus_arn
  event_pattern = jsonencode({
    detail-type = ["Order_Status__e"]
    detail = {
      payload = {
        Partner__c = ["BMW"]
      }
    }
  })
}

resource "aws_cloudwatch_event_target" "order_status_queue_bmw" {
  rule           = aws_cloudwatch_event_rule.order_status_events_bmw.name
  arn            = module.order_status_event_queue_bmw.queue_arn
  event_bus_name = var.odp_sf_event_bus_arn
  input_path     = "$.detail"
}

resource "aws_cloudwatch_event_rule" "order_status_events_partner_portal" {
  name           = "order-status-events-partner-portal"
  event_bus_name = var.odp_sf_event_bus_arn
  event_pattern = jsonencode({
    detail-type = ["Order_Status__e"]
    detail = {
      payload = {
        Partner__c = [
          { "anything-but" : ["BMW"] }
        ]
      }
    }
  })
}

resource "aws_cloudwatch_event_target" "order_status_queue_partner_portal" {
  rule           = aws_cloudwatch_event_rule.order_status_events_partner_portal.name
  arn            = module.order_status_event_queue_partner_portal.queue_arn
  event_bus_name = var.odp_sf_event_bus_arn
  input_path     = "$.detail"
}

resource "aws_cloudwatch_event_rule" "installation_complete_events" {
  name           = "installation-complete-events"
  event_bus_name = var.odp_sf_event_bus_arn
  event_pattern = jsonencode({
    detail-type = ["Installation_Complete__e"]
  })
}

resource "aws_cloudwatch_event_target" "installation_complete_queue" {
  rule           = aws_cloudwatch_event_rule.installation_complete_events.name
  arn            = module.salesforce_installation_complete_queue.queue_arn
  event_bus_name = var.odp_sf_event_bus_arn
  input_path     = "$.detail"
}

resource "aws_cloudwatch_event_rule" "partner_portal_events" {
  name           = "forward-partner-events-to-main-account"
  event_bus_name = var.odp_event_bus_arn
  event_pattern = jsonencode({
    detail = {
      event = [
        "installation.completed",
        "order.status.updated",
        "pricingGroup.created",
        "pricingGroup.updated",
      ]
    }
  })
}

resource "aws_cloudwatch_event_target" "forward_partner_portal_events" {
  rule           = aws_cloudwatch_event_rule.partner_portal_events.name
  arn            = var.relay_event_bus_arn
  event_bus_name = var.odp_event_bus_arn
  role_arn       = aws_iam_role.forward_odp_events_to_main_account.arn
}

resource "aws_cloudwatch_event_rule" "bigcommerce_order_status_update" {
  name           = "bigcommerce-order-status-updated-events"
  event_bus_name = var.odp_event_bus_arn
  event_pattern = jsonencode({
    source = ["com.big-commerce"]
    detail = {
      data = {
        scope = [
          "store/order/statusUpdated",
        ]
      }
    }
  })
}

resource "aws_cloudwatch_event_target" "bigcommerce_order_status_update_events_queue" {
  rule           = aws_cloudwatch_event_rule.bigcommerce_order_status_update.name
  arn            = module.bigcommerce_order_status_update_events_queue.queue_arn
  event_bus_name = var.odp_event_bus_arn
  input_path     = "$.detail"
}

resource "aws_cloudwatch_event_rule" "order_created_record_order_service" {
  name           = "order-created-events-order-service"
  event_bus_name = var.odp_sf_event_bus_arn
  event_pattern = jsonencode({
    detail-type = ["Order_Created__e"]
  })
}

resource "aws_cloudwatch_event_target" "order_created_events_order_service_affiliates" {
  rule           = aws_cloudwatch_event_rule.order_created_record_order_service.name
  arn            = module.salesforce_order_events_attribute_capture_affiliate.queue_arn
  event_bus_name = var.odp_sf_event_bus_arn
  input_path     = "$.detail"
}

resource "aws_cloudwatch_event_rule" "qualified_opportunity_status_update_events" {
  name           = "opportunity-status-updated-to-qualified-events"
  event_bus_name = var.odp_sf_event_bus_arn
  event_pattern = jsonencode({
    detail-type = ["Opportunity_Status_Update__e"]
    detail = {
      payload = {
        Status__c = ["Qualified"]
      }
    }
  })
}

resource "aws_cloudwatch_event_target" "qualified_opportunity_status_update_events" {
  rule           = aws_cloudwatch_event_rule.qualified_opportunity_status_update_events.name
  arn            = module.salesforce_qualified_opportunity_reset_referral.queue_arn
  event_bus_name = var.odp_sf_event_bus_arn
  input_path     = "$.detail"
}
