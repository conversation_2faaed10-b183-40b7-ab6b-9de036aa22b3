resource "aws_secretsmanager_secret" "odp" {
  name                    = format("aws/%s/shared", local.identifier)
  description             = "Shared tokens and sensitive secrets used by ODP services."
  kms_key_id              = aws_kms_key.secrets.id
  recovery_window_in_days = 7
}

resource "aws_secretsmanager_secret_version" "this" {
  secret_id = aws_secretsmanager_secret.odp.id
  secret_string = jsonencode({
    BIGCOMMERCE_ACCESS_TOKEN    = "CHANGE_ME",
    SALESFORCE_AUTH_PRIVATE_KEY = "CHANGE ME",
    SALESFORCE_AUTH_CLIENT_ID   = "CHANGE ME",
    SALESFORCE_AUTH_USERNAME    = "CHANGE ME",
    JOTFORM_API_KEY             = "CHANGE ME"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}
