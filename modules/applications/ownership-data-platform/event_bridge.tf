module "odp_event_bridge_bus" {
  source = "./shared/event_bridge"
}

resource "aws_cloudwatch_event_bus_policy" "odp" {
  policy         = data.aws_iam_policy_document.odp_event_bridge_policy.json
  event_bus_name = module.odp_event_bridge_bus.name
}

data "aws_iam_policy_document" "odp_event_bridge_policy" {
  statement {
    sid     = "AllowPodPointToPutEvents"
    effect  = "Allow"
    actions = ["events:PutEvents"]
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.pod_point.account_id}:root"]
    }
    resources = ["*"]
  }
  statement {
    sid     = "AllowExperiencePutEvents"
    effect  = "Allow"
    actions = ["events:PutEvents"]
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.experience_account_id}:root"]
    }
    resources = ["*"]
  }
}

resource "aws_cloudwatch_event_bus" "salesforce" {
  name = "salesforce"
}

resource "aws_cloudwatch_event_bus_policy" "salesforce" {
  policy         = data.aws_iam_policy_document.salesforce_event_bridge_policy.json
  event_bus_name = aws_cloudwatch_event_bus.salesforce.name
}

data "aws_iam_policy_document" "salesforce_event_bridge_policy" {
  statement {
    sid     = "AllowSalesforceToPutEvents"
    effect  = "Allow"
    actions = ["events:PutEvents"]
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${var.salesforce_account_id}:root"]
    }
    resources = ["*"]
  }
}

resource "aws_cloudwatch_event_rule" "alerting" {
  for_each       = local.event_bridge_opsgenie_alerts
  name           = each.key
  event_bus_name = "default"
  event_pattern  = jsonencode(each.value)
}

resource "aws_cloudwatch_event_target" "alerting" {
  for_each       = local.event_bridge_opsgenie_alerts
  event_bus_name = "default"
  rule           = aws_cloudwatch_event_rule.alerting[each.key].name
  arn            = aws_sns_topic.opsgenie_cloudwatch_events.arn
}

module "forward_survey_completed_events_to_experience" {
  source = "./shared/event-bridge-forward-events"

  source_arn = module.odp_event_bridge_bus.arn
  target_arn = local.experience_event_bus_arn

  event_patterns = {
    survey_events = jsonencode({
      detail-type = ["Survey Submitted"]
    })
  }
}

resource "aws_cloudwatch_event_rule" "pod_point_default_bus_installation_completed_events" {
  provider = aws.pod-point

  name           = "odp-installation-completed-events-${var.environment}"
  event_bus_name = "default"

  event_pattern = jsonencode({
    source = ["com.odp.${var.environment}"]
    detail = {
      event = ["installation.completed"]
    },
    detail-type = ["Installation Event"]
  })
}

resource "aws_cloudwatch_event_target" "pod_point_default_bus_installation_completed_events_to_sns" {
  provider = aws.pod-point

  event_bus_name = "default"
  rule           = aws_cloudwatch_event_rule.pod_point_default_bus_installation_completed_events.name
  arn            = data.aws_sns_topic.pod_point_installation_events.arn
  input_path     = "$.detail"
}

module "pod_point_default_bus_vehicle_events_to_odp" {
  source = "./shared/event-bridge-forward-events"

  providers = {
    aws = aws.pod-point
  }

  event_patterns = {
    vehicle_events = jsonencode({
      detail-type = ["Vehicle Lifecycle Event"]
      source      = [var.vehicle_event_source]
      detail = {
        action = [
          "created",
          "updated",
          "deleted"
        ]
      },
    })
  }

  source_arn = data.aws_cloudwatch_event_bus.pod_point_default.arn
  target_arn = module.odp_event_bridge_bus.arn
}

data "aws_cloudwatch_event_bus" "pod_point_default" {
  provider = aws.pod-point

  name = "default"
}
