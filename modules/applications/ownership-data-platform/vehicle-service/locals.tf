locals {
  identifier = "vehicle-service"

  domain      = var.environment != "prod" ? "vehicles-api-${var.environment}" : "vehicles-api"
  domain_name = "${local.domain}.pod-point.com"

  cognito_domain = "pod-point-${local.domain}"

  api_gw_endpoint = "${aws_api_gateway_rest_api.this.id}.execute-api.${var.region}.amazonaws.com"

  api_gw_list_lambda_uri = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-west-1:${data.aws_caller_identity.this.account_id}:function:${module.list_vehicles.lambda_name}/invocations"

  // More: http://docs.aws.amazon.com/apigateway/latest/developerguide/api-gateway-control-access-using-iam-policies-to-invoke-api.html
  api_gw_list_vehicles_execute_api_arn = "arn:aws:execute-api:${var.region}:${data.aws_caller_identity.this.account_id}:${aws_api_gateway_rest_api.this.id}/*/${aws_api_gateway_method.list.http_method}${aws_api_gateway_resource.models.path}"

  dynamo_table_full_access_policy = {
    sid = "allowDynamoAccess"
    actions = [
      "dynamodb:*",
    ]
    resources = [
      aws_dynamodb_table.vehicles.arn
    ]
  }

  xray_write_policy = {
    sid = "allowXrayWritePolicy"
    actions = [
      "xray:PutTraceSegments",
      "xray:PutTelemetryRecords"
    ]
    resources = [
      "*"
    ]
  }

  invalidate_cloudfront_cache_policy = {
    sid = "InvalidateApiGatewayStageCache"
    actions = [
      "cloudfront:CreateInvalidation"
    ]
    resources = [
      aws_cloudfront_distribution.this.arn
    ]
  }

  read_vehicle_table_stream = {
    sid = "ReadDynamoVehicleTableStream"
    actions = [
      "dynamodb:GetRecords",
      "dynamodb:GetShardIterator",
      "dynamodb:DescribeStream",
      "dynamodb:ListStreams"
    ]
    resources = [
      aws_dynamodb_table.vehicles.stream_arn
    ]
  }
}
