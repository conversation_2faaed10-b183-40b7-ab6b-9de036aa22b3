resource "aws_route53_record" "this" {
  provider = aws.pod-point

  name    = local.domain_name
  type    = "A"
  zone_id = var.pod_point_com_hosted_zone

  alias {
    evaluate_target_health = true
    name                   = aws_cloudfront_distribution.this.domain_name
    zone_id                = aws_cloudfront_distribution.this.hosted_zone_id
  }

  depends_on = [
    aws_api_gateway_rest_api.this
  ]
}
