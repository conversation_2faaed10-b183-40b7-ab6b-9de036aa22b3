locals {
  identifier                = "odp"
  pod_point_com_domain_name = "pod-point.com"
  pod_point_com_hosted_zone = "ZI1YF8KE9MFAW"

  event_bridge_opsgenie_alerts = {

    ecs-task-deployment-failure = {
      source : ["aws.ecs"],
      detail-type : ["ECS Deployment State Change"],
      detail : {
        eventType = ["ERROR"]
      }
    }

    #   This rule can be used to trigger an alert when an ECS task is stopped due
    #   to a spot interruption. This is not enabled, but left here for reference.

    #    ecs-task-change-spot-termination = {
    #      source : ["aws.ecs"],
    #      detail-type : ["ECS Task State Change"],
    #      detail : {
    #        stopCode = ["SpotInterruption"]
    #      }
    #    }

    #   This rule can be used to trigger an alert when an ECS task fails to be placed due
    #   to lack of spot capacity. This is not enabled, but left here for reference.

    #     ecs-task-placement-failure-no-spot-capacity = {
    #       source : ["aws.ecs"],
    #       detail-type : ["ECS Service Action"],
    #       detail : {
    #         eventName = ["SERVICE_TASK_PLACEMENT_FAILURE"],
    #         reason : ["RESOURCE:FARGATE"]
    #       }
    #     }
  }

  unit_link_verification_app_name = "unit-link-verification-app"
  unit_link_verification_port     = 80
  unit_link_verification_domain   = var.environment == "prod" ? format("link.%s", local.pod_point_com_domain_name) : format("link-%s.%s", var.environment, local.pod_point_com_domain_name)

  cart_admin_app_name = "cart-admin-app"
  cart_admin_port     = 80
  cart_admin_domain   = var.environment == "prod" ? format("cart-admin.%s", local.pod_point_com_domain_name) : format("cart-admin-%s.%s", var.environment, local.pod_point_com_domain_name)

  cluster_name = "ownership-data-platform"

  linking_app_identifier = "${local.unit_link_verification_app_name}-${var.environment}"
  cart_app_identifier    = "${local.cart_admin_app_name}-${var.environment}"

  main_account_default_bus = "arn:aws:events:eu-west-1:************:event-bus/default"

  kms_admins = flatten([
    data.aws_iam_session_context.current.issuer_arn,
    var.kms_admins
  ])

  additional_policy_statements_get_shared_secrets = [
    {
      sid       = "AllowSharedSecretsManagerAccess",
      actions   = ["secretsmanager:GetSecretValue"],
      resources = [aws_secretsmanager_secret.odp.arn]
    },
    {
      sid       = "AllowKMSDecrypt"
      actions   = ["kms:Decrypt"]
      resources = [aws_kms_key.secrets.arn]
    }
  ]

  additional_policy_statements_assume_read_ecommerce_dynamo = [
    {
      sid       = "AssumeReadEcommerceDynamo"
      actions   = ["sts:AssumeRole"]
      resources = [aws_iam_role.allow_read_ecommerce_dynamo.arn]
    }
  ]

  additional_policy_statements_metric_data = [
    {
      sid = "AllowPutMetricData"
      actions = [
        "cloudwatch:PutMetricData",
      ]
      // custom metric won't exist until the first time time the `PutMetricDataCommand` is executed
      resources = ["*"]
    }
  ]

  // aws_iam_role.my_role.arn seems to be returning the name in some instances
  allow_read_ecommerce_dynamo_arn = "arn:aws:iam::${data.aws_caller_identity.pod_point.account_id}:role/${aws_iam_role.allow_read_ecommerce_dynamo.name}"

  bigcommerce_access_token_secret_arn = format("%s:%s::", aws_secretsmanager_secret.odp.arn, "BIGCOMMERCE_ACCESS_TOKEN")
  jotform_api_key_secret_arn          = format("%s:%s::", aws_secretsmanager_secret.odp.arn, "JOTFORM_API_KEY")

  sf_auth_private_key_arn = format("%s:%s::", aws_secretsmanager_secret.odp.arn, "SALESFORCE_AUTH_PRIVATE_KEY")
  sf_auth_client_id_arn   = format("%s:%s::", aws_secretsmanager_secret.odp.arn, "SALESFORCE_AUTH_CLIENT_ID")
  sf_auth_username_arn    = format("%s:%s::", aws_secretsmanager_secret.odp.arn, "SALESFORCE_AUTH_USERNAME")

  experience_event_bus_arn = "arn:aws:events:${var.region}:${var.experience_account_id}:event-bus/experience-event-bus"
}
