module "cart_admin_ecs_service" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "11.1.1"

  service_type                                = "rolling"
  identifier                                  = local.cart_admin_app_name
  vpc_id                                      = module.vpc.vpc_id
  subnet_ids                                  = module.vpc.private_subnets_ids
  cluster_name                                = module.cluster.name
  cluster_arn                                 = module.cluster.arn
  pipeline_role_name                          = module.cluster.github_role_name
  cpu                                         = "256"
  memory                                      = "512"
  capacity_fargate_base                       = var.sync_tasks_use_spot_instances ? 0 : 1
  capacity_fargate_weight                     = var.sync_tasks_use_spot_instances ? 0 : 1
  capacity_fargate_spot_base                  = var.sync_tasks_use_spot_instances ? 1 : 0
  capacity_fargate_spot_weight                = var.sync_tasks_use_spot_instances ? 1 : 0
  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.shared_secret_access_policy.json

  # Image tag in following definition is a placeholder and should never exist or be deployed.
  # Deployments are only triggered from the CD pipeline.
  container_definitions = jsonencode([
    {
      name : local.cart_admin_app_name,
      image : "${var.build_account_id}.dkr.ecr.${var.region}.amazonaws.com/${local.cart_admin_app_name}:TERRAFORM",
      essential : true,
      networkMode : "awsvpc",
      portMappings : [
        {
          "containerPort" : local.cart_admin_port,
          "hostPort" : local.cart_admin_port
        }
      ],
      linuxParameters : {
        "initProcessEnabled" : true
      },
      environment : [
        {
          "name" : "STORE_HASH",
          "value" : var.bigcommerce_store_hash
        },
        {
          "name" : "SENTRY_DSN",
          "value" : var.sentry_keys.cart_admin_app
        }
      ],
      secrets : [
        {
          "name" : "ACCESS_TOKEN",
          "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.odp.arn, "BIGCOMMERCE_ACCESS_TOKEN")
        },
      ],
      logConfiguration : {
        logDriver : "awslogs",
        options : {
          "awslogs-group" : "/ecs/${local.cart_admin_app_name}",
          "awslogs-region" : var.region,
          "awslogs-stream-prefix" : "ecs"
        }
      }
    }
  ])

  enable_auto_scaling      = true
  use_default_step_scaling = false

  custom_step_scaling = [{
    name = "CPUUtilization-high"
    alarm = {
      threshold = 60 // crossing the threshold triggers a scale out
      metrics = [
        {
          id          = "metric_high"
          return_data = true
          metric = {
            namespace   = "AWS/ECS"
            metric_name = "CPUUtilization"
            period      = 60
            stat        = "Maximum"
            dimensions = {
              ClusterName = module.cluster.name
              ServiceName = local.cart_admin_app_name
            }
          }
        }
      ]
    }
    scale_out_policy = {
      cooldown             = 120
      gradual_lower_bound  = 0
      gradual_upper_bound  = 20
      gradual_adjustment   = 1
      critical_lower_bound = 20
      critical_adjustment  = 3
    }
    scale_in_policy = {
      cooldown    = 120
      upper_bound = 0
      adjustment  = -5
    }
  }]

  scaling_min_capacity = 1
  scaling_max_capacity = 2

  load_balancing_configuration = [
    {
      target_group_arn = module.cart_admin_alb.target_group_arns[0]
      container_name   = local.cart_admin_app_name
      container_port   = local.cart_admin_port
    }
  ]

  additional_kms_administrators = local.kms_admins

  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"
      ]
    }]
  }]

  depends_on = [
    module.cluster,
    module.cart_admin_alb
  ]
}

data "aws_iam_policy_document" "shared_secret_access_policy" {
  statement {
    sid     = "RetrieveSecretManagerSecretValues"
    actions = ["secretsmanager:GetSecretValue"]
    resources = [
      aws_secretsmanager_secret.odp.arn,
    ]
  }

  statement {
    sid     = "AllowKMSBackendKeyDecrypt"
    actions = ["kms:Decrypt"]
    resources = [
      aws_kms_key.secrets.arn
    ]
  }
}

moved {
  from = module.cart_admin_app_ingress.aws_security_group_rule.this
  to   = aws_security_group_rule.cart_admin_app_ingress
}

resource "aws_security_group_rule" "cart_admin_app_ingress" {
  type                     = "ingress"
  protocol                 = "TCP"
  description              = "Access permitted from the load balancer."
  from_port                = 80
  to_port                  = local.cart_admin_port
  source_security_group_id = module.cart_admin_alb.security_group_id
  security_group_id        = module.cart_admin_ecs_service.security_group_id
}

moved {
  from = module.cart_admin_app_egress.aws_security_group_rule.this
  to   = aws_security_group_rule.cart_admin_app_egress
}

resource "aws_security_group_rule" "cart_admin_app_egress" {
  description       = "Permit all egress traffic."
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = module.cart_admin_ecs_service.security_group_id
}

module "cart_admin_alb" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws"
  version = "3.0.0"

  load_balancer_name = local.cart_admin_app_name
  enable_internal_lb = true
  load_balancer_type = "application"

  vpc_id                = module.vpc.vpc_id
  load_balancer_subnets = module.vpc.private_subnets_ids

  access_logs = {
    enable_logs          = var.enable_load_balancer_access_logs
    create_bucket_policy = true
  }

  target_groups = [
    {
      name                 = local.cart_admin_app_name
      backend_protocol     = "HTTP"
      backend_port         = local.cart_admin_port
      target_type          = "ip"
      deregistration_delay = 60
      vpc_id               = module.vpc.vpc_id

      health_check = {
        enabled             = true
        interval            = 10
        path                = "/"
        port                = "traffic-port"
        healthy_threshold   = 3
        unhealthy_threshold = 2
        timeout             = 5
        protocol            = "HTTP"
        matcher             = "200"
      }
    }
  ]

  http_tcp_listeners = [
    {
      port        = 80
      protocol    = "HTTP"
      action_type = "redirect"
      redirect = {
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  ]

  https_listeners = [
    {
      port               = 443
      protocol           = "HTTPS"
      certificate_arn    = module.acm.arn
      target_group_index = 0
    }
  ]

  security_group_name        = "${local.cart_admin_app_name}-lb"
  security_group_description = "Security Group for the ${local.cart_admin_app_name} load balancer."

  security_group_ingress_rules = {
    "port_http_all" = {
      description = "HTTP permitted from the Internet."
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }

    "port_https_all" = {
      description = "HTTPS permitted from the Internet."
      from_port   = 443
      to_port     = 443
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }
}

resource "aws_route53_record" "cart_admin_load_balancer" {
  provider = aws.pod-point

  zone_id         = local.pod_point_com_hosted_zone
  type            = "A"
  name            = local.cart_admin_domain
  allow_overwrite = false

  alias {
    name                   = module.cart_admin_alb.load_balancer_dns_name
    zone_id                = module.cart_admin_alb.load_balancer_zone_id
    evaluate_target_health = true
  }
}
