openapi: 3.0.1
info:
  title: bmw-referral
  version: 0.0.1
paths:
  /referrals/bmw:
    post:
      security:
        - WebhookTokenAuthoriser: []
      responses:
        "200":
          $ref: '#/components/responses/200'
      x-amazon-apigateway-integration:
        type: aws
        credentials: ${integration_iam_role_arn}
        httpMethod: POST
        uri: arn:aws:apigateway:${region}:events:action/PutEvents
        responses:
          default:
            statusCode: "400"
            responseTemplates:
              application/json: |
                ${indent(16, bmw_referral_failure_template)}
          "200":
            statusCode: "200"
            responseTemplates:
              application/json: |
                ${indent(16, bmw_referral_success_template)}
        requestTemplates:
          application/json: |
            ${indent(12, referral_event_template)}
        passthroughBehavior: when_no_templates
components:
  securitySchemes:
    WebhookTokenAuthoriser:
      type: "apiKey"
      name: "Authorization"
      in: "header"
      x-amazon-apigateway-authtype: "custom"
      x-amazon-apigateway-authorizer:
        authorizerUri: "${lambda_invoke_arn}"
        authorizerCredentials: "${authorizer_credentials}"
        authorizerResultTtlInSeconds: 300
        type: "token"
  responses:
    "200":
      description: 200 response
      content: {}
