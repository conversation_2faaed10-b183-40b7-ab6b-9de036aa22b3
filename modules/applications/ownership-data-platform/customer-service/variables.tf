variable "odp_event_bus_name" {
  description = "The name of the odp event bus to associate with the rule"
}

variable "odp_event_bus_arn" {
  description = "The arn of the odp event bus"
}

variable "odp_sf_event_bus_arn" {
  description = "The ARN of the SF bus that lives within the ODP module"
}

variable "main_podpoint_account_bus_arn" {
  description = "The arn of the main podpoint account event bus"
}

variable "cloudwatch_alarm_topic_arn" {
  description = "The alarm topic arn"
}

variable "odp_shared_secret_arn" {
  description = "The arn of the odp shared secret"
}

variable "environment" {
  description = "The environment name being deployed to."
}

variable "cluster_name" {
  description = "The name of the cluster to deploy tasks to"
}

variable "cluster_arn" {
  description = "The arn of the cluster to deploy tasks to"
}

variable "cluster_github_role_name" {
  description = "The role name on github"
}

variable "vpc_id" {
  description = "The id of the vpc"
}

variable "subnet_ids" {
  description = "The ids of the subnets"
}

variable "build_account_id" {
  description = "The id of the build account"
}

variable "pod_point_account_id" {
  description = "The id of the Pod Point account"
}

variable "region" {
  description = "The aws region"
}

variable "kms_admins" {
  description = "List of ARNs to be admins for the KMS key"
}

variable "bigcommerce_store_hash" {
  description = "The store hash of the bigcommerce store"
}

variable "bigcommerce_access_token_secret_arn" {
  description = "The secret ARN of the bigcommerce access token, including key name. e.g. `arn:aws:secretsmanager:region:aws_account_id:secret:secret_name-AbCdEf`"
}

variable "hubspot_api_base_url" {
  description = "The base URL for the HubSpot API"
}

variable "hubspot_consent_workflow_id" {
  description = "Id of Hubspot Marketing consent workflow"
}

variable "event_processor_min_workers" {
  description = "The minimum number of queue processing instances."
  default     = 1
}

variable "event_processor_use_spot_capacity" {
  default = false
}

variable "additional_ecs_task_execution_policy_statements" {
  description = "Additional statements to add to the Customer Service apps task execution policy"
  type = object({
    event_processor = list(object({
      sid       = string
      actions   = list(string)
      resources = list(string)
    }))
  })
  default = {
    event_processor = []
  }
}

variable "additional_lambda_execution_policy_statements" {
  description = "Additional statements to add to the Customer Service lambda execution policy"
  type = object({
    hubspot_event_aggregator = list(object({
      sid       = string
      actions   = list(string)
      resources = list(string)
    }))
  })
  default = {
    hubspot_event_aggregator = []
  }
}

variable "additional_ecs_task_policy_statements" {
  description = "Additional statements to add to the Customer Service apps task policy"
  type = object({
    event_processor = list(object({
      sid       = string
      actions   = list(string)
      resources = list(string)
    }))
  })
  default = {
    event_processor = []
  }
}

variable "event_processor_sentry_dsn" {
  description = "DSN for Sentry events"
}

variable "sync_event_aggregator_sentry_dsn" {
  description = "DSN for Sentry events"
}

variable "hubspot_contact_event_aggregator_sentry_dsn" {
  description = "DSN for Sentry events"
}

variable "sf_auth_audience" {
  description = "URL to use for retrieving auth tokens from Salesforce"
}

variable "sf_instance_url" {
  description = "Salesforce instance URL (API base URL)"
}

variable "sf_auth_private_key_arn" {
  description = "The secret ARN of the salesforce auth private key"
}

variable "sf_auth_client_id_arn" {
  description = "The secret ARN of the salesforce auth client id"
}

variable "sf_auth_username_arn" {
  description = "The secret ARN of the salesforce auth username"
}

variable "send_salesforce_updates" {
  description = "Toggle to send updates to Salesforce or Request Bin"
}

variable "request_bin_base_url" {
  description = "Base URL of the Request Bin"
}

variable "ecommerce_dynamo_table_name" {
  description = "The table name for the commerce service dynamo table"
}

variable "ecommerce_dynamo_table_arn" {
  description = "The arn of the table for the commerce service dynamo table"
}

variable "record_customer_sync_metric" {
  description = "Flag to indicate if the app should record the customer sync metric."
}

variable "ecommerce_dynamo_role_name" {
  description = "Role ARN to assume to access the Dynamo table in the main account."
}

variable "bigcommerce_uk_channel_id" {
  description = "Id of the UK channel in BigCommerce."
}

variable "bigcommerce_roi_channel_id" {
  description = "Id of the ROI channel in BigCommerce."
}

variable "bigcommerce_pod_uk_channel_id" {
  description = "Id of the Po channel in BigCommerce."
}

variable "enable_event_processor_auto_scaling" {
  default = true
}

variable "driver_account_api_endpoint" {
  description = "The endpoint for the driver account API"
}
