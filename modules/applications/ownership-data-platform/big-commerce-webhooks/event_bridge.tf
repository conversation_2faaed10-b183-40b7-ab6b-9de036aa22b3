resource "aws_cloudwatch_event_rule" "all" {
  name        = "bigcommerce-events"
  description = "capture all bigcommerce events for logging"
  event_pattern = jsonencode({
    source = [local.event_source]
  })
  event_bus_name = var.event_bridge_bus.name
  state          = "ENABLED"
}

resource "aws_cloudwatch_log_group" "event_logging" {
  name              = "/aws/events/bigcommerce"
  retention_in_days = 365
}

resource "aws_cloudwatch_event_target" "event_logging" {
  rule           = aws_cloudwatch_event_rule.all.name
  arn            = aws_cloudwatch_log_group.event_logging.arn
  event_bus_name = var.event_bridge_bus.name
  target_id      = "event-logging"
}
