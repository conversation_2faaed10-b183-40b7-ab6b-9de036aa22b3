output "invoke_urls" {
  value = {
    cart     = format("%s/cart", aws_api_gateway_stage.this.invoke_url)
    customer = format("%s/customer", aws_api_gateway_stage.this.invoke_url)
    order    = format("%s/order", aws_api_gateway_stage.this.invoke_url)
  }
}

output "lambda_execution_role_arn" {
  value = module.bigcommerce_webhook_authoriser.lambda_role_arn
}

output "codebuild_role_arn" {
  value = module.codebuild.role_arn
}
