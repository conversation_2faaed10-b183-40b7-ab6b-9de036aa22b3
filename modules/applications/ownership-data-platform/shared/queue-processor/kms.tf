resource "aws_kms_key" "this" {
  description             = format("Used for encrypting %s secrets.", var.identifier)
  policy                  = data.aws_iam_policy_document.kms.json
  deletion_window_in_days = 7
}

resource "aws_kms_alias" "this" {
  name          = format("alias/%s/secrets", var.identifier)
  target_key_id = aws_kms_key.this.key_id
}

data "aws_iam_policy_document" "kms" {
  statement {
    sid       = "EnableAccountAdministration"
    actions   = ["kms:*"]
    resources = ["*"]

    principals {
      type        = "AWS"
      identifiers = var.kms_admins
    }
  }

  statement {
    sid       = "AllowKMSDecrypt"
    actions   = ["kms:Decrypt"]
    resources = ["*"]

    principals {
      type = "AWS"
      identifiers = [
        module.fargate_service.execution_role_arn,
      ]
    }
  }

  statement {
    sid = "Permit Break Glass"
    actions = [
      "kms:*"
    ]
    resources = ["*"]

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    condition {
      test = "ArnLike"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"
      ]
      variable = "aws:PrincipalArn"
    }
  }
}
