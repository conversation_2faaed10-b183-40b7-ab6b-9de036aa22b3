module "codebuild" {
  source  = "terraform-enterprise.pod-point.com/technology/codebuild/aws"
  version = "1.0.1"

  namespace   = "${var.identifier}-e2e"
  name        = "smoke-tests"
  description = "${var.identifier} smoke tests"

  build_timeout          = 15
  concurrent_build_limit = 6

  repo_name = "odp"

  vpc_id          = var.vpc_id
  subnets_ids     = var.subnet_ids
  privileged_mode = true

  environment_variables = concat(
    [for name, value in var.codebuild_environment_variables_plain : { name : name, value : value, type : "PLAINTEXT" }],
    [for name, secretArn in var.codebuild_environment_variables_secrets : { name : name, value : secretArn, type : "SECRETS_MANAGER" }]
  )
}

locals {
  has_additional_codebuild_policies = length(var.codebuild_additional_policy_statements) > 0 ? 1 : 0
}

data "aws_iam_policy_document" "code_build_smoke_test_policy" {
  count = local.has_additional_codebuild_policies

  dynamic "statement" {
    for_each = var.codebuild_additional_policy_statements
    content {
      sid       = statement.value.sid
      actions   = statement.value.actions
      resources = statement.value.resources
    }
  }
}
resource "aws_iam_policy" "code_build_smoke_test_policy" {
  count = local.has_additional_codebuild_policies

  name   = "codebuild-policy-${var.identifier}"
  policy = data.aws_iam_policy_document.code_build_smoke_test_policy[0].json
}

resource "aws_iam_role_policy_attachment" "codebuild_smoke_test_additional_policy_attachments" {
  count = local.has_additional_codebuild_policies

  role       = module.codebuild.role_id
  policy_arn = aws_iam_policy.code_build_smoke_test_policy[0].arn
}
