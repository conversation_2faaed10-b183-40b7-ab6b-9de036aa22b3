module "nlb" {
  source  = "terraform-aws-modules/alb/aws"
  version = "~> 8.7.0"

  name               = var.identifier
  load_balancer_type = "network"
  internal           = true

  vpc_id  = var.vpc_id
  subnets = var.subnet_ids

  target_groups = [
    {
      name                 = var.identifier
      backend_protocol     = "TCP"
      backend_port         = var.container_port
      target_type          = "ip"
      deregistration_delay = 30
      health_check = {
        enabled             = true
        interval            = var.health_check_config.interval
        path                = var.health_check_config.path
        healthy_threshold   = var.health_check_config.healthy_threshold
        unhealthy_threshold = var.health_check_config.unhealthy_threshold
        timeout             = var.health_check_config.timeout
        port                = var.container_port
      }
    }
  ]

  http_tcp_listeners = [
    {
      port               = var.lb_port
      protocol           = "TCP"
      target_group_index = 0
    }
  ]

}

data "aws_network_interface" "lb" {
  for_each = toset(var.subnet_ids)

  filter {
    name   = "description"
    values = ["ELB ${module.nlb.lb_arn_suffix}"]
  }

  filter {
    name   = "subnet-id"
    values = [each.value]
  }
}
