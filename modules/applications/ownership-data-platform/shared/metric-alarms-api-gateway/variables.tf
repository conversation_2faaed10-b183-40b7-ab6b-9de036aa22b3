variable "alarm_identifier" {
  description = "The identifier to prepend to alarm names and descriptions"
}

variable "api_gateway_name" {
  description = "The name of the api gateway"
}

variable "actions_enabled" {
  description = "Whether or not actions should be enabled for these alarms"
  default     = true
}

variable "action_topic_arn" {
  description = "The topic arn for alarm actions"
}

variable "latency_threshold" {
  description = "The threshold for the latency alarm, in milliseconds"
  type        = number
  default     = 2000
}

variable "http_5xx_threshold" {
  description = "The threshold for the 5xx error alarm"
  type        = number
  default     = 0
}

variable "http_4xx_threshold" {
  description = "The threshold for the 4xx error alarm"
  type        = number
  default     = 0
}
