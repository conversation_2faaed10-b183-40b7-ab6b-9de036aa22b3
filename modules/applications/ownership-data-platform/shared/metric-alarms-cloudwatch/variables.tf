variable "alarm_identifier" {
  description = "The identifier to prepend to alarm names and descriptions"
}

variable "cloudfront_distribution_id" {
  description = "The id of the cloudfront distribution to monitor"
}

variable "actions_enabled" {
  description = "Whether or not actions should be enabled for these alarms"
  default     = true
}

variable "action_topic_arn" {
  description = "The topic arn for alarm actions"
}

variable "origin_latency_threshold" {
  description = "The threshold for the latency alarm, in milliseconds"
  type        = number
  default     = 3000
}

variable "error_rates" {
  description = "The error rates for each error type to alarm on"
  default = {
    401 = 0
    403 = 0
    404 = 0
    502 = 0
    503 = 0
    504 = 0
  }
}
