data "aws_iam_policy_document" "permit_event_bridge_publish" {
  statement {
    sid    = "PermitEventBridgeSendMessage"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }

    actions   = ["sqs:SendMessage"]
    resources = [aws_sqs_queue.this.arn]

    condition {
      test     = "ArnEquals"
      variable = "aws:SourceArn"
      values   = [var.permit_publish_from_rule_arn]
    }
  }
}
