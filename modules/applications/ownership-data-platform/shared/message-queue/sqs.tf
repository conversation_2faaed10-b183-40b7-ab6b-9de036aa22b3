resource "aws_sqs_queue" "this" {
  name                        = var.fifo ? "${var.identifier}.fifo" : var.identifier
  fifo_queue                  = var.fifo
  receive_wait_time_seconds   = 20
  delay_seconds               = var.delay_seconds
  visibility_timeout_seconds  = var.visibility_timeout_seconds
  content_based_deduplication = var.fifo ? var.content_based_deduplication : false
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.dlq.arn
    maxReceiveCount     = var.max_receive_count,
  })
}

resource "aws_sqs_queue" "dlq" {
  name                      = var.fifo ? "${var.identifier}-dlq.fifo" : "${var.identifier}-dlq"
  fifo_queue                = var.fifo
  receive_wait_time_seconds = 20
  message_retention_seconds = 1209600
}

resource "aws_sqs_queue_redrive_allow_policy" "dlq_policy" {
  queue_url = aws_sqs_queue.dlq.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.this.arn]
  })
}

resource "aws_sqs_queue_policy" "this" {
  count     = var.attach_queue_policy ? 1 : 0
  queue_url = aws_sqs_queue.this.url
  policy    = var.queue_policy
}

resource "aws_sqs_queue_policy" "from_event_bridge" {
  count     = var.attach_queue_policy == false && var.allow_permit_publish_from_rule == true ? 1 : 0
  queue_url = aws_sqs_queue.this.url
  policy    = data.aws_iam_policy_document.permit_event_bridge_publish.json
}
