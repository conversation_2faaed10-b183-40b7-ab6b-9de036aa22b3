resource "aws_secretsmanager_secret" "this" {
  name                    = format("aws/%s", var.identifier)
  description             = format("Sensitive secrets used by %s.", var.identifier)
  kms_key_id              = aws_kms_key.this.id
  recovery_window_in_days = 7
}

resource "aws_secretsmanager_secret_version" "this" {
  secret_id = aws_secretsmanager_secret.this.id
  secret_string = jsonencode({
    for item in var.environment_variables_secret_placeholders :
    item => "CHANGEME"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}
