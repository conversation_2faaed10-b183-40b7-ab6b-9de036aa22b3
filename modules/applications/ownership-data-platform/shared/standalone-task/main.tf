module "fargate" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/standalone-task"
  version = "11.1.1"

  identifier         = var.identifier
  pipeline_role_name = var.cluster_github_role_name
  vpc_id             = var.vpc_id
  cpu                = var.resources.cpu
  memory             = var.resources.memory

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  #  attach_custom_ecs_task_iam_policy           = true
  #  ecs_task_custom_policy                      = data.aws_iam_policy_document.custom_ecs_task_container_policy.json

  container_definition = jsonencode([
    {
      name : var.identifier
      image : "${var.build_account_id}.dkr.ecr.${var.region}.amazonaws.com/${var.identifier}:latest",
      essential : true,
      networkMode : "awsvpc",
      linuxParameters : {
        "initProcessEnabled" : true
      },
      portMappings : [],
      environment : [for key, value in var.environment_variables_plain : { name : key, value : value }],
      secrets : concat(
        [for key in var.environment_variables_secret_placeholders : { name : key, valueFrom : format("%s:%s::", aws_secretsmanager_secret.this.arn, key) }],
        [for key, secretArn in var.environment_variables_secrets : { name : key, valueFrom : secretArn }]
      ),
      logConfiguration : {
        logDriver : "awslogs",
        options : {
          "awslogs-group" : "/ecs/standalone/${var.identifier}",
          "awslogs-region" : var.region,
          "awslogs-stream-prefix" : "ecs"
        }
      }
    }
  ])

  kms_additional_policy_statements = [{
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*AWSReservedSSO_PP-AdminBreakglass*"
      ]
    }]
  }]
}

moved {
  from = module.fargate_egress.aws_security_group_rule.this
  to   = aws_security_group_rule.fargate_egress
}

resource "aws_security_group_rule" "fargate_egress" {
  description       = "Permit all egress traffic."
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = module.fargate.security_group_id
}
