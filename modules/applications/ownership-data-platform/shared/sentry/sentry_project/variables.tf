variable "sentry_organisation_slug" {
  type = string
}

variable "sentry_team_slug" {
  type = string
}

variable "slack_workspace_id" {
  type = string
}

variable "name" {
  type = string
}

variable "platform" {
  type = string
}

variable "slack_channel_config_production" {
  type = object({
    channel_id : string
    channel_name : string
  })
}

variable "slack_channel_config_non_production" {
  type = object({
    channel_id : string
    channel_name : string
  })
}
