module "survey_service" {
  source = "./survey-service"

  environment            = var.environment
  odp_event_bus_name     = module.odp_event_bridge_bus.name
  jotform_event_bus_name = module.jotform_webhooks.event_bus_name

  build_account_id           = var.build_account_id
  cluster_arn                = module.cluster.arn
  cluster_github_role_name   = module.cluster.github_role_name
  cluster_name               = module.cluster.name
  kms_admins                 = local.kms_admins
  region                     = var.region
  subnet_ids                 = module.vpc.private_subnets_ids
  vpc_id                     = module.vpc.vpc_id
  cloudwatch_alarm_topic_arn = data.aws_sns_topic.opsgenie.arn
  submission_sentry_dsn      = var.sentry_keys.survey_service_submission_event_processor
  asset_sentry_dsn           = var.sentry_keys.survey_service_asset_event_processor
  pod_point_account_id       = data.aws_caller_identity.pod_point.account_id
  odp_event_bus_arn          = module.odp_event_bridge_bus.arn

  odp_bus_events_log_group_arn = aws_cloudwatch_log_group.odp_bus_events.arn

  event_processor_use_spot_capacity = var.async_tasks_use_spot_instances

  enable_event_processor_auto_scaling = var.enable_event_processor_auto_scaling

  sf_auth_private_key_arn = local.sf_auth_private_key_arn
  sf_auth_client_id_arn   = local.sf_auth_client_id_arn
  sf_auth_username_arn    = local.sf_auth_username_arn
  sf_auth_audience        = var.sf_auth_audience
  sf_instance_url         = var.sf_instance_url

  jotform_api_key_secret_arn = local.jotform_api_key_secret_arn
  jotform_team_id            = var.jotform_team_id

  additional_ecs_task_execution_policy_statements = local.additional_policy_statements_get_shared_secrets

  survey_service_asset_uploaded_event_source      = var.survey_service_asset_uploaded_event_source
  survey_service_asset_uploaded_event_detail_type = var.survey_service_asset_uploaded_event_detail_type
}
