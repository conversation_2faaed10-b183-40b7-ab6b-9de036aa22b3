module "jotform_webhook_authoriser" {
  source = "../shared/lambda"

  identifier       = "jotform-webhook-authoriser"
  build_account_id = var.build_account_id
  environment_variables = {
    SENTRY_DSN                 = var.authoriser_sentry_dsn
    ENVIRONMENT                = var.environment
    WEBHOOK_VERIFICATION_TOKEN = random_password.webhook_verification_token.result
  }
  additional_policy_statements = [
    {
      sid     = "AllowKMSBackendKeyDecrypt"
      actions = ["kms:Decrypt"]
      resources = [
        var.kms_key_arn
      ]
    }
  ]
}
