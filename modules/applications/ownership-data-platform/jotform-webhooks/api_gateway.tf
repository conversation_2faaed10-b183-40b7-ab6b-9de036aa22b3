resource "aws_api_gateway_rest_api" "this" {
  name = local.identifier

  body = templatefile("${path.module}/apispec.yaml", {
    lambda_invoke_arn        = module.jotform_webhook_authoriser.lambda_invoke_arn
    authorizer_credentials   = aws_iam_role.invoke_authoriser_lambda.arn
    integration_iam_role_arn = aws_iam_role.api_gateway_put_events_to_event_bridge.arn
    region                   = var.region
    response_200_template    = file("${path.module}/templates/event_bridge/200_response.vtl")
    request_template = templatefile("${path.module}/templates/event_bridge/put_events_request.vtl", {
      bus_name    = aws_cloudwatch_event_bus.jotform.name
      source      = local.event_source
      detail_type = local.event_detail_type
    })
  })

  endpoint_configuration {
    types = ["REGIONAL"]
  }
}

resource "aws_cloudwatch_log_group" "access_logs" {
  name = "/aws/apigw/access-logs/${local.identifier}"
}

resource "aws_api_gateway_stage" "this" {
  deployment_id = aws_api_gateway_deployment.this.id
  rest_api_id   = aws_api_gateway_rest_api.this.id
  stage_name    = var.environment
  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.access_logs.arn
    format          = <<EOF
      {"requestTime":"$context.requestTime","requestId":"$context.requestId","httpMethod":"$context.httpMethod","path":"$context.path","resourcePath":"$context.resourcePath","status":$context.status,"responseLatency":$context.responseLatency,"xrayTraceId":"$context.xrayTraceId","integrationRequestId":"$context.integration.requestId","functionResponseStatus":"$context.integration.status","integrationLatency":"$context.integration.latency","integrationServiceStatus":"$context.integration.integrationStatus","authorizeStatus":"$context.authorize.status","ip":"$context.identity.sourceIp","userAgent":"$context.identity.userAgent","userArn":"$context.identity.userArn","authError":"$context.authorize.error"}
    EOF
  }
}

resource "aws_api_gateway_deployment" "this" {
  rest_api_id = aws_api_gateway_rest_api.this.id

  triggers = {
    redeployment = sha1(jsonencode([
      filesha256("${path.module}/api_gateway.tf"),
      filesha256("${path.module}/apispec.yaml"),
      aws_api_gateway_rest_api.this.body
    ]))
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_api_gateway_method_settings" "this" {
  rest_api_id = aws_api_gateway_rest_api.this.id
  stage_name  = aws_api_gateway_stage.this.stage_name
  method_path = "*/*"

  settings {
    metrics_enabled = true
    logging_level   = var.api_gw_logging_level
  }
}

resource "aws_iam_role" "invoke_authoriser_lambda" {
  name               = "jotform_api_gateway_auth_invocation"
  assume_role_policy = data.aws_iam_policy_document.api_gw_assume_role.json
}

data "aws_iam_policy_document" "invoke_authoriser_lambda" {
  statement {
    effect    = "Allow"
    actions   = ["lambda:InvokeFunction"]
    resources = [module.jotform_webhook_authoriser.lambda_arn]
  }
}

resource "aws_iam_policy" "invoke_authoriser_lambda" {
  policy = data.aws_iam_policy_document.invoke_authoriser_lambda.json
}

resource "aws_iam_role_policy_attachment" "invoke_authoriser_lambda" {
  policy_arn = aws_iam_policy.invoke_authoriser_lambda.arn
  role       = aws_iam_role.invoke_authoriser_lambda.name
}
