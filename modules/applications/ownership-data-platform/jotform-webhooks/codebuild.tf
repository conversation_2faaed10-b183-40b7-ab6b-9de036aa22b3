module "codebuild" {
  source  = "terraform-enterprise.pod-point.com/technology/codebuild/aws"
  version = "1.0.1"

  namespace   = "jotform-webhook-authoriser-e2e"
  name        = "smoke-tests"
  description = "${local.identifier} smoke tests"

  build_timeout          = 15
  concurrent_build_limit = 6

  repo_name = "odp"

  vpc_id          = var.codebuild_vpc_config.id
  subnets_ids     = var.codebuild_vpc_config.subnet_ids
  privileged_mode = true

  environment_variables = [
    {
      name  = "VERIFICATION_TOKEN"
      value = "${aws_secretsmanager_secret.dynamic.arn}:${local.webhook_token_key}"
      type  = "SECRETS_MANAGER"
    },
    {
      name  = "BASE_URL"
      value = aws_api_gateway_stage.this.invoke_url
      type  = "PLAINTEXT"
    }
  ]
}

data "aws_iam_policy_document" "code_build_smoke_test_policy" {

  statement {
    sid     = "RetrieveSecretManagerSecretValues"
    actions = ["secretsmanager:GetSecretValue"]
    resources = [
      aws_secretsmanager_secret.dynamic.arn
    ]
  }

  statement {
    sid     = "AllowKMSBackendKeyDecrypt"
    actions = ["kms:Decrypt"]
    resources = [
      var.kms_key_arn
    ]
  }
}
resource "aws_iam_policy" "code_build_smoke_test_policy" {
  name   = "codebuild-policy-${local.identifier}"
  policy = data.aws_iam_policy_document.code_build_smoke_test_policy.json
}

resource "aws_iam_role_policy_attachment" "codebuild_smoke_test_additional_policy_attachments" {
  role       = module.codebuild.role_id
  policy_arn = aws_iam_policy.code_build_smoke_test_policy.arn
}
