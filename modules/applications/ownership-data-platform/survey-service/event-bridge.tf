resource "aws_cloudwatch_event_rule" "survey_service_submission_events" {
  name           = "${local.identifier}-submission-events"
  event_bus_name = var.jotform_event_bus_name
  event_pattern = jsonencode({
    source = ["com.jotform"]
    detail = {
      scope = ["submission"]
    }
  })
}

resource "aws_cloudwatch_event_target" "submission_events_queue" {
  rule           = aws_cloudwatch_event_rule.survey_service_submission_events.name
  arn            = module.survey_submission_event_queue.queue_arn
  event_bus_name = var.jotform_event_bus_name
  input_path     = "$.detail"
}

resource "aws_cloudwatch_event_rule" "survey_service_asset_events" {
  name           = "${local.identifier}-asset-events"
  event_bus_name = var.odp_event_bus_name
  event_pattern = jsonencode({
    source      = [var.survey_service_asset_uploaded_event_source],
    detail-type = [var.survey_service_asset_uploaded_event_detail_type]
  })
}

resource "aws_cloudwatch_event_target" "asset_events_queue" {
  rule           = aws_cloudwatch_event_rule.survey_service_asset_events.name
  arn            = module.survey_asset_event_queue.queue_arn
  event_bus_name = var.odp_event_bus_name
  input_path     = "$.detail"
}

resource "aws_cloudwatch_event_rule" "survey_submitted_events" {
  name           = "${local.identifier}-survey-submitted-events"
  event_bus_name = var.odp_event_bus_name
  event_pattern = jsonencode({
    detail-type = ["Survey Submitted"]
  })
}

resource "aws_cloudwatch_event_target" "log_survey_submitted_events" {
  arn            = var.odp_bus_events_log_group_arn
  rule           = aws_cloudwatch_event_rule.survey_submitted_events.name
  event_bus_name = var.odp_event_bus_name
}

