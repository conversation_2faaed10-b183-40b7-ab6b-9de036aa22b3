resource "aws_cloudwatch_dashboard" "salesforce_integration" {
  dashboard_name = "salesforce-integration"
  dashboard_body = jsonencode({
    "widgets" : [
      {
        "height" : 6,
        "width" : 24,
        "y" : 13,
        "x" : 0,
        "type" : "log",
        "properties" : {
          "query" : "SOURCE '/ecs/cart-service-event-processor' | fields  @message\n| filter @message like /\\[Salesforce Client\\]|\\[Queue processor\\]/\n| sort @timestamp desc",
          "region" : "eu-west-1",
          "stacked" : false,
          "title" : "Cart Service",
          "view" : "table"
        }
      },
      {
        "height" : 6,
        "width" : 24,
        "y" : 7,
        "x" : 0,
        "type" : "log",
        "properties" : {
          "query" : "SOURCE '/ecs/customer-service-event-processor' | fields @message\n| filter @message like /\\[Salesforce Client\\]|\\[Queue processor\\]/\n| sort @timestamp desc",
          "region" : "eu-west-1",
          "stacked" : false,
          "title" : "Customer Service",
          "view" : "table"
        }
      },
      {
        "height" : 5,
        "width" : 8,
        "y" : 2,
        "x" : 0,
        "type" : "metric",
        "properties" : {
          "metrics" : [
            [
              "AWS/SQS", "NumberOfMessagesSent", "QueueName",
              module.cart_service.cart_converted_queue_name,
              { label : "Received", region : "eu-west-1" }
            ],
            [
              ".", "NumberOfMessagesDeleted", ".", ".",
              { color : "#2ca02c", label : "Processed", region : "eu-west-1" }
            ],
            [
              ".", "ApproximateNumberOfMessagesNotVisible", ".", ".", {
                color : "#ff7f0e", label : "Pending", region : "eu-west-1",
                stat : "Maximum"
              }
            ],
            [
              ".", "ApproximateNumberOfMessagesVisible", ".",
              module.cart_service.cart_converted_queue_dlq_name, {
                color : "#d62728", label : "Failed", region : "eu-west-1",
                stat : "Maximum"
              }
            ]
          ],
          "period" : 300,
          "region" : "eu-west-1",
          "stacked" : false,
          "stat" : "Sum",
          "title" : "Cart Converted Events Queue",
          "view" : "timeSeries"
        }
      },
      {
        "height" : 5,
        "width" : 8,
        "y" : 2,
        "x" : 16,
        "type" : "metric",
        "properties" : {
          "metrics" : [
            [
              "AWS/SQS", "NumberOfMessagesReceived", "QueueName",
              module.customer_service.customer_events_queue_name,
              { label : "Received", region : "eu-west-1", stat : "Sum" }
            ],
            [
              ".", "NumberOfMessagesDeleted", ".", ".", {
                color : "#2ca02c", label : "Processed", region : "eu-west-1",
                stat : "Sum"
              }
            ],
            [
              ".", "ApproximateNumberOfMessagesNotVisible", ".", ".",
              { color : "#ff7f0e", label : "Pending", region : "eu-west-1" }
            ],
            [
              ".", "ApproximateNumberOfMessagesVisible", ".",
              module.customer_service.customer_events_queue_dlq_name,
              { label : "Failed", region : "eu-west-1", yAxis : "left" }
            ]
          ],
          "period" : 300,
          "region" : "eu-west-1",
          "stacked" : false,
          "stat" : "Maximum",
          "title" : "Customer Events Queue",
          "view" : "timeSeries"
        }
      },
      {
        "type" : "custom",
        "x" : 0,
        "y" : 0,
        "width" : 8,
        "height" : 2,
        "properties" : {
          "endpoint" : module.widgets.redrive_button_invoke_url,
          "updateOn" : {
            "refresh" : true,
            "resize" : true,
            "timeRange" : true
          },
          "params" : {
            "queue" : module.cart_service.cart_converted_queue_dlq_name
          },
          "title" : ""
        }
      },
      {
        "type" : "custom",
        "x" : 8,
        "y" : 0,
        "width" : 8,
        "height" : 2,
        "properties" : {
          "endpoint" : module.widgets.redrive_button_invoke_url,
          "updateOn" : {
            "refresh" : true,
            "resize" : true,
            "timeRange" : true
          },
          "title" : ""
        }
      },
      {
        "type" : "custom",
        "x" : 16,
        "y" : 0,
        "width" : 8,
        "height" : 2,
        "properties" : {
          "endpoint" : module.widgets.redrive_button_invoke_url,
          "updateOn" : {
            "refresh" : true,
            "resize" : true,
            "timeRange" : true
          },
          "params" : {
            "queue" : module.customer_service.customer_events_queue_dlq_name
          },
          "title" : ""
        }
      }
    ]
  })
}
