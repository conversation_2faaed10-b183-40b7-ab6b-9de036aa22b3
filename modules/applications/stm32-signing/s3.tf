module "signed_storage" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.0.1"
  bucket  = "podpoint-firmware-arch5-signed"

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true

  versioning = {
    enabled = true
  }

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        kms_master_key_id = var.kms_master_key_arn
        sse_algorithm     = "aws:kms"
      }
    }
  }

  attach_policy = true
  policy        = data.aws_iam_policy_document.signed_storage_policy.json
}

data "aws_iam_policy_document" "signed_storage_policy" {
  statement {
    sid = "PermitDownloadByFCTAppBuildWorkflow"

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::553176264302:role/github-actions-fct-s3-role"]
    }

    actions   = ["s3:GetObject"]
    resources = ["arn:aws:s3:::podpoint-firmware-arch5-signed/*"]
  }

  statement {
    sid = "PermitDownloadFromProductionRunner"

    principals {
      type        = "AWS"
      identifiers = [var.github_prod_runner_arn]
    }

    actions = [
      "s3:GetObject",
      "s3:ListBucket"
    ]
    resources = [
      "arn:aws:s3:::podpoint-firmware-arch5-signed",
      "arn:aws:s3:::podpoint-firmware-arch5-signed/*"
    ]
  }
}


module "artifact_storage" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.0.1"
  bucket  = "podpoint-stm32-cpl-artifacts"

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true

  versioning = {
    enabled = false
  }

  lifecycle_rule = [
    {
      id      = "ExpireOldArtifacts"
      enabled = true

      expiration = {
        days = 90
      }
    }
  ]

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
}
