version: 0.2

env:
  shell: bash
  git-credential-helper: yes

phases:
  install:
    commands:
      - sudo apt update
      - sudo apt install -y python3-pip
      - pip3 install git-remote-codecommit
  pre_build:
    commands:
      - git remote remove origin
      - git remote add origin codecommit::$REGION://team-idc-app
  build:
    commands:
      - cd ./deployment
      - |
        aws cloudformation deploy --region $REGION --template-file template.yml \
          --stack-name TEAM-IDC-APP \
          --parameter-overrides \
            Login=$IDC_LOGIN_URL \
            CloudTrailAuditLogs=$CLOUDTRAIL_AUDIT_LOGS \
            teamAdminGroup="$TEAM_ADMIN_GROUP" \
            teamAuditGroup="$TEAM_AUDITOR_GROUP" \
            tags="$TAGS" \
            teamAccount="$TEAM_ACCOUNT" \
          --tags $TAGS \
          --no-fail-on-empty-changeset --capabilities CAPABILITY_NAMED_IAM
  post_build:
    commands:
      - git push origin main