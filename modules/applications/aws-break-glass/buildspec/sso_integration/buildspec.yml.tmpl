version: 0.2

env:
  shell: bash

phases:
  build:
    commands:
      - |
        green='\033[0;32m'
        clear='\033[0m'
        cognitoUserpoolId=`aws cognito-idp list-user-pools --region $REGION --max-results 10 --output json | jq -r '.UserPools[] | select(.Name | contains("team06dbb7fc")) | .Id'`
        cognitouserpoolhostedUIdomain=`aws cognito-idp describe-user-pool --region $REGION --user-pool-id $cognitoUserpoolId --output json | jq -r '.UserPool.Domain'`
        applicationURL=`aws amplify list-apps --region $REGION --output json | jq -r '.apps[] | select(.name=="TEAM-IDC-APP") | .defaultDomain' `
        clientID=`aws cognito-idp list-user-pool-clients --region $REGION --user-pool-id $cognitoUserpoolId --output json | jq -r '.UserPoolClients[] | select(.ClientName | contains("clientWeb")) | .ClientId'`
        
        hostedUIdomain=$cognitouserpoolhostedUIdomain.auth.$REGION.amazoncognito.com
        appURL=https://main.$applicationURL
        
        applicationStartURL="https://$hostedUIdomain/authorize?client_id=$clientID&response_type=code&scope=aws.cognito.signin.user.admin+email+openid+phone+profile&redirect_uri=$appURL/&idp_identifier=team"
        applicationACSURL="https://$hostedUIdomain/saml2/idpresponse"
        applicationSAMLAudience="urn:amazon:cognito:sp:$cognitoUserpoolId"
        
        printf "\nIAM Identity Center Applications cannot be created programmatically. Please follow the guide from \"Configure IAM Identity Center SAML Integration\" using the outputs below:\nhttps://aws-samples.github.io/iam-identity-center-team/docs/deployment/configuration/idc.html#configure-iam-identity-center-saml-integration\n\n${green}applicationStartURL:${clear} %s\n${green}applicationACSURL:${clear} %s\n${green}applicationSAMLAudience:${clear} %s\n\nIMPORTANT: Please update the SSM Parameter /iam-identity-center/break-glass-app/meta-data-url with the value of AWS IAM Identity Center SAML metadata file URL\n\n" "$applicationStartURL" "$applicationACSURL" "$applicationSAMLAudience"