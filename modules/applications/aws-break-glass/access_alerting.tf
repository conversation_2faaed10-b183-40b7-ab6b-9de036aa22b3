module "lambda_logger" {
  count  = var.deploy_break_glass_alerts ? 1 : 0
  source = "./submodules/access_alerting"
  providers = {
    aws                 = aws,
    aws.chatbot-account = aws.chatbot-account,
    awscc               = awscc
  }

  request_table_name       = var.break_glass_alerts_config.request_table_name
  slack_alert_channel_id   = var.break_glass_alerts_config.slack_alert_channel_id
  slack_alert_workspace_id = var.break_glass_alerts_config.slack_alert_workspace_id
}