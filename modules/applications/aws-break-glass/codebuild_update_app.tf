// CodeBuild project

data "local_file" "update_break_glass_app" {
  count    = var.deploy_update_pipeline ? 1 : 0
  filename = "${path.module}/buildspec/update_application/buildspec.yml.tmpl"
}

resource "aws_codebuild_project" "update_break_glass_app" {
  count        = var.deploy_update_pipeline ? 1 : 0
  name         = "break-glass-app-update"
  description  = "update the aws break glass app using their latest CloudFormation & code."
  service_role = aws_iam_role.update_break_glass_app[0].arn

  source {
    type            = "CODEPIPELINE"
    git_clone_depth = 0
    buildspec       = data.local_file.update_break_glass_app[0].content
  }

  artifacts {
    type = "CODEPIPELINE"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:7.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
  }
}

// Permissions

data "aws_iam_policy_document" "update_break_glass_app" {
  count = var.deploy_update_pipeline ? 1 : 0
  statement {
    effect = "Allow"

    actions = [
      "SNS:Subscribe"
    ]

    resources = [
      "arn:aws:sns:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:amplify_codecommit_topic"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "amplify:TagResource",
      "amplify:Get*",
      "amplify:List*",
      "amplify:Update*"
    ]
    resources = [
      "arn:aws:amplify:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:apps/*"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "lambda:*"
    ]

    resources = [
      "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:*TEAM-IDC-APP*"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "iam:*"
    ]

    resources = [
      "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/*TEAM-IDC-APP*"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "codecommit:GitPush",
      "codecommit:GetRepository",
      "codecommit:GetRepositoryTriggers",
      "codecommit:PutRepositoryTriggers"
    ]

    resources = [
      "arn:aws:codecommit:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:team-idc-app"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "codecommit:GitPull"
    ]

    resources = [
      "*"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "codestar-connections:UseConnection"
    ]

    resources = [
      aws_codestarconnections_connection.pod_point_github_access.arn
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "cloudformation:*"
    ]

    resources = [
      "arn:aws:cloudformation:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:stack/TEAM-IDC-APP/*"
    ]
  }
}

data "aws_iam_policy_document" "base_codebuild_policy_update_app" {
  count = var.deploy_update_pipeline ? 1 : 0
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]

    resources = [
      "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/codebuild/break-glass-app-update",
      "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/codebuild/break-glass-app-update:*",
      "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:break-glass-app-update",
      "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:break-glass-app-update:*"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "s3:PutObject",
      "s3:GetObject",
      "s3:GetObjectVersion",
      "s3:GetBucketAcl",
      "s3:GetBucketLocation"
    ]

    resources = [
      module.artifact_storage.s3_bucket_arn,
      "${module.artifact_storage.s3_bucket_arn}/*"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "codebuild:CreateReportGroup",
      "codebuild:CreateReport",
      "codebuild:UpdateReport",
      "codebuild:BatchPutTestCases",
      "codebuild:BatchPutCodeCoverages"
    ]

    resources = [
      "arn:aws:codebuild:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:report-group/break-glass-app-update*"
    ]
  }
}

resource "aws_iam_role" "update_break_glass_app" {
  count              = var.deploy_update_pipeline ? 1 : 0
  name               = "update-break-glass-app"
  assume_role_policy = data.aws_iam_policy_document.codebuild_assume_role.json
}

resource "aws_iam_role_policy" "update_break_glass_app" {
  count  = var.deploy_update_pipeline ? 1 : 0
  name   = "update-break-glass-app"
  role   = aws_iam_role.update_break_glass_app[0].id
  policy = data.aws_iam_policy_document.update_break_glass_app[0].json
}

resource "aws_iam_role_policy" "base_codebuild_policy_update_app" {
  count  = var.deploy_update_pipeline ? 1 : 0
  name   = "base-codebuild-policy"
  role   = aws_iam_role.update_break_glass_app[0].id
  policy = data.aws_iam_policy_document.base_codebuild_policy_update_app[0].json
}
