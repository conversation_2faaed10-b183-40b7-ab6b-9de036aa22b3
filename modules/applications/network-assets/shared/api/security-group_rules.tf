resource "aws_security_group_rule" "vpn_ingress" {
  type              = "ingress"
  from_port         = local.listener_port
  to_port           = local.listener_port
  protocol          = "tcp"
  description       = "Access permitted from the VPN"
  security_group_id = module.privatelink_target.alb_security_group_id
  cidr_blocks = [
    "**********/22",
    "**********/22"
  ]
}

resource "aws_security_group_rule" "egress" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  description       = "Permit everything"
  security_group_id = module.fargate_service.security_group_id
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
}
