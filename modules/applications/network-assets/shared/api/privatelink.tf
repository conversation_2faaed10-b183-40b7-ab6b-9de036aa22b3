module "privatelink_target" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws//modules/privatelink-target"
  version = "2.4.0"

  client_account_ids = var.api_access_account_ids

  target = {
    name              = var.api_name
    port              = local.container_port
    listener_port     = local.listener_port
    security_group_id = module.fargate_service.security_group_id
    health_check_config = {
      path                = local.healthcheck_path
      interval            = 6
      unhealthy_threshold = 2
    }
  }

  vpc_id         = var.vpc_id
  vpc_cidr_block = var.vpc_cidr_block
  subnet_ids     = var.private_subnet_ids
}
