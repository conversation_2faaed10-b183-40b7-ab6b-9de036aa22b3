output "configuration_queued_jobs_queue_url" {
  value = module.configuration_queued_jobs.queue_url
}

output "configuration_queued_jobs_queue_arn" {
  value = module.configuration_queued_jobs.queue_arn
}

output "queue_consumer_security_group_id" {
  value = module.queue_consumer.security_group_id
}

output "configuration_events_topic_arn" {
  description = "The arn of the configuration events topic."
  value       = aws_sns_topic.configuration_events.arn
}

output "api_dns_name" {
  description = "The nlb base url to connect to the api."
  value       = module.api.dns_name
}

output "configuration_test_queue_url" {
  description = "The url of the configuration events test queue."
  value       = var.should_enable_test_queues ? aws_sqs_queue.configuration_events_test[0].url : null
}

output "configuration_test_queue_arn" {
  description = "The arn of the configuration events test queue."
  value       = var.should_enable_test_queues ? aws_sqs_queue.configuration_events_test[0].arn : null
}

output "api_security_group_id" {
  description = "The ID of the fargate service security group."
  value       = module.api.security_group_id
}

output "api_vpc_endpoint_service_name" {
  description = "The name of the vpc endpoint service."
  value       = module.api.vpc_endpoint_service_name
}

output "charging_station_updated_events_queue_arn" {
  description = "The arn of the charging station updated events queue."
  value       = module.charging_station_updated_events_queue.queue_arn
}

output "charging_station_updated_events_queue_url" {
  description = "The url of the charging station updated events queue."
  value       = module.charging_station_updated_events_queue.queue_url
}
