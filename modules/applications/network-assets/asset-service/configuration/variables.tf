variable "ecs_cluster_arn" {
  description = "The arn of the ECS cluster for the fargate service."
}

variable "ecs_cluster_name" {
  description = "The name of the ECS cluster for the fargate service."
}

variable "ecs_cluster_github_role_name" {
  description = "The github role name for the ECS cluster."
}

variable "environment" {
  description = "Environment name infrastructure is being deployed to."
  type        = string
}

variable "configcat_sdk_key_secret_arn" {
  description = "Config Cat Feature Flag SDK Key"
  type        = string
}

variable "kms_key_arn" {
  description = "The arn of the KMS key to be used by services"
  type        = string
}

variable "kms_key_id" {
  description = "The id of the KMS key to be used by services"
  type        = string
}

variable "secrets_kms_key_arn" {
  type        = string
  description = "ARN of the KMS key used to encrypt secrets"
}

variable "private_subnet_ids" {
  type        = list(string)
  description = "A list of private subnet ids to assigned to the fargate service"
}

variable "queue_consumer_scaling_min_capacity" {
  description = "Lower limit for queue consumer autoscaling"
  type        = number
}

variable "queue_consumer_scaling_max_capacity" {
  description = "Upper limit for queue consumer autoscaling"
  type        = number
}

variable "vpc_id" {
  description = "The VPC ID."
  type        = string
}

variable "vpc_cidr_block" {
  description = "The CIDR range for the vpc"
  type        = string
}

variable "additional_kms_administrators" {
  description = "IAM ARN of a trusted entity for decrypting the module owned encrypted resources like CloudWatch Logs"
  type        = list(string)
  default     = []
}

variable "aurora_reader_endpoint" {
  description = "The endpoint for the aurora reader."
}

variable "aurora_cluster_endpoint" {
  description = "The name for the cluster."
}

variable "aurora_cluster_resource_id" {
  description = "The cluster resource id for the aurora instance."
}

variable "aurora_cluster_security_group_id" {
  description = "The security group id for the aurora instance."
}

variable "log_level" {
  description = "maximum level to generate logs at - debug, warn or error"
  type        = string
  default     = "info"
  validation {
    condition     = contains(["debug", "info", "warn", "error"], var.log_level)
    error_message = "The value must be one of 'debug', 'info', 'warn' or 'error'"
  }
}

variable "commands_api_url" {
  description = "The url for the commands API"
  type        = string
}

variable "configuration_api_url" {
  description = "The url for the configuration API"
  type        = string
}

variable "asset_service_api_url" {
  description = "The asset service base url"
  type        = string
}

variable "enable_alarms" {
  type        = bool
  description = "Set to true to enable CloudWatch Metric Alarms. cloudwatch_alarm_topic_arn must also be set"
}

variable "cloudwatch_alarm_topic_arn" {
  type        = string
  description = "The ARN of the SNS topic to which Cloudwatch alarms should be published"
}

variable "should_enable_test_queues" {
  description = "A boolean on whether test queues should be enabled for testing sns topics."
  type        = bool
}

variable "configuration_events_publish_role_arns" {
  description = "The ARNs of roles that are allowed to publish configuration events."
  type        = list(string)
}

variable "api_access_account_ids" {
  type        = list(string)
  description = "The IDs of the accounts that should have access to the API."
}

variable "cs_lifecycle_events_topic_arn" {
  type        = string
  description = "The ARN of the CS lifecycle events SNS topic."
}

variable "podadmin_environment" {
  description = "The environment of the podadmin database. Required purely to adhere to our secret manager secret naming convention."
  type        = string
}

variable "podadmin_read_endpoint" {
  description = "The Podadmin database read endpoint"
  type        = string
}

variable "podadmin_read_write_endpoint" {
  description = "The Podadmin database read/write endpoint"
  type        = string
}

variable "podadmin_port" {
  description = "The tcp port for Podadmin database"
  type        = string
}

variable "podadmin_connection_limit" {
  description = "The maximum number of connections to the database that will be created at once"
  type        = string
  default     = "20"
}
