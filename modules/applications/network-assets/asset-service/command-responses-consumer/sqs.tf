/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *       Command Responses
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/
resource "aws_sqs_queue" "command_responses" {
  name = "command-responses"

  kms_master_key_id = var.kms_key_id

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.command_responses_dlq.arn
    maxReceiveCount     = var.command_responses_max_receive_count
  })
}

resource "aws_sqs_queue" "command_responses_dlq" {
  name                      = "command-responses-dlq"
  message_retention_seconds = 1209600 // 14 days (the maximum)

  kms_master_key_id = var.kms_key_id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = ["arn:aws:sqs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:command-responses"]
  })
}

resource "aws_sns_topic_subscription" "cs_command_responses_sub" {
  endpoint             = aws_sqs_queue.command_responses.arn
  protocol             = "sqs"
  raw_message_delivery = true
  topic_arn            = var.cs_command_responses_topic_arn
  filter_policy = jsonencode(
    {
      "Type" : ["GetVariables", "SetVariables"],
    }
  )
}

resource "aws_sqs_queue_policy" "command_responses_queue_policy" {
  queue_url = aws_sqs_queue.command_responses.id
  policy    = <<POLICY
{
  "Version": "2012-10-17",
  "Id": "${aws_sqs_queue.command_responses.arn}/SQSDefaultPolicy",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "sqs:SendMessage",
      "Resource": "${aws_sqs_queue.command_responses.arn}",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "${var.cs_command_responses_topic_arn}"
        }
      }
    }
  ]
}
POLICY
}
