module "model_api_db_user_for_podadmin_kms_key" {
  source      = "terraform-enterprise.pod-point.com/technology/kms/aws"
  version     = "1.0.0"
  alias_name  = format("%s-db-user-for-podadmin", local.api_name)
  description = "Used to encrypt the database passwords stored in secrets managers and of which are used by the model api."
  policy      = data.aws_iam_policy_document.model_api_db_user_for_podadmin_kms_key.json
}

data "aws_iam_policy_document" "model_api_db_user_for_podadmin_kms_key" {
  statement {
    sid = "KeyAdministrators"

    principals {
      type        = "AWS"
      identifiers = concat([format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id)], var.additional_kms_administrators)
    }

    actions   = ["kms:*"]
    resources = ["*"]
  }

  statement {
    sid = "AllowECSTaskExecutionDecryptOperations"

    principals {
      type = "AWS"
      identifiers = [
        module.api.task_execution_role_arn,
      ]
    }

    actions   = ["kms:Decrypt"]
    resources = ["*"]
  }

  statement {
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }
    condition {
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }
  }
}

resource "aws_secretsmanager_secret" "model_api_podadmin_credentials" {
  name                    = format("/aurora/podadmin-%s/model_api/credentials", var.podadmin_environment)
  description             = "Credentials for the model_api to use when reading from Podadmin"
  kms_key_id              = module.model_api_db_user_for_podadmin_kms_key.id
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "model_api_podadmin_credentials" {
  secret_id = aws_secretsmanager_secret.model_api_podadmin_credentials.id
  secret_string = jsonencode({
    username = "model_api"
    password = "MANUAL_INTERACTION_REQUIRED_REPLACE_ME"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}
