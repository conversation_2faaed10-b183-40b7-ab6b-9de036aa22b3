resource "aws_security_group_rule" "vpn_ingress" {
  type        = "ingress"
  description = "Access permitted from the VPN"
  from_port   = local.caddy_port
  to_port     = local.caddy_port
  protocol    = "tcp"
  cidr_blocks = [
    "**********/22",
    "**********/22"
  ]
  security_group_id = module.fargate_service.security_group_id
}

resource "aws_security_group_rule" "app_ingress" {
  for_each = { for security_group in var.security_groups_with_access : security_group.name => security_group }

  type                     = "ingress"
  description              = "Access permitted from ${each.value.name}"
  from_port                = local.caddy_port
  to_port                  = local.caddy_port
  protocol                 = "tcp"
  security_group_id        = module.fargate_service.security_group_id
  source_security_group_id = each.value.security_group_id
}

moved {
  from = module.egress.aws_security_group_rule.this
  to   = aws_security_group_rule.egress
}

resource "aws_security_group_rule" "egress" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  description       = "Permit everything"
  security_group_id = module.fargate_service.security_group_id
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
}
