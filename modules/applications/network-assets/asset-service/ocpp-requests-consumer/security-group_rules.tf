moved {
  from = module.egress.aws_security_group_rule.this
  to   = aws_security_group_rule.egress
}

resource "aws_security_group_rule" "egress" {
  description       = "Permit everything"
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = module.fargate_service.security_group_id
}

moved {
  from = module.database_ingress_rules["permit_api"].aws_security_group_rule.this
  to   = aws_security_group_rule.database_ingress_rules["permit_api"]
}

resource "aws_security_group_rule" "database_ingress_rules" {
  for_each = local.database_ingress_rules

  type                     = "ingress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = lookup(each.value, "security_group_id", var.asset_service_aurora_cluster_security_group_id)
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)
}
