module "configuration" {
  source = "./configuration"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
  }

  environment = var.environment

  log_level = var.log_level

  vpc_id             = var.vpc_id
  vpc_cidr_block     = var.vpc_cidr_block
  private_subnet_ids = var.private_subnet_ids

  ecs_cluster_name             = module.asset_service_ecs_cluster.name
  ecs_cluster_arn              = module.asset_service_ecs_cluster.arn
  ecs_cluster_github_role_name = module.asset_service_ecs_cluster.github_role_name

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.asset_service_configcat_sdk_key.arn

  kms_key_id  = module.kms.id
  kms_key_arn = module.kms.arn

  secrets_kms_key_arn           = aws_kms_key.secrets.arn
  additional_kms_administrators = var.additional_kms_administrators

  queue_consumer_scaling_min_capacity = var.provisioning_queue_consumer_scaling_min_capacity
  queue_consumer_scaling_max_capacity = var.provisioning_queue_consumer_scaling_max_capacity

  aurora_cluster_endpoint          = module.aurora.cluster_endpoint
  aurora_reader_endpoint           = module.aurora.reader_endpoint
  aurora_cluster_resource_id       = module.aurora.cluster_resource_id
  aurora_cluster_security_group_id = module.aurora.security_group_id

  commands_api_url      = var.environment == "prod" ? local.commands_api_url : "http://${module.pact_stub_server[0].service_discovery_name}.${aws_service_discovery_private_dns_namespace.this.name}/commands-api"
  configuration_api_url = module.configuration.api_dns_name

  enable_alarms              = var.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  configuration_events_publish_role_arns = [module.ocpp_requests_consumer.task_role_arn]

  should_enable_test_queues = var.should_enable_test_queues

  asset_service_api_url = module.privatelink_asset_api_target.nlb_dns_name

  api_access_account_ids = var.configuration_api_access_account_ids

  cs_lifecycle_events_topic_arn = module.event_publisher.cs_lifecycle_events_topic_arn

  podadmin_environment         = var.podadmin_environment
  podadmin_port                = var.podadmin_port
  podadmin_read_endpoint       = var.podadmin_read_endpoint
  podadmin_read_write_endpoint = var.podadmin_read_write_endpoint
}

module "event_publisher" {
  source = "./event-publisher"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
  }

  environment = var.environment
  region      = var.region

  log_level = var.log_level

  private_subnet_ids = var.private_subnet_ids
  vpc_id             = var.vpc_id

  kms_key_id  = module.kms.id
  kms_key_arn = module.kms.arn

  asset_service_ecs_cluster_name             = module.asset_service_ecs_cluster.name
  asset_service_ecs_cluster_arn              = module.asset_service_ecs_cluster.arn
  asset_service_ecs_cluster_github_role_name = module.asset_service_ecs_cluster.github_role_name

  asset_service_aurora_cluster_endpoint          = module.aurora.cluster_endpoint
  asset_service_aurora_reader_endpoint           = module.aurora.reader_endpoint
  asset_service_aurora_cluster_resource_id       = module.aurora.cluster_resource_id
  asset_service_aurora_cluster_security_group_id = module.aurora.security_group_id

  additional_kms_administrators = var.additional_kms_administrators

  enable_alarms              = var.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.asset_service_configcat_sdk_key.arn

  secrets_kms_key_arn = aws_kms_key.secrets.arn

  pcb_provisioning_events_topic_arn              = module.provisioning.pcb_provisioning_events_topic_arn
  charging_station_provisioning_events_topic_arn = module.provisioning.charging_station_provisioning_events_topic_arn
  configuration_events_topic_arn                 = module.configuration.configuration_events_topic_arn

  experience_account_id              = var.experience_account_id
  salesforce_account_id              = var.salesforce_account_id
  ownership_data_platform_account_id = var.ownership_data_platform_account_id
}

module "provisioning" {
  source = "./provisioning"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
  }

  environment = var.environment

  log_level = var.log_level

  vpc_id             = var.vpc_id
  private_subnet_ids = var.private_subnet_ids

  ecs_cluster_name             = module.asset_service_ecs_cluster.name
  ecs_cluster_arn              = module.asset_service_ecs_cluster.arn
  ecs_cluster_github_role_name = module.asset_service_ecs_cluster.github_role_name

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.asset_service_configcat_sdk_key.arn

  kms_key_id  = module.kms.id
  kms_key_arn = module.kms.arn

  secrets_kms_key_arn           = aws_kms_key.secrets.arn
  additional_kms_administrators = var.additional_kms_administrators

  queue_consumer_scaling_min_capacity = var.provisioning_queue_consumer_scaling_min_capacity
  queue_consumer_scaling_max_capacity = var.provisioning_queue_consumer_scaling_max_capacity

  configuration_events_topic_arn = module.configuration.configuration_events_topic_arn

  aurora_cluster_endpoint          = module.aurora.cluster_endpoint
  aurora_reader_endpoint           = module.aurora.reader_endpoint
  aurora_cluster_resource_id       = module.aurora.cluster_resource_id
  aurora_cluster_security_group_id = module.aurora.security_group_id
  commands_api_url                 = var.environment == "prod" ? local.commands_api_url : "http://${module.pact_stub_server[0].service_discovery_name}.${aws_service_discovery_private_dns_namespace.this.name}/commands-api"

  enable_alarms              = var.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  api_access_account_ids = var.provisioning_api_access_account_ids

  asset_service_api_url                                   = module.privatelink_asset_api_target.nlb_dns_name
  charging_station_provisioning_events_client_account_ids = var.charging_station_provisioning_events_client_account_ids
  configuration_api_url                                   = module.configuration.api_dns_name
  model_api_url                                           = module.model.api_dns_name
  firmware_upgrade_api_url                                = var.firmware_upgrade_api_url
  ocpp_endpoint_url                                       = var.ocpp_endpoint_url

  firmware_upgrade_api_gateway_id         = var.firmware_upgrade_api_gateway_id
  firmware_upgrade_api_gateway_stage_name = var.firmware_upgrade_api_gateway_stage_name
  firmware_upgrade_account_id             = var.firmware_upgrade_account_id

  podadmin_environment         = var.podadmin_environment
  podadmin_port                = var.podadmin_port
  podadmin_read_endpoint       = var.podadmin_read_endpoint
  podadmin_read_write_endpoint = var.podadmin_read_write_endpoint

  should_enable_test_queues = var.should_enable_test_queues

  vpc_cidr_block = var.vpc_cidr_block
}

module "smoke_tests" {
  count  = var.environment == "staging" ? 1 : 0
  source = "./smoke-tests"

  identifier = local.identifier
  vpc_id     = var.vpc_id

  environment = var.environment
  region      = var.region

  asset_service_ecs_cluster_github_role_name = module.asset_service_ecs_cluster.github_role_name

  asset_service_api_dns_name = module.privatelink_asset_api_target.nlb_dns_name
  provisioning_api_dns_name  = module.provisioning.api_dns_name
  configuration_api_dns_name = module.configuration.api_dns_name
  model_api_dns_name         = module.model.api_dns_name

  elasticache_endpoint            = module.asset_service_elasticache_redis.endpoint
  elasticache_port                = module.asset_service_elasticache_redis.port
  elasticache_password_secret_arn = module.asset_service_elasticache_redis.auth_token_secret_manager_arn
  elasticache_security_group_id   = module.asset_service_elasticache_redis.security_group_id

  asset_service_db_read_endpoint       = module.aurora.reader_endpoint
  asset_service_db_read_write_endpoint = module.aurora.cluster_endpoint
  asset_service_db_port                = local.database_port

  podadmin_secret_kms_key_arn  = module.asset_service_db_user_for_podadmin_kms_key.arn
  podadmin_secret_kms_key_id   = module.asset_service_db_user_for_podadmin_kms_key.id
  podadmin_environment         = var.podadmin_environment
  podadmin_port                = var.podadmin_port
  podadmin_read_endpoint       = var.podadmin_read_endpoint
  podadmin_read_write_endpoint = var.podadmin_read_write_endpoint

  pcb_provisioning_test_events_queue_url = module.provisioning.pcb_provisioning_test_queue_url
  pcb_provisioning_test_events_queue_arn = module.provisioning.pcb_provisioning_test_queue_arn

  command_responses_queue_arn = module.command_responses_consumer.command_responses_queue_arn
  command_responses_queue_url = module.command_responses_consumer.command_responses_queue_url

  installation_completed_events_queue_arn = module.install-complete-consumer.installation_completed_events_queue_arn
  installation_completed_events_queue_url = module.install-complete-consumer.installation_completed_events_queue_url

  assets_installation_completed_events_queue_arn = module.assets_queue_consumer.assets_installation_completed_events_queue_arn
  assets_installation_completed_events_queue_url = module.assets_queue_consumer.assets_installation_completed_events_queue_url

  charging_station_updated_events_queue_arn = module.configuration.charging_station_updated_events_queue_arn
  charging_station_updated_events_queue_url = module.configuration.charging_station_updated_events_queue_url

  additional_kms_administrators = var.additional_kms_administrators

  aurora_cluster_resource_id = module.aurora.cluster_resource_id
  aurora_security_group_id   = module.aurora.security_group_id

  secrets_kms_key_arn = aws_kms_key.secrets.arn

  sqs_kms_key_arn = module.kms.arn

  ocpp_endpoint_url = var.ocpp_endpoint_url

  charging_station_provisioning_test_events_queue_url = module.provisioning.charging_station_provisioning_test_queue_url
  charging_station_provisioning_test_events_queue_arn = module.provisioning.charging_station_provisioning_test_queue_arn
  configuration_test_events_queue_url                 = module.configuration.configuration_test_queue_url
  configuration_test_events_queue_arn                 = module.configuration.configuration_test_queue_arn
  configuration_events_queue_url                      = module.provisioning.configuration_events_queue_url
  configuration_events_queue_arn                      = module.provisioning.configuration_events_queue_arn
  assets_configuration_events_queue_url               = module.assets_queue_consumer.assets_configuration_events_queue_url
  assets_configuration_events_queue_arn               = module.assets_queue_consumer.assets_configuration_events_queue_arn
  configuration_queued_jobs_queue_url                 = module.configuration.configuration_queued_jobs_queue_url
  configuration_queued_jobs_queue_arn                 = module.configuration.configuration_queued_jobs_queue_arn
  pcb_swap_events_queue_url                           = module.provisioning.pcb_swap_events_queue_url
  pcb_swap_events_queue_arn                           = module.provisioning.pcb_swap_events_queue_arn

  soft_ap_credentials_bucket_name = module.provisioning.soft_ap_credentials_bucket_name
  soft_ap_credentials_bucket_arn  = module.provisioning.soft_ap_credentials_bucket_arn
  cs_lifecycle_events_queue_arn   = var.should_enable_test_queues ? module.test_cs_lifecycle_events_queue[0].queue_arn : null
  cs_lifecycle_events_queue_url   = var.should_enable_test_queues ? module.test_cs_lifecycle_events_queue[0].queue_url : null
}

module "command_responses_consumer" {
  source = "./command-responses-consumer"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
  }

  environment = var.environment
  region      = var.region

  log_level = var.log_level

  private_subnet_ids = var.private_subnet_ids
  vpc_id             = var.vpc_id

  kms_key_id                     = module.kms.id
  kms_key_arn                    = module.kms.arn
  cs_command_responses_topic_arn = var.cs_command_responses_topic_arn

  asset_service_ecs_cluster_name             = module.asset_service_ecs_cluster.name
  asset_service_ecs_cluster_arn              = module.asset_service_ecs_cluster.arn
  asset_service_ecs_cluster_github_role_name = module.asset_service_ecs_cluster.github_role_name

  command_responses_queue_consumer_scaling_max_capacity = var.command_responses_queue_consumer_scaling_max_capacity
  command_responses_queue_consumer_scaling_min_capacity = var.command_responses_queue_consumer_scaling_min_capacity

  asset_service_aurora_cluster_endpoint          = module.aurora.cluster_endpoint
  asset_service_aurora_reader_endpoint           = module.aurora.reader_endpoint
  asset_service_aurora_cluster_resource_id       = module.aurora.cluster_resource_id
  asset_service_aurora_cluster_security_group_id = module.aurora.security_group_id

  additional_kms_administrators = var.additional_kms_administrators

  enable_alarms              = var.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.asset_service_configcat_sdk_key.arn

  secrets_kms_key_arn   = aws_kms_key.secrets.arn
  commands_api_url      = var.environment == "prod" ? local.commands_api_url : "http://${module.pact_stub_server[0].service_discovery_name}.${aws_service_discovery_private_dns_namespace.this.name}/commands-api"
  asset_service_api_url = module.privatelink_asset_api_target.nlb_dns_name
  configuration_api_url = module.configuration.api_dns_name


  pcb_provisioning_events_topic_arn              = module.provisioning.pcb_provisioning_events_topic_arn
  charging_station_provisioning_events_topic_arn = module.provisioning.charging_station_provisioning_events_topic_arn
  configuration_events_topic_arn                 = module.configuration.configuration_events_topic_arn
}

module "ocpp_requests_consumer" {
  source = "./ocpp-requests-consumer"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
  }

  environment = var.environment
  region      = var.region

  log_level = var.log_level

  private_subnet_ids = var.private_subnet_ids
  vpc_id             = var.vpc_id

  kms_key_id                 = module.kms.id
  kms_key_arn                = module.kms.arn
  ocpp_cs_requests_topic_arn = var.ocpp_cs_requests_topic_arn

  asset_service_ecs_cluster_name             = module.asset_service_ecs_cluster.name
  asset_service_ecs_cluster_arn              = module.asset_service_ecs_cluster.arn
  asset_service_ecs_cluster_github_role_name = module.asset_service_ecs_cluster.github_role_name

  ocpp_requests_queue_consumer_scaling_max_capacity = var.ocpp_requests_queue_consumer_scaling_max_capacity
  ocpp_requests_queue_consumer_scaling_min_capacity = var.ocpp_requests_queue_consumer_scaling_min_capacity

  asset_service_aurora_cluster_endpoint          = module.aurora.cluster_endpoint
  asset_service_aurora_reader_endpoint           = module.aurora.reader_endpoint
  asset_service_aurora_cluster_resource_id       = module.aurora.cluster_resource_id
  asset_service_aurora_cluster_security_group_id = module.aurora.security_group_id

  additional_kms_administrators = var.additional_kms_administrators

  enable_alarms              = var.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  commands_api_url = var.environment == "prod" ? local.commands_api_url : "http://${module.pact_stub_server[0].service_discovery_name}.${aws_service_discovery_private_dns_namespace.this.name}/commands-api"

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.asset_service_configcat_sdk_key.arn

  secrets_kms_key_arn = aws_kms_key.secrets.arn
}

module "pact_stub_server" {
  count = var.environment != "prod" ? 1 : 0

  source = "./pact-stub-server"

  region = var.region

  private_subnet_ids = var.private_subnet_ids
  vpc_id             = var.vpc_id

  asset_service_ecs_cluster_name             = module.asset_service_ecs_cluster.name
  asset_service_ecs_cluster_arn              = module.asset_service_ecs_cluster.arn
  asset_service_ecs_cluster_github_role_name = module.asset_service_ecs_cluster.github_role_name

  aws_service_discovery_private_dns_namespace_id = aws_service_discovery_private_dns_namespace.this.id

  additional_kms_administrators = var.additional_kms_administrators

  commands_api_url     = local.commands_api_url
  connectivity_api_url = local.connectivity_api_url

  security_groups_with_access = [
    {
      name : "configuration-api",
      security_group_id : module.configuration.api_security_group_id
    },
    {
      name : "ocpp-requests-consumer",
      security_group_id : module.ocpp_requests_consumer.security_group_id
    },
    {
      name : "command-responses-consumer",
      security_group_id : module.command_responses_consumer.security_group_id
    },
    {
      name : "provisioning-api",
      security_group_id : module.provisioning.api_security_group_id
    },
    {
      name : "provisioning-queue-consumer",
      security_group_id : module.provisioning.queue_consumer_security_group_id
    },
    {
      name : "configuration-queue-consumer",
      security_group_id : module.configuration.queue_consumer_security_group_id
    },
    {
      name : "asset-service-api",
      security_group_id : module.asset_api.security_group_id
    }
  ]
}

module "fatp" {
  source = "./fatp"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
    aws.shared-services     = aws.shared-services
  }

  environment = var.environment
  region      = var.region

  log_level = var.log_level

  provisioning_api_dns_name = module.provisioning.api_dns_name

  asset_service_ecs_cluster_name             = module.asset_service_ecs_cluster.name
  asset_service_ecs_cluster_arn              = module.asset_service_ecs_cluster.arn
  asset_service_ecs_cluster_github_role_name = module.asset_service_ecs_cluster.github_role_name

  additional_kms_administrators = var.additional_kms_administrators

  public_subnet_ids  = var.public_subnet_ids
  private_subnet_ids = var.private_subnet_ids

  enable_alarms              = var.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  vpc_id = var.vpc_id

  kms_key_id  = module.kms.id
  kms_key_arn = module.kms.arn

  acm_arn = module.acm.arn

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.asset_service_configcat_sdk_key.arn

  secrets_kms_key_arn = aws_kms_key.secrets.arn

  codebuild_security_group_id = var.environment == "staging" ? module.codebuild_fatp_e2e[0].security_group_id : ""
}

module "model" {
  source = "./model"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
  }

  environment          = var.environment
  podadmin_environment = var.podadmin_environment

  log_level = var.log_level

  vpc_id             = var.vpc_id
  vpc_cidr_block     = var.vpc_cidr_block
  private_subnet_ids = var.private_subnet_ids

  kms_key_arn = module.kms.arn

  ecs_cluster_arn              = module.asset_service_ecs_cluster.arn
  ecs_cluster_name             = module.asset_service_ecs_cluster.name
  ecs_cluster_github_role_name = module.asset_service_ecs_cluster.github_role_name

  aurora_cluster_endpoint          = module.aurora.cluster_endpoint
  aurora_reader_endpoint           = module.aurora.reader_endpoint
  aurora_cluster_resource_id       = module.aurora.cluster_resource_id
  aurora_cluster_security_group_id = module.aurora.security_group_id

  podadmin_port                = var.podadmin_port
  podadmin_read_endpoint       = var.podadmin_read_endpoint
  podadmin_read_write_endpoint = var.podadmin_read_write_endpoint

  additional_kms_administrators = var.additional_kms_administrators

  enable_alarms              = var.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.asset_service_configcat_sdk_key.arn

  secrets_kms_key_arn = aws_kms_key.secrets.arn

  api_access_account_ids = []
}

module "install-complete-consumer" {
  source = "./install-complete-consumer"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
  }

  environment = var.environment

  log_level = var.log_level

  vpc_id             = var.vpc_id
  private_subnet_ids = var.private_subnet_ids

  ecs_cluster_name             = module.asset_service_ecs_cluster.name
  ecs_cluster_arn              = module.asset_service_ecs_cluster.arn
  ecs_cluster_github_role_name = module.asset_service_ecs_cluster.github_role_name

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.asset_service_configcat_sdk_key.arn

  kms_key_id  = module.kms.id
  kms_key_arn = module.kms.arn

  secrets_kms_key_arn           = aws_kms_key.secrets.arn
  additional_kms_administrators = var.additional_kms_administrators

  queue_consumer_scaling_min_capacity = var.provisioning_queue_consumer_scaling_min_capacity
  queue_consumer_scaling_max_capacity = var.provisioning_queue_consumer_scaling_max_capacity

  installation_completed_events_topic_arn = var.installation_completed_events_topic_arn

  enable_alarms              = var.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  podadmin_environment = var.podadmin_environment

  asset_service_api_url = module.privatelink_asset_api_target.nlb_dns_name

}

module "asset_creator" {
  count = var.environment == "staging" ? 1 : 0

  source = "./asset-creator"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
  }

  environment        = var.environment
  vpc_cidr_block     = var.vpc_cidr_block
  vpc_id             = var.vpc_id
  private_subnet_ids = var.private_subnet_ids

  log_level = var.log_level

  api_access_account_ids = []

  enable_alarms              = var.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.asset_service_configcat_sdk_key.arn

  ecs_cluster_arn              = module.asset_service_ecs_cluster.arn
  ecs_cluster_github_role_name = module.asset_service_ecs_cluster.github_role_name
  ecs_cluster_name             = module.asset_service_ecs_cluster.name

  kms_key_arn                   = module.kms.arn
  secrets_kms_key_arn           = aws_kms_key.secrets.arn
  additional_kms_administrators = var.additional_kms_administrators

  certificate_service_api = var.certificate_service_api

  charger_simulator_security_group_id       = module.charger_simulator[0].security_group_id
  charger_simulator_task_execution_role_arn = module.charger_simulator[0].task_execution_role_arn
  charger_simulator_task_role_arn           = module.charger_simulator[0].task_role_arn

  codebuild_security_group_id = var.environment == "staging" ? module.codebuild_fatp_e2e[0].security_group_id : ""
  asset_service_api_url       = module.privatelink_asset_api_target.nlb_dns_name
  provisioning_api_url        = module.provisioning.api_dns_name
}

module "charger_simulator" {
  count = var.environment == "staging" ? 1 : 0

  source = "./charger-simulator"

  ecs_cluster_github_role_name  = module.asset_service_ecs_cluster.github_role_name
  vpc_id                        = var.vpc_id
  additional_kms_administrators = var.additional_kms_administrators

  charger_simulator_resources_bucket_arn = module.asset_creator[0].charger_simulator_resources_bucket_arn
}

module "assets_queue_consumer" {
  source = "./assets/"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
  }

  log_level   = var.log_level
  environment = var.environment

  vpc_id                        = var.vpc_id
  private_subnet_ids            = var.private_subnet_ids
  additional_kms_administrators = var.additional_kms_administrators

  ecs_cluster_name             = module.asset_service_ecs_cluster.name
  ecs_cluster_arn              = module.asset_service_ecs_cluster.arn
  ecs_cluster_github_role_name = module.asset_service_ecs_cluster.github_role_name

  kms_key_id  = module.kms.id
  kms_key_arn = module.kms.arn

  queue_consumer_scaling_min_capacity = var.assets_queue_consumer_scaling_min_capacity
  queue_consumer_scaling_max_capacity = var.assets_queue_consumer_scaling_max_capacity

  secrets_kms_key_arn = aws_kms_key.secrets.arn

  enable_alarms              = var.enable_alarms
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  configcat_sdk_key_secret_arn = aws_secretsmanager_secret.asset_service_configcat_sdk_key.arn

  podadmin_environment         = var.podadmin_environment
  podadmin_port                = var.podadmin_port
  podadmin_read_endpoint       = var.podadmin_read_endpoint
  podadmin_read_write_endpoint = var.podadmin_read_write_endpoint

  aurora_cluster_endpoint          = module.aurora.cluster_endpoint
  aurora_reader_endpoint           = module.aurora.reader_endpoint
  aurora_cluster_resource_id       = module.aurora.cluster_resource_id
  aurora_cluster_security_group_id = module.aurora.security_group_id

  configuration_events_topic_arn = module.configuration.configuration_events_topic_arn

  asset_service_api_url = module.privatelink_asset_api_target.nlb_dns_name

  installation_completed_events_topic_arn = var.installation_completed_events_topic_arn

  cache_endpoint            = module.asset_service_elasticache_redis.endpoint
  cache_port                = module.asset_service_elasticache_redis.port
  cache_password_secret_arn = module.asset_service_elasticache_redis.auth_token_secret_manager_arn
}

module "salesforce_installed_events_lambda" {
  source = "./salesforce-installed-lambda"
  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
  }

  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn

  kms_key_id  = module.kms.id
  kms_key_arn = module.kms.arn

  cs_lifecycle_events_topic_arn = module.event_publisher.cs_lifecycle_events_topic_arn
  private_subnet_ids            = var.private_subnet_ids
  vpc_id                        = var.vpc_id
  build_account_id              = var.network_assets_build_account_id
  environment                   = var.environment
  salesforce_api_url            = var.salesforce_api_url
  salesforce_auth_audience      = var.salesforce_auth_audience
}
