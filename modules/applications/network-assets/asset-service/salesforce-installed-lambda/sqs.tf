module "salesforce_installed_events_queue" {
  source = "../../../shared/assets/queue"

  kms_key_id = var.kms_key_id

  queue_name = "salesforce-installed-events"

  enable_alarms              = false
  cloudwatch_alarm_topic_arn = var.cloudwatch_alarm_topic_arn
}

resource "aws_sns_topic_subscription" "salesforce_installed_events" {
  endpoint             = module.salesforce_installed_events_queue.queue_arn
  protocol             = "sqs"
  topic_arn            = var.cs_lifecycle_events_topic_arn
  raw_message_delivery = true
  filter_policy_scope  = "MessageBody"
  filter_policy = jsonencode({
    "type" : ["CS.Lifecycle.Installed"],
  })
}

resource "aws_sqs_queue_policy" "salesforce_installed_events_queue_policy" {
  queue_url = module.salesforce_installed_events_queue.queue_url
  policy    = <<POLICY
{
  "Version": "2012-10-17",
  "Id": "${module.salesforce_installed_events_queue.queue_arn}/SQSDefaultPolicy",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "sqs:SendMessage",
      "Resource": "${module.salesforce_installed_events_queue.queue_arn}",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "${var.cs_lifecycle_events_topic_arn}"
        }
      }
    }
  ]
}
POLICY
}
