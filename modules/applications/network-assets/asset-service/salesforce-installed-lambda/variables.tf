variable "cloudwatch_alarm_topic_arn" {
  description = "The ARN of the SNS topic to which CloudWatch alarms will publish notifications."
  type        = string
}

variable "cs_lifecycle_events_topic_arn" {
  description = "The ARN of the SNS topic for CS lifecycle events."
  type        = string
}

variable "kms_key_id" {
  description = "The id of the KMS key to be used by services"
  type        = string
}

variable "kms_key_arn" {
  description = "The id of the KMS key to be used by services"
  type        = string
}

variable "private_subnet_ids" {
  description = "List of private subnet IDs where the Lambda function will be deployed."
  type        = list(string)
}

variable "build_account_id" {
  description = "The AWS account ID where the ECR repository is located."
  type        = string
}

variable "memory_size" {
  description = "The amount of memory available to the Lambda function in MB."
  type        = number
  default     = 128
}

variable "timeout" {
  description = "The maximum time in seconds that the Lambda function can run before it is terminated."
  type        = number
  default     = 10
}

variable "tags" {
  description = "A map of tags to assign to the resources."
  type        = map(string)
  default     = {}
}

variable "vpc_id" {
  description = "The ID of the VPC where the Lambda function will be deployed."
  type        = string
}

variable "environment" {
  description = "The environment of the service."
  type        = string
}

variable "salesforce_api_url" {
  description = "The base URL for the Salesforce API."
  type        = string
}

variable "salesforce_auth_audience" {
  description = "The base URL for the Salesforce auth audience."
  type        = string
}
