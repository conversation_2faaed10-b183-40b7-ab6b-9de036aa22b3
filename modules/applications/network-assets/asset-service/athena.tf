module "asset_api_load_balancer_access_logs_database" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws//modules/athena_access_logs_database"
  version = "4.1.1"

  athena_workgroup_identifier          = local.api_name
  athena_workgroup_description         = format("Collection of settings for %s", local.api_name)
  athena_database_identifier           = replace(local.api_name, "-", "_")
  glue_catalogue_identifier            = "alb_access_logs"
  s3_bucket_name                       = module.privatelink_asset_api_target.s3_access_log_bucket_id
  query_execution_bucket_force_destroy = true

  tags = {
    GrafanaDataSource = true
  }
}
