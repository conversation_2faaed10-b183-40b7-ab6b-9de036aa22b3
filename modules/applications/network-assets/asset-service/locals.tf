locals {
  identifier                  = "asset-service"
  api_name                    = "${local.identifier}-api"
  cache_name                  = "${local.identifier}-cache"
  load_test_task_name         = "${local.identifier}-load-test"
  migrations_runner_task_name = "${local.identifier}-migrations-runner"

  database_admin_username = "podpoint"
  database_port           = 5432

  environment    = var.environment
  container_port = 3000
  listener_port  = 80

  healthcheck_path = "/health"

  pod_point_com_domain_name = "pod-point.com"
  pod_point_com_hosted_zone = "ZI1YF8KE9MFAW"

  database_ingress_rules = merge({
    permit_vpn_access = {
      description = "AWS Client VPN"
      from_port   = local.database_port
      to_port     = local.database_port
      protocol    = "TCP"
      ipv4_cidrs = [
        "**********/22",
        "**********/22"
      ]
    },
    permit_api = {
      description           = "API permission"
      from_port             = local.database_port
      to_port               = local.database_port
      protocol              = "TCP"
      source_security_group = module.asset_api.security_group_id
    },
    permit_grafana_access = {
      description = "Allow Grafana - Pod Point"
      from_port   = local.database_port
      to_port     = local.database_port
      protocol    = "TCP"
      ipv4_cidrs  = ["*********/32"]
    },
    permit_migrations_runner = {
      description           = "Migrations runner permission"
      from_port             = local.database_port
      to_port               = local.database_port
      protocol              = "TCP"
      source_security_group = module.migrations_runner.security_group_id
    },
    },
    var.logging_comms_grafana_cidr_block == "" ? {} : {
      permit_logging_comms_grafana_access = {
        description = "Allow access from grafana logging comms VPC"
        from_port   = local.database_port
        to_port     = local.database_port
        protocol    = "TCP"
        ipv4_cidrs  = [var.logging_comms_grafana_cidr_block]
      },
    },
  )
  database_egress_rules = {
    ports_all_open = {
      description = "Permit all egress traffic."
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }

  commands_api_url     = "http://${module.commands_api_privatelink_client.dns_entry[0].dns_name}"
  connectivity_api_url = "http://${module.connectivity_status_api_privatelink_client.dns_entry[0].dns_name}"
}
