module "codebuild_fatp_e2e" {
  count   = var.environment == "staging" ? 1 : 0
  source  = "terraform-enterprise.pod-point.com/technology/codebuild/aws"
  version = "3.0.0"

  namespace       = "network-assets"
  name            = "fatp-e2e"
  description     = "network-assets FATP E2E testing"
  source_location = "https://github.com/Pod-Point/asset-service.git"

  buildspec              = ".codebuild/buildspec-fatp-e2e.yml"
  build_timeout          = 60
  concurrent_build_limit = 5

  repo_name = "asset-service"

  vpc_config = {
    vpc_id     = var.vpc_id
    subnet_ids = var.private_subnet_ids
  }

  build_image_pull_credentials_type = "SERVICE_ROLE"
  build_image                       = "TO_BE_CHANGED_BY_GITHUB_WORKFLOW"

  environment_variables = [
    { "name" : "ASSET_CREATOR_URL", "type" : "PLAINTEXT", "value" : "http://${module.asset_creator[0].api_dns_name}" },
    { "name" : "BUILD_ACCOUNT_ID", "type" : "PLAINTEXT", "value" : "${var.network_assets_build_account_id}" },
  ]

  extra_permissions = [
    "ecs:ListTasks",
    "ecs:DescribeTasks",
    "codeconnections:GetConnectionToken",
    "codeconnections:GetConnection",
    "sts:GetServiceBearerToken",
    "ecr:BatchGetImage",
    "ecr:GetDownloadUrlForLayer"
  ]

  custom_policy = data.aws_iam_policy_document.additional_codebuild_policy_document[0].json

  artifact_type     = "S3"
  artifact_location = module.fatp_e2e_playwright_results[0].s3_bucket_id
}

resource "aws_codestarconnections_connection" "pod_point_github_access" {
  count         = var.environment == "staging" ? 1 : 0
  name          = "pod-point-access"
  provider_type = "GitHub"
}


data "aws_iam_policy_document" "github_role_additional_policy_document_fatp_e2e" {
  count = var.environment == "staging" ? 1 : 0

  statement {
    sid    = "AllowGithubToUpdateCodeBuildImage"
    effect = "Allow"
    actions = [
      "codebuild:UpdateProject",
      "codebuild:BatchGetProjects"
    ]

    resources = [
      "arn:aws:codebuild:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:project/network-assets-fatp-e2e",
    ]
  }
}

resource "aws_iam_policy" "github_role_additional_policy_fatp_e2e" {
  count       = var.environment == "staging" ? 1 : 0
  name        = "GithubRoleAdditionalPolicy"
  description = "Policy to allow GitHub pipeline to update CodeBuild image"
  policy      = data.aws_iam_policy_document.github_role_additional_policy_document_fatp_e2e[0].json
}

resource "aws_iam_role_policy_attachment" "github_role_policy_attachment_fatp_e2e" {
  count      = var.environment == "staging" ? 1 : 0
  role       = module.codebuild_fatp_e2e[0].github_role_id
  policy_arn = aws_iam_policy.github_role_additional_policy_fatp_e2e[0].arn
}

module "codebuild_smoke_tests" {
  count   = var.environment == "staging" ? 1 : 0
  source  = "terraform-enterprise.pod-point.com/technology/codebuild/aws"
  version = "1.1.3"

  namespace       = "network-assets"
  name            = "smoke-tests"
  description     = "network-assets Smoke Tests"
  source_location = "https://github.com/Pod-Point/asset-service.git"

  buildspec              = ".codebuild/buildspec-smoke-test.yml"
  build_timeout          = 15
  concurrent_build_limit = 1

  repo_name = "asset-service"

  vpc_id      = var.vpc_id
  subnets_ids = var.private_subnet_ids

  environment_variables = [
    { "name" : "ASSET_SERVICE_API_ENDPOINT", "type" : "PLAINTEXT", "value" : "http://${module.privatelink_asset_api_target.nlb_dns_name}" },
    { "name" : "BUILD_ACCOUNT_ID", "type" : "PLAINTEXT", "value" : "${var.network_assets_build_account_id}" }

  ]

  extra_permissions = [
    "codeconnections:GetConnectionToken",
    "codeconnections:GetConnection",
    "sts:GetServiceBearerToken",
  ]

  custom_policy = data.aws_iam_policy_document.access_codeartifact[0].json
}

data "aws_iam_policy_document" "additional_codebuild_policy_document" {
  count = var.environment == "staging" ? 1 : 0

  # This statement is needed to allow CodeBuild to access the s3 bucket to store the results of the tests.
  statement {
    actions = [
      "s3:PutObject",
      "s3:GetObject",
      "s3:ListBucket"
    ]
    resources = [
      "arn:aws:s3:::${module.fatp_e2e_playwright_results[0].s3_bucket_id}/*",
      "arn:aws:s3:::${module.fatp_e2e_playwright_results[0].s3_bucket_id}",
    ]
    effect = "Allow"
  }
}

data "aws_iam_policy_document" "access_codeartifact" {
  count = var.environment == "staging" ? 1 : 0

  statement {
    sid    = "AllowCodeArtifactAccess"
    effect = "Allow"
    actions = [
      "sts:GetServiceBearerToken",
      "codeartifact:GetAuthorizationToken",
      "codeartifact:ReadFromRepository",
      "codeartifact:DescribeRepository",
      "codeartifact:GetRepositoryEndpoint",
      "codeartifact:ListPackages",
      "codeartifact:ListPackageVersions"
    ]
    resources = [
      "arn:aws:codeartifact:eu-west-1:${var.network_assets_build_account_id}:repository/pod-point/network-assets",
      "arn:aws:codeartifact:eu-west-1:${var.network_assets_build_account_id}:repository/pod-point/network-assets/*",
      "arn:aws:codeartifact:eu-west-1:${var.network_assets_build_account_id}:domain/pod-point",
      "arn:aws:codeartifact:eu-west-1:${var.network_assets_build_account_id}:package/pod-point/*"
    ]
  }
}
