resource "aws_secretsmanager_secret" "asset_service_tests_podadmin_credentials" {
  name                    = format("/aurora/podadmin-%s/asset_service_tests/credentials", var.podadmin_environment)
  description             = "Credentials for the asset service to use to read/write from Podadmin ${var.podadmin_environment} for test purposes"
  kms_key_id              = var.podadmin_secret_kms_key_id
  recovery_window_in_days = 0
}

resource "aws_secretsmanager_secret_version" "asset_service_tests_podadmin_credentials" {
  secret_id = aws_secretsmanager_secret.asset_service_tests_podadmin_credentials.id
  secret_string = jsonencode({
    username = "asset_service_tests"
    password = "MANUAL_INTERACTION_REQUIRED_REPLACE_ME"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}
