resource "aws_secretsmanager_secret" "secrets" {
  name                    = "aws/certificate_service/secrets"
  description             = "The secret on SecretsManager holding all secrets for certificate-service"
  kms_key_id              = aws_kms_key.secrets.id
  recovery_window_in_days = 0

  tags = local.module_tags
}

resource "aws_secretsmanager_secret_version" "secrets" {
  secret_id = aws_secretsmanager_secret.secrets.id
  secret_string = jsonencode({
    sentry_dsn = "MANUAL_INTERACTION_REQUIRED_REPLACE_ME"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}
