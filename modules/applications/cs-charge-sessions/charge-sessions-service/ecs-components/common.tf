locals {
  container_definition = {
    image : "terraform",
    essential : true,
    networkMode : "awsvpc",
    readonly_root_filesystem : false,
    linuxParameters : {
      "initProcessEnabled" : false
    },
    logConfiguration : {
      "logDriver" : "awslogs",
      "options" : {
        "awslogs-group" : "/ecs/${local.identifier}",
        "awslogs-region" : "eu-west-1",
        "awslogs-stream-prefix" : "ecs"
      }
    },
    environment : [
      {
        "name" : "LOG_LEVEL",
        "value" : var.environment == "prod" ? "info" : "debug"
      },
      {
        "name" : "ENVIRONMENT",
        "value" : var.environment
      },
      {
        "name" : "DB_NAME",
        "value" : "charge_sessions_service"
      },
      {
        "name" : "DB_HOST",
        "value" : var.database.writer_endpoint
      },
      {
        "name" : "DB_HOST_READONLY",
        "value" : var.database.reader_endpoint
      },
    ],
    secrets : [
      {
        "name" : "SENTRY_DSN",
        "valueFrom" : format("%s:%s::", var.shared_secrets_arns.common, "sentry_dsn")
      },
    ],
  }
}

data "aws_iam_policy_document" "task_execution_common" {
  statement {
    sid       = "AllowAccessToSecrets"
    effect    = "Allow"
    actions   = ["secretsmanager:GetSecretValue"]
    resources = [for arn in var.shared_secrets_arns : arn]
  }
  statement {
    sid       = "AllowKmsDecrypt"
    effect    = "Allow"
    actions   = ["kms:Decrypt"]
    resources = [var.kms_key_arn]
  }
}
