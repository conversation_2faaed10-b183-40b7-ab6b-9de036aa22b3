variable "environment" {
  description = <<EOF
    The environment of the service.

    This needs to be declared while the service is in the old Pod Point account.

    Otherwise naming conflicts will be present in our terraform apply runs.
  EOF
  type        = string
}

variable "tags" {
  description = "key, value pair of tags to associate to your resources."
  type        = map(any)
  default     = {}
}

variable "pipeline_role_name" {
  description = "The name of the role being assumed by the pipeline. E.g. github actions ecs role made in the ecs/cluster module."
  type        = string
}

variable "vpc" {
  description = "All necessary values regarding the VPC"
  type = object({
    id                 = string
    private_subnet_ids = list(string)
    cidr_block         = string
  })
}

variable "shared_secrets_arns" {
  description = "The ARNs of the secrets needed by ECS tasks."
  type = object({
    common : string
    database : string
  })
}

variable "kms_key_arn" {
  description = <<EOF
    The KMS key used to encrypt the secrets.
    This is used to grant the ECS task execution roles access to decrypt the secrets.
  EOF
  type        = string
}

variable "database" {
  description = "The database attributes required by the ECS tasks"
  type = object({
    cluster_id : string
    cluster_resource_id : string,
    writer_endpoint : string
    reader_endpoint : string
  })
}

variable "logs_retention_in_days" {
  description = "Specifies the number of days you want to retain the container logs."
  type        = number
  default     = 7
}

variable "ecs_cluster" {
  description = ""
  type = object({
    name : string
    arn : string
  })
}

variable "fargate" {
  description = ""
  type = object({
    memory : number
    cpu : number
    capacity_base : number
    capacity_weight : number
    spot_capacity_base : number
    spot_capacity_weight : number
  })
  default = {
    memory : 512,
    cpu : 256,
    capacity_base : 0,
    capacity_weight : 1,
    spot_capacity_base : 1,
    spot_capacity_weight : 1,
  }
}

variable "scaling_min_capacity" {
  description = "The minimum scaling capacity for the ECS tasks"
  type        = number
  default     = 0
}

variable "transactions_api_client_account_ids" {
  description = "List of AWS accounts ID that consume the transactions API."
  type        = list(string)
  default     = []
}

variable "ocpp16_rest_api" {
  description = "Object containing details of the OCPP 1.6 Rest API Gateway."
  type = object({
    region     = string
    account_id = string
    api_id     = string
    stage      = string
  })
}

variable "pod_unit_event_received_topic_arn" {
  description = "The ARN of the SNS topic to publish pod unit events to."
  type        = string
}

variable "vpc_endpoint_commands_api_dns" {
  description = "DNS of the VPC endpoint to the commands api"
  type        = string
}

variable "transaction_telemetry_stream" {
  description = "Object containing details of the transaction-telemetry kinesis stream"
  type = object({
    arn  = string
    name = string
    role = string
  })
}

variable "ocpp16_charge_sessions_sqs_name" {
  description = "The ARN of the OCPP 1.6 charge sessions SQS queue."
}
