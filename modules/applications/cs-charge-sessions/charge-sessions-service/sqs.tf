/*
* OCPP1.6 Charge Sessions
*/

data "aws_iam_policy_document" "ocpp16_charge_sessions" {
  statement {
    sid     = "AllowRawCommsSnsTopicToSendMessage"
    actions = ["SQS:SendMessage"]

    resources = ["*"]

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    condition {
      test     = "ArnEquals"
      variable = "aws:SourceArn"

      values = [
        var.ocpp_cs_requests_sns_topic_arn,
      ]
    }
  }
}

resource "aws_sqs_queue" "ocpp16_charge_sessions" {
  name                       = "ocpp16-charge-sessions"
  visibility_timeout_seconds = 45
  policy                     = data.aws_iam_policy_document.ocpp16_charge_sessions.json
  message_retention_seconds  = 14400 // 4 hours

  tags = local.tags
}

resource "aws_sqs_queue" "ocpp16_charge_sessions_dlq" {
  name                       = "ocpp16-charge-sessions-DLQ"
  visibility_timeout_seconds = 45
  message_retention_seconds  = 604800 // 7 days

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.ocpp16_charge_sessions.arn]
  })

  tags = local.tags
}

resource "aws_sqs_queue_redrive_policy" "ocpp16_charge_sessions_dlq" {
  queue_url = aws_sqs_queue.ocpp16_charge_sessions.id
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.ocpp16_charge_sessions_dlq.arn
    maxReceiveCount     = 3
  })
}

resource "aws_sns_topic_subscription" "ocpp16_charge_sessions" {
  topic_arn            = var.ocpp_cs_requests_sns_topic_arn
  protocol             = "sqs"
  endpoint             = aws_sqs_queue.ocpp16_charge_sessions.arn
  raw_message_delivery = true

  filter_policy = jsonencode(
    {
      "Type" : [
        "StartTransaction",
        "StopTransaction",
        "MeterValues",
        "DataTransfer(com.pod-point:transactionEvent:v1)",
      ],
      "Protocol" = ["OCPP1.6"],
    }
  )
}

resource "aws_cloudwatch_metric_alarm" "messages_DLQ" {
  count = length(var.alarm_actions) > 0 ? 1 : 0

  alarm_name        = format("%s-messages-DLQ", aws_sqs_queue.ocpp16_charge_sessions.name)
  alarm_description = "This alarm triggers when there are messages in the ocpp16-charge-sessions-DLQ."

  comparison_operator = "GreaterThanOrEqualToThreshold"
  period              = 60 # 1 minute
  evaluation_periods  = 1
  datapoints_to_alarm = 1

  metric_name        = "ApproximateNumberOfMessagesVisible"
  namespace          = "AWS/SQS"
  statistic          = "Maximum"
  threshold          = 1
  alarm_actions      = var.alarm_actions
  ok_actions         = var.alarm_actions
  treat_missing_data = "notBreaching"

  dimensions = {
    QueueName = aws_sqs_queue.ocpp16_charge_sessions_dlq.name
  }

  tags = local.tags
}
