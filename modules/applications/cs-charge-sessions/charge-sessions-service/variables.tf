variable "environment" {
  description = <<EOF
    The environment of the service.

    This needs to be declared while the service is in the old Pod Point account.

    Otherwise naming conflicts will be present in our terraform apply runs.
  EOF
  type        = string
}

variable "tags" {
  description = "key, value pair of tags to associate to your resources."
  type        = map(any)
  default     = {}
}

variable "vpc" {
  description = "All necessary values regarding the VPC"
  type = object({
    id                 = string
    private_subnet_ids = list(string)
    cidr_block         = string
  })
}

variable "database" {
  description = "All necessary values regarding the Database"
  type = object({
    instance_class = string
    instance_count = number
  })
}

variable "transactions_api_client_account_ids" {
  description = "List of AWS accounts ID that consume the transactions API."
  type        = list(string)
  default     = []
}

variable "ocpp16_rest_api" {
  description = "Object containing details of the OCPP 1.6 Rest API Gateway."
  type = object({
    region     = string
    account_id = string
    api_id     = string
    stage      = string
  })
}

variable "pod_unit_event_received_topic_arn" {
  description = "The ARN of the SNS topic to publish pod unit events to."
  type        = string
}

variable "vpc_endpoint_commands_api_security_group_id" {
  description = "Id of security group of the VPC endpoint to the commands api."
  type        = string
}

variable "vpc_endpoint_commands_api_dns" {
  description = "DNS of the VPC endpoint to the commands api"
  type        = string
}

variable "transaction_telemetry_stream_arn" {
  description = "The ARN of the transaciton-telemetry Kinesis stream."
  type        = string
}

variable "transaction_telemetry_external_publisher_role_arn" {
  description = "The ARN of the role to assume that allows publishing to the transaction-telemetry Kinesis stream"
  type        = string
}

variable "ocpp_cs_requests_sns_topic_arn" {
  description = "The ARN of the ocpp-cs-requests SNS topic."
  type        = string
}

variable "alarm_actions" {
  description = "The ARNs of the SNS topics to publish cloudwatch alarms to."
  type        = list(string)
  default     = []
}

variable "logging_comms_grafana_cidr_block" {
  description = "CIDR block for logging comms Grafana access."
  type        = string
  default     = ""
}

variable "fargate_config" {
  description = "Fargate configuration"
  type = object({
    memory : number
    cpu : number
    capacity_base : number
    capacity_weight : number
    spot_capacity_base : number
    spot_capacity_weight : number
  })
  default = {
    memory : 512,
    cpu : 256,
    capacity_base : 0,
    capacity_weight : 1,
    spot_capacity_base : 1,
    spot_capacity_weight : 1,
  }
}
