resource "aws_security_group" "vpc_endpoint_execute_api" {
  name_prefix = "vpce-execute-api-sg-"
  description = "Security group for the execute api VPC endpoint"
  vpc_id      = module.vpc.vpc_id

  lifecycle {
    create_before_destroy = true
  }

  tags = local.tags
}

resource "aws_security_group" "vpc_endpoint_commands_api" {
  name_prefix = "vpce-commands-api-sg-"
  description = "Security group for the commands api VPC endpoint"
  vpc_id      = module.vpc.vpc_id

  lifecycle {
    create_before_destroy = true
  }

  tags = local.tags
}
