variable "cidr_block" {
  type        = string
  description = "CIDR block used for the VPC."
}

variable "tags" {
  description = "key, value pair of tags to associate to your resources."
  type        = map(any)
  default     = {}
}

variable "single_nat_gateway" {
  type        = bool
  description = "Decides whether the single nat gateway is enabled or not."
}

variable "vpc_endpoint_service_name_commands_api" {
  type        = string
  description = "VPC endpoint service name for the commands api."
}
