resource "aws_secretsmanager_secret" "this" {
  name       = "holistics-secrets"
  kms_key_id = aws_kms_key.this.key_id
  tags       = var.tags
}

resource "aws_kms_key" "this" {
  tags = var.tags

  description             = "Used for encrypting secrets for the holistics service"
  policy                  = data.aws_iam_policy_document.key.json
  deletion_window_in_days = 7
}

resource "aws_secretsmanager_secret_version" "this" {
  secret_id = aws_secretsmanager_secret.this.id
  secret_string = jsonencode({
    TUNNEL_PUBLIC_KEY  = "MANUAL_INTERACTION_REQUIRED_REPLACE_ME"
    TUNNEL_PRIVATE_KEY = "MANUAL_INTERACTION_REQUIRED_REPLACE_ME"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}

data "aws_iam_policy_document" "key" {
  statement {
    sid     = "Enable Terraform Administration"
    actions = ["kms:*"]
    resources = [
      "*"
    ]
    principals {
      type = "AWS"
      identifiers = [
        format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id)
      ]
    }
  }

  statement {
    sid = "Permit ECS Service Task Execution KMS Decrypt"
    actions = [
      "kms:Decrypt"
    ]

    principals {
      type = "AWS"
      identifiers = [
        module.proxy.execution_role_arn
      ]
    }

    resources = [
      "*"
    ]
  }

  statement {
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }
    condition {
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }
  }
}
