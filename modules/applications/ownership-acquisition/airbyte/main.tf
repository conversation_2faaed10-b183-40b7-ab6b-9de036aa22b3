##########################################
// ----------- LAUNCH TEMPLATE -------- //
#########################################

resource "aws_launch_template" "airbyte_lt" {
  tags = var.tags

  name                   = "airbyte-launch-template-${var.environment}"
  image_id               = data.aws_ami.ubuntu.id
  instance_type          = var.instance_type
  description            = "Launch template for airbyte server on ec2."
  update_default_version = true

  iam_instance_profile {
    arn = aws_iam_instance_profile.airbyte.arn
  }
  network_interfaces {
    associate_public_ip_address = false
    subnet_id                   = sort(var.private_subnet_ids)[0]
    delete_on_termination       = true
    description                 = "Network interface for Airbyte ec2 server in ${var.environment} environment."
    security_groups = [
      aws_security_group.airbyte_server.id
    ]
  }
  monitoring {
    // Allow for detailed monitoring
    enabled = true
  }
  lifecycle {
    ignore_changes = [
      image_id
    ]
  }
}

locals {
  airbyte_db_host = module.aurora.cluster_endpoint
}

resource "aws_ssm_parameter" "cloudwatch_agent_config" {
  name        = "/airbyte/${var.environment}/cloudwatch-agent-config"
  description = "The cloudwatch agent configuration for airbyte server."
  type        = "String"
  value       = file("${path.module}/templates/cloudwatch-agent-config.json")
}

###########################################
// ----------- AIRBYTE INSTANCE -------- //
##########################################

resource "aws_instance" "airbyte_server" {
  tags = var.tags

  user_data = templatefile("${path.module}/templates/user_data.txt",
    {
      airbyte_version                               = "v0.58.0"
      frequency                                     = "never"
      database_user                                 = var.database_admin_username,
      database_password                             = jsondecode(data.aws_secretsmanager_secret_version.aurora.secret_string)["password"],
      database_host                                 = local.airbyte_db_host
      database_port                                 = var.database_port,
      database_db                                   = var.db_name,
      database_url                                  = "jdbc:postgresql://${local.airbyte_db_host}:${var.database_port}/${var.db_name}"
      max_sync_workers                              = var.max_sync_workers
      max_spec_workers                              = var.max_spec_workers
      max_check_workers                             = var.max_check_workers
      max_discover_workers                          = var.max_discover_workers
      job_main_container_cpu_limit                  = var.job_main_container_cpu_limit
      job_main_container_memory_limit               = var.job_main_container_memory_limit
      normalization_job_main_container_memory_limit = var.normalization_job_main_container_memory_limit
      cloudwatch_agent_config                       = aws_ssm_parameter.cloudwatch_agent_config.name
    }
  )

  launch_template {
    id = aws_launch_template.airbyte_lt.id
  }

  root_block_device {
    volume_size           = var.instance_root_volume_storage
    volume_type           = "gp3"
    delete_on_termination = true
    encrypted             = true
    kms_key_id            = resource.aws_kms_alias.ec2.target_key_arn
    tags = {
      Name = "airbyte-additional-storage-${var.environment}"
    }
  }
}
