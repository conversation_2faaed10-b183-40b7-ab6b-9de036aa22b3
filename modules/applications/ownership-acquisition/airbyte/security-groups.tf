resource "aws_security_group" "airbyte_server" {
  tags = var.tags

  name        = "airbyte-server-sg-${var.environment}"
  vpc_id      = var.vpc_id
  description = "Allow inbound traffic to airbyte server."
}

resource "aws_security_group_rule" "ingress_lb" {
  type                     = "ingress"
  description              = "Loadbalancer webapp and api."
  from_port                = 8000
  to_port                  = 8001
  protocol                 = "tcp"
  source_security_group_id = module.alb.security_group_id
  security_group_id        = aws_security_group.airbyte_server.id
}

resource "aws_security_group_rule" "egress_all" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.airbyte_server.id
}
