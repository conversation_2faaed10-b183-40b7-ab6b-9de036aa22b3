resource "aws_security_group" "redshift_db" {
  tags = var.tags

  name        = "redshift-sg-${var.environment}"
  vpc_id      = var.vpc_id
  description = "Security group for Redshift instance."
}

resource "aws_security_group_rule" "ingress_vpn" {
  type        = "ingress"
  description = "AWS Client VPN."
  from_port   = 5439
  to_port     = 5439
  protocol    = "tcp"
  cidr_blocks = [
    "**************/32", # AWS Client VPN - Public NAT 1
    "************/32",   # AWS Client VPN - Public NAT 2
    "**************/32", # AWS Client VPN - Public NAT 3
    "***********/32",    # NEW AWS Client VPN - Public NAT 1
    "************/32",   # NEW AWS Client VPN - Public NAT 2
    "*************/32",  # NEW AWS Client VPN - Public NAT 3
    "**********/22",     # AWS Client VPN - Private NAT
    "**********/22",     # AWS Client VPN - Private NAT
    "**********/22",     # Tasman VPN
  ]
  security_group_id = aws_security_group.redshift_db.id
}

resource "aws_security_group_rule" "ingress_vpc" {
  type              = "ingress"
  description       = "VPC."
  from_port         = 5439
  to_port           = 5439
  protocol          = "tcp"
  cidr_blocks       = [data.aws_vpc.this.cidr_block]
  security_group_id = aws_security_group.redshift_db.id
}

resource "aws_security_group_rule" "ingress_airbyte" {
  type                     = "ingress"
  description              = "Airbyte server."
  from_port                = 5439
  to_port                  = 5439
  protocol                 = "tcp"
  source_security_group_id = var.airbyte_security_group_id
  security_group_id        = aws_security_group.redshift_db.id
}

resource "aws_security_group_rule" "egress_all" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.redshift_db.id
}
