module "vpc" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws"
  version = "2.9.1"
  tags    = var.tags

  availability_zones_count = 3
  vpc_name                 = local.identifier
  vpc_cidr_block           = var.vpc_cidr_block
  new_bits                 = 3
  single_nat_gateway       = var.single_nat_gateway

  enable_vpc_endpoint_ecr_api = true
  enable_vpc_endpoint_ecr_dkr = true
}
