/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *        Blue Resources
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "aws_security_group_rule" "web_service_egress" {
  description       = "Permit all egress traffic."
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = module.web_service.security_group_id
}

resource "aws_security_group_rule" "listener_egress" {
  description       = "Permit all egress traffic."
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = module.listener_service.security_group_id
}

resource "aws_security_group_rule" "cli_migration_egress_mysql" {
  description              = "Permit MySQL egress traffic for the Pod Point website database migrations."
  type                     = "egress"
  from_port                = 3306
  to_port                  = 3306
  protocol                 = "tcp"
  source_security_group_id = module.cli_migration.security_group_id
  security_group_id        = module.aurora_blue.security_group_id
}

resource "aws_security_group_rule" "listener_egress_mysql" {
  description              = "Permit MySQL egress traffic for the Pod Point website listener."
  type                     = "egress"
  from_port                = 3306
  to_port                  = 3306
  protocol                 = "tcp"
  source_security_group_id = module.listener_service.security_group_id
  security_group_id        = module.aurora_blue.security_group_id
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *        Green Resources
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

resource "aws_security_group_rule" "web_service_egress_green" {
  count             = local.blue_green_enabled ? 1 : 0
  description       = "Permit all egress traffic."
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = one(module.web_service_green[*].security_group_id)
}

resource "aws_security_group_rule" "listener_egress_green" {
  count             = local.blue_green_enabled ? 1 : 0
  description       = "Permit all egress traffic (green)."
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = one(module.listener_service_green[*].security_group_id)
}

resource "aws_security_group_rule" "cli_migration_egress_mysql_green" {
  count                    = local.blue_green_enabled ? 1 : 0
  description              = "Permit MySQL egress traffic for the Pod Point website database migrations (green)."
  type                     = "egress"
  from_port                = 3306
  to_port                  = 3306
  protocol                 = "tcp"
  source_security_group_id = one(module.cli_migration_green[*].security_group_id)
  security_group_id        = one(module.aurora_green[*].security_group_id)
}

resource "aws_security_group_rule" "listener_egress_mysql_green" {
  count                    = local.blue_green_enabled ? 1 : 0
  description              = "Permit MySQL egress traffic for the Pod Point website listener (green)."
  type                     = "egress"
  from_port                = 3306
  to_port                  = 3306
  protocol                 = "tcp"
  source_security_group_id = one(module.listener_service_green[*].security_group_id)
  security_group_id        = one(module.aurora_green[*].security_group_id)
}

