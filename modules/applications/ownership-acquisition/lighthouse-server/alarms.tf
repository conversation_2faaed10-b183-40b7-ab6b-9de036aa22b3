resource "aws_cloudwatch_metric_alarm" "unhealthy_hosts_alarm" {
  tags = var.tags

  count = var.unhealthy_hosts_alarm_enabled ? 1 : 0

  alarm_name        = format("%s_unhealthy_hosts", local.identifier)
  alarm_description = "Triggers when the target group has unhealthy hosts."

  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  datapoints_to_alarm = 2
  period              = 120

  metric_name        = "UnHealthyHostCount"
  namespace          = "AWS/ApplicationELB"
  statistic          = "Maximum"
  threshold          = var.unhealthy_hosts_threshold
  actions_enabled    = true
  alarm_actions      = [data.aws_sns_topic.opsgenie.arn]
  ok_actions         = [data.aws_sns_topic.opsgenie.arn]
  treat_missing_data = "missing"

  dimensions = {
    TargetGroup  = module.alb.target_group_arn_suffix[0]
    LoadBalancer = module.alb.load_balancer_arn_suffix
  }

}

resource "aws_sns_topic_subscription" "cloudwatch_opsgenie_notifications" {
  topic_arn = data.aws_sns_topic.opsgenie.arn
  protocol  = "https"
  endpoint  = var.cloudwatch_opsgenie_api_url
}
