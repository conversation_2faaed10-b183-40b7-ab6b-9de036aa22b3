/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *               Execute API
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_vpc_endpoint" "execute_api" {
  vpc_id            = module.vpc.vpc_id
  service_name      = "com.amazonaws.${data.aws_region.current.name}.execute-api"
  vpc_endpoint_type = "Interface"
  subnet_ids        = module.vpc.private_subnets_ids

  private_dns_enabled = true

  security_group_ids = [
    aws_security_group.execute_api_vpc_endpoint.id
  ]

  tags = {
    Name = "oa-execute-api"
  }
}

resource "aws_security_group" "execute_api_vpc_endpoint" {
  name        = "${local.identifier}-vpc-endpoint-execute-api"
  description = "Security group for the Execute API VPC Endpoint."
  vpc_id      = module.vpc.vpc_id
}

resource "aws_security_group_rule" "execute_api" {
  type              = "ingress"
  security_group_id = aws_security_group.execute_api_vpc_endpoint.id

  description = "Permit All VPC subnets CIDRs to the Execute API VPC endpoint."
  protocol    = -1
  from_port   = 0
  to_port     = 0
  cidr_blocks = ["0.0.0.0/0"]
}
