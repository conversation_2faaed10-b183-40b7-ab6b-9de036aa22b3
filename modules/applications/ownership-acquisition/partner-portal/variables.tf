variable "acm_arn" {
  description = "The arn of the ACM certificate that will be referenced by the load balancer. The value the ACM certificate must support the route53 record name."
  type        = string
}

variable "alb_public_subnets" {
  description = "The subnets to assign to the ALB. These will be public facing as this is a public web app."
  type        = list(string)
}

variable "build_account_id" {
  type        = number
  description = "The ID of the build account from which ECR images will be deployed."
}

variable "cache_security_group_id" {
  description = "Cache security group ID"
  type        = string
}

variable "cloudfront_acm_arn" {
  description = "The arn of the ACM certificate that will be referenced by CloudFront. The ACM must exist in the us-east-1 region."
  type        = string
}

variable "cloudfront_enable_logging" {
  description = "Whether to enable logging for the cloudfront distribution."
  type        = bool
  default     = false
  nullable    = false
}

variable "cloudfront_realtime_metrics" {
  description = <<EOF
    A flag that indicates whether additional CloudWatch metrics are enabled for a given CloudFront distribution.
    Valid values are `Enabled` and `Disabled`.
  EOF
  type        = string
  default     = "Disabled"
  nullable    = false
}

variable "cloudwatch_opsgenie_api_url" {
  type        = string
  description = "Opsgenie Cloudwatch integration URL"
}

variable "create_lb_bucket_policy" {
  description = "Toggle that decided whether the module built bucket policy should be used."
  type        = bool
  default     = true
  nullable    = false
}

variable "cluster_instance_count" {
  description = "How many instances to provision for the aurora cluster."
  type        = number
  default     = 1
  nullable    = false
}

variable "cluster_backup_retention_period" {
  description = "The days to retain backups for. - Must be between 0 and 35. - Must be greater than 0 if the database is used as a source for a Read Replica. - Only takes effect if a backup window is set."
  type        = number
  default     = 1
  nullable    = false
}

variable "database_instance_class" {
  description = "instance class to use for the database instance."
  type        = string
  default     = "db.t4g.medium"
  nullable    = false
}

variable "database_root_user" {
  description = "The root user for the database."
  type        = string
}

variable "database_app_user" {
  description = "The user used by the fargate app. "
  type        = string
}

variable "database_enable_performance_insights" {
  type     = bool
  default  = true
  nullable = false
}

variable "database_enhanced_metrics_interval" {
  type     = number
  default  = 0
  nullable = false
}

variable "database_enable_cw_alarms" {
  description = "A boolean toggle which enables/disables some common CW alarms for the aurora database."
  type        = bool
  default     = false
  nullable    = false
}

variable "ecs_private_subnets_ids" {
  description = "The Private subnets the ECS Fargate service and tasks will use."
  type        = list(string)
}

variable "enable_lb_logs" {
  description = "A toggle that can enable or disable load balancer logs. Usually, we only want this enabling for production only."
  type        = bool
  default     = true
  nullable    = false
}

variable "enable_waf" {
  type        = bool
  description = "Toggle to decide whether AWS WAF ACL resources will be provisioned."
  default     = false
  nullable    = false
}

variable "env_app_env" {
  description = "The environment of the Laravel application."
  type        = string
}

variable "env_app_url" {
  description = "The base URL for the Laravel application."
  type        = string
}

variable "env_aws_default_region" {
  description = "The default AWS region to be used by the application."
  type        = string
}

variable "env_aws_eventbridge_source" {
  description = "The domain name used as an identifier for AWS EventBridge events. Should look like 'com.pod-point.partners.staging' for example."
  type        = string
}

variable "env_broadcast_driver" {
  description = "The name of the broadcasting driver to use to broadcast events from the application."
  type        = string
  default     = "eventbridge"
  nullable    = false
}

variable "env_cache_driver" {
  description = "The name of the cache driver to use."
  type        = string
  default     = "redis"
  nullable    = false
}

variable "env_elasticsearch_driver" {
  description = "The name of the elasticsearch driver to use."
  type        = string
  default     = "aws"
  nullable    = false
}

variable "env_elasticsearch_host" {
  description = "The host to use to connect to the elasticsearch cluster."
  type        = string
}

variable "env_firebase_credentials_sdk_client_email" {
  description = "The client email to use to connect to the firebase admin sdk."
  type        = string
}

variable "env_firebase_credentials_sdk_project_id" {
  description = "The project id to use to connect to the firebase admin sdk."
  type        = string
}

variable "env_firebase_credentials_web_app_id" {
  description = "The app id to use to connect to the firebase web sdk."
  type        = string
}

variable "env_log_channel" {
  description = "The name of the channel to use for logging purposes."
  type        = string
  default     = "single" # @todo upgrade and fix for "stack"
  nullable    = false
}

variable "env_mail_mailer" {
  description = "The name of the type of mailer to use to send transactional emails."
  type        = string
  default     = "smtp"
  nullable    = false
}

variable "env_mail_host" {
  description = "The host of the driver to use to send transactional emails."
  type        = string
  default     = "sandbox.smtp.mailtrap.io"
  nullable    = false
}

variable "env_mail_port" {
  description = "The port number of the driver to use to send transactional emails."
  type        = string
  default     = "2525"
  nullable    = false
}

variable "env_podpoint_auth_key" {
  description = "The client identifier to use to connect to the Account Service OAuth2 server. "
  type        = string
}

variable "env_podpoint_auth_url" {
  description = "The base URL of the Account Service OAuth2 server."
  type        = string
}

variable "env_psa_api_system_id" {
  description = "The base URL for the PSA Group UK API used for Single Sign On purposes."
  type        = string
}

variable "env_segment_enabled" {
  description = "Wether or not Segment tracking should be enabled."
  type        = string
  default     = "false"
  nullable    = false
}

variable "env_session_driver" {
  description = "The name of the driver to use for session persistance."
  type        = string
  default     = "redis"
  nullable    = false
}

variable "env_session_secure_cookie" {
  description = "Wether or not cookie sessions are used with https or not."
  type        = string
  default     = "true"
  nullable    = false
}

variable "env_sqs_prefix" {
  description = "The prefix for the AWS ARN of the SQS queues used within the same AWS account."
  type        = string
}

variable "env_gtm_id" {
  description = "The Google Tag Manager identifier."
  type        = string
}

variable "env_gtm_auth" {
  description = "The Google Tag Manager auth token to use."
  type        = string
}

variable "env_gtm_preview" {
  description = "The Google Tag Manager preview identifier to use."
  type        = string
}

variable "env_view_asset_cdn_url" {
  description = "The URL for the CDN to use when serving view assets from a CDN."
  type        = string
}

variable "env_mysql_ssl_ca_path" {
  description = "The path for the CA SSL cert to use when connecting to the database."
  type        = string
  default     = "/etc/ssl/certs/ca-certificates.crt"
}

variable "environment" {
  description = <<EOF
    The environment of the service.
    This needs to be declared while the service is in the old Pod Point account.
    Otherwise naming conflicts will be present in our terraform apply runs.
  EOF
  type        = string
}

variable "alarm_4xx" {
  description = "The options for 4xx alarms"
  type = object({
    evaluation_period = number
    statistic         = string
    period            = number
    threshold         = number
  })
  default = {
    evaluation_period = 1
    statistic         = "Sum"
    period            = 120
    threshold         = 100
  }
  validation {
    error_message = "alarm_4xx.statistic must be one of SampleCount, Average, Sum, Minimum or Maximum"
    condition     = length(regexall("^(SampleCount|Average|Sum|Minimum|Maximum)$", var.alarm_4xx.statistic)) > 0
  }
  nullable = false
}

variable "error_count_threshold_5xx" {
  description = "The threshold to set for the 5xx error alarm."
  type        = number
  default     = 10
  nullable    = false
}

variable "health_check_path" {
  description = "The health check path used by the target group."
  type        = string
  default     = "/health-check"
  nullable    = false
}

variable "installs_opensearch_name" {
  description = "name of the installs tools opensearch cluster"
  type        = string
}

variable "kms_admins" {
  description = "A list of administrators to assign to module managed KMS keys and infrastructure."
  type        = list(string)
}

variable "legacy_alb_dns_name" {
  description = "The URL/Domain of the OpsWorks stack."
  type        = string
  default     = null
}

variable "legacy_alb_hosted_zone" {
  description = "The r53 hosted zone for the legacy OpwWorks ALB."
  default     = "Z32O12XQLNTSW2"
  nullable    = false
}

variable "migrations_resources" {
  type     = object({ cpu : string, memory : string })
  default  = { cpu = "1024", "memory" : "2048" }
  nullable = false
}

variable "opsworks_alb_dns_name" {
  description = "The DNS name of the alb used by the old opsworks stack."
  type        = string
  default     = null
}

variable "opsworks_alb_zone_id" {
  description = "The ALB zone id used by the old opsworks stack."
  type        = string
  default     = null
}

variable "odp_events_source_environment" {
  description = "The environment to use for the odp events source."
  type        = string
}

variable "pod_point_com_hosted_zone" {
  description = "The r53 hosted zone for the pod-point.com domain."
  default     = "ZI1YF8KE9MFAW"
  nullable    = false
}

variable "primary_queue_connection" {
  description = "The name of the default primary queue connection to use."
  type        = string
  default     = "sqs"
  nullable    = false
}

variable "primary_queue_task_capacity" {
  type     = object({ min : number, max : number })
  default  = { min = 1, max = 10 }
  nullable = false
}

variable "queue_worker_resources" {
  type     = object({ cpu : string, memory : string })
  default  = { cpu = "512", "memory" : "1024" }
  nullable = false
}

# For valid resource combinations, see:
# https://docs.aws.amazon.com/AmazonECS/latest/developerguide/AWS_Fargate.html#fargate-tasks-size
variable "request_handler_resources" {
  type     = object({ cpu : string, memory : string })
  default  = { cpu = "1024", "memory" : "2048" }
  nullable = false
}

variable "request_handler_task_capacity" {
  type     = object({ min : number, max : number })
  default  = { min = 1, max = 10 }
  nullable = false
}

variable "api_handler_resources" {
  type     = object({ cpu : string, memory : string })
  default  = { cpu = "1024", "memory" : "2048" }
  nullable = false
}

variable "api_handler_task_capacity" {
  type     = object({ min : number, max : number })
  default  = { min = 1, max = 10 }
  nullable = false
}

variable "route53_record_name" {
  description = "The route 53 record name to use for the Ordering Tool."
  type        = string
}

variable "route53_record_name_redirects" {
  description = "The route 53 record names to redirect to the primary one route53_record_name."
  type        = list(string)
  default     = []
  nullable    = false
}

variable "route53_weighted_routing_policy_weight" {
  description = "How much traffic to send to the new service."
  type        = number
  default     = 0
  nullable    = false
}

variable "schedule_task_worker_enabled" {
  default  = true
  nullable = false
}

variable "scheduler_resources" {
  type     = object({ cpu : string, memory : string })
  default  = { cpu = "512", "memory" : "1024" }
  nullable = false
}

variable "secondary_queue_connection" {
  description = "The name of the default primary queue connection to use."
  type        = string
  default     = "pub_sub"
  nullable    = false
}

variable "secondary_queue_task_capacity" {
  type     = object({ min : number, max : number })
  default  = { min = 1, max = 10 }
  nullable = false
}

variable "sso_role_name_terraform_ci" {
  description = "The SSO role of the Terraform CI account."
  default     = "terraform-ci"
  nullable    = false
}

variable "tags" {
  description = "Any additional tags you may want to configure to your resources."
  type        = map(any)
  default     = {}
  nullable    = false
}

variable "target_4xx_alarm_enabled" {
  description = "Toggle for whether to enable or disable the 5xx alarm."
  type        = bool
  default     = false
  nullable    = false
}

variable "target_5xx_alarm_enabled" {
  description = "Toggle for whether to enable or disable the 5xx alarm."
  type        = bool
  default     = false
  nullable    = false
}

variable "unhealthy_hosts_alarm_enabled" {
  description = "Toggle for whether to enable or disable the unhealthy hosts alarm."
  type        = bool
  default     = false
  nullable    = false
}

variable "unhealthy_hosts_threshold" {
  description = "The threshold to set for the unhealthy hosts alarm."
  type        = number
  default     = 0
  nullable    = false
}

variable "vpc_id" {
  description = "The VPC ID."
  type        = string
}

variable "vpc_cidr" {
  description = "The CIDR block of the VPC."
  type        = string
}

variable "api_permitted_client_account_ids" {
  description = "A list of account IDs that are permitted to access the API."
  type        = list(string)
  default     = []
}

variable "vehicle_event_source" {
  description = "The event bridge source for vehicle events"
  type        = string
}
