module "ecs_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "13.0.0"
  tags    = var.tags

  identifier         = local.identifier
  repo_names         = [local.repo_name]
  container_insights = "enabled"

  additional_github_ci_policy_statements = [{
    sid    = "AllowDescribeSecurityGroupsForECSTaskRunGitHubAction"
    effect = "Allow"
    actions = [
      "ec2:DescribeSecurityGroups"
    ]
    resources = [
      "*"
    ]
  }]
}

module "request_handler" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "13.0.0"
  tags    = var.tags

  identifier   = local.request_handler_identifier
  service_type = "rolling"

  vpc_id                       = var.vpc_id
  subnet_ids                   = var.ecs_private_subnets_ids
  cluster_name                 = module.ecs_cluster.name
  cluster_arn                  = module.ecs_cluster.arn
  pipeline_role_name           = module.ecs_cluster.github_role_name
  cpu                          = var.request_handler_resources.cpu
  memory                       = var.request_handler_resources.memory
  capacity_fargate_base        = var.environment != "prod" ? 0 : var.request_handler_task_capacity.min
  capacity_fargate_weight      = 0
  capacity_fargate_spot_base   = var.environment != "prod" ? var.request_handler_task_capacity.min : 0
  capacity_fargate_spot_weight = 1
  enable_auto_scaling          = false # We can enable once we have a baseline.

  scaling_min_capacity = var.request_handler_task_capacity.min
  scaling_max_capacity = var.request_handler_task_capacity.max

  load_balancing_configuration = [
    {
      target_group_arn = module.alb.target_group_arns[0]
      container_name   = local.request_handler_identifier
      container_port   = local.container_port
    }
  ]

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.custom_ecs_task_container_policy.json

  volumes = [{
    name = "logs"
  }]

  container_definitions = templatefile("${path.module}/templates/container-definitions-request-handler.tftpl", {
    "build_account_id" : var.build_account_id
    "environment" : concat(
      local.container_config_env_vars,
      local.container_app_env_vars,
      [{
        name : "API_ENABLED"
        value : var.environment == "dev" ? "true" : "false"
      }],
    )
    "name" : local.request_handler_identifier
    "region" : data.aws_region.current.name
    "port" : local.container_port
    "secrets" : local.container_secret_env_vars
  })

  additional_kms_administrators = local.kms_admins
  kms_additional_policy_statements = [
    local.permit_break_glass_kms_access
  ]
}

module "api_handler" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "13.0.0"
  tags    = var.tags

  identifier   = local.api_handler_identifier
  service_type = "rolling"

  vpc_id                       = var.vpc_id
  subnet_ids                   = var.ecs_private_subnets_ids
  cluster_name                 = module.ecs_cluster.name
  cluster_arn                  = module.ecs_cluster.arn
  pipeline_role_name           = module.ecs_cluster.github_role_name
  cpu                          = var.api_handler_resources.cpu
  memory                       = var.api_handler_resources.memory
  capacity_fargate_base        = var.environment != "prod" ? 0 : var.request_handler_task_capacity.min
  capacity_fargate_weight      = 0
  capacity_fargate_spot_base   = var.environment != "prod" ? var.request_handler_task_capacity.min : 0
  capacity_fargate_spot_weight = 1
  enable_auto_scaling          = false # We can enable once we have a baseline.

  scaling_min_capacity = var.api_handler_task_capacity.min
  scaling_max_capacity = var.api_handler_task_capacity.max

  load_balancing_configuration = [
    {
      target_group_arn = module.privatelink_target_api.alb_target_group_arn
      container_name   = local.api_handler_identifier
      container_port   = local.container_port
    }
  ]

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.custom_ecs_task_container_policy.json

  volumes = [{
    name = "logs"
  }]

  container_definitions = templatefile("${path.module}/templates/container-definitions-request-handler.tftpl", {
    "build_account_id" : var.build_account_id
    "environment" : concat(
      local.container_config_env_vars,
      local.container_app_env_vars,
      [{
        name : "API_ENABLED"
        value : "true"
      }],
    )
    "name" : local.api_handler_identifier
    "region" : data.aws_region.current.name
    "port" : local.container_port
    "secrets" : local.container_secret_env_vars
  })

  additional_kms_administrators = local.kms_admins
  kms_additional_policy_statements = [
    local.permit_break_glass_kms_access
  ]
}

module "sqs_primary_queue_worker" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "13.0.0"
  tags    = var.tags

  identifier   = local.primary_queue_worker_identifier
  service_type = "rolling"

  vpc_id                       = var.vpc_id
  subnet_ids                   = var.ecs_private_subnets_ids
  cluster_name                 = module.ecs_cluster.name
  cluster_arn                  = module.ecs_cluster.arn
  pipeline_role_name           = module.ecs_cluster.github_role_name
  cpu                          = var.queue_worker_resources.cpu
  memory                       = var.queue_worker_resources.memory
  capacity_fargate_base        = var.environment != "prod" ? 0 : var.primary_queue_task_capacity.min
  capacity_fargate_weight      = 0
  capacity_fargate_spot_base   = var.environment != "prod" ? var.primary_queue_task_capacity.min : 0
  capacity_fargate_spot_weight = 1
  enable_auto_scaling          = false # We can enable once we have a baseline.

  scaling_min_capacity = var.primary_queue_task_capacity.min
  scaling_max_capacity = var.primary_queue_task_capacity.max

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.custom_ecs_task_container_policy.json

  volumes = [{
    name = "logs"
  }]

  container_definitions = templatefile("${path.module}/templates/container-definitions-queue-worker.tftpl", {
    "queue_connection" : var.primary_queue_connection
    "build_account_id" : var.build_account_id
    "environment" : local.container_app_env_vars
    "name" : local.primary_queue_worker_identifier
    "region" : data.aws_region.current.name
    "port" : local.container_port
    "secrets" : local.container_secret_env_vars
  })

  additional_kms_administrators = local.kms_admins
  kms_additional_policy_statements = [
    local.permit_break_glass_kms_access
  ]
}

module "sqs_secondary_queue_worker" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "13.0.0"
  tags    = var.tags

  service_type                 = "rolling"
  identifier                   = local.secondary_queue_worker_identifier
  vpc_id                       = var.vpc_id
  cluster_name                 = module.ecs_cluster.name
  cluster_arn                  = module.ecs_cluster.arn
  pipeline_role_name           = module.ecs_cluster.github_role_name
  cpu                          = var.queue_worker_resources.cpu
  memory                       = var.queue_worker_resources.memory
  capacity_fargate_base        = var.environment != "prod" ? 0 : var.secondary_queue_task_capacity.min
  capacity_fargate_weight      = 0
  capacity_fargate_spot_base   = var.environment != "prod" ? var.secondary_queue_task_capacity.min : 0
  capacity_fargate_spot_weight = 1
  subnet_ids                   = var.ecs_private_subnets_ids
  enable_auto_scaling          = false # We can enable once we have a baseline.

  scaling_min_capacity = var.secondary_queue_task_capacity.min
  scaling_max_capacity = var.secondary_queue_task_capacity.max

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.custom_ecs_task_container_policy.json

  volumes = [{
    name = "logs"
  }]

  container_definitions = templatefile("${path.module}/templates/container-definitions-queue-worker.tftpl", {
    "queue_connection" : var.secondary_queue_connection
    "build_account_id" : var.build_account_id
    "environment" : local.container_app_env_vars
    "name" : local.secondary_queue_worker_identifier
    "region" : data.aws_region.current.name
    "port" : local.container_port
    "secrets" : local.container_secret_env_vars
  })

  additional_kms_administrators = local.kms_admins
  kms_additional_policy_statements = [
    local.permit_break_glass_kms_access
  ]
}

module "scheduled_task_worker" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "13.0.0"
  tags    = var.tags

  identifier   = local.scheduled_task_worker_identifier
  service_type = "rolling"

  vpc_id                       = var.vpc_id
  subnet_ids                   = var.ecs_private_subnets_ids
  cluster_name                 = module.ecs_cluster.name
  cluster_arn                  = module.ecs_cluster.arn
  pipeline_role_name           = module.ecs_cluster.github_role_name
  cpu                          = var.scheduler_resources.cpu
  memory                       = var.scheduler_resources.memory
  capacity_fargate_base        = var.environment != "prod" ? 0 : 1
  capacity_fargate_weight      = 0
  capacity_fargate_spot_base   = var.environment != "prod" ? 1 : 0
  capacity_fargate_spot_weight = 1
  enable_auto_scaling          = false # We can enable once we have a baseline.
  scaling_min_capacity         = var.schedule_task_worker_enabled ? 1 : 0

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.custom_ecs_task_container_policy.json

  volumes = [{
    name = "logs"
  }]

  container_definitions = templatefile("${path.module}/templates/container-definitions-scheduled-task-worker.tftpl", {
    "build_account_id" : var.build_account_id
    "environment" : local.container_app_env_vars
    "name" : local.scheduled_task_worker_identifier
    "region" : data.aws_region.current.name
    "port" : local.container_port
    "secrets" : local.container_secret_env_vars
  })

  additional_kms_administrators = local.kms_admins
  kms_additional_policy_statements = [
    local.permit_break_glass_kms_access
  ]
}

module "database_migrations" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/standalone-task"
  version = "13.0.0"
  tags    = var.tags

  identifier         = local.migrations_task_identifier
  pipeline_role_name = module.ecs_cluster.github_role_name
  vpc_id             = var.vpc_id
  cpu                = var.migrations_resources.cpu
  memory             = var.migrations_resources.memory

  attach_custom_ecs_task_execution_iam_policy = true
  ecs_task_execution_custom_policy            = data.aws_iam_policy_document.custom_ecs_task_execution_policy.json
  attach_custom_ecs_task_iam_policy           = true
  ecs_task_custom_policy                      = data.aws_iam_policy_document.custom_ecs_task_container_policy.json

  volumes = [{
    name = "logs"
  }]

  container_definition = templatefile("${path.module}/templates/container-definitions-db-migrate.tftpl", {
    "build_account_id" : var.build_account_id
    "environment" : local.container_app_env_vars
    "name" : local.migrations_task_identifier
    "region" : data.aws_region.current.name
    "secrets" : local.container_secret_env_vars
  })

  additional_kms_administrators = local.kms_admins
  kms_additional_policy_statements = [
    local.permit_break_glass_kms_access
  ]
}
