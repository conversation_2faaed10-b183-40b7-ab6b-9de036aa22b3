[
  {
    "environment": ${jsonencode(environment)},
    "mountPoints": [
        {
            "sourceVolume": "logs",
            "containerPath": "/var/www/html/storage/logs",
            "readOnly": false
        }
    ],
    "essential": true,
    "image": "${build_account_id}.dkr.ecr.${region}.amazonaws.com/${name}:latest",
    "linuxParameters": {
      "initProcessEnabled": false
    },
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "/ecs/${name}",
        "awslogs-region": "${region}",
        "awslogs-stream-prefix": "ecs"
      }
    },
    "name": "${name}",
    "networkMode": "awsvpc",
    "portMappings": [
      {
        "protocol" : "tcp",
        "containerPort": ${port},
        "hostPort": ${port}
      }
    ],
    "secrets": ${jsonencode(secrets)},
    "healthCheck": {
      "command": ["CMD-SHELL", "curl -f http://localhost/health-check || exit 1"],
      "interval": 15,
      "retries": 5
    },
    "dependsOn": [
        {
            "containerName": "logs",
            "condition": "START"
        }
    ]
  },
  {
      "name": "logs",
      "image": "busybox:1.36",
      "cpu": 32,
      "portMappings": [],
      "essential": true,
      "command": [
          "/bin/sh",
          "-c",
          "touch /logs/laravel.log && chmod 777 /logs/laravel.log && tail -F /logs/laravel.log"
      ],
      "environment": [],
      "mountPoints": [
          {
              "sourceVolume": "logs",
              "containerPath": "/logs",
              "readOnly": false
          }
      ],
      "volumesFrom": [],
      "logConfiguration": {
          "logDriver": "awslogs",
          "options": {
              "awslogs-group": "/ecs/${name}",
              "awslogs-region": "${region}",
              "awslogs-stream-prefix": "ecs"
         }
      }
  }
]
