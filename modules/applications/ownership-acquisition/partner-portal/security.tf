locals {
  fargate_service_security_group_ids = [
    module.request_handler.security_group_id,
    module.api_handler.security_group_id,
    module.sqs_primary_queue_worker.security_group_id,
    module.sqs_secondary_queue_worker.security_group_id,
    module.scheduled_task_worker.security_group_id,
    module.database_migrations.security_group_id,
  ]
}

resource "aws_security_group_rule" "cache_security_group_ingress" {
  count = length(local.fargate_service_security_group_ids)
  depends_on = [
    module.request_handler,
    module.api_handler,
    module.sqs_primary_queue_worker,
    module.sqs_secondary_queue_worker,
    module.scheduled_task_worker,
    module.database_migrations,
  ]

  description              = "Permit ingress traffic from the Partner Portal."
  type                     = "ingress"
  from_port                = 6379
  to_port                  = 6379
  protocol                 = "tcp"
  source_security_group_id = local.fargate_service_security_group_ids[count.index]
  security_group_id        = var.cache_security_group_id
}
