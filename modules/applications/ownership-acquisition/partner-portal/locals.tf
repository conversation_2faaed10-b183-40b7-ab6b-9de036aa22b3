locals {
  app_name       = "partner-portal"
  identifier     = format("%s-%s", local.app_name, var.environment)
  container_port = "80"
  repo_name      = "partner-portal" # GitHub repository name

  request_handler_identifier        = format("partner-portal-request-handler-%s", var.environment)
  api_handler_identifier            = format("partner-portal-api-handler-%s", var.environment)
  primary_queue_worker_identifier   = format("partner-portal-primary-queue-wrkr-%s", var.environment)
  secondary_queue_worker_identifier = format("partner-portal-secondary-queue-wrkr-%s", var.environment)
  scheduled_task_worker_identifier  = format("partner-portal-scheduled-task-wrkr-%s", var.environment)
  migrations_task_identifier        = format("%s-db-migrate", local.identifier)

  kms_admins = flatten([
    data.aws_iam_role.terraform_ci.arn,
    var.kms_admins
  ])

  container_config_env_vars = [
    {
      "name" : "PHP_PM_CONTROL",
      "value" : "static"
    },
    {
      "name" : "PHP_PM_MAX_CHILDREN",
      "value" : "30"
    }
  ]

  container_app_env_vars = [
    {
      "name" : "APP_ENV"
      "value" : var.env_app_env
    },
    {
      "name" : "APP_URL"
      "value" : var.env_app_url
    },
    {
      "name" : "AWS_DEFAULT_REGION"
      "value" : var.env_aws_default_region
    },
    {
      "name" : "AWS_EVENTBRIDGE_SOURCE"
      "value" : var.env_aws_eventbridge_source
    },
    {
      "name" : "AWS_INSTALLATIONS_SQS_SNS_TOPIC_ARN"
      "value" : aws_sns_topic.installation_events.arn
    },
    {
      "name" : "AWS_MEDIA_BUCKET"
      "value" : module.s3_media.s3_bucket_id
    },
    {
      "name" : "AWS_ORDER_STATUSES_SQS_SNS_TOPIC_ARN"
      "value" : aws_sns_topic.order_status_events.arn
    },
    {
      "name" : "AWS_PRICING_GROUPS_SQS_SNS_TOPIC_ARN"
      "value" : aws_sns_topic.pricing_group_events.arn
    },
    {
      "name" : "AWS_SQS_SNS_QUEUE"
      "value" : aws_sqs_queue.pub_sub_events.name
    },
    {
      "name" : "AWS_VEHICLES_SQS_SNS_TOPIC_ARN"
      "value" : aws_sns_topic.vehicle_events.arn
    },
    {
      "name" : "BROADCAST_DRIVER"
      "value" : var.env_broadcast_driver
    },
    {
      "name" : "CACHE_DRIVER"
      "value" : var.env_cache_driver
    },
    {
      "name" : "DB_DATABASE"
      "value" : local.database_name
    },
    {
      "name" : "DB_HOST"
      "value" : module.aurora.cluster_endpoint
    },
    {
      "name" : "DB_USERNAME"
      "value" : var.database_app_user
    },
    {
      "name" : "ELASTICSEARCH_DRIVER"
      "value" : var.env_elasticsearch_driver
    },
    {
      "name" : "ELASTICSEARCH_HOST"
      "value" : var.env_elasticsearch_host
    },
    {
      "name" : "FIREBASE_CREDENTIALS_SDK_CLIENT_EMAIL"
      "value" : var.env_firebase_credentials_sdk_client_email
    },
    {
      "name" : "FIREBASE_CREDENTIALS_SDK_PROJECT_ID"
      "value" : var.env_firebase_credentials_sdk_project_id
    },
    {
      "name" : "FIREBASE_CREDENTIALS_WEB_APP_ID"
      "value" : var.env_firebase_credentials_web_app_id
    },
    {
      "name" : "LOG_CHANNEL"
      "value" : var.env_log_channel
    },
    {
      "name" : "LOGGING_STREAM"
      "value" : data.aws_kinesis_stream.logging.name
    },
    {
      "name" : "MAIL_DRIVER"
      "value" : var.env_mail_mailer
    },
    {
      "name" : "MAIL_MAILER"
      "value" : var.env_mail_mailer
    },
    {
      "name" : "MAIL_HOST"
      "value" : var.env_mail_host
    },
    {
      "name" : "MAIL_PORT"
      "value" : var.env_mail_port
    },
    {
      "name" : "PODPOINT_AUTH_KEY"
      "value" : var.env_podpoint_auth_key
    },
    {
      "name" : "PODPOINT_AUTH_URL"
      "value" : var.env_podpoint_auth_url
    },
    {
      "name" : "PSA_API_SYSTEM_ID"
      "value" : var.env_psa_api_system_id
    },
    {
      "name" : "QUEUE_CONNECTION"
      "value" : var.primary_queue_connection
    },
    {
      "name" : "REDIS_HOST"
      "value" : aws_elasticache_replication_group.partner_portal_cache.primary_endpoint_address
    },
    {
      "name" : "SEGMENT_ENABLED"
      "value" : var.env_segment_enabled
    },
    {
      "name" : "SESSION_DRIVER"
      "value" : var.env_session_driver
    },
    {
      "name" : "SESSION_SECURE_COOKIE"
      "value" : var.env_session_secure_cookie
    },
    {
      "name" : "SQS_PREFIX"
      "value" : var.env_sqs_prefix
    },
    {
      "name" : "SQS_QUEUE"
      "value" : aws_sqs_queue.primary.name
    },
    {
      "name" : "GTM_ID",
      "value" : var.env_gtm_id
    },
    {
      "name" : "GTM_AUTH",
      "value" : var.env_gtm_auth
    },
    {
      "name" : "GTM_PREVIEW",
      "value" : var.env_gtm_preview
    },
    {
      "name" : "VIEW_ASSET_CDN_URL"
      "value" : var.env_view_asset_cdn_url
    },
    {
      "name" : "MYSQL_ATTR_SSL_CA",
      "value" : var.env_mysql_ssl_ca_path
    }
  ]

  container_secret_env_vars = [
    {
      "name" : "APP_KEY"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "APP_KEY")
    },
    {
      "name" : "CONFIGCAT_KEY"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "CONFIGCAT_KEY")
    },
    {
      "name" : "DB_PASSWORD"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "DB_PASSWORD")
    },
    {
      "name" : "FIREBASE_CREDENTIALS_SDK_CLIENT_ID"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "FIREBASE_CREDENTIALS_SDK_CLIENT_ID")
    },
    {
      "name" : "FIREBASE_CREDENTIALS_SDK_PRIVATE_KEY"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "FIREBASE_CREDENTIALS_SDK_PRIVATE_KEY")
    },
    {
      "name" : "FIREBASE_CREDENTIALS_SDK_PRIVATE_KEY_ID"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "FIREBASE_CREDENTIALS_SDK_PRIVATE_KEY_ID")
    },
    {
      "name" : "FIREBASE_CREDENTIALS_WEB_API_KEY"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "FIREBASE_CREDENTIALS_WEB_API_KEY")
    },
    {
      "name" : "FIREBASE_CREDENTIALS_WEB_RECAPTCHA_KEY"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "FIREBASE_CREDENTIALS_WEB_RECAPTCHA_KEY")
    },
    {
      "name" : "MAIL_USERNAME"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "MAIL_USERNAME")
    },
    {
      "name" : "MAIL_PASSWORD"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "MAIL_PASSWORD")
    },
    {
      "name" : "MANDRILL_SECRET"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "MANDRILL_SECRET")
    },
    {
      "name" : "PODPOINT_AUTH_SECRET"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "PODPOINT_AUTH_SECRET")
    },
    {
      "name" : "PSA_API_SECRET"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "PSA_API_SECRET")
    },
    {
      "name" : "SEGMENT_WRITE_KEY"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "SEGMENT_WRITE_KEY")
    },
    {
      "name" : "SENTRY_DSN"
      "valueFrom" : format("%s:%s::", aws_secretsmanager_secret.partner_portal.arn, "SENTRY_DSN")
    }
  ]

  database_name                = "dealerships"
  parameter_group_family       = "aurora-mysql8.0"
  db_engine_version            = "8.0.mysql_aurora.3.05.2"
  apply_db_changes_immediately = false

  permit_break_glass_kms_access = {
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals = [{
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }]
    condition = [{
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }]
  }
}
