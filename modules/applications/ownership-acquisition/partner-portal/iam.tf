data "aws_iam_policy_document" "custom_ecs_task_execution_policy" {
  statement {
    sid     = "RetrieveSecretManagerSecretValues"
    actions = ["secretsmanager:GetSecretValue"]
    resources = [
      aws_secretsmanager_secret.partner_portal.arn,
    ]
  }
  statement {
    sid     = "AllowKMSBackendKeyDecrypt"
    actions = ["kms:Decrypt"]
    resources = [
      aws_kms_key.backend.arn
    ]
  }
}

data "aws_iam_policy_document" "custom_ecs_task_container_policy" {
  source_policy_documents = [
    data.aws_iam_policy_document.logging.json,
    data.aws_iam_policy_document.loadbalancer_registration.json,
    data.aws_iam_policy_document.restrict_workload.json,
    data.aws_iam_policy_document.put_events.json,
    data.aws_iam_policy_document.dms_full_access.json,
    data.aws_iam_policy_document.installs_opensearch.json,
  ]

  statement {
    actions = [
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel",
    ]
    resources = ["*"]
    effect    = "Allow"
  }

  statement {
    sid = "WritesToLoggingKinesis"
    actions = [
      "kinesis:PutRecord",
      "kinesis:PutRecords"
    ]
    resources = [
      data.aws_kinesis_stream.logging.arn
    ]
    effect = "Allow"
  }

  statement {
    sid     = "AllowKMSBackendKeyDecrypt"
    actions = ["kms:Decrypt"]
    resources = [
      aws_kms_key.backend.arn
    ]
  }

  statement {
    sid = "AllowS3Access"
    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:DeleteObject"
    ]
    resources = ["${module.s3_media.s3_bucket_arn}/*"]
  }
}

resource "aws_iam_role_policy_attachment" "request_handler_additional_policy_attachments" {
  for_each = toset([
    data.aws_iam_policy.es_full_access.arn,
    data.aws_iam_policy.sqs_full_access.arn,
  ])
  role       = module.request_handler.task_role_name
  policy_arn = each.key
}

resource "aws_iam_role_policy_attachment" "sqs_primary_queue_worker_additional_policy_attachments" {
  for_each = toset([
    data.aws_iam_policy.es_full_access.arn,
    data.aws_iam_policy.sqs_full_access.arn,
  ])
  role       = module.sqs_primary_queue_worker.task_role_name
  policy_arn = each.key
}

resource "aws_iam_role_policy_attachment" "sqs_secondary_queue_worker_additional_policy_attachments" {
  for_each = toset([
    data.aws_iam_policy.es_full_access.arn,
    data.aws_iam_policy.sqs_full_access.arn,
  ])
  role       = module.sqs_secondary_queue_worker.task_role_name
  policy_arn = each.key
}

resource "aws_iam_role_policy_attachment" "scheduled_task_worker_additional_policy_attachments" {
  for_each = toset([
    data.aws_iam_policy.es_full_access.arn,
    data.aws_iam_policy.sqs_full_access.arn,
  ])
  role       = module.scheduled_task_worker.task_role_name
  policy_arn = each.key
}

data "aws_iam_policy_document" "backend" {
  statement {
    sid     = "Enable Account Administration"
    actions = ["kms:*"]
    resources = [
      "*"
    ]
    principals {
      type        = "AWS"
      identifiers = local.kms_admins
    }
  }

  statement {
    sid = "Permit ECS Service Task Execution KMS Decrypt"
    actions = [
      "kms:Decrypt"
    ]

    principals {
      type = "AWS"
      identifiers = [
        module.request_handler.execution_role_arn,
        module.api_handler.execution_role_arn,
        module.sqs_primary_queue_worker.execution_role_arn,
        module.sqs_secondary_queue_worker.execution_role_arn,
        module.scheduled_task_worker.execution_role_arn,
        module.database_migrations.execution_role_arn,
      ]
    }

    resources = [
      "*"
    ]
  }

  statement {
    sid    = "PermitBreakGlass"
    effect = "Allow"
    actions = [
      "kms:*"
    ]
    resources = ["*"]
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }
    condition {
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }
  }
}
