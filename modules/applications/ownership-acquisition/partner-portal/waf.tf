/**
 * IP Sets managed in the organisation/aws-security workspace.
 * Currently IP'sets are only provisioned in the Pod Point AWS account.
*/

locals {
  environments_restricted_to_vpn = ["staging", "dev"]
}

data "aws_wafv2_ip_set" "allow_list" {
  provider = aws.us-east-1

  count = var.enable_waf ? 1 : 0

  name  = "cf-allow-list"
  scope = "CLOUDFRONT"
}

data "aws_wafv2_ip_set" "block_list" {
  provider = aws.us-east-1

  count = var.enable_waf ? 1 : 0

  name  = "cf-block-list"
  scope = "CLOUDFRONT"
}

resource "aws_wafv2_ip_set" "allow_list_vpc" {
  provider = aws.us-east-1
  count    = var.enable_waf && var.environment != "prod" ? 1 : 0

  name               = "${local.identifier}-allow-list-vpc"
  description        = "Trusted VPC NAT IP addresses"
  scope              = "CLOUDFRONT"
  ip_address_version = "IPV4"
  addresses = [
    "************/32",  # AWS Pod Point VPC, NAT Gateway public IP
    "*************/32", # AWS Pod Point VPC, NAT Gateway public IP
    "************/32",  # AWS Pod Point VPC, NAT Gateway public IP
  ]
}

resource "aws_wafv2_web_acl" "cloudfront" {
  provider = aws.us-east-1
  tags     = var.tags

  count = var.enable_waf ? 1 : 0

  name        = local.identifier
  description = "WAF to monitor and protect traffic for the ${local.identifier} Cloudfront distribution"
  scope       = "CLOUDFRONT"

  dynamic "default_action" {
    for_each = contains(local.environments_restricted_to_vpn, var.environment) ? [1] : []

    content {
      block {}
    }
  }

  dynamic "default_action" {
    for_each = contains(local.environments_restricted_to_vpn, var.environment) ? [] : [1]

    content {
      allow {}
    }
  }

  visibility_config {
    metric_name                = local.identifier
    cloudwatch_metrics_enabled = true
    sampled_requests_enabled   = true
  }

  rule {
    name     = "BlockListedAddr"
    priority = 0

    action {
      block {}
    }

    statement {
      ip_set_reference_statement {
        arn = data.aws_wafv2_ip_set.block_list[0].arn
      }
    }

    visibility_config {
      metric_name                = "${local.identifier}-BlockListedAddr"
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  dynamic "rule" {
    for_each = contains(local.environments_restricted_to_vpn, var.environment) ? [1] : []

    content {
      name     = "AllowListedVPCAddr"
      priority = 3

      action {
        allow {}
      }

      statement {
        ip_set_reference_statement {
          arn = aws_wafv2_ip_set.allow_list_vpc[0].arn
        }
      }

      visibility_config {
        metric_name                = format("%sAllowListedVPCAddr", local.identifier)
        cloudwatch_metrics_enabled = true
        sampled_requests_enabled   = true
      }
    }
  }

  dynamic "rule" {
    for_each = contains(local.environments_restricted_to_vpn, var.environment) ? [1] : []

    content {
      name     = "AllowListedAddr"
      priority = 4

      action {
        allow {}
      }

      statement {
        ip_set_reference_statement {
          arn = data.aws_wafv2_ip_set.allow_list[0].arn
        }
      }

      visibility_config {
        metric_name                = format("%sAllowListedAddr", local.identifier)
        cloudwatch_metrics_enabled = true
        sampled_requests_enabled   = true
      }
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesAmazonIpReputationList"
    priority = 5

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAmazonIpReputationList"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      metric_name                = "${local.identifier}-AmazonIpReputationList"
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 10

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
        version     = "Version_1.17"
      }
    }

    visibility_config {
      metric_name                = "${local.identifier}-KnownBadInputsRuleSet"
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesLinuxRuleSet"
    priority = 15

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesLinuxRuleSet"
        vendor_name = "AWS"
        version     = "Version_2.1"
      }
    }

    visibility_config {
      metric_name                = "${local.identifier}-LinuxRuleSet"
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesUnixRuleSet"
    priority = 20

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesUnixRuleSet"
        vendor_name = "AWS"
        version     = "Version_1.1"
      }
    }

    visibility_config {
      metric_name                = "${local.identifier}-UnixRuleSet"
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesPHPRuleSet"
    priority = 25

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesPHPRuleSet"
        vendor_name = "AWS"
        version     = "Version_2.0"
      }
    }

    visibility_config {
      metric_name                = "${local.identifier}-PHPRuleSet"
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesCommonRuleSet"
    priority = 30

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
        version     = "Version_1.6"
      }
    }

    visibility_config {
      metric_name                = "${local.identifier}-CommonRuleSet"
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesSQLiRuleSet"
    priority = 35

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesSQLiRuleSet"
        vendor_name = "AWS"
        version     = "Version_2.0"
      }
    }

    visibility_config {
      metric_name                = "${local.identifier}-SQLiRuleSet"
      cloudwatch_metrics_enabled = true
      sampled_requests_enabled   = true
    }
  }
}

module "waf_access_logs_database" {
  providers = {
    aws = aws.us-east-1
  }

  source  = "terraform-enterprise.pod-point.com/technology/waf/aws//modules/waf_access_logs_database"
  version = "2.1.1"
  tags    = var.tags

  count = var.enable_waf ? 1 : 0

  acl_scope                  = "CLOUDFRONT"
  athena_database_identifier = replace(local.identifier, "-", "_")
  glue_catalogue_identifier  = "${replace(local.identifier, "-", "_")}_waf"

  query_execution_s3_bucket_identifier = "${local.identifier}-query"
  log_bucket_s3_bucket_identifier      = local.identifier

  waf_acl_arn                  = aws_wafv2_web_acl.cloudfront[0].arn
  waf_acl_name                 = aws_wafv2_web_acl.cloudfront[0].name
  athena_workgroup_identifier  = "${local.identifier}-waf"
  athena_workgroup_description = "WAF for the ${local.identifier} cloudfront distribution."
}
