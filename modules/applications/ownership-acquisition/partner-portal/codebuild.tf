module "smoke_tests_host" {
  source  = "terraform-enterprise.pod-point.com/technology/codebuild/aws"
  version = "1.0.1"
  tags    = var.tags

  namespace   = local.identifier
  name        = "smoke-tests"
  description = "Partner Portal smoke tests"

  buildspec              = ".codebuild/buildspec-smoke-tests.yml"
  build_timeout          = 15
  concurrent_build_limit = 6

  repo_name = local.repo_name

  vpc_id            = var.vpc_id
  subnets_ids       = var.ecs_private_subnets_ids
  extra_permissions = ["ecs:DescribeServices"]
  privileged_mode   = true
}
