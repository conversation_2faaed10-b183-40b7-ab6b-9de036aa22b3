resource "aws_cloudwatch_metric_alarm" "airbyte_cpu" {
  count               = var.environment == "prod" ? 1 : 0
  alarm_name          = "airbyte-${var.environment}-CPUUtilization"
  alarm_description   = "Alarm when CPU Utilization is above 80%"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  unit                = "Percent"
  alarm_actions       = [var.sns_arn]
  ok_actions          = [var.sns_arn]

  dimensions = {
    InstanceId = aws_instance.airbyte_server.id
  }
}

resource "aws_cloudwatch_metric_alarm" "airbyte_network_util" {
  count = var.environment == "prod" ? 1 : 0
  // Establishing Network Utilization from docs https://marbot.io/blog/monitoring-ec2-network-utilization.html
  // Combines both NetworkIn + NetworkOut. Up to 10Gbps is an upper bound.
  alarm_name          = "airbyte-${var.environment}-NetworkUtilization"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  // Above baseline bandwidth for instance r5a.large:: ref https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/memory-optimized-instances.html
  threshold         = "0.75"
  alarm_description = "Network utilizations exceeding baseline bandwidth of 0.75 Gbit/s, in burstable mode up to 10Gbit/s."
  alarm_actions     = [var.sns_arn]
  ok_actions        = [var.sns_arn]
  metric_query {
    id = "e2"
    // divide by 300 to get seconds, and NetworkIn+NetworkOut / 8.0e-9 converts bytes -> Gbits
    // This expression will give Gbit/s (per second) which ra5.large has a baseline bandwidths of 0.75
    expression  = "((n1+n2)/1.25e+8)/300"
    label       = "NetworkUtilization"
    return_data = "true"
  }

  metric_query {
    id = "n1"
    metric {
      metric_name = "NetworkIn"
      namespace   = "AWS/EC2"
      period      = "300"
      stat        = "Sum"
      unit        = "Bytes"

      dimensions = {
        InstanceId = aws_instance.airbyte_server.id
      }
    }
  }

  metric_query {
    id = "n2"
    metric {
      metric_name = "NetworkOut"
      namespace   = "AWS/EC2"
      period      = "300"
      stat        = "Sum"
      unit        = "Bytes"

      dimensions = {
        InstanceId = aws_instance.airbyte_server.id
      }
    }
  }
}
