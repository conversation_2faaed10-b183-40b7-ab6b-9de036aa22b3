##########################################
// ----------- LAUNCH TEMPLATE -------- //
#########################################

resource "aws_launch_template" "airbyte_lt" {
  name          = "airbyte-launch-template-${var.environment}"
  image_id      = data.aws_ami.ubuntu.id
  instance_type = var.instance_type
  description   = "Launch template for airbyte server on ec2."

  iam_instance_profile {
    name = aws_iam_instance_profile.airbyte.name
  }
  network_interfaces {
    associate_public_ip_address = false
    subnet_id                   = sort(var.private_subnet_ids)[0]
    delete_on_termination       = true
    description                 = "Network interface for Airbyte ec2 server in ${var.environment} environment."
    security_groups = [
      aws_security_group.airbyte_server.id
    ]
  }
  monitoring {
    // Allow for detailed monitoring
    enabled = true
  }
  lifecycle {
    ignore_changes = [
      image_id
    ]
  }
}

###########################################
// ----------- AIRBYTE INSTANCE -------- //
##########################################

resource "aws_instance" "airbyte_server" {

  user_data = templatefile("${path.module}/scripts/user_data.txt",
    {
      airbyte_version                 = "v0.59.1"
      frequency                       = "never"
      database_user                   = var.database_admin_username,
      database_password               = jsondecode(data.aws_secretsmanager_secret_version.airbyte.secret_string)["password"],
      database_host                   = aws_db_instance.airbyte.address,
      database_port                   = var.database_port,
      database_db                     = var.db_name,
      database_url                    = "jdbc:postgresql://${aws_db_instance.airbyte.address}:${var.database_port}/${var.db_name}"
      max_sync_workers                = var.max_sync_workers
      max_spec_workers                = var.max_spec_workers
      max_check_workers               = var.max_check_workers
      max_discover_workers            = var.max_discover_workers
      job_main_container_cpu_limit    = var.job_main_container_cpu_limit
      job_main_container_memory_limit = var.job_main_container_memory_limit
    }
  )

  lifecycle {
    ignore_changes = [
      user_data,
    ]
  }

  launch_template {
    id = aws_launch_template.airbyte_lt.id
  }

  root_block_device {

    volume_size = lookup(
      {
        "dev"     = 70
        "staging" = 30
        "prod"    = 50
      },
      var.environment,
      var.instance_root_volume_storage
    )
    volume_type           = "gp3"
    delete_on_termination = true
    encrypted             = true
    kms_key_id            = data.aws_kms_alias.ec2.target_key_arn
    tags = {
      Name = "airbyte-additional-storage-${var.environment}"
    }
  }

  tags = {
    Name = "airbyte-server-${var.environment}"
    Role = "airbyte-server"
  }
}
