#######################################
// ------- POLICY DOCUMENTS -------- //
#######################################

data "aws_iam_policy_document" "ec2_assume" {
  statement {
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "airbyte" {
  # s3 permissions for airbyte-server
  statement {
    sid = "S3ListAccess"
    actions = [
      "s3:ListBucket",
      "s3:ListAllMyBuckets",
    ]
    resources = [
      "*"
    ]
  }

  statement {
    sid = "AllowObjectActions"
    actions = [
      "s3:*Object",
    ]
    resources = [
      "${local.bucket_arn}/*",
    ]
  }

  # Pull ECR images
  statement {
    sid = "AllowPull"
    actions = [
      "ecr:BatchGetImage",
      "ecr:GetDownloadUrlForLayer",
      "ecr:GetAuthorizationToken"
    ]
    resources = ["*"]
  }

  # SSM Permissions to use Session Manager
  statement {
    sid = "SsmAccess"
    actions = [
      "ssm:UpdateInstanceInformation",
      "ssmmessages:CreateControlChannel",
      "ssmmessages:CreateDataChannel",
      "ssmmessages:OpenControlChannel",
      "ssmmessages:OpenDataChannel"
    ]
    resources = ["*"]
  }

  statement {
    sid = "SsmAccessS3"
    actions = [
      "s3:GetEncryptionConfiguration"
    ]
    resources = ["*"]
  }
}

#############################
// ------- POLICIES -------- //
#############################

resource "aws_iam_policy" "airbyte" {
  name   = "AirbytePolicy"
  path   = "/custom/airbyte/"
  policy = data.aws_iam_policy_document.airbyte.json
}

############################
// ------- ROLES -------- //
############################

resource "aws_iam_role" "airbyte" {
  name               = "AirbyteRole"
  path               = "/custom/airbyte/"
  assume_role_policy = data.aws_iam_policy_document.ec2_assume.json
}

resource "aws_iam_role_policy_attachment" "airbyte" {
  role       = aws_iam_role.airbyte.name
  policy_arn = aws_iam_policy.airbyte.arn
}

############################
// ------- USERS -------- //
############################

resource "aws_iam_user" "airbyte_s3" {
  name = "airbyte-s3"
  tags = {
    "Purpose" = "S3 Gets / Puts"
  }

}

resource "aws_iam_user_policy_attachment" "airbyte_s3" {
  user       = aws_iam_user.airbyte_s3.id
  policy_arn = aws_iam_policy.airbyte.arn
}

########################################
// ------- INSTANCE PROFILES -------- //
########################################

resource "aws_iam_instance_profile" "airbyte" {
  name = "AirbyteInstanceProfile"
  path = "/custom/airbyte/"
  role = aws_iam_role.airbyte.name
}
