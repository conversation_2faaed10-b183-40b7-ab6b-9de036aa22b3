

###################################
// ------- LOADER USERS -------- //
###################################

resource "redshift_user" "dms" {
  name     = "dms"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["dms"]
}

resource "redshift_user" "airbyte" {
  name     = "airbyte"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["airbyte"]
}

resource "redshift_user" "dbtload" {
  # dbt generated COPY command user
  name     = "dbtload"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["dbtload"]
}

###################################
// ------- READER USERS -------- //
###################################

resource "redshift_user" "analyst" {
  name     = "analyst"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["analyst"]
}

resource "redshift_user" "data_analyst" {
  name     = "data_analyst"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["data_analyst"]
}

resource "redshift_user" "dagster" {
  name     = "dagster"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["dagster"]
}
## As part of DE-172, `dagster` user has been manually granted the sys:operator default role to run VACUUM and ANALYZE commands using:
## GRANT ROLE sys:operator TO dagster
##
## Current provider doesn't support RBAC : https://github.com/brainly/terraform-provider-redshift/issues/87
## When they do, please back the grant statement to terraform

#####################################
// ------- ENGINEER USERS -------- //
#####################################

resource "redshift_user" "data_engineer" {
  name     = "data_engineer"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["data_engineer"]
}

resource "redshift_user" "software_engineer" {
  name     = "software_engineer"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["software_engineer"]
}

resource "redshift_user" "devops_engineer" {
  name     = "devops_engineer"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["devops_engineer"]
}

#####################################
// ------ TRANFORMER USERS ------- //
#####################################
resource "redshift_user" "dbt" {
  name     = "dbt"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["dbt"]
}

resource "redshift_user" "dbt_dev" {
  count    = var.environment == "dev" ? 1 : 0
  name     = "dbt_dev"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["dbt_dev"]
}

#####################################
// ------ EXTERNAL USERS --------- //
#####################################

resource "redshift_user" "tasman_ro" {
  name     = "tasman_ro"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["tasman_ro"]
}
