resource "redshift_user" "amourou" {
  name     = "amourou"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["default_user_password"]

  lifecycle {
    ignore_changes = [password]
  }
}

resource "redshift_schema" "ops_analysis" {
  # gridbu schema
  name  = "ops_analysis"
  owner = redshift_user.data_engineer.name
}

resource "redshift_group" "ops_analysis" {
  name = "ops_analysis"
  users = [
    redshift_user.amourou.name,
  ]
}

locals {
  ops_analysis_read_schemas = merge({
    mis_dms                 = redshift_schema.mis_dms.name,
    salesforce_prod_airbyte = redshift_schema.salesforce_prod_airbyte.name,
    utility                 = redshift_schema.utility.name,
    exchange_rates_airbyte  = redshift_schema.exchange_rates_airbyte.name,
  })
  ops_analysis_write_schemas = merge({
    ops_analysis = redshift_schema.ops_analysis.name
  })
}
resource "redshift_grant" "ops_analysis_usage" {
  for_each    = local.ops_analysis_read_schemas
  group       = redshift_group.ops_analysis.name
  schema      = each.value
  object_type = "schema"
  privileges  = ["USAGE"]
}

resource "redshift_grant" "ops_analysis_create" {
  for_each    = local.ops_analysis_write_schemas
  group       = redshift_group.ops_analysis.name
  schema      = each.value
  object_type = "schema"
  privileges  = ["USAGE", "CREATE"]
}

resource "redshift_grant" "ops_analysis_select" {
  for_each    = local.ops_analysis_read_schemas
  group       = redshift_group.ops_analysis.name
  schema      = each.value
  object_type = "table"
  privileges  = ["SELECT"]
}

resource "redshift_grant" "ops_analysis_full" {
  for_each    = local.ops_analysis_write_schemas
  group       = redshift_group.ops_analysis.name
  schema      = each.value
  object_type = "table"
  privileges  = ["SELECT", "INSERT", "UPDATE", "DELETE", "DROP"]
}

# GRANT engineers read on all tables created by gridbu
resource "redshift_default_privileges" "engineers_default_privs_on_ops_analysis" {
  for_each    = redshift_group.ops_analysis.users
  owner       = each.value
  group       = redshift_group.engineers.name
  object_type = "table"
  privileges  = ["SELECT", "INSERT", "UPDATE", "DELETE", "DROP"]
}

# GRANT ops_analysis group SELECT on all tables created by dms, airbyte and data_engineer
resource "redshift_default_privileges" "ops_default_privs" {
  for_each    = toset([redshift_user.dms.name, redshift_user.airbyte.name, redshift_user.data_engineer.name])
  owner       = each.value
  group       = redshift_group.ops_analysis.name
  object_type = "table"
  privileges  = ["SELECT"]
}
