resource "redshift_user" "alees" {
  name     = "alees"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["default_user_password"]

  lifecycle {
    ignore_changes = [password]
  }
}

resource "redshift_user" "ichatzikonstantinou" {
  name     = "ichatzikonstantinou"
  password = jsondecode(data.aws_secretsmanager_secret_version.redshift.secret_string)["default_user_password"]

  lifecycle {
    ignore_changes = [password]
  }
}


resource "redshift_schema" "ownership" {
  name  = "ownership"
  owner = redshift_user.data_engineer.name
}

resource "redshift_group" "ownership" {
  name  = "ownership"
  users = [redshift_user.ichatzikonstantinou.name, redshift_user.alees.name]
}

locals {
  ownership_read_schemas = merge({
    mis_dms                                    = redshift_schema.mis_dms.name,
    salesforce_prod_airbyte                    = redshift_schema.salesforce_prod_airbyte.name,
    smart_charging_prod_smart_charging_airbyte = redshift_schema.smart_charging_prod_smart_charging_airbyte.name
    smart_charging_prod_competitions_airbyte   = redshift_schema.smart_charging_prod_competitions_airbyte.name
    dbt_published                              = redshift_schema.dbt_published.name,
    carbon_dagster                             = redshift_schema.carbon_dagster.name,
    utility                                    = redshift_schema.utility.name,
    exchange_rates_airbyte                     = redshift_schema.exchange_rates_airbyte.name,
    dealerships_airbyte                        = redshift_schema.dealerships_airbyte.name,
  })
  ownership_write_schemas = merge({
    ownership = redshift_schema.ownership.name
  })
}
resource "redshift_grant" "ownership_usage" {
  for_each    = local.ownership_read_schemas
  group       = redshift_group.ownership.name
  schema      = each.value
  object_type = "schema"
  privileges  = ["USAGE"]
}

resource "redshift_grant" "ownership_create" {
  for_each    = local.ownership_write_schemas
  group       = redshift_group.ownership.name
  schema      = each.value
  object_type = "schema"
  privileges  = ["USAGE", "CREATE"]
}

resource "redshift_grant" "ownership_select" {
  for_each    = local.ownership_read_schemas
  group       = redshift_group.ownership.name
  schema      = each.value
  object_type = "table"
  privileges  = ["SELECT"]
}

resource "redshift_grant" "ownership_full" {
  for_each    = local.ownership_write_schemas
  group       = redshift_group.ownership.name
  schema      = each.value
  object_type = "table"
  privileges  = ["SELECT", "INSERT", "UPDATE", "DELETE", "DROP"]
}

# GRANT engineers read on all tables created by ownership
resource "redshift_default_privileges" "engineers_default_privs_on_ownership" {
  for_each    = redshift_group.ownership.users
  owner       = each.value
  group       = redshift_group.engineers.name
  object_type = "table"
  privileges  = ["SELECT", "INSERT", "UPDATE", "DELETE", "DROP"]
}

# GRANT ownership SELECT on all future tables created by users in readers_default_privs
resource "redshift_default_privileges" "ownership_default_privs" {
  for_each    = local.readers_default_privs
  owner       = each.value
  group       = redshift_group.ownership.name
  object_type = "table"
  privileges  = ["SELECT"]
}

# GRANT ownership group the privileges listed on all future tables created by users in ownership group
resource "redshift_default_privileges" "ownership_group_default_privs" {
  for_each    = redshift_group.ownership.users
  owner       = each.value
  group       = redshift_group.ownership.name
  object_type = "table"
  privileges  = ["SELECT", "INSERT", "UPDATE", "DELETE", "DROP"]
  schema      = redshift_schema.ownership.name
}
