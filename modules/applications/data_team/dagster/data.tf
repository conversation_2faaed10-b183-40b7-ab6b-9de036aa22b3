// general

data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

// Secrets

data "aws_secretsmanager_secret" "dagster" {
  name = "dagster/${var.environment}"
}

// Temporary - subnet group to be replaced with the subnet group made in this module

data "aws_db_subnet_group" "this" {
  name = var.database_subnet_group
}

// Temporary - to be replaced by terraform resource "db_admin_credentials" in future PR

data "aws_secretsmanager_secret" "dagster_db" {
  name = "podpoint/dagster/${var.environment}"
}

data "aws_secretsmanager_secret_version" "dagster_db" {
  secret_id = data.aws_secretsmanager_secret.dagster_db.id
}
