/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *               Daemon
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

locals {
  dagster_daemon_ecs_ingress_rules = {
    "allow_all_internal_network" = {
      description = "Allows inbound connection to daemon from network"
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      ipv4_cidrs = [
        var.vpc_cidr_block
      ]
      security_group_id = module.daemon.security_group_id
    }
  }
  dagster_daemon_ecs_egress_rules = {
    "allow_all" = {
      description       = "Permit all egress traffic."
      from_port         = 0
      to_port           = 0
      protocol          = "-1"
      ipv4_cidrs        = ["0.0.0.0/0"]
      ipv6_cidrs        = ["::/0"]
      security_group_id = module.daemon.security_group_id
    }
  }
}

moved {
  from = module.dagster_daemon_ecs_ingress_rules["allow_all_internal_network"].aws_security_group_rule.this
  to   = aws_security_group_rule.dagster_daemon_ecs_ingress_rules["allow_all_internal_network"]
}

resource "aws_security_group_rule" "dagster_daemon_ecs_ingress_rules" {
  for_each = { for k, v in local.dagster_daemon_ecs_ingress_rules : k => v }

  type                     = "ingress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = each.value.security_group_id
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)

  depends_on = [
    module.daemon
  ]
}

moved {
  from = module.dagster_daemon_ecs_egress_rules["allow_all"].aws_security_group_rule.this
  to   = aws_security_group_rule.dagster_daemon_ecs_egress_rules["allow_all"]
}

resource "aws_security_group_rule" "dagster_daemon_ecs_egress_rules" {
  for_each = { for k, v in local.dagster_daemon_ecs_egress_rules : k => v }

  type                     = "egress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = each.value.security_group_id
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)

  depends_on = [
    module.daemon
  ]
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *           GRPC Services
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

locals {
  dagster_reports_ecs_ingress_rules = {
    "allow_dagit_ui" = {
      description           = "Allows inbound connection from dagit ui"
      from_port             = 4000
      to_port               = 4000
      protocol              = "tcp"
      source_security_group = module.dagit.security_group_id
      security_group_id     = module.grpc["reports"].security_group_id
    },
    "allow_dagit_daemon" = {
      description           = "Allows inbound connection from dagit daemon"
      from_port             = 4000
      to_port               = 4000
      protocol              = "tcp"
      source_security_group = module.daemon.security_group_id
      security_group_id     = module.grpc["reports"].security_group_id
    }
  }
  dagster_reports_ecs_egress_rules = {
    "allow_all" = {
      description       = "Permit all egress traffic."
      from_port         = 0
      to_port           = 0
      protocol          = "-1"
      ipv4_cidrs        = ["0.0.0.0/0"]
      ipv6_cidrs        = ["::/0"]
      security_group_id = module.grpc["reports"].security_group_id
    }
  }
}

moved {
  from = module.dagster_reports_ecs_ingress_rules["allow_dagit_ui"].aws_security_group_rule.this
  to   = aws_security_group_rule.dagster_reports_ecs_ingress_rules["allow_dagit_ui"]
}

moved {
  from = module.dagster_reports_ecs_ingress_rules["allow_dagit_daemon"].aws_security_group_rule.this
  to   = aws_security_group_rule.dagster_reports_ecs_ingress_rules["allow_dagit_daemon"]
}

resource "aws_security_group_rule" "dagster_reports_ecs_ingress_rules" {
  for_each = { for k, v in local.dagster_reports_ecs_ingress_rules : k => v }

  type                     = "ingress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = each.value.security_group_id
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)

  depends_on = [
    module.grpc["reports"]
  ]
}

moved {
  from = module.dagster_reports_ecs_egress_rules["allow_all"].aws_security_group_rule.this
  to   = aws_security_group_rule.dagster_reports_ecs_egress_rules["allow_all"]
}

resource "aws_security_group_rule" "dagster_reports_ecs_egress_rules" {
  for_each = { for k, v in local.dagster_reports_ecs_egress_rules : k => v }

  type                     = "egress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = each.value.security_group_id
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)

  depends_on = [
    module.grpc["reports"]
  ]
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *               Dagit
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

locals {
  dagster_dagit_ecs_ingress_rules = {
    "allow_lb" = {
      description           = "Allows inbound connection from the loadbalancer."
      from_port             = 3000
      to_port               = 3000
      protocol              = "tcp"
      source_security_group = var.lb_security_group_id
      security_group_id     = module.dagit.security_group_id
    }
  }
  dagster_dagit_ecs_egress_rules = {
    "allow_all" = {
      description       = "Permit all egress traffic."
      from_port         = 0
      to_port           = 0
      protocol          = "-1"
      ipv4_cidrs        = ["0.0.0.0/0"]
      ipv6_cidrs        = ["::/0"]
      security_group_id = module.dagit.security_group_id
    }
  }
}

moved {
  from = module.dagster_dagit_ecs_ingress_rules["allow_lb"].aws_security_group_rule.this
  to   = aws_security_group_rule.dagster_dagit_ecs_ingress_rules["allow_lb"]
}

resource "aws_security_group_rule" "dagster_dagit_ecs_ingress_rules" {
  for_each = { for k, v in local.dagster_dagit_ecs_ingress_rules : k => v }

  type                     = "ingress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = each.value.security_group_id
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)

  depends_on = [
    module.dagit
  ]
}

moved {
  from = module.dagster_dagit_ecs_egress_rules["allow_all"].aws_security_group_rule.this
  to   = aws_security_group_rule.dagster_dagit_ecs_egress_rules["allow_all"]
}

resource "aws_security_group_rule" "dagster_dagit_ecs_egress_rules" {
  for_each = { for k, v in local.dagster_dagit_ecs_egress_rules : k => v }

  type                     = "egress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = each.value.security_group_id
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)

  depends_on = [
    module.dagit
  ]
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *     Dagster Instance Migrate
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

locals {
  dagster_instance_migrate_ecs_ingress_rules = {
    "allow_lb" = {
      description           = "Allows inbound connection from the loadbalancer."
      from_port             = 3000
      to_port               = 3000
      protocol              = "tcp"
      source_security_group = var.lb_security_group_id
      security_group_id     = module.dagster_instance_migrate.security_group_id
    }
  }
  dagster_instance_migrate_ecs_egress_rules = {
    "allow_all" = {
      description       = "Permit all egress traffic."
      from_port         = 0
      to_port           = 0
      protocol          = "-1"
      ipv4_cidrs        = ["0.0.0.0/0"]
      ipv6_cidrs        = ["::/0"]
      security_group_id = module.dagster_instance_migrate.security_group_id
    }
  }
}

moved {
  from = module.dagster_instance_migrate_ecs_ingress_rules["allow_lb"].aws_security_group_rule.this
  to   = aws_security_group_rule.dagster_instance_migrate_ecs_ingress_rules["allow_lb"]
}

resource "aws_security_group_rule" "dagster_instance_migrate_ecs_ingress_rules" {
  for_each = { for k, v in local.dagster_instance_migrate_ecs_ingress_rules : k => v }

  type                     = "ingress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = each.value.security_group_id
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)

  depends_on = [
    module.dagster_instance_migrate
  ]
}

moved {
  from = module.dagster_instance_migrate_ecs_egress_rules["allow_all"].aws_security_group_rule.this
  to   = aws_security_group_rule.dagster_instance_migrate_ecs_egress_rules["allow_all"]
}

resource "aws_security_group_rule" "dagster_instance_migrate_ecs_egress_rules" {
  for_each = { for k, v in local.dagster_instance_migrate_ecs_egress_rules : k => v }

  type                     = "egress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = each.value.protocol
  description              = each.value.description
  security_group_id        = each.value.security_group_id
  cidr_blocks              = lookup(each.value, "ipv4_cidrs", null)
  ipv6_cidr_blocks         = lookup(each.value, "ipv6_cidrs", null)
  source_security_group_id = lookup(each.value, "source_security_group", null)

  depends_on = [
    module.dagster_instance_migrate
  ]
}
