# DMS VPC Role

resource "aws_iam_role" "dms_vpc_role" {
  name                = "dms-vpc-role"
  description         = "DMS IAM role for VPC permissions"
  assume_role_policy  = data.aws_iam_policy_document.dms_assume_role.json
  managed_policy_arns = ["arn:${local.partition}:iam::aws:policy/service-role/AmazonDMSVPCManagementRole"]
}

resource "aws_iam_role_policy_attachment" "dms_vpc_role" {
  role       = aws_iam_role.dms_vpc_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonDMSVPCManagementRole"
}

# DMS Endpoint

resource "aws_iam_role" "dms_access_for_endpoint" {

  name                = "dms-access-for-endpoint"
  description         = "DMS IAM role for endpoint access permissions"
  assume_role_policy  = data.aws_iam_policy_document.dms_assume_role_redshift.json
  managed_policy_arns = ["arn:${local.partition}:iam::aws:policy/service-role/AmazonDMSRedshiftS3Role"]
}

# DMS CloudWatch Logs
resource "aws_iam_role" "dms_cloudwatch_logs_role" {
  name                = "dms-cloudwatch-logs-role"
  description         = "DMS IAM role for CloudWatch logs permissions"
  assume_role_policy  = data.aws_iam_policy_document.dms_assume_role.json
  managed_policy_arns = ["arn:${local.partition}:iam::aws:policy/service-role/AmazonDMSCloudWatchLogsRole"]
}

data "aws_iam_policy_document" "dms_assume_role" {
  statement {
    actions = [
      "sts:AssumeRole",
      "sts:TagSession",
    ]

    principals {
      identifiers = ["dms.${local.dns_suffix}"]
      type        = "Service"
    }

    condition {
      test     = "ArnLike"
      variable = "aws:SourceArn"
      values   = ["arn:${local.partition}:dms:${local.region}:${local.account_id}:*"]
    }

    condition {
      test     = "StringEquals"
      variable = "aws:SourceAccount"
      values   = [local.account_id]
    }
  }
}

data "aws_iam_policy_document" "dms_assume_role_redshift" {
  source_policy_documents = [data.aws_iam_policy_document.dms_assume_role.json]

  statement {
    actions = [
      "sts:AssumeRole",
      "sts:TagSession",
    ]

    principals {
      identifiers = ["redshift.${local.dns_suffix}"]
      type        = "Service"
    }
  }
}
