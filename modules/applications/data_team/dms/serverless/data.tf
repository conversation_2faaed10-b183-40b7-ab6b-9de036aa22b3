data "aws_secretsmanager_secret" "redshift" {
  name = "podpoint-dw-${var.environment}/redshift/users"
}

data "aws_secretsmanager_secret_version" "redshift" {
  secret_id = data.aws_secretsmanager_secret.redshift.id
}

data "aws_secretsmanager_secret" "podadmin" {
  name = "podadmin-prod/aurora-mysql/data-service-users"
}

data "aws_secretsmanager_secret_version" "podadmin" {
  secret_id = data.aws_secretsmanager_secret.podadmin.id
}

data "aws_secretsmanager_secret" "auth" {
  name = "auth/production/rds"
}

data "aws_secretsmanager_secret_version" "auth" {
  secret_id = data.aws_secretsmanager_secret.auth.id
}

data "aws_region" "current" {}
data "aws_partition" "current" {}
data "aws_caller_identity" "current" {}



data "aws_kms_alias" "dms" {
  name = "alias/aws/dms"
}

data "aws_vpc" "this" {
  id = var.vpc_id
}
