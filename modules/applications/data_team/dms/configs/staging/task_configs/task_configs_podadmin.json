{"TargetMetadata": {"InlineLobMaxSize": 0, "LoadMaxFileSize": 0, "LobChunkSize": 64, "ParallelApplyQueuesPerThread": 0, "ParallelLoadQueuesPerThread": 0, "TargetSchema": "mis_dms", "SupportLobs": true, "FullLobMode": false, "LimitedSizeLobMode": true, "LobMaxSize": 63, "BatchApplyEnabled": true, "TaskRecoveryTableEnabled": false, "ParallelLoadThreads": 10, "ParallelLoadBufferSize": 200, "ParallelApplyThreads": 0, "ParallelApplyBufferSize": 500}, "FullLoadSettings": {"TargetTablePrepMode": "DROP_AND_CREATE", "CreatePkAfterFullLoad": false, "StopTaskCachedChangesApplied": false, "StopTaskCachedChangesNotApplied": false, "MaxFullLoadSubTasks": 1, "TransactionConsistencyTimeout": 600, "CommitRate": 100}, "Logging": {"EnableLogging": true, "LogComponents": [{"Id": "TRANSFORMATION", "Severity": "LOGGER_SEVERITY_WARNING"}, {"Id": "SOURCE_UNLOAD", "Severity": "LOGGER_SEVERITY_DEFAULT"}, {"Id": "IO", "Severity": "LOGGER_SEVERITY_WARNING"}, {"Id": "TARGET_LOAD", "Severity": "LOGGER_SEVERITY_DEFAULT"}, {"Id": "PERFORMANCE", "Severity": "LOGGER_SEVERITY_WARNING"}, {"Id": "SOURCE_CAPTURE", "Severity": "LOGGER_SEVERITY_DEFAULT"}, {"Id": "SORTER", "Severity": "LOGGER_SEVERITY_WARNING"}, {"Id": "REST_SERVER", "Severity": "LOGGER_SEVERITY_WARNING"}, {"Id": "VALIDATOR_EXT", "Severity": "LOGGER_SEVERITY_WARNING"}, {"Id": "TARGET_APPLY", "Severity": "LOGGER_SEVERITY_INFO"}, {"Id": "TASK_MANAGER", "Severity": "LOGGER_SEVERITY_DEBUG"}, {"Id": "TABLES_MANAGER", "Severity": "LOGGER_SEVERITY_WARNING"}, {"Id": "METADATA_MANAGER", "Severity": "LOGGER_SEVERITY_WARNING"}, {"Id": "FILE_FACTORY", "Severity": "LOGGER_SEVERITY_WARNING"}, {"Id": "COMMON", "Severity": "LOGGER_SEVERITY_WARNING"}, {"Id": "ADDONS", "Severity": "LOGGER_SEVERITY_WARNING"}, {"Id": "DATA_STRUCTURE", "Severity": "LOGGER_SEVERITY_WARNING"}, {"Id": "COMMUNICATION", "Severity": "LOGGER_SEVERITY_DEFAULT"}, {"Id": "FILE_TRANSFER", "Severity": "LOGGER_SEVERITY_DEFAULT"}]}, "LoopbackPreventionSettings": null, "PostProcessingRules": null, "ControlTablesSettings": {"ControlSchema": "", "FullLoadExceptionTableEnabled": false, "HistoryTimeslotInMinutes": 5, "HistoryTableEnabled": false, "SuspendedTablesTableEnabled": false, "StatusTableEnabled": false}, "StreamBufferSettings": {"CtrlStreamBufferSizeInMB": 5, "StreamBufferCount": 3, "StreamBufferSizeInMB": 5}, "TTSettings": {"EnableTT": false, "TTRecordSettings": null, "TTS3Settings": null}, "BeforeImageSettings": null, "CharacterSetSettings": null, "FailTaskWhenCleanTaskResourceFailed": false, "ChangeProcessingTuning": {"BatchApplyPreserveTransaction": true, "BatchApplyTimeoutMin": 300, "BatchApplyTimeoutMax": 600, "BatchApplyMemoryLimit": 500, "BatchSplitSize": 0, "CommitTimeout": 1, "MemoryLimitTotal": 1024, "MemoryKeepTime": 60, "MinTransactionSize": 1000, "StatementCacheSize": 50}, "ChangeProcessingDdlHandlingPolicy": {"HandleSourceTableDropped": true, "HandleSourceTableTruncated": true, "HandleSourceTableAltered": true}, "ErrorBehavior": {"ApplyErrorFailOnTruncationDdl": false, "DataErrorPolicy": "LOG_ERROR", "DataTruncationErrorPolicy": "LOG_ERROR", "DataErrorEscalationPolicy": "SUSPEND_TABLE", "DataErrorEscalationCount": 50, "EventErrorPolicy": "IGNORE", "FailOnNoTablesCaptured": true, "FailOnTransactionConsistencyBreached": false, "TableErrorPolicy": "SUSPEND_TABLE", "TableErrorEscalationPolicy": "STOP_TASK", "TableErrorEscalationCount": 50, "RecoverableErrorCount": 6, "RecoverableErrorInterval": 10, "RecoverableErrorThrottling": true, "RecoverableErrorThrottlingMax": 1800, "RecoverableErrorStopRetryAfterThrottlingMax": true, "ApplyErrorDeletePolicy": "IGNORE_RECORD", "ApplyErrorInsertPolicy": "LOG_ERROR", "ApplyErrorUpdatePolicy": "LOG_ERROR", "ApplyErrorEscalationPolicy": "LOG_ERROR", "ApplyErrorEscalationCount": 0, "FullLoadIgnoreConflicts": true}}