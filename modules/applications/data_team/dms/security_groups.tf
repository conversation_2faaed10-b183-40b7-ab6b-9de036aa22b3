resource "aws_security_group" "dms" {
  name        = "dms-sg-${var.environment}"
  vpc_id      = var.vpc_id
  description = "Allows ingress from source and destination endpoints."

  ingress {
    description = "Allows external connection to db on vpn."
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = var.vpn_cidr
  }

  ingress {
    description = "Allows access to all resource inside network"
    from_port   = 5439
    to_port     = 5439
    protocol    = "tcp"
    cidr_blocks = [data.aws_vpc.this.cidr_block]
  }

  egress {
    protocol         = "-1"
    from_port        = 0
    to_port          = 0
    cidr_blocks      = ["0.0.0.0/0"]
    ipv6_cidr_blocks = ["::/0"]
  }

  tags = {
    Name = "dms-${var.environment}"
  }
}