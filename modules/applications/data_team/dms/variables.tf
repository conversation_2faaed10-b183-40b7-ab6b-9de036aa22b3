#######################################
// ----------- General -------- //
#######################################

# Remove this in the future, "environment" variable is redundant when the account only has dev or prod resources in it
variable "environment" {
  type        = string
  description = "Dagster environment, explicitly either `prod` or `dev`."
}

variable "vpc_id" {
  type        = string
  description = "Id of the vpc to create dms security group in."
}

variable "vpn_cidr" {
  type        = list(string)
  description = "Vpn cidr to allow into dms."
}

variable "private_subnet_ids" {
  type        = list(string)
  description = "List of private subnet ids to host dms in."
}

###################################
// ----------- INSTANCE -------- //
###################################

variable "dms_instance_class" {
  description = "Instance type for replication instance."
  type        = string
  default     = "dms.t3.medium"
}


variable "multi_az" {
  description = "Replication instance in multiple availability zones."
  type        = bool
}

########################################
// ----------- OTHER SETTINGS ------- //
########################################


variable "migration_type" {
  description = "Migration type, either full-load, cdc, full-load-and-cdc"
  type        = string
}
