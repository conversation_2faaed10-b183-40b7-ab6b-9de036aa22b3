/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *          Pod Point Account
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

# IAM ROLE for events to be consumed on PP main

data "aws_iam_policy_document" "event_trust" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }
  }
}

data "aws_iam_policy_document" "event_push" {
  statement {
    effect    = "Allow"
    actions   = ["events:PutEvents"]
    resources = ["arn:aws:events:eu-west-1:************:event-bus/default"]
  }
}

resource "aws_iam_policy" "events" {
  name   = "DataEventPolicy"
  policy = data.aws_iam_policy_document.event_push.json
}

resource "aws_iam_role" "events" {
  name               = "DataEventRole"
  assume_role_policy = data.aws_iam_policy_document.event_trust.json
}

resource "aws_iam_role_policy_attachment" "events" {
  policy_arn = aws_iam_policy.events.arn
  role       = aws_iam_role.events.name
}

# Cloudwatch

resource "aws_cloudwatch_event_rule" "dms_pp" {
  // Event Rule which is created on PP side
  count       = var.environment == "staging" ? 1 : 0
  provider    = aws.pod-point
  name        = "dms-event-failure"
  description = "Capture replication task failures."
  event_pattern = jsonencode(
    {
      "source" : ["aws.dms"],
      "account" : ["************", "************"],
      "detail" : {
        "category" : ["Failure"]
      }
    }
  )
}

resource "aws_cloudwatch_event_target" "dms" {
  count = var.environment == "staging" ? 1 : 0
  // Will target the SNS topic after event rule is evaluated when passed
  // through PP account default bus
  provider  = aws.pod-point
  rule      = aws_cloudwatch_event_rule.dms_pp[0].name
  target_id = "SNSDataAlarm"
  arn       = var.sns_arn
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *         Cloudwatch Events
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_cloudwatch_event_rule" "dms_data" {
  // Data account sending to PP main
  // Ref event_pattern https://docs.aws.amazon.com/dms/latest/userguide/CHAP_EventBridge.html
  name        = "dms-event-failure"
  description = "Capture replication task failures."
  event_pattern = jsonencode(
    {
      "source" : ["aws.dms"],
      "account" : [var.account_id],
      "detail" : {
        "category" : ["Failure"]
      }
    }
  )
}

resource "aws_cloudwatch_event_target" "pp_bus" {
  // Send events over to PP main defualt bus
  rule      = aws_cloudwatch_event_rule.dms_data.name
  target_id = "SNSDataAlarm"
  role_arn  = aws_iam_role.events.arn
  arn       = "arn:aws:events:eu-west-1:************:event-bus/default"
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *       DMS REP INSTANCE ALARMS
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_cloudwatch_metric_alarm" "dms_cpu" {
  // CPUUtilization monitor
  // Monitors instance when CPU Utilization is more than 80% for period of 5 minutes for 3 data points
  alarm_name          = "dms-${var.environment}-cpu"
  alarm_description   = "alarm when cpu is more than threshold"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/DMS"
  period              = "300"
  threshold           = "80"
  comparison_operator = "GreaterThanThreshold"
  statistic           = "Average"
  evaluation_periods  = 3
  unit                = "Percent"
  alarm_actions       = [var.sns_arn]
  ok_actions          = [var.sns_arn]
  dimensions = {
    ReplicationInstanceIdentifier = local.dms_instance_identifier
  }
}

resource "aws_cloudwatch_metric_alarm" "dms_cpu_lt" {
  // CPUUtilization monitor
  // Monitors instance when CPU Utilization is less than 5% which indicates being IDLE and has failed
  alarm_name          = "dms-${var.environment}-cpu-Failure"
  alarm_description   = "alarm when cpu is less than 5% indicating task has failed and instance is idle."
  metric_name         = "CPUUtilization"
  namespace           = "AWS/DMS"
  period              = "300"
  threshold           = "3"
  comparison_operator = "LessThanThreshold"
  statistic           = "Average"
  evaluation_periods  = 3
  unit                = "Percent"
  alarm_actions       = [var.sns_arn]
  ok_actions          = [var.sns_arn]
  dimensions = {
    ReplicationInstanceIdentifier = local.dms_instance_identifier
  }
}

resource "aws_cloudwatch_metric_alarm" "dms_free_storage" {
  // Available storage remaining on DMS rep instance
  alarm_name          = "dms-instance-${var.environment}-FreeStorage"
  alarm_description   = "when less than 20% of the allocated storage"
  metric_name         = "FreeStorageSpace"
  namespace           = "AWS/DMS"
  statistic           = "Average"
  threshold           = "12.0e+09"
  comparison_operator = "LessThanOrEqualToThreshold"
  period              = "300"
  evaluation_periods  = 1
  unit                = "Bytes"
  alarm_actions       = [var.sns_arn]
  ok_actions          = [var.sns_arn]
  dimensions = {
    ReplicationInstanceIdentifier = local.dms_instance_identifier
  }
}

resource "aws_cloudwatch_metric_alarm" "dms_freeable_mem" {
  alarm_name          = "dms-instance-${var.environment}-FreeableMemory"
  alarm_description   = "Free Memory is lower than 1GB"
  metric_name         = "FreeableMemory"
  namespace           = "AWS/DMS"
  statistic           = "Average"
  period              = "180"
  threshold           = "1.0e+09"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = 3
  unit                = "Bytes"
  alarm_actions       = [var.sns_arn]
  ok_actions          = [var.sns_arn]
  dimensions = {
    ReplicationInstanceIdentifier = local.dms_instance_identifier
  }
}

resource "aws_cloudwatch_metric_alarm" "dms_writeiops" {
  // The average number of disk write I/O operations per second.
  alarm_name          = "dms-instance-${var.environment}-WriteIOPS"
  alarm_description   = "High Write IOPs ALARM: More than 1000/Secs"
  metric_name         = "WriteIOPS"
  namespace           = "AWS/DMS"
  statistic           = "Average"
  period              = "120"
  threshold           = "1000"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  unit                = "Count/Second"
  alarm_actions       = [var.sns_arn]
  ok_actions          = [var.sns_arn]
  dimensions = {
    ReplicationInstanceIdentifier = local.dms_instance_identifier
  }
}

resource "aws_cloudwatch_metric_alarm" "dms_writethroughput" {
  // The average number of bytes written to disk per second.
  alarm_name          = "dms-instance-${var.environment}-WriteThroughput"
  alarm_description   = "More than 3MB of data being written to disk per second"
  metric_name         = "WriteThroughput"
  namespace           = "AWS/DMS"
  statistic           = "Average"
  period              = "30"
  threshold           = "3.0e+09"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  unit                = "Bytes/Second"
  alarm_actions       = [var.sns_arn]
  ok_actions          = [var.sns_arn]
  dimensions = {
    ReplicationInstanceIdentifier = local.dms_instance_identifier
  }
  treat_missing_data = "notBreaching"
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *       DMS TASK ALARMS
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_cloudwatch_metric_alarm" "dms_cdcthroughputrows_target" {
  // Outgoing task changes for the target in rows per second.
  alarm_name          = "dms-podadmin-task-${var.environment}-CDCThroughputRowsTarget"
  alarm_description   = "Outgoing task changes for the target is greater than 5000 rows per second."
  metric_name         = "CDCThroughputRowsTarget"
  namespace           = "AWS/DMS"
  threshold           = "28000"
  period              = "60"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  statistic           = "Average"
  unit                = "Count/Second"
  alarm_actions       = [var.sns_arn]
  ok_actions          = [var.sns_arn]

  dimensions = {
    ReplicationInstanceIdentifier = local.dms_instance_identifier
    ReplicationTaskIdentifier     = local.podadmin_task_identifier
  }

}

resource "aws_cloudwatch_metric_alarm" "dms_cdcthroughputrows_source" {
  // Outgoing task changes for the target in rows per second.
  alarm_name          = "dms-podadmin-task-${var.environment}-CDCThroughputRowsSource"
  alarm_description   = "Incoming task changes for the target is greater than 1000 rows per second."
  metric_name         = "CDCThroughputRowsSource"
  namespace           = "AWS/DMS"
  threshold           = "1000"
  period              = "60"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 3
  statistic           = "Average"
  unit                = "Count/Second"
  alarm_actions       = [var.sns_arn]
  ok_actions          = [var.sns_arn]

  dimensions = {
    ReplicationInstanceIdentifier = local.dms_instance_identifier
    ReplicationTaskIdentifier     = local.podadmin_task_identifier
  }

}

resource "aws_cloudwatch_metric_alarm" "dms_cdclatencyrows_source" {
  // Gap between time last event captured from source endpoint and current timestamp
  // If this grows means theres a lag in the process of capturing change from source db.
  alarm_name          = "dms-podadmin-task-${var.environment}-CDCLatencySource"
  alarm_description   = "Number of seconds between last event captured from source endpoint and current time is greater than 2 std from the expected value of that period"
  comparison_operator = "GreaterThanUpperThreshold"
  threshold_metric_id = "e1"
  evaluation_periods  = 3
  alarm_actions       = [var.sns_arn]
  ok_actions          = [var.sns_arn]

  metric_query {
    id          = "e1"
    expression  = "ANOMALY_DETECTION_BAND(m1,2)"
    label       = "CDCLatencySource (Expected)"
    return_data = "true"
  }

  metric_query {
    id          = "m1"
    return_data = "true"

    metric {
      metric_name = "CDCLatencySource"
      namespace   = "AWS/DMS"
      period      = "300"
      stat        = "Average"
      unit        = "Seconds"

      dimensions = {
        ReplicationInstanceIdentifier = local.dms_instance_identifier
        ReplicationTaskIdentifier     = local.podadmin_task_identifier
      }
    }
  }
}

resource "aws_cloudwatch_metric_alarm" "dms_cdclatencyrows_target" {
  // Gap between the first event timestamp waiting to commit on the target and the
  // current timestamp of the AWS DMS instance. When high, indicates the process of applying
  // change events to target is delayed. Bottlenecks include No primary keys on tables, target/rep instance,
  // Or network issues between target and rep instance.
  alarm_name          = "dms-podadmin-task-${var.environment}-CDCLatencyTarget"
  alarm_description   = "Number of seconds between last event captured from source endpoint greater than 10 seconds."
  comparison_operator = "GreaterThanUpperThreshold"
  threshold_metric_id = "e1"
  evaluation_periods  = 3
  alarm_actions       = [var.sns_arn]
  ok_actions          = [var.sns_arn]

  metric_query {
    id          = "e1"
    expression  = "ANOMALY_DETECTION_BAND(m1,2)"
    label       = "CDCLatencyTarget (Expected)"
    return_data = "true"
  }

  metric_query {
    id          = "m1"
    return_data = "true"

    metric {
      metric_name = "CDCLatencyTarget"
      namespace   = "AWS/DMS"
      period      = "300"
      stat        = "Average"
      unit        = "Seconds"

      dimensions = {
        ReplicationInstanceIdentifier = local.dms_instance_identifier
        ReplicationTaskIdentifier     = local.podadmin_task_identifier
      }
    }
  }
}
