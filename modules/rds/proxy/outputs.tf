output "common_attributes" {
  description = "Essential attributes produced by the proxy"
  value = {
    id       = aws_db_proxy.this.id
    arn      = aws_db_proxy.this.arn
    endpoint = aws_db_proxy.this.endpoint
  }
}

output "reader_endpoint" {
  description = "Essential attributes produced by the proxy"

  value = var.is_aurora_cluster_target ? {
    id          = one(aws_db_proxy_endpoint.this_read[*].id)
    name        = one(aws_db_proxy_endpoint.this_read[*].db_proxy_name)
    endpoint    = one(aws_db_proxy_endpoint.this_read[*].endpoint)
    target_role = one(aws_db_proxy_endpoint.this_read[*].target_role)
  } : {}

  depends_on = [
    aws_db_proxy.this
  ]
}

output "proxy_target_attributes" {
  description = "The RDS DB proxy target resource"
  value = {
    id                 = aws_db_proxy_target.this_target.id
    type               = aws_db_proxy_target.this_target.type
    endpoint           = aws_db_proxy_target.this_target.endpoint
    targeT_arn         = aws_db_proxy_target.this_target.target_arn
    tracked_cluster_id = aws_db_proxy_target.this_target.tracked_cluster_id
  }
}

output "proxy_iam_role_attributes" {
  description = "The IAM role attributes used by the proxy"
  value = {
    name = aws_iam_role.service_role.name
    arn  = aws_iam_role.service_role.arn
  }
}