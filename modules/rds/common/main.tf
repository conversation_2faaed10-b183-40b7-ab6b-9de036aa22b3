/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *        RDS Monitoring Role
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

data "aws_iam_policy_document" "rds_mononitoring_assume_role" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["monitoring.rds.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "rds_monitoring_role" {
  name                = "rds-monitoring-role"
  assume_role_policy  = data.aws_iam_policy_document.rds_mononitoring_assume_role.json
  managed_policy_arns = ["arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"]
}