variable "podadmin_user_password" {
  description = "The password for the firmware_upgrade user in podadmin"
  type        = string
  sensitive   = true
}

variable "grid_opsgenie_api_key" {
  description = "The API key for the opsgenie cloudwatch integration which publishes to the grid squad."
  type        = string
  sensitive   = true
}

variable "opsgenie_network_state_grid_timestream_scheduled_queries_integration_url" {
  description = "The integration url for the Opsgenie scheduled query integration"
  type        = string
  sensitive   = true
}

variable "opsgenie_network_state_grid_budgets_integration_url" {
  description = "The integration url for the Opsgenie budgets integration"
  type        = string
  sensitive   = true
}
