module "certificate_service" {
  source = "../../modules/applications/certificate_service"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1
  }

  environment = local.environment
  region      = local.region
  subnet_ids  = module.vpc.private_subnets_ids
  vpc_id      = module.vpc.vpc_id

  allow_ipv4_cidr_list = module.vpc.private_subnets_cidr_blocks

  # Private subnet IPv4 addresses for cs-connectivity-dev
  connectivity_ipv4_cidrs = ["*********/24", "*********/24", "*********/24"]

  execute_api_vpc_endpoint_id = module.vpc_endpoints.endpoints["execute-api"].id
  api_allowed_vpc_endpoint_ids = [
    module.vpc_endpoints.endpoints["execute-api"].id,
    "vpce-08cd1cce6485b82b7", // cs-connectivity-dev / connectivity's execute-api
  ]

  additional_kms_administrators = local.developer_iam_role_arns

  api_gateway_allowed_principals       = local.certificate_service_api_gateway_allowed_principals
  api_mfp_signature_allowed_principals = local.api_mfp_signature_allowed_principals

  vault_address       = "https://${local.vault_zone_name}:8200"
  vault_namespace     = "cs-connectivity/dev"
  vault_aws_auth_role = "pki_writer"

  pki_design_secret_arn = "arn:aws:secretsmanager:eu-west-1:************:secret:/pki/design/dev-LHeqrY"
  pki_design_kms_arn    = "arn:aws:kms:eu-west-1:************:key/5d3dee4c-a80a-41ea-8b12-c61589042230"

  alarm_actions = [
    "arn:aws:sns:eu-west-1:${data.aws_caller_identity.current.id}:opsgenie-cw-network-connectivity"
  ]

  slack_notifications_sns_topic_arn = format("arn:aws:sns:eu-west-1:%s:notifications-network-connectivity", local.cs_connectivity_account_id)

  ppcp_api_lambda_execution_roles = [
    format("arn:aws:iam::%s:role/arch2_lambda_execution_role_%s", local.pod_point_main_account_id, "staging")
  ]

  # Certificate service DB
  aurora_cluster_instance_count = 1
}
