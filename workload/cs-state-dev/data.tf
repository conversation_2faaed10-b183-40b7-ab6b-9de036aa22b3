data "terraform_remote_state" "cs_connectivity_dev" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "cs-connectivity-dev"
    }
  }
}

data "terraform_remote_state" "network_assets_dev" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "network-assets-dev"
    }
  }
}

data "terraform_remote_state" "experience_commercial_dev" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "experience-commercial-dev"
    }
  }
}

data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

data "aws_iam_roles" "administrator_and_software" {
  name_regex = ".*PP-Administrator|PP-Software.*"
}

data "aws_ssm_parameter" "shared_service_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.id}:parameter/account/${local.shared_services_account_name}/id"
}

data "aws_ssm_parameter" "experience_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.experience_account_name}/id"
}

