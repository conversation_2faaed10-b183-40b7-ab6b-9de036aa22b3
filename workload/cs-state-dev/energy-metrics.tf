module "energy_metrics" {
  source = "../../modules/applications/cs-state/energy-metrics"

  providers = {
    aws                     = aws,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1
  }

  # energy-metrics-api
  environment = local.environment
  subnet_ids  = module.vpc.private_subnets_ids
  vpc_id      = module.vpc.vpc_id

  api_gateway_ingress_rules   = local.common_api_ingress_rules
  execute_api_vpc_endpoint_id = module.vpc_endpoints.endpoints["execute-api"].id
  vpc_endpoint_ids            = [module.vpc_endpoints.endpoints["execute-api"].id]

  # kinesis / timestream
  # use on-demand for low-data-rate dev/staging environments
  energy_metrics_stream_shard_count = 0

  opsgenie_network_state_grid_budgets_integration_url                      = var.opsgenie_network_state_grid_budgets_integration_url
  opsgenie_network_state_grid_timestream_scheduled_queries_integration_url = var.opsgenie_network_state_grid_timestream_scheduled_queries_integration_url

  arch2_lambda_execution_roles = [
    format("arn:aws:iam::%s:role/arch2_lambda_execution_role_%s", local.pod_point_main_account_id, "staging")
  ]

  additional_kms_administrators = local.developer_iam_role_arns

  smart_charging_kms_key_arn = module.smart_charge.kms_smart_charging_service[0].arn

  cs_connectivity_account_id = local.cs_connectivity_account_id
}
