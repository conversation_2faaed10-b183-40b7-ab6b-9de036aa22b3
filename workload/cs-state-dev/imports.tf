import {
  to = module.diagnostics.module.diagnostics_api.aws_cloudwatch_log_group.api_gateway
  id = "API-Gateway-Execution-Logs_vypuvc8jx0/dev"
}

import {
  to = module.firmware_upgrade.module.api.aws_cloudwatch_log_group.api_gateway
  id = "API-Gateway-Execution-Logs_oj1uagd70a/dev"
}

import {
  to = module.smart_charge.module.charging_optimisation_api.aws_cloudwatch_log_group.api_gateway
  id = "API-Gateway-Execution-Logs_8ug3rg7tml/dev"
}

import {
  to = module.smart_charge.module.competitions_api.aws_cloudwatch_log_group.api_gateway
  id = "API-Gateway-Execution-Logs_3uyc6umgza/dev"
}

import {
  to = module.smart_charge.module.energy_metrics_api.aws_cloudwatch_log_group.api_gateway
  id = "API-Gateway-Execution-Logs_98ucpjvlnf/dev"
}

import {
  to = module.smart_charge.module.smart_charging_service_api.aws_cloudwatch_log_group.api_gateway
  id = "API-Gateway-Execution-Logs_1fvff416u0/dev"
}

import {
  to = module.energy_metrics.aws_cloudwatch_log_group.energy_metrics_ingester
  id = "/aws/lambda/energy-metrics-ingester-dev-ingest-to-timestream"
}
