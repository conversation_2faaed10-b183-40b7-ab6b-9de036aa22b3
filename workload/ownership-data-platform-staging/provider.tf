provider "aws" {
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", var.assume_role_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:domain"                           = "ownership"
      "pp:owner"                            = "ownership:orders"
      "pp:service"                          = "ownership-data-platform"
      "pp:terraformWorkspace"               = "ownership-data-platform-staging"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "us-east-1"
  region = "us-east-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", var.assume_role_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:domain"                           = "ownership"
      "pp:owner"                            = "ownership:orders"
      "pp:service"                          = "ownership-data-platform"
      "pp:terraformWorkspace"               = "ownership-data-platform-staging"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "pod-point"
  region = var.region

  assume_role {
    role_arn    = format("arn:aws:iam::%s:role/%s", var.pod_point_account_id, var.assume_role_name)
    external_id = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:domain"                           = "ownership"
      "pp:owner"                            = "ownership:orders"
      "pp:service"                          = "ownership-data-platform"
      "pp:terraformWorkspace"               = "ownership-data-platform-staging"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "pod-point-us-east-1"
  region = "us-east-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", var.pod_point_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "staging"
      "pp:domain"                           = "ownership"
      "pp:owner"                            = "ownership:orders"
      "pp:service"                          = "ownership-data-platform"
      "pp:terraformWorkspace"               = "ownership-data-platform-staging"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  alias  = "shared-services"
  region = "eu-west-1"

  assume_role {
    role_arn     = "arn:aws:iam::${var.shared_services_account_id}:role/${var.assume_role_name}"
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "development"
      "pp:domain"                           = "ownership"
      "pp:owner"                            = "ownership:orders"
      "pp:service"                          = "ownership-data-platform"
      "pp:terraformWorkspace"               = "ownership-data-platform-staging"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}