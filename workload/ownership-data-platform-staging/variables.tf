variable "environment" {
  description = "The environment being deployed to."
  default     = "staging"
}

variable "assume_role_account_id" {
  description = "The account ID to assume when initialising the AWS provider."
  type        = string
  default     = "************" # ownership-staging
}

variable "pod_point_account_id" {
  description = "The account ID for the Pod Point account."
  type        = string
  default     = "************" # Pod Point
}

variable "build_account_id" {
  description = "The account ID for the Ownership Data Platform Build account."
  type        = string
  default     = "************" # odp-build
}

variable "shared_services_account_id" {
  description = "The account ID for the Shared Services account."
  type        = string
  default     = "************"
}

variable "assume_role_name" {
  description = "The role to assume when initialising the AWS provider."
  type        = string
  default     = "terraform-ci"
}

variable "region" {
  description = "The region to deploy resources to."
  default     = "eu-west-1"
}

variable "vpc_cidr_block" {
  default = "*********/16"
}
