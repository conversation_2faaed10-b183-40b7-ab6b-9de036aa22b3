data "terraform_remote_state" "ownership_data_platform_build" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "ownership-data-platform-build"
    }
  }
}

data "terraform_remote_state" "salesforce" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "salesforce-dev"
    }
  }
}

data "terraform_remote_state" "ownership_acquisition" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "ownership-acquisition-dev"
    }
  }
}

data "terraform_remote_state" "ownership_orders" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "ownership-orders-dev"
    }
  }
}

data "terraform_remote_state" "experience_commercial" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "experience-commercial-dev"
    }
  }
}
