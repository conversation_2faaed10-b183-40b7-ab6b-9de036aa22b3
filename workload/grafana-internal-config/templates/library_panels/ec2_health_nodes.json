{"datasource": "pod-point", "description": "The number of nodes passing their health checks", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "dark-red", "value": 0}, {"color": "semi-dark-green", "value": 1}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "DatabaseConnections"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 2, "x": 0, "y": 1}, "id": 17, "libraryPanel": {"name": "EC2 - Health Nodes"}, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "dimensions": {"AvailabilityZone": "*", "LoadBalancer": "$service_alb", "TargetGroup": "targetgroup/$service_tg"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HealthyHostCount", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "HealthyHosts", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Health Nodes", "type": "stat"}