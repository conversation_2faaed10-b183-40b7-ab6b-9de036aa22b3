{"datasource": "pod-point", "description": "The amount of available storage space.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 46, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 5, "w": 7, "x": 17, "y": 19}, "id": 4, "libraryPanel": {"name": "RDS - Free Storage Space (MB)"}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"alias": "", "dimensions": {"DBInstanceIdentifier": "$service-$stage"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "FreeStorageSpace", "metricQueryType": 0, "namespace": "AWS/RDS", "period": "$rds_interval", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Free Storage Space (MB)", "type": "timeseries"}