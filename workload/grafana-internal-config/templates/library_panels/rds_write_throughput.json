{"datasource": "pod-point", "description": "The average number of bytes written to disk per second.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisGridShow": true, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 46, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "Bps"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "WriteLatency"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "WriteThroughput"}, "properties": [{"id": "color", "value": {"fixedColor": "semi-dark-green", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 14}, "id": 23763571993, "libraryPanel": {"description": "The average number of bytes written to disk per second.", "name": "RDS - Write Throughput (bytes/sec(SI))", "type": "timeseries", "uid": "LibPanRdsWriteThro", "version": 1}, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"alias": "", "dimensions": {"DBInstanceIdentifier": "$service-$stage"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "WriteThroughput", "metricQueryType": 0, "namespace": "AWS/RDS", "period": "$rds_interval", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Write Throughput (bytes/sec(SI))", "type": "timeseries"}