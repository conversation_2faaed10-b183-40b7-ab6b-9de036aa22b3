{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 39, "links": [], "liveNow": false, "panels": [{"collapsed": true, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "EC2 / LB", "type": "row"}, {"gridPos": {"h": 7, "w": 2, "x": 0, "y": 1}, "id": 4, "libraryPanel": {"name": "EC2 - Health Nodes", "uid": "LibPanEc2HealthNode"}, "title": "Health Nodes"}, {"gridPos": {"h": 7, "w": 2, "x": 2, "y": 1}, "id": 6, "libraryPanel": {"name": "EC2 - Unhealthy Nodes", "uid": "LibPanEc2UnhealthNode"}, "title": "Unhealthy Nodes"}, {"gridPos": {"h": 7, "w": 12, "x": 4, "y": 1}, "id": 8, "libraryPanel": {"name": "EC2 - HTTP Response Codes", "uid": "LibPanEc2HttpResponse"}, "title": "HTTP Response Codes"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The number of requests processed over IPv4 and IPv6. This metric is only incremented for requests where the load balancer node was able to choose a target. Requests rejected before a target is chosen (for example, HTTP 460, HTTP 400, some kinds of HTTP 503 and 500) are not reflected in this metric.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 49, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 3600000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 1}, "id": 10, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"LoadBalancer": "$service_alb", "TargetGroup": "targetgroup/$service_tg"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "RequestCount", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Service Request Count", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "TargetGroup"}}], "type": "timeseries"}, {"gridPos": {"h": 5, "w": 14, "x": 0, "y": 8}, "id": 12, "libraryPanel": {"name": "EC2 - Target Response Time (s)", "uid": "LibPanEc2TargRespTime"}, "title": "Target Response Time (s)"}, {"gridPos": {"h": 5, "w": 10, "x": 14, "y": 8}, "id": 14, "libraryPanel": {"name": "EC2 - Target Connection Error Count", "uid": "LibPanEc2TargConnErrCnt"}, "title": "Target Connection Error Count"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The percentage of allocated EC2 compute units that are currently in use on the instance. This metric identifies the processing power required to run an application on a selected instance.\n\nDepending on the instance type, tools in your operating system can show a different percentage than CloudWatch when the instance is not allocated a full processor core.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 49, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 3600000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-green", "value": null}, {"color": "dark-green", "value": 0}, {"color": "semi-dark-orange", "value": 60}, {"color": "dark-red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 5, "w": 10, "x": 0, "y": 13}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"InstanceId": "$instance_ids"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/EC2", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Node CPU Utilizstion (%)", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "InstanceId"}}], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The number of bytes received by the instance on all network interfaces. This metric identifies the volume of incoming network traffic to a single instance.\n\nThe number reported is the number of bytes received during the period. If you are using basic (5-minute) monitoring and the statistic is Sum, you can divide this number by 300 to find Bytes/second. If you have detailed (1-minute) monitoring and the statistic is Sum, divide it by 60.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 49, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 3600000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 5, "w": 7, "x": 10, "y": 13}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"InstanceId": "$instance_ids"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "NetworkIn", "metricQueryType": 0, "namespace": "AWS/EC2", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Network In", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "InstanceId"}}], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The number of bytes sent out by the instance on all network interfaces. This metric identifies the volume of outgoing network traffic from a single instance.\n\nThe number reported is the number of bytes sent during the period. If you are using basic (5-minute) monitoring and the statistic is Sum, you can divide this number by 300 to find Bytes/second. If you have detailed (1-minute) monitoring and the statistic is Sum, divide it by 60.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 49, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 3600000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 5, "w": 7, "x": 17, "y": 13}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"InstanceId": "$instance_ids"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "NetworkOut", "metricQueryType": 0, "namespace": "AWS/EC2", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Network Out", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "InstanceId"}}], "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"hide": 2, "label": "Service", "name": "service", "query": "pdf", "skipUrlSync": false, "type": "constant"}, {"current": {"selected": true, "text": "staging", "value": "staging"}, "hide": 0, "includeAll": false, "label": "Stage", "multi": false, "name": "stage", "options": [{"selected": true, "text": "staging", "value": "staging"}, {"selected": false, "text": "prod", "value": "prod"}], "query": "staging, prod", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "definition": "resource_arns(eu-west-1, elasticloadbalancing:loadbalancer,{\"Grafana\":[\"$service-$stage-grafana\"]})", "description": "The ALB of the service", "hide": 2, "includeAll": false, "label": "Service ALB", "multi": false, "name": "service_alb", "options": [], "query": "resource_arns(eu-west-1, elasticloadbalancing:loadbalancer,{\"Grafana\":[\"$service-$stage-grafana\"]})", "refresh": 1, "regex": "/\\/(?<text>.*)/", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "definition": "resource_arns(eu-west-1, elasticloadbalancing:targetgroup,{\"Grafana\":[\"$service-$stage-grafana\"]})", "hide": 2, "includeAll": false, "label": "Service Target Group", "multi": false, "name": "service_tg", "options": [], "query": "resource_arns(eu-west-1, elasticloadbalancing:targetgroup,{\"Grafana\":[\"$service-$stage-grafana\"]})", "refresh": 1, "regex": "/\\/(?<text>.*)/", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "definition": "ec2_instance_attribute(eu-west-1, InstanceId, { \"tag:Grafana\": [\"$service-$stage\"] });", "description": "instance id's for the current service", "hide": 0, "includeAll": true, "label": "Instance IDs", "multi": true, "name": "instance_ids", "options": [], "query": "ec2_instance_attribute(eu-west-1, InstanceId, { \"tag:Grafana\": [\"$service-$stage\"] });", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "PDF (PDF Manipulator)", "uid": "NV-v93l7z", "version": 6, "weekStart": ""}