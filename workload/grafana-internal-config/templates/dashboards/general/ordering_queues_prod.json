{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 117, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "NumberOfMessagesDeleted_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "NumberOfEmptyReceives_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "id": 2, "interval": "60s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$primary_queue"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfEmptyReceives", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$primary_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ApproximateNumberOfMessagesVisible", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$primary_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfMessagesDeleted", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "C", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$primary_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ApproximateAgeOfOldestMessage", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "D", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "$primary_queue", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "NumberOfMessagesDeleted_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "NumberOfEmptyReceives_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "id": 4, "interval": "60s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$salesforce_primary_queue"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfEmptyReceives", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$salesforce_primary_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ApproximateNumberOfMessagesVisible", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$salesforce_primary_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfMessagesDeleted", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "C", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$salesforce_primary_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ApproximateAgeOfOldestMessage", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "D", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "$salesforce_primary_queue", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "NumberOfMessagesDeleted_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "NumberOfEmptyReceives_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "id": 6, "interval": "60s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$secondary_queue"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfEmptyReceives", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$secondary_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ApproximateNumberOfMessagesVisible", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$secondary_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfMessagesDeleted", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "C", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$secondary_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ApproximateAgeOfOldestMessage", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "D", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "$documents_queue", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "NumberOfMessagesDeleted_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "NumberOfEmptyReceives_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "id": 3, "interval": "60s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$documents_queue"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfEmptyReceives", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$documents_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ApproximateNumberOfMessagesVisible", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$documents_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfMessagesDeleted", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "C", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$documents_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ApproximateAgeOfOldestMessage", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "D", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "$documents_queue", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "NumberOfMessagesDeleted_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "NumberOfEmptyReceives_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 18}, "id": 5, "interval": "60s", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$salesforce_secondary_queue"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfEmptyReceives", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$salesforce_secondary_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ApproximateNumberOfMessagesVisible", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$salesforce_secondary_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfMessagesDeleted", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "C", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"QueueName": "$salesforce_secondary_queue"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ApproximateAgeOfOldestMessage", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "D", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "$salesforce_secondary_queue", "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "installs", "value": "installs"}, "hide": 0, "name": "primary_queue", "options": [{"selected": true, "text": "installs", "value": "installs"}], "query": "installs", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": "installs-secondary", "value": "installs-secondary"}, "hide": 0, "name": "secondary_queue", "options": [{"selected": true, "text": "installs-secondary", "value": "installs-secondary"}], "query": "installs-secondary", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": "installs-documents", "value": "installs-documents"}, "hide": 0, "name": "documents_queue", "options": [{"selected": true, "text": "installs-documents", "value": "installs-documents"}], "query": "installs-documents", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": "installs-salesforce.fifo", "value": "installs-salesforce.fifo"}, "hide": 0, "label": "", "name": "salesforce_primary_queue", "options": [{"selected": true, "text": "installs-salesforce.fifo", "value": "installs-salesforce.fifo"}], "query": "installs-salesforce.fifo", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": "installs-pwc-update.fifo", "value": "installs-pwc-update.fifo"}, "hide": 0, "name": "salesforce_secondary_queue", "options": [{"selected": true, "text": "installs-pwc-update.fifo", "value": "installs-pwc-update.fifo"}], "query": "installs-pwc-update.fifo", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Ordering Queues - prod", "uid": "kzwFIdf4k", "version": 2, "weekStart": ""}