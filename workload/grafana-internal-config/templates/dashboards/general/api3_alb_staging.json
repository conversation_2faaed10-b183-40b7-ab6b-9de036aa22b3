{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 42, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 6, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "responses 503"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 11, "x": 0, "y": 0}, "id": 2, "maxDataPoints": 200, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 0, "hide": false, "rawSQL": "select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time, \nelb_status_code, count(*) as responses\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand year = '${__from:date:YYYY}'\nand month between '${__from:date:MM}' and '${__to:date:MM}'\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'), elb_status_code\norder by time", "refId": "A", "table": "api3_staging_alb"}], "title": "HTTP responses by code", "type": "timeseries"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 6, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "responses 503"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}]}, "gridPos": {"h": 10, "w": 11, "x": 11, "y": 0}, "id": 14, "maxDataPoints": 200, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 0, "hide": false, "rawSQL": "select cast(cast(to_unixtime(date_parse(concat(cast(date as varchar), ' ', time), '%Y-%m-%d %T'))/($__interval_ms / 100) as double)*($__interval_ms / 100) as double), count(*) as total\nfrom $__table\nwhere to_unixtime(date_parse(concat(cast(date as varchar), ' ', time), '%Y-%m-%d %T')) > ${__from:date:seconds}\nand to_unixtime(date_parse(concat(cast(date as varchar), ' ', time), '%Y-%m-%d %T')) < ${__to:date:seconds}\ngroup by cast(cast(to_unixtime(date_parse(concat(cast(date as varchar), ' ', time), '%Y-%m-%d %T'))/($__interval_ms / 100) as double)*($__interval_ms / 100) as double)\n", "refId": "A", "table": "api_staging_cf_dist"}], "title": "HTTP responses by code", "type": "timeseries"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user_agent"}, "properties": [{"id": "custom.width", "value": 216}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "client_ip"}, "properties": [{"id": "custom.width", "value": 124}]}]}, "gridPos": {"h": 8, "w": 11, "x": 0, "y": 10}, "id": 9, "maxDataPoints": 300, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select user_agent, count(request_verb) as count\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand (\n  year = '${__to:date:YYYY}'\n  and month between '${__from:date:MM}' and '${__to:date:MM}'\n)\nand user_agent not like 'Amazon-Route53%'\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by user_agent\norder by count(request_verb) desc\nlimit 20", "refId": "A", "table": "api3_staging_alb"}], "title": "Top user agents", "transformations": [{"id": "renameByRegex", "options": {"regex": "(https:\\/\\/api\\.pod-point\\.com:443)(.*)", "renamePattern": "$2"}}], "type": "table"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 13, "x": 11, "y": 10}, "id": 4, "maxDataPoints": 200, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time, sum(sent_bytes) as data_sent\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand (\n  year = '${__to:date:YYYY}'\n  and month between '${__from:date:MM}' and '${__to:date:MM}'\n)\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\norder by time", "refId": "A", "table": "api3_staging_alb"}], "title": "Data sent to clients", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user_agent"}, "properties": [{"id": "custom.width", "value": 216}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "client_ip"}, "properties": [{"id": "custom.width", "value": 124}]}]}, "gridPos": {"h": 8, "w": 7, "x": 0, "y": 18}, "id": 7, "maxDataPoints": 300, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select user_agent, client_ip, count(request_verb) as count\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand (\n  year = '${__to:date:YYYY}'\n  and month between '${__from:date:MM}' and '${__to:date:MM}'\n)\nand user_agent not like 'Amazon-Route53%'\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by user_agent, client_ip\norder by count(request_verb) desc\nlimit 20", "refId": "A", "table": "api3_staging_alb"}], "title": "Top user agents by IP", "transformations": [{"id": "renameByRegex", "options": {"regex": "(https:\\/\\/api\\.pod-point\\.com:443)(.*)", "renamePattern": "$2"}}], "type": "table"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 13, "x": 11, "y": 20}, "id": 10, "maxDataPoints": 200, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time, avg(target_processing_time) as processing_time\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand (\n  year = '${__to:date:YYYY}'\n  and month between '${__from:date:MM}' and '${__to:date:MM}'\n)\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\norder by time", "refId": "A", "table": "api3_staging_alb"}], "title": "Average request duration", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "orange", "value": 10}, {"color": "red", "value": 30}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "request_verb"}, "properties": [{"id": "custom.width", "value": 107}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "path"}, "properties": [{"id": "custom.width", "value": 364}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "seconds"}, "properties": [{"id": "custom.width", "value": 154}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "verb"}, "properties": [{"id": "custom.width", "value": 68}]}]}, "gridPos": {"h": 8, "w": 8, "x": 7, "y": 28}, "id": 6, "maxDataPoints": 300, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "seconds"}]}, "pluginVersion": "8.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select request_verb as verb,\n      regexp_replace(request_url, '\\?.*', '') AS path, \n      approx_percentile(target_processing_time,0.95) AS seconds\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand (\n  year = '${__to:date:YYYY}'\n  and month between '${__from:date:MM}' and '${__to:date:MM}'\n)\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by request_verb, regexp_replace(request_url, '\\?.*', '')\norder by approx_percentile(target_processing_time,0.95) desc\nlimit 20", "refId": "A", "table": "api3_staging_alb"}], "title": "Slowest endpoints (p95)", "transformations": [], "type": "table"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "path"}, "properties": [{"id": "custom.width", "value": 430}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.width", "value": 170}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "verb"}, "properties": [{"id": "custom.width", "value": 74}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "lcd", "type": "gauge"}}]}]}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 28}, "id": 8, "maxDataPoints": 300, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select request_verb as verb,\n      regexp_replace(request_url, '\\?.*', '') AS path, \n      count(*) as count\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand (\n  year = '${__to:date:YYYY}'\n  and month between '${__from:date:MM}' and '${__to:date:MM}'\n)\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by request_verb, regexp_replace(request_url, '\\?.*', '')\norder by count(*) desc\nlimit 50", "refId": "A", "table": "api3_staging_alb"}], "title": "Most active paths", "transformations": [{"id": "renameByRegex", "options": {"regex": "(https:\\/\\/api\\.pod-point\\.com:443)(.*)", "renamePattern": "$2"}}], "type": "table"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 9, "x": 0, "y": 36}, "id": 12, "options": {"displayMode": "gradient", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "showUnfilled": true}, "pluginVersion": "8.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "eu-west-1"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select replace(user_agent, '%20',' '), count(*) as total\nfrom $__table\nwhere to_unixtime(date_parse(concat(cast(date as varchar), ' ', time), '%Y-%m-%d %T')) > ${__from:date:seconds}\nand to_unixtime(date_parse(concat(cast(date as varchar), ' ', time), '%Y-%m-%d %T')) < ${__to:date:seconds}\nand request_ip like '%$client_ip%'\nand uri like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by user_agent\norder by total desc\n", "refId": "A", "table": "api_staging_cf_dist"}], "title": "CF - User Agents", "transformations": [], "type": "bargauge"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 7, "x": 9, "y": 36}, "id": 13, "options": {"displayMode": "gradient", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "limit": 10, "values": true}, "showUnfilled": true}, "pluginVersion": "8.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "eu-west-1"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select request_ip, count(*) as total\nfrom $__table\nwhere to_unixtime(date_parse(concat(cast(date as varchar), ' ', time), '%Y-%m-%d %T')) > ${__from:date:seconds}\nand to_unixtime(date_parse(concat(cast(date as varchar), ' ', time), '%Y-%m-%d %T')) < ${__to:date:seconds}\nand request_ip like '%$client_ip%'\nand uri like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by request_ip \norder by total desc\n", "refId": "A", "table": "api_staging_cf_dist"}], "title": "CF - IP Addresses", "transformations": [], "type": "bargauge"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "name": "client_ip", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": "", "value": ""}, "description": "", "hide": 0, "name": "request_url", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "name": "user_agent", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "API3 ALB - staging", "uid": "8eEUNMjnz", "version": 12, "weekStart": ""}