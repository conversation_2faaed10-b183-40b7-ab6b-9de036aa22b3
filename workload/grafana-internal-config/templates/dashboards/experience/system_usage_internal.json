{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 115, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "cloudwatch", "uid": "${environment}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "email"}, "properties": [{"id": "displayName", "value": "Email Address"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count(*)"}, "properties": [{"id": "displayName", "value": "Number of requests"}]}]}, "gridPos": {"h": 9, "w": 3, "x": 0, "y": 0}, "id": 7, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "${environment}"}, "expression": "filter @message like /request authorised/\n| filter @log like \"$application\"\n| filter ispresent(email)\n| filter email like /@pod-point.com/\n| stats count_distinct(email)", "id": "", "logGroupNames": ["/ecs/destination-site-admin-api", "/ecs/internal-site-admin-api"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Usage by user email address", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "${environment}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "email"}, "properties": [{"id": "displayName", "value": "Email Address"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count(*)"}, "properties": [{"id": "displayName", "value": "Number of requests"}, {"id": "custom.width", "value": 150}]}]}, "gridPos": {"h": 9, "w": 7, "x": 3, "y": 0}, "id": 3, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "${environment}"}, "expression": "filter @message like /request authorised/\n| filter @log like \"$application\"\n| filter ispresent(email)\n| filter email like /@pod-point.com/\n| stats count(*) as count by email\n| sort count desc", "id": "", "logGroupNames": ["/ecs/destination-site-admin-api", "/ecs/internal-site-admin-api"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["email"]}], "title": "Usage by user email address", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "${environment}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 75, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 14, "x": 10, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "${environment}"}, "expression": "filter @message like /request authorised/\n| filter @log like \"$application\"\n| filter ispresent(email)\n| filter email like /@pod-point.com/\n| stats count(*) by email, bin(15m)", "id": "", "logGroupNames": ["/ecs/destination-site-admin-api", "/ecs/internal-site-admin-api"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["email", "bin(15m)"]}], "title": "Usage by user email address", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "${environment}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "email"}, "properties": [{"id": "displayName", "value": "Email Address"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count(*)"}, "properties": [{"id": "displayName", "value": "Number of requests"}]}]}, "gridPos": {"h": 9, "w": 3, "x": 0, "y": 9}, "id": 9, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "${environment}"}, "expression": "filter @message like /request authorised/\n| filter @log like \"$application\"\n| filter ispresent(email)\n| filter email like /@pod-point.com/\n| filter ispresent(groupName)\n| stats count_distinct(groupName)", "id": "", "logGroupNames": ["/ecs/destination-site-admin-api", "/ecs/internal-site-admin-api"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Usage by user group", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "${environment}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "email"}, "properties": [{"id": "displayName", "value": "Email Address"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count(*)"}, "properties": [{"id": "displayName", "value": "Number of requests"}, {"id": "custom.width", "value": 150}]}]}, "gridPos": {"h": 9, "w": 7, "x": 3, "y": 9}, "id": 4, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "${environment}"}, "expression": "filter @message like /request authorised/\n| filter @log like \"$application\"\n| filter ispresent(email)\n| filter email like /@pod-point.com/\n| filter ispresent(groupName)\n| stats count(*) as count by groupName\n| sort count desc", "id": "", "logGroupNames": ["/ecs/destination-site-admin-api", "/ecs/internal-site-admin-api"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["groupName"]}], "title": "Usage by user group", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "${environment}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 75, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 14, "x": 10, "y": 9}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "${environment}"}, "expression": "filter @message like /request authorised/\n| filter @log like \"$application\"\n| filter ispresent(email)\n| filter email like /@pod-point.com/\n| filter ispresent(groupName)\n| stats count(*) by groupName, bin(15m)", "id": "", "logGroupNames": ["/ecs/destination-site-admin-api", "/ecs/internal-site-admin-api"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["groupName", "bin(15m)"]}], "title": "Usage by user group", "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "destination-prod", "value": "destination-prod"}, "hide": 0, "includeAll": false, "label": "Environment", "multi": false, "name": "environment", "options": [], "query": "cloudwatch", "queryValue": "", "refresh": 1, "regex": "destination-(.*)", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": true, "text": "Site Service", "value": "destination-site-admin-api"}, "hide": 0, "includeAll": false, "label": "Application", "multi": false, "name": "application", "options": [{"selected": false, "text": "Fleet Service", "value": "fleet-site-admin-api"}, {"selected": true, "text": "Site Service", "value": "destination-site-admin-api"}, {"selected": false, "text": "Site Service (Internal)", "value": "internal-site-admin-api"}], "query": "Fleet Service : fleet-site-admin-api, Site Service : destination-site-admin-api, Site Service (Internal) : internal-site-admin-api", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now/d", "to": "now/d"}, "timepicker": {}, "timezone": "", "title": "System Usage (Internal Users)", "uid": "cgowUDf4z", "version": 6, "weekStart": ""}