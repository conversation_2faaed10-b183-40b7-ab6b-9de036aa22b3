{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 197, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Prod", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "expression": "fields @timestamp, @message\n| parse @message \"* * * * * * * * *\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter url like /\"\\/sites/\n| parse time_ms \"http.time_ms=*\" as sites_r_ms", "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "/sites", "region": "default", "statsGroups": []}], "thresholds": [{"colorMode": "critical", "op": "gt", "value": 3000, "visible": true}], "title": "Endpoint performance over time", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [], "max": 1100, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 800}, {"color": "red", "value": 1000}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 12, "y": 1}, "id": 12, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "expression": "fields @timestamp, @message\n| parse @message \"time=* level=* request_id=* http.method=* http.url=* http.remote_addr=* http.status=* http.bytes=* http.time_ms=*\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter url like /\"\\/sites/\n| stats avg(time_ms)", "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "/sites Avg Response Time", "type": "gauge"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 4, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Staging", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwDestStag"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwDestStag"}, "expression": "fields @timestamp, @message\n| parse @message \"* * * * * * * * *\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter url like /\"\\/sites/\n| parse time_ms \"http.time_ms=*\" as sites_r_ms", "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "/sites", "region": "default", "statsGroups": []}], "thresholds": [], "title": "Endpoint performance over time", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwDestStag"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 1100, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 800}, {"color": "red", "value": 1000}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 12, "y": 10}, "id": 13, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwDestStag"}, "expression": "fields @timestamp, @message\n| parse @message \"time=* level=* request_id=* http.method=* http.url=* http.remote_addr=* http.status=* http.bytes=* http.time_ms=*\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter url like /\"\\/sites/\n| stats avg(time_ms)", "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "/sites Avg Response Time", "type": "gauge"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 6, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "<PERSON>", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwDestDev"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwDestDev"}, "expression": "fields @timestamp, @message\n| parse @message \"* * * * * * * * *\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter url like /\"\\/sites/\n| parse time_ms \"http.time_ms=*\" as sites_r_ms", "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "/sites", "region": "default", "statsGroups": []}], "thresholds": [], "title": "Endpoint performance over time", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwDestDev"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 1100, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 800}, {"color": "red", "value": 1000}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 12, "y": 19}, "id": 14, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwDestDev"}, "expression": "fields @timestamp, @message\n| parse @message \"time=* level=* request_id=* http.method=* http.url=* http.remote_addr=* http.status=* http.bytes=* http.time_ms=*\" as date_time, level, request_id, method, url, remote_addr, status, bytes_size, time_ms\n| filter url like /\"\\/sites/\n| stats avg(time_ms)", "id": "", "logGroupNames": ["/ecs/data-platform-api"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "/sites Avg Response Time", "type": "gauge"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": ["xdp", "data-platform-api"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Endpoint Performance (data-platform-api)", "uid": "pj6ZmfnSz", "version": 47, "weekStart": ""}