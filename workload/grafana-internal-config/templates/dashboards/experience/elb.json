{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 14361, "graphTooltip": 1, "id": 51, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "description": "The number of targets that are considered healthy.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 0}, "id": 19, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer", "TargetGroup": "*"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HealthyHostCount", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "$region", "sqlExpression": "", "statistic": "Average"}], "title": "Healthy targets", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "description": "The number of targets that are considered unhealthy.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 0}, "id": 20, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer", "TargetGroup": "*"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "UnHealthyHostCount", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "$region", "sqlExpression": "", "statistic": "Average"}], "title": "UnHealthy targets", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "description": "The number of load balancer capacity units (LCU) used by your load balancer.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 0}, "id": 21, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ConsumedLCUs", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "$region", "sqlExpression": "", "statistic": "Sum"}], "title": "Consumed LCUs", "type": "stat"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 6, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "HTTP Response", "type": "row"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "description": "HTTP response code returned from the targets", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 6}, "hiddenSeries": false, "id": 8, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.4.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "HTTP 2XX", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HTTPCode_Target_2XX_Count", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "HTTP 3XX", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HTTPCode_Target_3XX_Count", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "B", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "HTTP 4XX", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HTTPCode_Target_4XX_Count", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "C", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "HTTP 5XX", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HTTPCode_Target_5XX_Count", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "D", "region": "$region", "sqlExpression": "", "statistic": "Sum"}], "thresholds": [], "timeRegions": [], "title": "Target HTTP Response", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1282", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:1283", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "description": "HTTP response code returned from the load balancer itself, before it even reached the target", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 6}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "9.4.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "HTTP 3XX", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HTTPCode_ELB_3XX_Count", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "HTTP 4XX", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HTTPCode_ELB_4XX_Count", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "B", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "HTTP 5XX", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HTTPCode_ELB_5XX_Count", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "C", "region": "$region", "sqlExpression": "", "statistic": "Sum"}], "thresholds": [], "timeRegions": [], "title": "ELB HTTP Response", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:846", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:847", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "description": "Common 5XX response code returned by AWS ELB", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 6}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "9.4.7", "pointradius": 2, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "HTTP 500", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HTTPCode_ELB_500_Count", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "HTTP 502", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HTTPCode_ELB_502_Count", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "B", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "HTTP 503", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HTTPCode_ELB_503_Count", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "C", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "HTTP 504", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "HTTPCode_ELB_504_Count", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "D", "region": "$region", "sqlExpression": "", "statistic": "Sum"}], "thresholds": [], "timeRegions": [], "title": "ELB 500 Errors", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1018", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:1019", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 14}, "id": 14, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Clients", "type": "row"}, {"aliasColors": {"Active Connections": "blue", "Active Connections Count": "blue", "New Connections": "red", "New Connections Count": "red", "Rejected Connections": "purple"}, "bars": true, "dashLength": 10, "dashes": false, "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "description": "ELB Connections metrics", "fill": 1, "fillGradient": 0, "gridPos": {"h": 12, "w": 8, "x": 0, "y": 15}, "hiddenSeries": false, "id": 10, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "9.4.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "Active Connections", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ActiveConnectionCount", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "New Connections", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NewConnectionCount", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "B", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "Rejected Connections", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "RejectedConnectionCount", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "C", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "Target Errors", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "TargetConnectionErrorCount", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "D", "region": "$region", "sqlExpression": "", "statistic": "Sum"}], "thresholds": [], "timeRegions": [], "title": "Connections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1870", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:1871", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "description": "ELB Request metrics", "fill": 1, "fillGradient": 0, "gridPos": {"h": 12, "w": 8, "x": 8, "y": 15}, "hiddenSeries": false, "id": 12, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.4.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "Requests Total", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "RequestCount", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "Non Sticky Requests", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NonStickyRequestCount", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "B", "region": "$region", "sqlExpression": "", "statistic": "Sum"}, {"alias": "Request Per Target (Avg)", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "RequestCountPerTarget", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "C", "region": "$region", "sqlExpression": "", "statistic": "Sum"}], "thresholds": [], "timeRegions": [], "title": "Requests", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2052", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:2053", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "description": "The total number of bytes processed by the load balancer over IPv4 and IPv6. This count includes traffic to and from clients and Lambda functions, and traffic from an Identity Provider (IdP) if user authentication is enabled.", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 15}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.4.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "Processed Bytes", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ProcessedBytes", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "$region", "sqlExpression": "", "statistic": "Average"}], "thresholds": [], "timeRegions": [], "title": "Processed bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2513", "format": "bytes", "logBase": 1, "show": true}, {"$$hashKey": "object:2514", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "description": "The time elapsed, in seconds, after the request leaves the load balancer until a response from the target is received", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 21}, "hiddenSeries": false, "id": 17, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.4.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "Processed Bytes", "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "dimensions": {"LoadBalancer": "$loadbalancer"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "TargetResponseTime", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "$region", "sqlExpression": "", "statistic": "Average"}], "thresholds": [], "timeRegions": [], "title": "Target Response Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2513", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:2514", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": ["destination", "aws", "elb"], "templating": {"list": [{"current": {"selected": false, "text": "destination-prod", "value": "destination-prod"}, "hide": 0, "includeAll": false, "label": "Data Source", "multi": false, "name": "datasource", "options": [], "query": "cloudwatch", "queryValue": "", "refresh": 1, "regex": "/destination-dev|destination-staging|destination-prod/", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": false, "text": "default", "value": "default"}, "datasource": {"type": "cloudwatch", "uid": "CwDestProd"}, "definition": "regions()", "hide": 0, "includeAll": false, "label": "Region", "multi": false, "name": "region", "options": [], "query": "regions()", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "app/data-platform-api-prod-alb/4e8595b489f53b3e", "value": "app/data-platform-api-prod-alb/4e8595b489f53b3e"}, "datasource": {"type": "cloudwatch", "uid": "${datasource}"}, "definition": "dimension_values($region, AWS/ApplicationELB, ActiveConnectionCount, LoadBalancer)", "hide": 0, "includeAll": false, "label": "<PERSON><PERSON>r", "multi": false, "name": "loadbalancer", "options": [], "query": "dimension_values($region, AWS/ApplicationELB, ActiveConnectionCount, LoadBalancer)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Elastic Load Balancer", "uid": "vmIdaerGk", "version": 11, "weekStart": ""}