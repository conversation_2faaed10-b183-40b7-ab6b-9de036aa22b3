{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 192, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaExpProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 3, "mappings": [{"options": {"NaN": {"color": "green", "index": 0, "text": "N/A"}}, "type": "value"}], "noValue": "-", "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 0.999}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "id": 10, "links": [{"title": "Driver Account Web App", "url": "https://g-ef7d2baf7b.grafana-workspace.eu-west-1.amazonaws.com/d/gIV68gWIz/driver-account-web-app?orgId=1"}], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "/^actual_SLO$/", "values": true}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "access_logs", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaExpProd"}, "format": 1, "rawSQL": "select 0.999 as target_SLO, cast(x.success as double) / cast(y.total as double) as actual_SLO\n\nfrom (\n  select\n  count(*) as success\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  and elb_status_code < 500\n) x\njoin (\n  select\n  count(*) as total\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n) y\non 1=1", "refId": "A", "table": "driver_account_webapp_load_balancer"}], "title": "Driver Account Web App Availability 99.9%", "type": "stat"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaExpProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 3, "mappings": [{"options": {"NaN": {"color": "green", "index": 0, "text": "N/A"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 0.999}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "id": 8, "links": [{"title": "Driver Account Web App", "url": "https://g-ef7d2baf7b.grafana-workspace.eu-west-1.amazonaws.com/d/gIV68gWIz/driver-account-web-app?orgId=1"}], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "/^actual_SLO$/", "values": true}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "access_logs", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaExpProd"}, "format": 1, "rawSQL": "select 0.999 as target_SLO, cast(x.success as double) / cast(y.total as double) as actual_SLO\n\nfrom (\n  select\n  count(*) as success\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  and target_processing_time < (500.0 / 1000.0)\n) x\njoin (\n  select\n  count(*) as total\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n) y\non 1=1", "refId": "A", "table": "driver_account_webapp_load_balancer"}], "title": "Driver Account Web App Latency < 500ms 99.9%", "type": "stat"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "decimals": 3, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 0.999}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "format": 0, "hide": false, "rawSQL": "select x.time,\ncast(x.success as double) / cast(y.total as double) as Availability\nfrom (\n  select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time,\n  count(*) as success\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  and elb_status_code < 500\n  group by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  order by time\n) x\njoin (\n  select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time,\n  count(*) as total\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  group by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  order by time\n) y\non x.time=y.time\norder by x.time, y.time", "refId": "A", "table": "driver_account_webapp_load_balancer"}, {"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"uid": "AthenaDestProdAccLog"}, "format": 0, "hide": true, "rawSQL": "select x.time,\ncast(x.success as double) / cast(y.total as double) as Availability\nfrom (\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\n  count(*) as success\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  and elb_status_code < 500\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\n  order by time\n) x\njoin (\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\n  count(*) as total\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\n  order by time\n) y\non x.time=y.time\norder by x.time, y.time", "refId": "B", "table": "driver_account_api_load_balancer"}], "thresholds": [{"colorMode": "critical", "op": "lt", "value": 0.999, "visible": true}], "title": "Driver Account Webapp Availability 99.9%", "type": "timeseries"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "decimals": 3, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 0.999}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "format": 0, "rawSQL": "select x.time,\ncast(x.success as double) / cast(y.total as double) as Availability\nfrom (\n  select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time,\n  count(*) as success\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  and target_processing_time < (500.0 / 1000.0)\n  group by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  order by time\n) x\njoin (\n  select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time,\n  count(*) as total\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  group by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  order by time\n) y\non x.time = y.time\norder by x.time, y.time", "refId": "A", "table": "driver_account_webapp_load_balancer"}, {"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"uid": "AthenaDestProdAccLog"}, "format": 0, "hide": true, "rawSQL": "select x.time,\ncast(x.success as double) / cast(y.total as double) as Availability\nfrom (\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\n  count(*) as success\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  and target_processing_time < (500.0 / 1000.0)\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\n  order by time\n) x\njoin (\n  select FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800) as time,\n  count(*) as total\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  group by FROM_UNIXTIME(floor(TO_UNIXTIME(parse_datetime(time,'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'))/1800)*1800)\n  order by time\n) y\non x.time = y.time\norder by x.time, y.time", "refId": "B", "table": "driver_account_api_load_balancer"}], "thresholds": [{"colorMode": "critical", "op": "lt", "value": 0.999, "visible": true}], "title": "Driver Account Webapp Latency < 500ms 99.9%", "type": "timeseries"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 18}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "format": 0, "hide": false, "rawSQL": "select x.time,\nx.\"3xx_count\",\ny.\"4xx_count\",\nz.\"5xx_count\"\nfrom (\n  select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time,\n  count(*) as \"3xx_count\"\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  and elb_status_code between 300 and 399\n  group by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  order by time\n) x\nleft outer join (\n  select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time,\n  count(*) as \"4xx_count\"\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  and elb_status_code between 400 and 499\n  group by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  order by time\n) y\non x.time=y.time\nleft outer join (\n  select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time,\n  count(*) as \"5xx_count\"\n  from $__table\n  where $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  and elb_status_code between 500 and 599\n  group by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n  order by time\n) z\non x.time=z.time\norder by x.time, y.time, z.time\n", "refId": "A", "table": "driver_account_webapp_load_balancer"}], "title": "Driver Account Webapp HTTP Error Counts", "type": "timeseries"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 18}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "format": 0, "rawSQL": "select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time,\napprox_percentile(target_processing_time, 0.95) as p95_latency,\navg(target_processing_time) as avg_latency\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand target_processing_time != -1\ngroup by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\norder by time", "refId": "A", "table": "driver_account_webapp_load_balancer"}, {"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "format": 0, "hide": true, "rawSQL": "select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time,\np95(*) as \"p95\"\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand target_processing_time\ngroup by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\norder by time", "refId": "B", "table": "installer_api_load_balancer"}], "title": "Driver Account Webapp Response Time", "type": "timeseries"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 27}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "format": 0, "rawSQL": "select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time,\ncount(*) as requests\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\ngroup by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\norder by time", "refId": "A", "table": "driver_account_webapp_load_balancer"}], "title": "Driver Account Webapp Request Count", "type": "timeseries"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 3, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 27}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "format": 1, "rawSQL": "select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time,\ncount(*) / (to_milliseconds(parse_duration('$__interval')) / 1000.0) as requests\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\ngroup by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\norder by time", "refId": "A", "table": "fleet_site_admin_webapp_load_balancer"}], "title": "Driver Account Webapp Request/s", "type": "timeseries"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "verb"}, "properties": [{"id": "custom.width", "value": 45}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "path"}, "properties": [{"id": "custom.width", "value": 500}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.width", "value": 154}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 36}, "id": 17, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "format": 1, "rawSQL": "select request_verb as verb,\n      regexp_replace(regexp_replace(request_url, '\\?.*', ''), '\\/\\d+', '\\/{id}') AS path,\n      count(*) as count\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand to_unixtime(date_parse(cast(day as varchar), '%Y/%m/%d')) between to_unixtime(date_parse(${__from:date:'YYYY-MM-DD'}, '%Y-%m-%d')) and to_unixtime(date_parse(${__to:date:'YYYY-MM-DD'}, '%Y-%m-%d'))\n/*and client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'*/\ngroup by request_verb, regexp_replace(regexp_replace(request_url, '\\?.*', ''), '\\/\\d+', '\\/{id}')\norder by count(*) desc\nlimit 50", "refId": "A", "table": "driver_account_webapp_load_balancer"}], "title": "Most active paths", "type": "table"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 10}, {"color": "red", "value": 30}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "verb"}, "properties": [{"id": "custom.width", "value": 45}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "path"}, "properties": [{"id": "custom.width", "value": 500}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "seconds"}, "properties": [{"id": "custom.width", "value": 154}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 36}, "id": 15, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "8.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "format": 0, "rawSQL": "select request_verb as verb,\n      regexp_replace(regexp_replace(regexp_replace(request_url, '\\?.*', ''), '\\/sessions\\/\\w+', '\\/sessions\\/{sessionId}'), '\\/\\d+', '\\/{id}') AS path, \n      approx_percentile(target_processing_time,0.95) AS seconds\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\n/*and client_ip like '%%'\nand request_url like '%%'\nand user_agent like '%%'*/\ngroup by request_verb, regexp_replace(regexp_replace(regexp_replace(request_url, '\\?.*', ''), '\\/sessions\\/\\w+', '\\/sessions\\/{sessionId}'), '\\/\\d+', '\\/{id}')\norder by approx_percentile(target_processing_time,0.95) desc\nlimit 50", "refId": "A", "table": "driver_account_webapp_load_balancer"}], "title": "Slowest endpoints (p95)", "type": "table"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "user_agent"}, "properties": [{"id": "custom.width", "value": 500}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 44}, "id": 13, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "${Athena}"}, "format": 1, "rawSQL": "select user_agent, count(request_verb) as count\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand to_unixtime(date_parse(cast(day as varchar), '%Y/%m/%d')) between to_unixtime(date_parse(${__from:date:'YYYY-MM-DD'}, '%Y-%m-%d')) and to_unixtime(date_parse(${__to:date:'YYYY-MM-DD'}, '%Y-%m-%d'))\n--and user_agent not like 'ELB-HealthChecker%'\n/*and client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'*/\ngroup by user_agent\norder by count(request_verb) desc\n--limit 20", "refId": "A", "table": "driver_account_webapp_load_balancer"}], "title": "Top user agents", "type": "table"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "destination-prod-access-logs", "value": "destination-prod-access-logs"}, "hide": 0, "includeAll": false, "multi": false, "name": "Athena", "options": [], "query": "grafana-athena-datasource", "queryValue": "", "refresh": 1, "regex": "/^destination/", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Driver Account Web App", "uid": "gIV68gWIz", "version": 7, "weekStart": ""}