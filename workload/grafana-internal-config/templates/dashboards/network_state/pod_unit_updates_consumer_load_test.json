{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 83, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "cloudwatch", "uid": "CwCsStateDev"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 100, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsStateDev"}, "dimensions": {"DBClusterIdentifier": "firmware-upgrade-dev", "Role": "*"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/RDS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsStateDev"}, "dimensions": {"DBClusterIdentifier": "firmware-upgrade-dev", "Role": "*"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/RDS", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "DB: CPU", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateDev"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsStateDev"}, "dimensions": {"QueueName": "pod-unit-updates-dev"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfMessagesReceived", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsStateDev"}, "dimensions": {"QueueName": "pod-unit-updates-dev"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "NumberOfMessagesSent", "metricQueryType": 0, "namespace": "AWS/SQS", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "SQS: Message Rate", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateDev"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 100, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsStateDev"}, "dimensions": {"ClusterName": "firmware-upgrade", "ServiceName": "firmware-upgrade-pod-unit-updates-consumer"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwCsStateDev"}, "dimensions": {"ClusterName": "firmware-upgrade", "ServiceName": "firmware-upgrade-pod-unit-updates-consumer"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "ECS: CPU", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 100, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 18}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"DBClusterIdentifier": "podadmin-staging", "Role": "*"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/RDS", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "PodAdmin: CPU", "type": "timeseries"}], "refresh": "10s", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "pod-unit-updates-consumer Load Test Dashboard", "uid": "XMDZeR24k", "version": 4, "weekStart": ""}