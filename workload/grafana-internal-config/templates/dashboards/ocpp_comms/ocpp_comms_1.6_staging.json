{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 190, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaCsConnStag"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "metadata"}, "properties": [{"id": "custom.width", "value": 726}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "rawevent"}, "properties": [{"id": "custom.width", "value": 497}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "event"}, "properties": [{"id": "custom.width", "value": 548}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "serialnumber"}, "properties": [{"id": "custom.width", "value": 72}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "macaddress"}, "properties": [{"id": "custom.width", "value": 64}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "evseid"}, "properties": [{"id": "custom.width", "value": 65}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "origin"}, "properties": [{"id": "custom.width", "value": 79}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "action"}, "properties": [{"id": "custom.width", "value": 139}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "payload"}, "properties": [{"id": "custom.width", "value": 277}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "messageId"}, "properties": [{"id": "custom.width", "value": 194}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "ppid"}, "properties": [{"id": "custom.width", "value": 101}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "protocol"}, "properties": [{"id": "custom.width", "value": 106}]}]}, "gridPos": {"h": 27, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaCsConnStag"}, "format": 1, "rawSQL": "select date_parse(substr(receivedat,1,19), '%Y-%m-%dT%T') as timestamp,metadata.ppid, metadata.serialnumber, metadata.macaddress,metadata.evseid, origin, metadata.action, event.messageId, event.payload, protocol, receivedat\nfrom \"ocpp_logs\".\"ocpp_logs\" \nWHERE  date_parse(substr(receivedat,1,19), '%Y-%m-%dT%T') > $__timeFrom() \nand date_parse(substr(receivedat,1,19), '%Y-%m-%dT%T') <= $__timeTo()\nORDER BY timestamp ASC", "refId": "A"}], "title": "OCPP Comms", "type": "table"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "OCPP 1.6 Comms (Staging)", "uid": "8Ck3DSiIz", "version": 15, "weekStart": ""}