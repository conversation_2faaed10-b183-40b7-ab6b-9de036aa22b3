{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 2, "id": 201, "links": [{"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": ["EnergyMetrics"], "targetBlank": false, "title": "Dashboards", "tooltip": "", "type": "dashboards", "url": ""}], "liveNow": false, "panels": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"fillOpacity": 70, "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineWidth": 0, "spanNulls": false}, "mappings": [{"options": {"A": {"color": "#808080", "index": 0, "text": "A"}, "B": {"color": "blue", "index": 1, "text": "B"}, "C": {"color": "green", "index": 2, "text": "C"}, "F": {"color": "red", "index": 3, "text": "F"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "vehicle_chargeState_powerDeliveryState"}, "properties": [{"id": "mappings", "value": [{"options": {"PLUGGED_IN:CHARGING": {"color": "green", "index": 3, "text": "Plugged in, charging"}, "PLUGGED_IN:COMPLETE": {"color": "light-blue", "index": 2, "text": "Plugged in, complete"}, "PLUGGED_IN:INITIALIZING": {"color": "super-light-green", "index": 5, "text": "Plugged in, initialising"}, "PLUGGED_IN:NO_POWER": {"color": "blue", "index": 1, "text": "Plugged in, no power"}, "PLUGGED_IN:STOPPED": {"color": "blue", "index": 4, "text": "Plugged in, stopped"}, "UNPLUGGED": {"color": "#808080", "index": 0, "text": "Unplugged"}}, "type": "value"}]}, {"id": "displayName", "value": "car state"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "pilotpwn"}, "properties": [{"id": "mappings", "value": [{"options": {"1": {"color": "blue", "index": 0}, "2": {"color": "green", "index": 1}}, "type": "value"}]}]}]}, "gridPos": {"h": 4, "w": 13, "x": 0, "y": 0}, "id": 6, "options": {"alignValue": "left", "legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "mergeValues": true, "rowHeight": 0.9, "showValue": "never", "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "format": 0, "rawQuery": "SELECT  time,\n        pilot AS pilot,\n        pilotpwmstate AS pilotpwn\nFROM \"energy_metrics\".\"energy_metrics\"\nWHERE $__timeFilter\n  AND  metadata_ppid = '$ppid'\n  AND metadata_evseId = '$door'\n  AND (pilot = 'A' OR pilotpwmstate IS NOT NULL)\nORDER BY time\n", "refId": "A", "waitForResult": true}, {"database": "\"smart_charging\"", "datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "format": 0, "hide": false, "measure": "metrics", "rawQuery": "select time,\r\nvehicle_chargeState_powerDeliveryState\r\nfrom smart_charging.vehicle_state_of_charge\r\nwhere $__timeFilter\r\nand user_ppid = '$ppid' and vehicle_id = '$vehicle'\r\n", "refId": "B", "table": "\"vehicle_state_of_charge\"", "waitForResult": true}], "title": "Charge state", "type": "state-timeline"}, {"datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 11, "w": 4, "x": 13, "y": 0}, "id": 11, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "| User     |      ppid     |\n|----------|:--------------|\n| Pilgrim2 |  PSL-58657    |\n| Pilgrim5 |  PSL-607017   |\n| Doug3    |  PSL-532880   |\n| Doug5    |  PSL-607011   |\n| Ash      |  PSL-607137   |\n| <PERSON>   |  PSL-616479   |\n| Mel      |  PSL-620437   |\n| Rob      |  PSL-621130   |\n| <PERSON>    |  PSL-617580   |\n| Mark     |  PSL-622912   |", "mode": "markdown"}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Handy ppids", "type": "text"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "kwatth"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Max Power"}, "properties": [{"id": "unit", "value": "watt"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Uptime"}, "properties": [{"id": "unit", "value": "percent"}]}]}, "gridPos": {"h": 3, "w": 7, "x": 17, "y": 0}, "id": 15, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "text": {"titleSize": 24, "valueSize": 32}, "textMode": "value_and_name", "wideLayout": true}, "pluginVersion": "10.4.1", "targets": [{"database": "\"energy_metrics\"", "datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "measure": "metrics", "rawQuery": "SELECT  COALESCE(SUM(energy), 0) AS Energy\nFROM energy_metrics.energy_metrics_by_30_minutes\nWHERE $__timeFilter AND  ppid = '$ppid' and evseId = '$door'\n", "refId": "A", "table": "\"energy_metrics_by_30_minutes\"", "waitForResult": true}, {"database": "\"energy_metrics\"", "datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "hide": false, "measure": "metrics", "rawQuery": "SELECT   MAX(( if(current1 IS NOT NULL, current1 * COALESCE(v1rms, 230), 0.0) +\r\n              if(current2 IS NOT NULL, current2 * COALESCE(v2rms, 230), 0.0) +\r\n              if(current3 IS NOT NULL, current3 * COALESCE(v3rms, 230), 0.0) ) / (100.0)) AS \"Max Power\"\r\nFROM energy_metrics.energy_metrics\r\nWHERE $__timeFilter AND metadata_ppid ='$ppid'\r\n", "refId": "B", "table": "\"energy_metrics_by_30_minutes\"", "waitForResult": true}], "type": "stat"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Arch"}, "properties": [{"id": "custom.width", "value": 62}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "GSPGroup"}, "properties": [{"id": "custom.width", "value": 104}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "GSP"}, "properties": [{"id": "custom.width", "value": 85}]}]}, "gridPos": {"h": 4, "w": 7, "x": 17, "y": 3}, "id": 17, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "format": 0, "rawQuery": "SELECT  ARBITRARY(metadata_pcbType) AS Arch,\r\n        ARBITRARY(grid_gspGroup) AS GSPGroup,\r\n        ARBITRARY(grid_gsp) AS GSP,\r\n        ARBITRARY(grid_dno) AS DNO\r\nFROM energy_metrics.energy_metrics\r\nWHERE $__timeFilter AND metadata_ppid ='$ppid' and metadata_evseId = '$door'\r\n", "refId": "A"}], "type": "table"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "palette-classic", "seriesBy": "last"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "kwatth"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "battery_energy"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed", "seriesBy": "last"}}, {"id": "custom.fillOpacity", "value": 20}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "battery_percent"}, "properties": [{"id": "custom.lineWidth", "value": 2}, {"id": "custom.axisPlacement", "value": "hidden"}, {"id": "unit", "value": "percent"}, {"id": "custom.axisSoftMax", "value": 100}, {"id": "custom.drawStyle", "value": "line"}, {"id": "custom.showPoints", "value": "never"}]}]}, "gridPos": {"h": 5, "w": 13, "x": 0, "y": 4}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "repeat": "vehicle", "repeatDirection": "v", "targets": [{"database": "\"smart_charging\"", "datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "measure": "metrics", "rawQuery": "select  BIN(time, $__interval) AS time,\n  max(vehicle_chargeState_batteryCapacity) as battery_capacity,\n  max(vehicle_chargeState_batteryCapacity * (vehicle_chargeState_batteryLevel / 100)) as battery_energy,\n  max(vehicle_chargeState_batteryCapacity * (vehicle_chargeState_chargeLimit / 100)) as battery_charge_limit,\n  max(vehicle_chargeState_batteryLevel) as battery_percent\nfrom smart_charging.vehicle_state_of_charge\nwhere $__timeFilter and\n      user_ppid = '$ppid' and \n      vehicle_id = '$vehicle'\ngroup by BIN(time, $__interval)\norder by time\n", "refId": "A", "table": "\"vehicle_state_of_charge\""}], "title": "Battery Status ($vehicle)", "transformations": [{"id": "prepareTimeSeries", "options": {"format": "many"}}], "type": "timeseries"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Arch"}, "properties": [{"id": "custom.width", "value": 62}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "GSPGroup"}, "properties": [{"id": "custom.width", "value": 104}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "GSP"}, "properties": [{"id": "custom.width", "value": 85}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "model"}, "properties": [{"id": "custom.width", "value": 110}]}]}, "gridPos": {"h": 4, "w": 7, "x": 17, "y": 7}, "id": 21, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.4.1", "targets": [{"database": "\"smart_charging\"", "datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "format": 0, "measure": "metrics", "rawQuery": "select \r\n  vehicle_information_brand as brand,\r\n  vehicle_information_model as model,\r\n  max(vehicle_chargeState_batteryCapacity) as battery_capacity,\r\n  max(vehicle_chargeState_batteryCapacity * (vehicle_chargeState_chargeLimit / 100)) as battery_charge_limit\r\nfrom smart_charging.vehicle_state_of_charge\r\nwhere $__timeFilter\r\nand user_ppid = '$ppid'\r\ngroup by vehicle_information_brand, vehicle_information_model\r\n", "refId": "A", "table": "\"vehicle_state_of_charge\""}], "type": "table"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "scheme", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": 420000, "lineInterpolation": "stepBefore", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}]}, "unit": "amp"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, "properties": [{"id": "custom.axisPlacement", "value": "hidden"}, {"id": "unit", "value": "watt"}]}]}, "gridPos": {"h": 4, "w": 13, "x": 0, "y": 9}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "format": 0, "rawQuery": "  SELECT    BIN(time, $__interval) as ts,\r\n            AVG(( if(current1 IS NOT NULL, current1, 0.0) +\r\n              if(current2 IS NOT NULL, current2, 0.0) +\r\n              if(current3 IS NOT NULL, current3, 0.0) ) / 100.0) AS Current,\r\n            AVG(( if(current1 IS NOT NULL, current1 * COALESCE(v1rms, 230), 0.0) +\r\n              if(current2 IS NOT NULL, current2 * COALESCE(v2rms, 230), 0.0) +\r\n              if(current3 IS NOT NULL, current3 * COALESCE(v3rms, 230), 0.0) ) / (100.0)) AS RoughPower\r\n  FROM energy_metrics.energy_metrics\r\n  WHERE $__timeFilter AND metadata_ppid ='$ppid' AND metadata_evseId = '$door' AND current1 IS NOT NULL\r\n  GROUP BY BIN(time, $__interval)\r\n  ORDER BY ts\r\n", "refId": "A"}], "title": "Current / Power", "type": "timeseries"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "event"}, "properties": [{"id": "custom.width", "value": 166}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "time"}, "properties": [{"id": "custom.width", "value": 178}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "pilot"}, "properties": [{"id": "custom.width", "value": 58}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "rssi"}, "properties": [{"id": "custom.width", "value": 65}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "pilotpwmstate"}, "properties": [{"id": "custom.width", "value": 127}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "fault"}, "properties": [{"id": "custom.width", "value": 85}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "pilotpwm"}, "properties": [{"id": "custom.width", "value": 88}]}]}, "gridPos": {"h": 21, "w": 11, "x": 13, "y": 11}, "id": 13, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "time"}]}, "pluginVersion": "10.4.1", "targets": [{"database": "\"energy_metrics\"", "datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "format": 0, "measure": "metrics", "rawQuery": "  SELECT time,\r\n          event,\r\n          pilot,\r\n          fault,\r\n          uui,\r\n          pause,\r\n          current1\r\n  FROM \r\n       energy_metrics.energy_metrics\r\n  WHERE\r\n       $__timeFilter AND\r\n       metadata_ppid ='$ppid' AND\r\n       metadata_evseId = '$door'\r\n  ORDER BY\r\n       time DESC\r\n", "refId": "A", "table": "\"energy_metrics\""}], "title": "Telemetry Browser", "type": "table"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "scheme", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 900000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}]}, "unit": "volt"}, "overrides": []}, "gridPos": {"h": 4, "w": 13, "x": 0, "y": 13}, "id": 19, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "format": 0, "rawQuery": "  SELECT    BIN(time, $__interval) as ts,\r\n            MAX(if(v1rms>100,v1rms, NULL) ) AS v1rms\r\n  FROM \r\n            energy_metrics.energy_metrics\r\n  WHERE\r\n            $__timeFilter AND \r\n            metadata_ppid ='$ppid' AND\r\n            metadata_evseId = '$door' AND\r\n            current1 IS NOT NULL\r\n  GROUP BY\r\n            BIN(time, $__interval)\r\n  ORDER BY\r\n          ts\r\n", "refId": "A"}], "title": "Voltages", "type": "timeseries"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 1, "drawStyle": "bars", "fillOpacity": 41, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "kwatth"}, "overrides": []}, "gridPos": {"h": 4, "w": 13, "x": 0, "y": 17}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"database": "\"energy_metrics\"", "datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "format": 0, "measure": "metrics", "rawQuery": "SELECT  time,\n        MAX(energy) AS energy\nFROM energy_metrics.energy_metrics_by_30_minutes\nWHERE $__timeFilter AND  ppid = '$ppid' AND evseId = '$door'\nGROUP BY time\nORDER BY time", "refId": "A", "table": "\"energy_metrics\""}], "title": "Half Hour Energy Usage", "type": "timeseries"}, {"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "scheme", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": -100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "red", "value": -90}, {"color": "#EAB839", "value": -80}, {"color": "green", "value": -70}]}, "unit": "dBm"}, "overrides": []}, "gridPos": {"h": 5, "w": 13, "x": 0, "y": 21}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "format": 0, "rawQuery": "SELECT BIN(time, $__interval) as ts,\r\n         MIN(rssi) as rssi\r\nFROM energy_metrics.energy_metrics\r\nWHERE $__timeFilter AND metadata_ppid ='$ppid' AND metadata_evseId = '$door' AND rssi > -99\r\nGROUP BY BIN(time, $__interval)\r\nORDER BY ts\r\n", "refId": "A"}], "title": "<PERSON><PERSON><PERSON> Checker", "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 39, "tags": ["EnergyMetrics"], "templating": {"list": [{"current": {"selected": false, "text": "PSL-58657", "value": "PSL-58657"}, "hide": 0, "name": "ppid", "options": [{"selected": true, "text": "PSL-607137", "value": "PSL-607137"}], "query": "PSL-607011", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": "A", "value": "1"}, "datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "definition": "SELECT \nCONCAT('id=\"', metadata_evseId, '\",name=\"', metadata_door, '\"')\nFROM energy_metrics.energy_metrics\nWHERE\n  $__timeFilter\n  AND metadata_ppid = '$ppid'", "hide": 0, "includeAll": false, "multi": false, "name": "door", "options": [], "query": "SELECT \nCONCAT('id=\"', metadata_evseId, '\",name=\"', metadata_door, '\"')\nFROM energy_metrics.energy_metrics\nWHERE\n  $__timeFilter\n  AND metadata_ppid = '$ppid'", "refresh": 2, "regex": "/id=\"(?<value>[^\"]+)|name=\"(?<text>[^\"]+)/g", "skipUrlSync": false, "sort": 7, "type": "query"}, {"current": {"selected": false, "text": "Tesla Model 3", "value": "0e8aaa37-e6ba-4eb4-95c4-495260fb2f48"}, "datasource": {"type": "grafana-timestream-datasource", "uid": "TimeStreamCsStateProd"}, "definition": "SELECT\n  CONCAT('id=\"', vehicle_id, '\",name=\"', vehicle_information_brand, ' ', vehicle_information_model, '\"')\nFROM\n  smart_charging.vehicle_state_of_charge\nWHERE\n  $__timeFilter\n  AND user_ppid = '$ppid'\nGROUP BY\n  CONCAT('id=\"', vehicle_id, '\",name=\"', vehicle_information_brand, ' ', vehicle_information_model, '\"')", "hide": 0, "includeAll": false, "multi": false, "name": "vehicle", "options": [], "query": "SELECT\n  CONCAT('id=\"', vehicle_id, '\",name=\"', vehicle_information_brand, ' ', vehicle_information_model, '\"')\nFROM\n  smart_charging.vehicle_state_of_charge\nWHERE\n  $__timeFilter\n  AND user_ppid = '$ppid'\nGROUP BY\n  CONCAT('id=\"', vehicle_id, '\",name=\"', vehicle_information_brand, ' ', vehicle_information_model, '\"')", "refresh": 2, "regex": "/id=\"(?<value>[^\"]+)|name=\"(?<text>[^\"]+)/g", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Energy Metrics - Unit", "uid": "O2KGggXVz", "version": 78, "weekStart": ""}