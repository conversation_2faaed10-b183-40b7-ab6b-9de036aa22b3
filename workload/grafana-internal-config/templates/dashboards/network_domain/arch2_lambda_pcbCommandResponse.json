{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 123, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-pcbCommandResponse"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Invocations", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Invocations", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "Average", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-pcbCommandResponse"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"alias": "Maximum", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-pcbCommandResponse"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Maximum"}, {"alias": "Minimum", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-pcbCommandResponse"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "C", "region": "default", "sqlExpression": "", "statistic": "Minimum"}], "title": "Duration", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-pcbCommandResponse"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ConcurrentExecutions", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "Concurrent Executions", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "errors", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-pcbCommandResponse"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Errors", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "invocations", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-pcbCommandResponse"}, "expression": "", "hide": true, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Invocations", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"datasource": {"type": "__expr__", "uid": "__expr__"}, "expression": "100 - 100 * ${A} / ${B}", "hide": false, "refId": "C", "type": "math"}], "title": "Availability", "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "arch2-lambda pcbCommandResponse", "uid": "uOPPR_fVk", "version": 10, "weekStart": ""}