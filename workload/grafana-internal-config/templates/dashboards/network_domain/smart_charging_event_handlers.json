{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 313, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "repeat": "functionName", "repeatDirection": "h", "targets": [{"alias": "{{FunctionName}}", "datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {"FunctionName": "$functionName"}, "expression": "", "hide": false, "id": "", "label": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Invocations", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Lambda Invocations: $functionName", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": 3600000, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 13, "maxPerRow": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "repeat": "functionName", "repeatDirection": "h", "targets": [{"alias": "{{FunctionName}}", "datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {"FunctionName": "$functionName"}, "expression": "", "hide": false, "id": "", "label": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ConcurrentExecutions", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "Concurrent Executions: $functionName", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 11, "maxPerRow": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "repeat": "functionName", "repeatDirection": "h", "targets": [{"alias": "{{FunctionName}}", "datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {"FunctionName": "$functionName"}, "expression": "", "id": "", "label": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Errors", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "thresholds": [{"colorMode": "critical", "op": "gt", "value": 0, "visible": true}], "title": "Lambda Errors: $functionName", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 12, "maxPerRow": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "repeat": "functionName", "repeatDirection": "h", "targets": [{"alias": "{{FunctionName}}", "datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {"FunctionName": "$functionName"}, "expression": "", "id": "", "label": "Avg", "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {"FunctionName": "$functionName"}, "expression": "", "hide": false, "id": "", "label": "Max", "logGroups": [], "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "Lambda Duration: $functionName", "type": "timeseries"}], "refresh": "30s", "revision": 1, "schemaVersion": 39, "tags": [], "templating": {"list": [{"allValue": "", "current": {"selected": true, "text": ["flex-provider-notifier-prod-plugged-in", "flex-provider-notifier-prod-unplugged"], "value": ["flex-provider-notifier-prod-plugged-in", "flex-provider-notifier-prod-unplugged"]}, "hide": 0, "includeAll": true, "label": "Function Name", "multi": true, "name": "functionName", "options": [{"selected": false, "text": "All", "value": "$__all"}, {"selected": false, "text": "flex-provider-notifier-prod-charge-now-created", "value": "flex-provider-notifier-prod-charge-now-created"}, {"selected": false, "text": "flex-provider-notifier-prod-charge-now-deleted", "value": "flex-provider-notifier-prod-charge-now-deleted"}, {"selected": false, "text": "flex-provider-notifier-prod-charge-now-ended", "value": "flex-provider-notifier-prod-charge-now-ended"}, {"selected": false, "text": "flex-provider-notifier-prod-charge-schedule-expired", "value": "flex-provider-notifier-prod-charge-schedule-expired"}, {"selected": false, "text": "flex-provider-notifier-prod-charge-schedules-updated", "value": "flex-provider-notifier-prod-charge-schedules-updated"}, {"selected": true, "text": "flex-provider-notifier-prod-plugged-in", "value": "flex-provider-notifier-prod-plugged-in"}, {"selected": false, "text": "flex-provider-notifier-prod-schedule-flex-request-execution", "value": "flex-provider-notifier-prod-schedule-flex-request-execution"}, {"selected": false, "text": "flex-provider-notifier-prod-stream-flex-status", "value": "flex-provider-notifier-prod-stream-flex-status"}, {"selected": true, "text": "flex-provider-notifier-prod-unplugged", "value": "flex-provider-notifier-prod-unplugged"}, {"selected": false, "text": "flex-provider-notifier-prod-update-flex-request-status", "value": "flex-provider-notifier-prod-update-flex-request-status"}, {"selected": false, "text": "flex-provider-notifier-prod-user-intent-changed", "value": "flex-provider-notifier-prod-user-intent-changed"}, {"selected": false, "text": "flex-provider-notifier-prod-user-vehicle-deleted", "value": "flex-provider-notifier-prod-user-vehicle-deleted"}, {"selected": false, "text": "flex-provider-notifier-prod-user-vehicle-updated", "value": "flex-provider-notifier-prod-user-vehicle-updated"}, {"selected": false, "text": "flex-provider-notifier-prod-vehicle-plugged-in-set-schedules", "value": "flex-provider-notifier-prod-vehicle-plugged-in-set-schedules"}], "query": "flex-provider-notifier-prod-charge-now-created,\nflex-provider-notifier-prod-charge-now-deleted,\nflex-provider-notifier-prod-charge-now-ended,\nflex-provider-notifier-prod-charge-schedule-expired,\nflex-provider-notifier-prod-charge-schedules-updated,\nflex-provider-notifier-prod-plugged-in,\nflex-provider-notifier-prod-schedule-flex-request-execution,\nflex-provider-notifier-prod-stream-flex-status,\nflex-provider-notifier-prod-unplugged,\nflex-provider-notifier-prod-update-flex-request-status,\nflex-provider-notifier-prod-user-intent-changed,\nflex-provider-notifier-prod-user-vehicle-deleted,\nflex-provider-notifier-prod-user-vehicle-updated,\nflex-provider-notifier-prod-vehicle-plugged-in-set-schedules", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Smart Charging Event Handlers", "uid": "edw3ijjuaiigwa", "version": 18, "weekStart": ""}