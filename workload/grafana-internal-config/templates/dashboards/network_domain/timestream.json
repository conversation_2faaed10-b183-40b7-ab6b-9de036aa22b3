{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 276, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "unit", "value": "currencyUSD"}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {}, "expression": "select SUM(CumulativeBytesMetered)\nfrom \"AWS/Timstream\"\ngroup by DatabaseName", "id": "", "label": "MemoryCumulativeBytesMetered", "logGroups": [], "matchExact": true, "metricEditorMode": 1, "metricName": "DataScannedBytes", "metricQueryType": 1, "namespace": "AWS/Timestream", "period": "1h", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "SELECT SUM(MemoryCumulativeBytesMetered)\nFROM SCHEMA(\"AWS/Timestream\", DatabaseName, Operation)\nwhere DatabaseName = 'energy_metrics' and Operation = 'Storage'", "statistic": "Sum"}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "($A / 1024 / 1024 / 1024) * 0.0407", "hide": false, "refId": "B", "type": "math"}], "title": "MemoryStore - energy_metrics", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "unit", "value": "currencyUSD"}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {}, "expression": "select SUM(CumulativeBytesMetered)\nfrom \"AWS/Timstream\"\ngroup by DatabaseName", "id": "", "label": "MagneticCumulativeBytesMetered", "logGroups": [], "matchExact": true, "metricEditorMode": 1, "metricName": "DataScannedBytes", "metricQueryType": 1, "namespace": "AWS/Timestream", "period": "1h", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "SELECT SUM(MagneticCumulativeBytesMetered)\nFROM SCHEMA(\"AWS/Timestream\", DatabaseName, Operation)\nwhere DatabaseName = 'energy_metrics' and Operation = 'Storage'", "statistic": "Sum"}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "($A / 1024 / 1024 / 1024 / 24 / 30) * 0.0339", "hide": false, "refId": "B", "type": "math"}], "title": "MagneticStore - energy_metrics", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "unit", "value": "currencyUSD"}]}]}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {}, "expression": "select SUM(CumulativeBytesMetered)\nfrom \"AWS/Timstream\"\ngroup by DatabaseName", "id": "", "label": "CumulativeBytesMetered", "logGroups": [], "matchExact": true, "metricEditorMode": 1, "metricName": "DataScannedBytes", "metricQueryType": 1, "namespace": "AWS/Timestream", "period": "1h", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "SELECT SUM(CumulativeBytesMetered)\nFROM SCHEMA(\"AWS/Timestream\", DatabaseName, Operation)\nwhere DatabaseName = 'energy_metrics' and Operation != 'WriteRecords'\ngroup by Operation", "statistic": "Sum"}], "title": "CumulativeBytesMetered (Reads) - energy_metrics", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byFrameRefID", "options": "B"}, "properties": [{"id": "unit", "value": "currencyUSD"}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsStateProd"}, "dimensions": {}, "expression": "select SUM(CumulativeBytesMetered)\nfrom \"AWS/Timstream\"\ngroup by DatabaseName", "id": "", "label": "CumulativeBytesMetered", "logGroups": [], "matchExact": true, "metricEditorMode": 1, "metricName": "DataScannedBytes", "metricQueryType": 1, "namespace": "AWS/Timestream", "period": "1h", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "SELECT SUM(CumulativeBytesMetered)\nFROM SCHEMA(\"AWS/Timestream\", DatabaseName, Operation)\nwhere DatabaseName = 'energy_metrics' and Operation = 'WriteRecords'", "statistic": "Sum"}, {"datasource": {"name": "Expression", "type": "__expr__", "uid": "__expr__"}, "expression": "($A / 1024 / 1000000) * 0.5654", "hide": false, "refId": "B", "type": "math"}], "title": "CumulativeBytesMetered (Writes) - energy_metrics", "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-4h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Timestream", "uid": "cTYceusSk", "version": 7, "weekStart": ""}