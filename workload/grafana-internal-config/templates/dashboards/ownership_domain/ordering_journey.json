{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 194, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "method"}, "properties": [{"id": "custom.width", "value": 110}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.width", "value": 228}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "path"}, "properties": [{"id": "custom.width", "value": 882}]}]}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Time"}]}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "expression": "fields @timestamp, @message, @logStream, @log\n| filter @message like /\"originatingCustomerId\":${customerId}/\n| parse '\"method\":\"*\"'as method\n| parse '\"path\":\"*\"' as path\n| parse '\"payload\":*,\"path\"' as payload\n| display @timestamp, method, path, payload\n| sort @timestamp desc", "id": "", "logGroupNames": ["/aws/lambda/ecommerce-service-prod-requestForwarder"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Recent Storefront API Activity", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwOwnerDataPlatProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 13}, "id": 6, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwOwnerDataPlatProd"}, "expression": "fields @timestamp, `detail.data.scope` as event, `detail.data.data.type` as resource, `detail.data.data.id` as identifier, @message\n| filter @message like /${customerId}/\n| sort @timestamp desc\n| limit 100", "id": "", "logGroupNames": ["/aws/events/bigcommerce"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "eu-west-1", "statsGroups": []}], "title": "BigCommerce Webhooks", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 13}, "id": 4, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Time"}]}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "expression": "fields @timestamp, `detail-type`, `detail.event`, `detail.data.customer.email`, @message\n| filter ispresent(`detail-type`)\n| filter `detail.data.customer.id` like /${customerId}/\n| sort @timestamp desc", "id": "", "logGroupNames": ["/aws/lambda/mulesoft-event-ingress-prod-logMulesoftEvents"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Mulesoft Events - Inbound", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwOwnerDataPlatProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.width", "value": 145}]}]}, "gridPos": {"h": 8, "w": 5, "x": 0, "y": 22}, "id": 8, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwOwnerDataPlatProd"}, "expression": "fields @timestamp\n| filter @message like 'SyncMissing'\n| parse '\"customer\",\"id\":*}' as customerId", "id": "", "logGroupNames": ["/ecs/customer-service-event-processor"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Missing links", "type": "table"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "20796", "value": "20796"}, "description": "The customer ID within the BigCommerce platform to limit logs to. Leave empty to show all.", "hide": 0, "label": "BigCommerce Customer ID", "name": "customerId", "options": [{"selected": true, "text": "20796", "value": "20796"}], "query": "20796", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Ordering Journey", "uid": "MrYDv0WSk", "version": 16, "weekStart": ""}