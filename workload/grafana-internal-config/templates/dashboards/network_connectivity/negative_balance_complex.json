{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 219, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 16, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "General", "type": "row"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 7, "x": 0, "y": 1}, "id": 3, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["allValues"], "fields": "/^Count$/", "values": true}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [{"field": "context.is_Ocpp", "id": "2", "settings": {"min_doc_count": "1", "order": "desc", "orderBy": "_term", "size": "10"}, "type": "terms"}], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "type": "count"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: true", "refId": "A", "timeField": "timestamp"}], "title": "Total of Rapid Charges that got user balance negative", "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue", "value": null}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 7, "x": 7, "y": 1}, "id": 22, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["allValues"], "fields": "/^Count$/", "values": true}, "textMode": "value"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [{"field": "context.is_Ocpp", "id": "2", "settings": {"min_doc_count": "1", "order": "desc", "orderBy": "_term", "size": "10"}, "type": "terms"}], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "type": "count"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: false", "refId": "A", "timeField": "timestamp"}], "title": "Total of non Rapid Charges that got user balance negative", "type": "stat"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 14, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Rapids", "type": "row"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 0, "y": 7}, "id": 5, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["allValues"], "fields": "/^Count$/", "values": true}, "textMode": "value"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [{"field": "context.is_Ocpp", "id": "2", "settings": {"min_doc_count": "1", "order": "desc", "orderBy": "_term", "size": "10"}, "type": "terms"}], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "type": "count"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: true AND context.settlement_currency: GBP", "refId": "A", "timeField": "timestamp"}], "title": "Total of Rapid Charges that got user balance negative(GBP)", "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "currencyGBP"}, "overrides": [{"matcher": {"id": "byType", "options": "number"}, "properties": [{"id": "decimals", "value": 2}]}]}, "gridPos": {"h": 7, "w": 8, "x": 4, "y": 7}, "id": 4, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "5000"}, "type": "raw_data"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: true  AND context.settlement_currency: GBP", "refId": "A", "timeField": "timestamp"}], "title": "Stats on Negative balance for charges in Rapids (GBP)", "transformations": [{"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.account_balance_after_billing": false, "context.account_balance_before_billing": true, "context.billing_account_id": true, "context.billing_event_id": true, "context.charge_ends_at": true, "context.charge_energy_used": true, "context.charge_id": true, "context.charge_start_at": true, "context.is_Ocpp": true, "context.is_stale": true, "context.location_id": true, "context.settlement_amount": true, "context.settlement_currency": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": true}, "indexByName": {}, "renameByName": {}}}, {"id": "calculateField", "options": {"binary": {"left": "context.account_balance_after_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "reduce", "options": {"includeTimeField": false, "labelsToFields": false, "mode": "reduceFields", "reducers": ["max", "min", "mean", "sum"]}}, {"id": "organize", "options": {"excludeByName": {"context.account_balance_after_billing / 100 Total": true, "context.account_balance_after_billing Max": true, "context.account_balance_after_billing Mean": true, "context.account_balance_after_billing Min": true, "context.account_balance_after_billing Total": true}, "indexByName": {"context.account_balance_after_billing / 100 Max": 4, "context.account_balance_after_billing / 100 Mean": 5, "context.account_balance_after_billing / 100 Min": 6, "context.account_balance_after_billing / 100 Total": 7, "context.account_balance_after_billing Max": 1, "context.account_balance_after_billing Mean": 2, "context.account_balance_after_billing Min": 0, "context.account_balance_after_billing Total": 3}, "renameByName": {"context.account_balance_after_billing / 100 Max": "Lowest", "context.account_balance_after_billing / 100 Mean": "Average", "context.account_balance_after_billing / 100 Min": "Highest", "context.account_balance_after_billing / 100 Total": "Sum"}}}], "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Billing Account Id"}, "properties": [{"id": "custom.width", "value": 126}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge Cost"}, "properties": [{"id": "custom.width", "value": 96}, {"id": "unit", "value": "currencyGBP"}, {"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance Before Billing"}, "properties": [{"id": "custom.width", "value": 156}, {"id": "unit", "value": "currencyGBP"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance After Billing"}, "properties": [{"id": "unit", "value": "currencyGBP"}, {"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Location Id"}, "properties": [{"id": "links", "value": [{"title": "", "url": "https://admin.pod-point.com/locations/${__value.raw}\n"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Billing Account Id"}, "properties": [{"id": "links", "value": [{"title": "", "url": "https://admin.pod-point.com/billing-accounts/${__value.raw}\n"}]}]}]}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 7}, "id": 7, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Charge Cost"}]}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "5000"}, "type": "raw_data"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: true AND context.settlement_currency: GBP ", "refId": "A", "timeField": "timestamp"}], "title": "Charges Info (GBP)", "transformations": [{"id": "calculateField", "options": {"binary": {"left": "context.account_balance_after_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "context.account_balance_before_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "context.settlement_amount", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.account_balance_after_billing": true, "context.account_balance_before_billing": true, "context.billing_event_id": true, "context.charge_ends_at": true, "context.charge_start_at": true, "context.is_Ocpp": true, "context.is_stale": true, "context.settlement_amount": true, "context.settlement_currency": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": false}, "indexByName": {"_id": 1, "_index": 2, "_type": 3, "channel": 4, "context.account_balance_after_billing": 27, "context.account_balance_after_billing / 100": 9, "context.account_balance_before_billing": 26, "context.account_balance_before_billing / 100": 8, "context.billing_account_id": 12, "context.billing_event_id": 14, "context.charge_ends_at": 28, "context.charge_energy_used": 7, "context.charge_id": 6, "context.charge_start_at": 19, "context.is_Ocpp": 16, "context.is_stale": 15, "context.location_id": 5, "context.settlement_amount": 25, "context.settlement_amount / 100": 11, "context.settlement_currency": 10, "env": 20, "extra": 13, "highlight": 17, "host": 18, "level": 21, "message": 22, "project": 23, "sort": 24, "timestamp": 0}, "renameByName": {"context.account_balance_after_billing": "", "context.account_balance_after_billing / 100": "Balance After Billing", "context.account_balance_before_billing": "", "context.account_balance_before_billing / 100": "Balance Before Billing", "context.billing_account_id": "Billing Account Id", "context.billing_event_id": "", "context.charge_ends_at": "Unplug Time(UTC)", "context.charge_energy_used": "Energy Used(kWh)", "context.charge_id": "Charge Id", "context.charge_start_at": "Plug Time(UTC)", "context.location_id": "Location Id", "context.settlement_amount": "", "context.settlement_amount / 100": "Charge Cost", "context.settlement_currency": ""}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "Balance After Billing"}]}}], "type": "table"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 0, "y": 14}, "id": 24, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["allValues"], "fields": "/^Count$/", "values": true}, "textMode": "value"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [{"field": "context.is_Ocpp", "id": "2", "settings": {"min_doc_count": "1", "order": "desc", "orderBy": "_term", "size": "10"}, "type": "terms"}], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "type": "count"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: true AND context.settlement_currency: EUR", "refId": "A", "timeField": "timestamp"}], "title": "Total of Rapid Charges that got user balance negative(EUR)", "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "-", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "currencyGBP"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 4, "y": 14}, "id": 19, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: true  AND context.settlement_currency: EUR", "refId": "A", "timeField": "timestamp"}], "title": "Stats on Negative balance for charges in Rapids (EUR)", "transformations": [{"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.account_balance_after_billing": false, "context.account_balance_before_billing": true, "context.billing_account_id": true, "context.billing_event_id": true, "context.charge_ends_at": true, "context.charge_energy_used": true, "context.charge_id": true, "context.charge_start_at": true, "context.is_Ocpp": true, "context.is_stale": true, "context.location_id": true, "context.settlement_amount": true, "context.settlement_currency": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": true}, "indexByName": {}, "renameByName": {}}}, {"id": "calculateField", "options": {"binary": {"left": "context.account_balance_after_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "reduce", "options": {"includeTimeField": false, "labelsToFields": false, "mode": "reduceFields", "reducers": ["max", "min", "mean", "sum"]}}, {"id": "organize", "options": {"excludeByName": {"context.account_balance_after_billing Max": true, "context.account_balance_after_billing Mean": true, "context.account_balance_after_billing Min": true, "context.account_balance_after_billing Total": true}, "indexByName": {"Average": 4, "Highest": 5, "Lowest": 3, "context.account_balance_after_billing Max": 1, "context.account_balance_after_billing Mean": 2, "context.account_balance_after_billing Min": 0}, "renameByName": {"context.account_balance_after_billing / 100 Max": "Lowest", "context.account_balance_after_billing / 100 Mean": "Average", "context.account_balance_after_billing / 100 Min": "Highest", "context.account_balance_after_billing / 100 Total": "Sum"}}}], "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance After Billing"}, "properties": [{"id": "custom.width", "value": 149}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Billing Account Id"}, "properties": [{"id": "custom.width", "value": 126}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge Cost"}, "properties": [{"id": "custom.width", "value": 96}, {"id": "unit", "value": "currencyEUR"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge Currency"}, "properties": [{"id": "custom.width", "value": 125}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance Before Billing"}, "properties": [{"id": "custom.width", "value": 156}, {"id": "unit", "value": "currencyGBP"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance After Billing"}, "properties": [{"id": "unit", "value": "currencyGBP"}]}]}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 14}, "id": 17, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "5000"}, "type": "raw_data"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: true  AND context.settlement_currency: EUR", "refId": "A", "timeField": "timestamp"}], "title": "Charges Info (EUR)", "transformations": [{"id": "calculateField", "options": {"binary": {"left": "context.account_balance_after_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "context.account_balance_before_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "context.settlement_amount", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.account_balance_after_billing": true, "context.account_balance_before_billing": true, "context.billing_event_id": true, "context.charge_ends_at": true, "context.charge_start_at": true, "context.is_Ocpp": true, "context.is_stale": true, "context.settlement_amount": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": true}, "indexByName": {"_id": 1, "_index": 2, "_type": 3, "channel": 4, "context.account_balance_after_billing": 27, "context.account_balance_after_billing / 100": 9, "context.account_balance_before_billing": 26, "context.account_balance_before_billing / 100": 8, "context.billing_account_id": 12, "context.billing_event_id": 14, "context.charge_ends_at": 28, "context.charge_energy_used": 7, "context.charge_id": 6, "context.charge_start_at": 19, "context.is_Ocpp": 16, "context.is_stale": 15, "context.location_id": 5, "context.settlement_amount": 25, "context.settlement_amount / 100": 11, "context.settlement_currency": 10, "env": 20, "extra": 13, "highlight": 17, "host": 18, "level": 21, "message": 22, "project": 23, "sort": 24, "timestamp": 0}, "renameByName": {"context.account_balance_after_billing": "", "context.account_balance_after_billing / 100": "Balance After Billing", "context.account_balance_before_billing": "", "context.account_balance_before_billing / 100": "Balance Before Billing", "context.billing_account_id": "Billing Account Id", "context.billing_event_id": "", "context.charge_ends_at": "Unplug Time(UTC)", "context.charge_energy_used": "Energy Used(kWh)", "context.charge_id": "Charge Id", "context.charge_start_at": "Plug Time(UTC)", "context.location_id": "Location ID", "context.settlement_amount": "", "context.settlement_amount / 100": "Charge Cost", "context.settlement_currency": "Charge Currency"}}}], "type": "table"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 0, "y": 20}, "id": 26, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["allValues"], "fields": "/^Count$/", "values": true}, "textMode": "value"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [{"field": "context.is_Ocpp", "id": "2", "settings": {"min_doc_count": "1", "order": "desc", "orderBy": "_term", "size": "10"}, "type": "terms"}], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "type": "count"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: true AND context.settlement_currency: NOK", "refId": "A", "timeField": "timestamp"}], "title": "Total of Rapid Charges that got user balance negative(NOK)", "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "-", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "currencyNOK"}, "overrides": [{"matcher": {"id": "byType", "options": "number"}, "properties": [{"id": "decimals", "value": 2}]}]}, "gridPos": {"h": 7, "w": 8, "x": 4, "y": 20}, "id": 27, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: true  AND context.settlement_currency: NOK", "refId": "A", "timeField": "timestamp"}], "title": "Stats on Negative balance for charges in Rapids (NOK)", "transformations": [{"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.account_balance_after_billing": false, "context.account_balance_before_billing": true, "context.billing_account_id": true, "context.billing_event_id": true, "context.charge_ends_at": true, "context.charge_energy_used": true, "context.charge_id": true, "context.charge_start_at": true, "context.is_Ocpp": true, "context.is_stale": true, "context.location_id": true, "context.settlement_amount": true, "context.settlement_currency": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": true}, "indexByName": {}, "renameByName": {}}}, {"id": "calculateField", "options": {"binary": {"left": "context.account_balance_after_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "reduce", "options": {"includeTimeField": false, "labelsToFields": false, "mode": "reduceFields", "reducers": ["max", "min", "mean", "sum"]}}, {"id": "organize", "options": {"excludeByName": {"context.account_balance_after_billing / 100 Total": true, "context.account_balance_after_billing Max": true, "context.account_balance_after_billing Mean": true, "context.account_balance_after_billing Min": true, "context.account_balance_after_billing Total": true}, "indexByName": {"context.account_balance_after_billing / 100 Max": 4, "context.account_balance_after_billing / 100 Mean": 5, "context.account_balance_after_billing / 100 Min": 6, "context.account_balance_after_billing / 100 Total": 7, "context.account_balance_after_billing Max": 1, "context.account_balance_after_billing Mean": 2, "context.account_balance_after_billing Min": 0, "context.account_balance_after_billing Total": 3}, "renameByName": {"context.account_balance_after_billing / 100 Max": "Lowest", "context.account_balance_after_billing / 100 Mean": "Average", "context.account_balance_after_billing / 100 Min": "Highest", "context.account_balance_after_billing / 100 Total": "Sum"}}}], "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance After Billing"}, "properties": [{"id": "custom.width", "value": 149}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Billing Account Id"}, "properties": [{"id": "custom.width", "value": 126}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge Cost"}, "properties": [{"id": "custom.width", "value": 96}, {"id": "unit", "value": "currencyNOK"}, {"id": "decimals", "value": 0}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance Before Billing"}, "properties": [{"id": "custom.width", "value": 156}, {"id": "unit", "value": "currencyNOK"}, {"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance After Billing"}, "properties": [{"id": "unit", "value": "currencyNOK"}, {"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Location Id"}, "properties": [{"id": "links", "value": [{"title": "", "url": "https://admin.pod-point.com/locations/${__value.raw}\n"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Billing Account Id"}, "properties": [{"id": "links", "value": [{"title": "", "url": "https://admin.pod-point.com/billing-accounts/${__value.raw}\n"}]}]}]}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 20}, "id": 28, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "5"}, "type": "raw_data"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: true  AND context.settlement_currency: NOK", "refId": "A", "timeField": "timestamp"}], "title": "Charges Info (NOK)", "transformations": [{"id": "calculateField", "options": {"binary": {"left": "context.account_balance_after_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "context.account_balance_before_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "context.settlement_amount", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.account_balance_after_billing": true, "context.account_balance_before_billing": true, "context.billing_event_id": true, "context.charge_ends_at": true, "context.charge_start_at": true, "context.is_Ocpp": true, "context.is_stale": true, "context.settlement_amount": true, "context.settlement_currency": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": false}, "indexByName": {"_id": 1, "_index": 2, "_type": 3, "channel": 4, "context.account_balance_after_billing": 27, "context.account_balance_after_billing / 100": 9, "context.account_balance_before_billing": 26, "context.account_balance_before_billing / 100": 8, "context.billing_account_id": 12, "context.billing_event_id": 14, "context.charge_ends_at": 28, "context.charge_energy_used": 7, "context.charge_id": 6, "context.charge_start_at": 19, "context.is_Ocpp": 16, "context.is_stale": 15, "context.location_id": 5, "context.settlement_amount": 25, "context.settlement_amount / 100": 11, "context.settlement_currency": 10, "env": 20, "extra": 13, "highlight": 17, "host": 18, "level": 21, "message": 22, "project": 23, "sort": 24, "timestamp": 0}, "renameByName": {"context.account_balance_after_billing": "", "context.account_balance_after_billing / 100": "Balance After Billing", "context.account_balance_before_billing": "", "context.account_balance_before_billing / 100": "Balance Before Billing", "context.billing_account_id": "Billing Account Id", "context.billing_event_id": "", "context.charge_ends_at": "Unplug Time(UTC)", "context.charge_energy_used": "Energy Used(kWh)", "context.charge_id": "Charge Id", "context.charge_start_at": "Plug Time(UTC)", "context.location_id": "Location Id", "context.settlement_amount": "", "context.settlement_amount / 100": "Charge Cost", "context.settlement_currency": ""}}}], "type": "table"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "id": 12, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Non Rapids", "type": "row"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "blue", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 0, "y": 28}, "id": 25, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["allValues"], "fields": "/^Count$/", "values": true}, "textMode": "value"}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "bucketAggs": [{"field": "context.is_Ocpp", "id": "2", "settings": {"min_doc_count": "1", "order": "desc", "orderBy": "_term", "size": "10"}, "type": "terms"}], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "type": "count"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: false AND context.settlement_currency: GBP", "refId": "A", "timeField": "timestamp"}], "title": "Total of Rapid Charges that got user balance negative(GBP)", "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "blue"}]}, "unit": "currencyGBP"}, "overrides": [{"matcher": {"id": "byType", "options": "number"}, "properties": [{"id": "decimals", "value": 2}]}]}, "gridPos": {"h": 7, "w": 8, "x": 4, "y": 28}, "id": 21, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "5000"}, "type": "raw_data"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: false AND context.settlement_currency: GBP", "refId": "A", "timeField": "timestamp"}], "title": "Stats on Negative balance for charges in non Rapids (GBP)", "transformations": [{"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.account_balance_after_billing": false, "context.account_balance_before_billing": true, "context.billing_account_id": true, "context.billing_event_id": true, "context.charge_ends_at": true, "context.charge_energy_used": true, "context.charge_id": true, "context.charge_start_at": true, "context.is_Ocpp": true, "context.is_stale": true, "context.location_id": true, "context.settlement_amount": false, "context.settlement_currency": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": true}, "indexByName": {}, "renameByName": {}}}, {"id": "calculateField", "options": {"alias": "postBalance", "binary": {"left": "context.account_balance_after_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}, "replaceFields": false}}, {"id": "calculateField", "options": {"alias": "BillingAmt", "binary": {"left": "context.settlement_amount", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"include": ["context.settlement_amount"], "reducer": "sum"}}}, {"id": "calculateField", "options": {"alias": "Overspend", "mode": "reduceRow", "reduce": {"include": ["postBalance", "BillingAmt"], "reducer": "max"}}}, {"id": "reduce", "options": {"includeTimeField": false, "labelsToFields": false, "mode": "reduceFields", "reducers": ["max", "min", "mean", "sum"]}}, {"id": "organize", "options": {"excludeByName": {"BillingAmt Max": true, "BillingAmt Mean": true, "BillingAmt Min": true, "BillingAmt Total": true, "context.account_balance_after_billing / 100 Total": true, "context.account_balance_after_billing Max": true, "context.account_balance_after_billing Mean": true, "context.account_balance_after_billing Min": true, "context.account_balance_after_billing Total": true, "context.settlement_amount Max": true, "context.settlement_amount Mean": true, "context.settlement_amount Min": true, "context.settlement_amount Total": true, "postBalance Max": true, "postBalance Mean": true, "postBalance Min": true, "postBalance Total": true}, "indexByName": {"context.account_balance_after_billing / 100 Max": 4, "context.account_balance_after_billing / 100 Mean": 5, "context.account_balance_after_billing / 100 Min": 6, "context.account_balance_after_billing / 100 Total": 7, "context.account_balance_after_billing Max": 1, "context.account_balance_after_billing Mean": 2, "context.account_balance_after_billing Min": 0, "context.account_balance_after_billing Total": 3}, "renameByName": {"Overspend Max": "Lowest", "Overspend Mean": "Average", "Overspend Min": "Highest", "Overspend Total": "Sum", "context.account_balance_after_billing / 100 Max": "Lowest", "context.account_balance_after_billing / 100 Mean": "Average", "context.account_balance_after_billing / 100 Min": "Highest", "context.account_balance_after_billing / 100 Total": "Sum"}}}], "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "orange"}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance After Billing"}, "properties": [{"id": "custom.width", "value": 232}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Billing Account Id"}, "properties": [{"id": "custom.width", "value": 161}, {"id": "links", "value": [{"title": "", "url": "https://admin.pod-point.com/billing-accounts/${__value.raw}\n"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge Cost"}, "properties": [{"id": "custom.width", "value": 151}, {"id": "unit", "value": "currencyGBP"}, {"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance Before Billing"}, "properties": [{"id": "custom.width", "value": 207}, {"id": "unit", "value": "currencyGBP"}, {"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance After Billing"}, "properties": [{"id": "unit", "value": "currencyGBP"}, {"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge Id"}, "properties": [{"id": "custom.width", "value": 164}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Energy Used(kWh)"}, "properties": [{"id": "custom.width", "value": 249}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Location Id"}, "properties": [{"id": "custom.width", "value": 106}, {"id": "links", "value": [{"title": "", "url": "https://admin.pod-point.com/locations/${__value.raw}\n"}]}]}]}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 28}, "id": 8, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Balance After Billing"}]}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "5000"}, "type": "raw_data"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: false AND context.settlement_currency: GBP", "refId": "A", "timeField": "timestamp"}], "title": "Charges Info (GBP)", "transformations": [{"id": "calculateField", "options": {"binary": {"left": "context.account_balance_after_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "context.account_balance_before_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "context.settlement_amount", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.account_balance_after_billing": true, "context.account_balance_before_billing": true, "context.billing_event_id": true, "context.charge_ends_at": true, "context.charge_start_at": true, "context.is_Ocpp": true, "context.is_stale": true, "context.settlement_amount": true, "context.settlement_currency": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": false}, "indexByName": {"_id": 1, "_index": 2, "_type": 3, "channel": 4, "context.account_balance_after_billing": 27, "context.account_balance_after_billing / 100": 9, "context.account_balance_before_billing": 26, "context.account_balance_before_billing / 100": 8, "context.billing_account_id": 11, "context.billing_event_id": 13, "context.charge_ends_at": 28, "context.charge_energy_used": 7, "context.charge_id": 6, "context.charge_start_at": 19, "context.is_Ocpp": 16, "context.is_stale": 14, "context.location_id": 5, "context.settlement_amount": 25, "context.settlement_amount / 100": 10, "context.settlement_currency": 17, "env": 20, "extra": 12, "highlight": 15, "host": 18, "level": 21, "message": 22, "project": 23, "sort": 24, "timestamp": 0}, "renameByName": {"context.account_balance_after_billing": "", "context.account_balance_after_billing / 100": "Balance After Billing", "context.account_balance_before_billing": "", "context.account_balance_before_billing / 100": "Balance Before Billing", "context.billing_account_id": "Billing Account Id", "context.billing_event_id": "", "context.charge_ends_at": "Unplug Time(UTC)", "context.charge_energy_used": "Energy Used(kWh)", "context.charge_id": "Charge Id", "context.charge_start_at": "Plug Time(UTC)", "context.location_id": "Location Id", "context.settlement_amount": "", "context.settlement_amount / 100": "Charge Cost", "context.settlement_currency": ""}}}], "type": "table"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "blue"}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 0, "y": 35}, "id": 23, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["allValues"], "fields": "/^Count$/", "values": true}, "textMode": "value"}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "bucketAggs": [{"field": "context.is_Ocpp", "id": "2", "settings": {"min_doc_count": "1", "order": "desc", "orderBy": "_term", "size": "10"}, "type": "terms"}], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "type": "count"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: false AND context.settlement_currency: EUR", "refId": "A", "timeField": "timestamp"}], "title": "Total of Rapid Charges that got user balance negative(EUR)", "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "noValue": "-", "thresholds": {"mode": "absolute", "steps": [{"color": "blue"}]}, "unit": "currencyEUR"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 4, "y": 35}, "id": 20, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: false  AND context.settlement_currency: EUR", "refId": "A", "timeField": "timestamp"}], "title": "Stats on Negative balance for charges in non Rapids (EUR)", "transformations": [{"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.account_balance_after_billing": false, "context.account_balance_before_billing": true, "context.billing_account_id": true, "context.billing_event_id": true, "context.charge_ends_at": true, "context.charge_energy_used": true, "context.charge_id": true, "context.charge_start_at": true, "context.is_Ocpp": true, "context.is_stale": true, "context.location_id": true, "context.settlement_amount": true, "context.settlement_currency": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": true}, "indexByName": {}, "renameByName": {}}}, {"id": "calculateField", "options": {"binary": {"left": "context.account_balance_after_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "reduce", "options": {"includeTimeField": false, "labelsToFields": false, "mode": "reduceFields", "reducers": ["max", "min", "mean", "sum"]}}, {"id": "organize", "options": {"excludeByName": {"context.account_balance_after_billing / 100 Total": true, "context.account_balance_after_billing Max": true, "context.account_balance_after_billing Mean": true, "context.account_balance_after_billing Min": true, "context.account_balance_after_billing Total": true}, "indexByName": {"context.account_balance_after_billing / 100 Max": 4, "context.account_balance_after_billing / 100 Mean": 5, "context.account_balance_after_billing / 100 Min": 6, "context.account_balance_after_billing / 100 Total": 7, "context.account_balance_after_billing Max": 2, "context.account_balance_after_billing Mean": 1, "context.account_balance_after_billing Min": 0, "context.account_balance_after_billing Total": 3}, "renameByName": {"context.account_balance_after_billing / 100 Max": "Lowest", "context.account_balance_after_billing / 100 Mean": "Average", "context.account_balance_after_billing / 100 Min": "Highest", "context.account_balance_after_billing / 100 Total": "Sum"}}}], "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance After Billing"}, "properties": [{"id": "custom.width", "value": 149}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Billing Account Id"}, "properties": [{"id": "custom.width", "value": 126}, {"id": "links", "value": [{"title": "", "url": "https://admin.pod-point.com/billing-accounts/${__value.raw}\n"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge Cost"}, "properties": [{"id": "custom.width", "value": 96}, {"id": "unit", "value": "currencyEUR"}, {"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge Currency"}, "properties": [{"id": "custom.width", "value": 125}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance Before Billing"}, "properties": [{"id": "custom.width", "value": 156}, {"id": "unit", "value": "currencyEUR"}, {"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance After Billing"}, "properties": [{"id": "unit", "value": "currencyEUR"}, {"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Energy Used(kWh)"}, "properties": [{"id": "custom.width", "value": 136}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Location Id"}, "properties": [{"id": "custom.width", "value": 176}, {"id": "links", "value": [{"title": "", "url": "https://admin.pod-point.com/locations/${__value.raw}\n"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge Id"}, "properties": [{"id": "custom.width", "value": 178}]}]}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 35}, "id": 10, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "5"}, "type": "raw_data"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: false  AND context.settlement_currency: EUR", "refId": "A", "timeField": "timestamp"}], "title": "Charges Info (EUR)", "transformations": [{"id": "calculateField", "options": {"binary": {"left": "context.account_balance_after_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "context.account_balance_before_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "context.settlement_amount", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.account_balance_after_billing": true, "context.account_balance_before_billing": true, "context.billing_event_id": true, "context.charge_ends_at": true, "context.charge_start_at": true, "context.is_Ocpp": true, "context.is_stale": true, "context.settlement_amount": true, "context.settlement_currency": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": false}, "indexByName": {"_id": 1, "_index": 2, "_type": 3, "channel": 4, "context.account_balance_after_billing": 27, "context.account_balance_after_billing / 100": 9, "context.account_balance_before_billing": 26, "context.account_balance_before_billing / 100": 8, "context.billing_account_id": 12, "context.billing_event_id": 14, "context.charge_ends_at": 28, "context.charge_energy_used": 7, "context.charge_id": 6, "context.charge_start_at": 19, "context.is_Ocpp": 16, "context.is_stale": 15, "context.location_id": 5, "context.settlement_amount": 25, "context.settlement_amount / 100": 11, "context.settlement_currency": 10, "env": 20, "extra": 13, "highlight": 17, "host": 18, "level": 21, "message": 22, "project": 23, "sort": 24, "timestamp": 0}, "renameByName": {"context.account_balance_after_billing": "", "context.account_balance_after_billing / 100": "Balance After Billing", "context.account_balance_before_billing": "", "context.account_balance_before_billing / 100": "Balance Before Billing", "context.billing_account_id": "Billing Account Id", "context.billing_event_id": "", "context.charge_ends_at": "Unplug Time(UTC)", "context.charge_energy_used": "Energy Used(kWh)", "context.charge_id": "Charge Id", "context.charge_start_at": "Plug Time(UTC)", "context.location_id": "Location Id", "context.settlement_amount": "", "context.settlement_amount / 100": "Charge Cost", "context.settlement_currency": ""}}}], "type": "table"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "blue"}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 0, "y": 42}, "id": 29, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["allValues"], "fields": "/^Count$/", "values": true}, "textMode": "value"}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "bucketAggs": [{"field": "context.is_Ocpp", "id": "2", "settings": {"min_doc_count": "1", "order": "desc", "orderBy": "_term", "size": "10"}, "type": "terms"}], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "type": "count"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: false AND context.settlement_currency: NOK", "refId": "A", "timeField": "timestamp"}], "title": "Total of Rapid Charges that got user balance negative(NOK)", "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "-", "thresholds": {"mode": "absolute", "steps": [{"color": "blue"}]}, "unit": "currencyGBP"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 4, "y": 42}, "id": 30, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: false  AND context.settlement_currency: NOK", "refId": "A", "timeField": "timestamp"}], "title": "Stats on Negative balance for charges in non Rapids (NOK)", "transformations": [{"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.account_balance_after_billing": false, "context.account_balance_before_billing": true, "context.billing_account_id": true, "context.billing_event_id": true, "context.charge_ends_at": true, "context.charge_energy_used": true, "context.charge_id": true, "context.charge_start_at": true, "context.is_Ocpp": true, "context.is_stale": true, "context.location_id": true, "context.settlement_amount": true, "context.settlement_currency": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": true}, "indexByName": {}, "renameByName": {}}}, {"id": "calculateField", "options": {"binary": {"left": "context.account_balance_after_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "reduce", "options": {"includeTimeField": false, "labelsToFields": false, "mode": "reduceFields", "reducers": ["max", "min", "mean", "sum"]}}, {"id": "organize", "options": {"excludeByName": {"context.account_balance_after_billing / 100 Total": true, "context.account_balance_after_billing Max": true, "context.account_balance_after_billing Mean": true, "context.account_balance_after_billing Min": true, "context.account_balance_after_billing Total": true}, "indexByName": {"Average": 4, "Highest": 5, "Lowest": 3, "context.account_balance_after_billing Max": 1, "context.account_balance_after_billing Mean": 2, "context.account_balance_after_billing Min": 0}, "renameByName": {"context.account_balance_after_billing / 100 Max": "Lowest", "context.account_balance_after_billing / 100 Mean": "Average", "context.account_balance_after_billing / 100 Min": "Highest", "context.account_balance_after_billing / 100 Total": "Sum"}}}], "type": "stat"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance After Billing"}, "properties": [{"id": "custom.width", "value": 149}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Billing Account Id"}, "properties": [{"id": "custom.width", "value": 126}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge Cost"}, "properties": [{"id": "custom.width", "value": 96}, {"id": "unit", "value": "currencyGBP"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge Currency"}, "properties": [{"id": "custom.width", "value": 125}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance Before Billing"}, "properties": [{"id": "custom.width", "value": 156}, {"id": "unit", "value": "currencyGBP"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Balance After Billing"}, "properties": [{"id": "unit", "value": "currencyGBP"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Energy Used(kWh)"}, "properties": [{"id": "custom.width", "value": 136}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Location Id"}, "properties": [{"id": "custom.width", "value": 176}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Charge Id"}, "properties": [{"id": "custom.width", "value": 178}]}]}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 42}, "id": 31, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "5"}, "type": "raw_data"}], "query": "message: \"Negative balance after billing event.\"AND env: production AND context.is_Ocpp: false  AND context.settlement_currency: NOK", "refId": "A", "timeField": "timestamp"}], "title": "Charges Info (NOK)", "transformations": [{"id": "calculateField", "options": {"binary": {"left": "context.account_balance_after_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "context.account_balance_before_billing", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "calculateField", "options": {"binary": {"left": "context.settlement_amount", "operator": "/", "reducer": "sum", "right": "100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "channel": true, "context.account_balance_after_billing": true, "context.account_balance_before_billing": true, "context.billing_event_id": true, "context.charge_ends_at": true, "context.charge_start_at": true, "context.is_Ocpp": true, "context.is_stale": true, "context.settlement_amount": true, "context.settlement_currency": true, "env": true, "extra": true, "highlight": true, "host": true, "level": true, "message": true, "project": true, "sort": true, "timestamp": true}, "indexByName": {"_id": 1, "_index": 2, "_type": 3, "channel": 4, "context.account_balance_after_billing": 27, "context.account_balance_after_billing / 100": 9, "context.account_balance_before_billing": 26, "context.account_balance_before_billing / 100": 8, "context.billing_account_id": 12, "context.billing_event_id": 14, "context.charge_ends_at": 28, "context.charge_energy_used": 7, "context.charge_id": 6, "context.charge_start_at": 19, "context.is_Ocpp": 16, "context.is_stale": 15, "context.location_id": 5, "context.settlement_amount": 25, "context.settlement_amount / 100": 11, "context.settlement_currency": 10, "env": 20, "extra": 13, "highlight": 17, "host": 18, "level": 21, "message": 22, "project": 23, "sort": 24, "timestamp": 0}, "renameByName": {"context.account_balance_after_billing": "", "context.account_balance_after_billing / 100": "Balance After Billing", "context.account_balance_before_billing": "", "context.account_balance_before_billing / 100": "Balance Before Billing", "context.billing_account_id": "Billing Account Id", "context.billing_event_id": "", "context.charge_ends_at": "Unplug Time(UTC)", "context.charge_energy_used": "Energy Used(kWh)", "context.charge_id": "Charge Id", "context.charge_start_at": "Plug Time(UTC)", "context.location_id": "Location Id", "context.settlement_amount": "", "context.settlement_amount / 100": "Charge Cost", "context.settlement_currency": ""}}}], "type": "table"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "description": "Daily average of all paid charges that resulted in a negative billing account balance (inclusive of those where the user subsequently topped up & reused)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 49}, "id": 32, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.4.7", "targets": [{"alias": "Rapids", "bucketAggs": [{"field": "timestamp", "id": "2", "settings": {"interval": "1d"}, "type": "date_histogram"}], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"field": "context.account_balance_after_billing", "hide": false, "id": "1", "settings": {"script": "_value/-100"}, "type": "avg"}], "query": "message: \"Negative balance after billing event.\" AND env: production AND context.is_Ocpp: true AND context.settlement_currency: GBP AND context.account_balance_before_billing:>0", "refId": "A", "timeField": "timestamp"}, {"alias": "Non-Rapids", "bucketAggs": [{"field": "timestamp", "id": "2", "settings": {"interval": "1d"}, "type": "date_histogram"}], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "hide": false, "metrics": [{"field": "context.account_balance_after_billing", "hide": false, "id": "1", "settings": {"script": "_value/-100"}, "type": "avg"}], "query": "message: \"Negative balance after billing event.\" AND env: production AND context.is_Ocpp: false AND context.settlement_currency: GBP AND context.account_balance_before_billing:>0", "refId": "B", "timeField": "timestamp"}], "title": "Average Charge overspend by charge type", "transformations": [{"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "Balance After Billing"}]}}, {"disabled": true, "id": "calculateField", "options": {"alias": "Non-Rapids £", "binary": {"left": "Non-Rapids", "operator": "/", "reducer": "sum", "right": "-100"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"disabled": true, "id": "calculateField", "options": {"binary": {"left": "Rapids", "operator": "/", "reducer": "sum", "right": "-1"}, "mode": "binary", "reduce": {"reducer": "sum"}}}], "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-30d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Negative Balance Complex", "uid": "JiT0ms2Ik", "version": 1, "weekStart": ""}