{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 129, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 17, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "HTTP API Monitoring", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": true, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 21, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 1}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "asc"}}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ApiName": "dev-PODPointAPI", "Resource": "/v4/pods/charge/continue"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "4XXError", "metricQueryType": 0, "namespace": "AWS/ApiGateway", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ApiName": "dev-PODPointAPI", "Resource": "/v4/pods/charge/continue"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "5XXError", "metricQueryType": 0, "namespace": "AWS/ApiGateway", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "HTTP Requests", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": true, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 21, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 1}, "id": 33, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "asc"}}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ApiName": "dev-PODPointAPI", "Resource": "/v4/pods/charge/continue"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "Latency", "metricQueryType": 0, "namespace": "AWS/ApiGateway", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Latency", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": true, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 21, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 10}, "id": 32, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "asc"}}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ApiName": "dev-PODPointAPI", "Resource": "/v4/pods/charge/continue"}, "expression": "", "hide": false, "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "IntegrationLatency", "metricQueryType": 0, "namespace": "AWS/ApiGateway", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Integration Latency", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "statusCode"}, "properties": [{"id": "custom.width", "value": 77}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "mac_address"}, "properties": [{"id": "custom.width", "value": 126}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "timeFromPayload"}, "properties": [{"id": "custom.width", "value": 163}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "custom.width", "value": 187}]}]}, "gridPos": {"h": 9, "w": 16, "x": 8, "y": 10}, "id": 9, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "expression": "fields @timestamp, statusCode\n| parse @message  \"\\\"mac_address\\\":\\\"*\\\"\" as mac_address\n| parse @message  \"\\\"timestamp\\\":\\\"*\\\"\" as timeFromPayload\n| parse @message   \"Error: * at\" as Error\n| filter @message like 'statusCode'", "id": "", "logGroupNames": ["/aws/lambda/PODPointAPI-prod-shouldPodContinueChargeResponseV4"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Table with more detail on Failed Requests", "transformations": [{"id": "organize", "options": {"excludeByName": {"__log__grafana_internal__": true, "__logstream__grafana_internal__": true}, "indexByName": {}, "renameByName": {}}}], "type": "table"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 19, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Should Continue V4 Lambda", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration_Minimum"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration_Average"}, "properties": [{"id": "color", "value": {"fixedColor": "light-yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration_Maximum"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Maximum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "C", "region": "default", "sqlExpression": "", "statistic": "Minimum"}], "title": "Duration", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Errors", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Errors", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Invocations_Maximum"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 28}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Invocations", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Invocations", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 8, "y": 28}, "id": 25, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "expression": "filter @message like /Process exited/\n| stats count() by bin(30m)\n", "id": "", "logGroupNames": ["/aws/lambda/PODPointAPI-prod-shouldPodContinueChargeResponseV4"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["bin(30m)"]}], "title": " Lambda exited invocations", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 11, "y": 28}, "id": 31, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "/^UnreservedConcurrentExecutions_Maximum$/", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"function_name": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "UnreservedConcurrentExecutions", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "SampleCount"}], "title": "Unreserved Concurrent Executions", "transformations": [], "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "ConcurrentExecutions_Minimum"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "ConcurrentExecutions_Maximum"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "ConcurrentExecutions_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 10, "x": 14, "y": 28}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ConcurrentExecutions", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ConcurrentExecutions", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "Concurrent Executions", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Invocations_Maximum"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 4, "w": 6, "x": 8, "y": 32}, "id": 30, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "/^Invocations_Sum$/", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Invocations", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Invocations", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Throttles_Maximum"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Throttles_Minimum"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Throttles_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Throttles_Average"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 36}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "<PERSON>hrottles", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "Lambda Throttles", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "<PERSON>hrottles", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Maximum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "<PERSON>hrottles", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Minimum"}], "title": "Throttling", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-YlBl"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}, "unit": "ms"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Throttles_Maximum"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Throttles_Minimum"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Throttles_Sum"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration_Minimum"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration_Maximum"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 36}, "id": 34, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Maximum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Minimum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "PODPointAPI-prod-shouldPodContinueChargeResponseV4"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "C", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Duration", "type": "timeseries"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "PPCP API Should Continue V4 Dev/Staging", "uid": "wYpKTKLVz", "version": 3, "weekStart": ""}