{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 80, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 6, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "WebSocket Connections", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": true, "axisLabel": "", "axisPlacement": "left", "barAlignment": 1, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 0, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 1}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp\n| filter event.requestContext.eventType = 'CONNECT'\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n| stats count(*) as connectionsPerMinute by bin(1m)\n\n", "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-connect"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["bin(1m)"]}], "title": "Distribution of WebSocket Connections", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": true, "axisLabel": "", "axisPlacement": "left", "barAlignment": 1, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 0, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-orange", "value": null}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "connectionsPerMinute"}, "properties": [{"id": "color", "value": {"mode": "continuous-RdYlGr"}}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 8}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp\n| filter msg = 'Disconnection event'\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n| stats count(*) as connectionsPerMinute by bin(1m)\n\n", "id": "", "logGroupNames": ["/ecs/connectivity-service/disconnections"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["bin(1m)"]}], "title": "Distribution of WebSocket Disconnections", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 15}, "id": 16, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": [], "fields": "/.*/", "values": true}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber\n| filter event.requestContext.eventType = 'CONNECT'\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n| stats count_distinct(serialNumber)", "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-connect"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Total of devices established WebSocket Connection", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "green", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 15}, "id": 9, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": [], "fields": "/^count\\(\\*\\)$/", "values": true}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber\n| filter event.requestContext.eventType = 'CONNECT'\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n| stats count(*)\n\n\n", "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-connect"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Total of established WebSocket Connections", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 15}, "id": 27, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "/^count\\(\\*\\)$/", "values": true}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber\n| filter event.requestContext.eventType = 'DISCONNECT'\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n| stats count(*)\n\n\n", "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-disconnect"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Total of established WebSocket Disconnections", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "diconnectsCount"}, "properties": [{"id": "custom.width", "value": 118}]}]}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 22}, "id": 20, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber, event.disconnectStatusCode as disconnectStatusCode\n| filter msg = 'Disconnection event'\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n| stats count() as diconnectsCount by disconnectStatusCode \n| sort by diconnectsCount desc\n\n\n", "id": "", "logGroupNames": ["/ecs/connectivity-service/disconnections"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["disconnectStatusCode"]}], "title": "Total of disconnections per disconnection codes ", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "serialNumber"}, "properties": [{"id": "custom.width", "value": 125}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "total"}, "properties": [{"id": "custom.width", "value": 120}]}]}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 22}, "id": 21, "options": {"footer": {"countRows": false, "fields": ["total"], "reducer": ["sum"], "show": true}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber, event.disconnectStatusCode as disconnectStatusCode\n| filter msg = 'Disconnection event'\n| filter disconnectStatusCode = 1006\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' | stats count() as total by serialNumber \n| sort by total desc\n\n\n", "id": "", "logGroupNames": ["/ecs/connectivity-service/disconnections"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["serialNumber"]}], "title": "Disconnection Reason: Closing Connection Abnormally", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "description": "We believe it's the server that closes the connection because it was idle or it lasted for 2 hours", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "serialNumber"}, "properties": [{"id": "custom.width", "value": 119}]}]}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 22}, "id": 22, "options": {"footer": {"countRows": false, "fields": ["total"], "reducer": ["sum"], "show": true}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber, event.disconnectStatusCode as disconnectStatusCode\n| filter disconnectStatusCode = 1001\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' | stats count() as total by serialNumber \n| sort by total desc\n\n\n", "id": "", "logGroupNames": ["/ecs/connectivity-service/disconnections"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["serialNumber"]}], "title": "Disconnection Reason: Going Away", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "description": "We believe this will only happen when the server closed the connection because of the following conditions is met:\n* cert serial number does not match url path\n* mac address is not a header on the request\n* Cert Issuer CN is not one of:\n  - 'Pod Point CS Intermediate Authority',\n  - 'Pod Point PPCP Intermediate Authority',\n   - 'Pod Point PPCP Intermediate Authority Temp',\n* OTT not found", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "serialNumber"}, "properties": [{"id": "custom.width", "value": 119}]}]}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 22}, "id": 23, "options": {"footer": {"countRows": false, "fields": ["total"], "reducer": ["sum"], "show": true}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber, event.disconnectStatusCode as disconnectStatusCode\n| filter disconnectStatusCode = 1000\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' | stats count() as total by serialNumber \n| sort by total desc\n\n\n", "id": "", "logGroupNames": ["/ecs/connectivity-service/disconnections"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["serialNumber"]}], "title": "Disconnection Reason: Connection Closed Normally", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 30}, "id": 25, "options": {"footer": {"fields": "", "reducer": ["count"], "show": false}, "frameIndex": 0, "showHeader": true, "sortBy": [{"desc": false, "displayName": "serialNumber"}]}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber,  event.requestContext.eventType as action,event.requestContext.connectionId as WebsocketConnectionId, from<PERSON><PERSON><PERSON>(event.requestContext.connectedAt) as connectedAt, from<PERSON><PERSON><PERSON>(event.requestContext.requestTimeEpoch) as disconnectedAt,\nevent.disconnectStatusCode as disconnectStatusCode, event.requestContext.disconnectReason as disconnectReason, ceil(((event.requestContext.requestTimeEpoch - event.requestContext.connectedAt )/1000)/60) as WebSocketConnectionOpenFor_InMinutes\n| filter disconnectStatusCode = 1001\n| filter action like 'DISCONNECT'\n| filter @type not like 'REPORT'  \n| filter @type not like 'START' \n| filter @type not like 'END' \n| filter WebSocketConnectionOpenFor_InMinutes < 120\n\n\n\n", "hide": false, "id": "", "logGroupNames": ["/ecs/connectivity-service/disconnections"], "namespace": "", "queryMode": "Logs", "refId": "DISCONNECTS", "region": "default", "statsGroups": []}], "title": "WebSocket Connections closed by the server due to idle connection", "transformations": [{"id": "convertFieldType", "options": {"conversions": [{"destinationType": "time", "targetField": "disconnectedAt"}], "fields": {}}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "__log__grafana_internal__": true, "__logstream__grafana_internal__": true, "action": true, "disconnectReason": true, "disconnectStatusCode": true}, "indexByName": {}, "renameByName": {}}}], "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 43}, "id": 28, "options": {"footer": {"fields": ["serialNumber"], "reducer": ["count"], "show": true}, "frameIndex": 0, "showHeader": true, "sortBy": [{"desc": true, "displayName": "serialNumber"}]}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber,  event.requestContext.eventType as action,event.requestContext.connectionId as WebsocketConnectionId, from<PERSON><PERSON><PERSON>(event.requestContext.connectedAt) as connectedAt, from<PERSON><PERSON><PERSON>(event.requestContext.requestTimeEpoch) as disconnectedAt,\nevent.requestContext.disconnectStatusCode as disconnectStatusCode, event.requestContext.disconnectReason as disconnectReason, ceil(((event.requestContext.requestTimeEpoch - event.requestContext.connectedAt )/1000)/60) as WebSocketConnectionOpenFor_InMinutes\n| filter disconnectReason = 'Going away'\n| filter disconnectStatusCode = 1001\n| filter action like 'DISCONNECT'\n| filter @type not like 'REPORT'  \n| filter @type not like 'START' \n| filter @type not like 'END' \n| filter WebSocketConnectionOpenFor_InMinutes < 120\n| stats count(*) by serialNumber\n\n\n\n", "hide": false, "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-disconnect"], "namespace": "", "queryMode": "Logs", "refId": "DISCONNECTS", "region": "default", "statsGroups": ["serialNumber"]}], "title": "Devices with Idle Connections", "transformations": [{"id": "convertFieldType", "options": {"conversions": [{"destinationType": "time", "targetField": "disconnectedAt"}], "fields": {}}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "__log__grafana_internal__": true, "__logstream__grafana_internal__": true, "action": true, "disconnectReason": true, "disconnectStatusCode": true}, "indexByName": {}, "renameByName": {}}}], "type": "table"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 56}, "id": 12, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "OCPP Messages", "type": "row"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisGridShow": true, "axisLabel": "", "axisPlacement": "left", "barAlignment": 1, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 0, "pointSize": 7, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 57}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp\n| filter event.requestContext.eventType = 'MESSAGE'\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n| stats count(*) as messagesPerMinute by bin(1m)\n\n", "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-message"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["bin(1m)"]}], "title": "Total of Messages", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 64}, "id": 15, "options": {"colorMode": "none", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": [], "fields": "/^count\\(\\*\\)$/", "limit": 1, "values": true}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber\n| filter event.requestContext.eventType = 'MESSAGE'\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n| stats count(*)\n\n\n", "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-message"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Total of Messages", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 64}, "id": 8, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["count"], "fields": "/^count_distinct\\(serialNumber\\)$/", "values": true}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber\n\n| filter event.requestContext.eventType = 'MESSAGE'\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n| stats count_distinct(serialNumber)\n\n\n", "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-message"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Total of Devices that sent at least a message", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "cellOptions": {"type": "auto"}, "filterable": true, "minWidth": 50}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 64}, "id": 14, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "MessagesCount"}]}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as PCB_SerialNumber\n| filter event.requestContext.eventType = 'MESSAGE'\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n| stats count() as MessagesCount by PCB_SerialNumber\n| sort by MessagesCount desc\n\n\n", "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-message"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": ["PCB_SerialNumber"]}], "title": "Total of Messages per device", "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "WebSocketConnectionOpenFor_InMinutes"}, "properties": [{"id": "custom.width", "value": 303}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "connectedAt"}, "properties": [{"id": "custom.width", "value": 149}]}]}, "gridPos": {"h": 8, "w": 10, "x": 0, "y": 71}, "id": 2, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber, event.requestContext.connectionId as WebsocketConnectionId, fromMillis(event.requestContext.connectedAt) as connectedAt, ceil(((event.requestContext.requestTimeEpoch - event.requestContext.connectedAt )/1000)/60) as WebSocketConnectionOpenFor_InMinutes\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n", "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-message"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Last(can be the current) WebSocket Connection Open for", "transformations": [{"id": "groupBy", "options": {"fields": {"WebSocketConnectionOpenFor_InMinutes": {"aggregations": ["last"], "operation": "aggregate"}, "WebsocketConnectionId": {"aggregations": []}, "serialNumber": {"aggregations": [], "operation": "groupby"}}}}], "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time (lastNotNull)"}, "properties": [{"id": "custom.width", "value": 303}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "connectedAt"}, "properties": [{"id": "custom.width", "value": 149}]}]}, "gridPos": {"h": 8, "w": 11, "x": 10, "y": 71}, "id": 18, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Time (lastNotNull)"}]}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber, event.requestContext.connectionId as WebsocketConnectionId, fromMillis(event.requestContext.connectedAt) as connectedAt, ceil(((event.requestContext.requestTimeEpoch - event.requestContext.connectedAt )/1000)/60) as WebSocketConnectionOpenFor_InMinutes\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n", "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-message"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Last message from PCB", "transformations": [{"id": "calculateField", "options": {"binary": {"left": "Time (lastNotNull)", "operator": "-", "reducer": "sum", "right": "now()"}, "mode": "binary", "reduce": {"reducer": "sum"}}}, {"id": "groupBy", "options": {"fields": {"Time": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "__logstream__grafana_internal__": {"aggregations": [], "operation": "aggregate"}, "serialNumber": {"aggregations": [], "operation": "groupby"}}}}], "type": "table"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-2h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Comms Whole Fleet", "uid": "S4-BPjcVk", "version": 67, "weekStart": ""}