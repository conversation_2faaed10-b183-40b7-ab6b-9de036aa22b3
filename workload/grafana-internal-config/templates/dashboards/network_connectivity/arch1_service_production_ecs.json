{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 114, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 14, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Application Traffic", "type": "row"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 25, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 6, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "responses 503"}, "properties": [{"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["elb_status_code"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 1}, "id": 2, "maxDataPoints": 200, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"column": "elb_status_code", "connectionArgs": {"catalog": "__default", "database": "arch1_prod", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 0, "hide": false, "rawSQL": "select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time, \nelb_status_code, \ncount(*) as responses\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z'), elb_status_code\norder by time", "refId": "A", "table": "arch1_prod_alb"}], "title": "HTTP responses by code", "type": "timeseries"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 11}, "id": 10, "maxDataPoints": 200, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"connectionArgs": {"catalog": "__default", "database": "arch1_prod", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time, avg(target_processing_time) as processing_time\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\norder by time", "refId": "A", "table": "arch1_prod_alb"}], "title": "Average request duration", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 11}, "id": 4, "maxDataPoints": 200, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"connectionArgs": {"catalog": "__default", "database": "arch1_prod", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z') as time, sum(sent_bytes) as data_sent\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by $__timeGroup(time,'$__interval','yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\norder by time", "refId": "A", "table": "arch1_prod_alb"}], "title": "Data sent to clients", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user_agent"}, "properties": [{"id": "custom.width", "value": 114}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "client_ip"}, "properties": [{"id": "custom.width", "value": 124}]}]}, "gridPos": {"h": 8, "w": 7, "x": 0, "y": 21}, "id": 7, "maxDataPoints": 300, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "arch1_prod", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select user_agent, client_ip, count(request_verb) as count\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand user_agent not like 'Amazon-Route53%'\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by user_agent, client_ip\norder by count(request_verb) desc\nlimit 20", "refId": "A", "table": "arch1_prod_alb"}], "title": "Top user agents by IP", "transformations": [{"id": "renameByRegex", "options": {"regex": "(https:\\/\\/api\\.pod-point\\.com:443)(.*)", "renamePattern": "$2"}}], "type": "table"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user_agent"}, "properties": [{"id": "custom.width", "value": 216}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "client_ip"}, "properties": [{"id": "custom.width", "value": 124}]}]}, "gridPos": {"h": 8, "w": 7, "x": 7, "y": 21}, "id": 9, "maxDataPoints": 300, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "arch1_prod", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select user_agent, count(request_verb) as count\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand user_agent not like 'Amazon-Route53%'\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by user_agent\norder by count(request_verb) desc\nlimit 20", "refId": "A", "table": "arch1_prod_alb"}], "title": "Top user agents", "transformations": [{"id": "renameByRegex", "options": {"regex": "(https:\\/\\/api\\.pod-point\\.com:443)(.*)", "renamePattern": "$2"}}], "type": "table"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 10}, {"color": "red", "value": 30}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "request_verb"}, "properties": [{"id": "custom.width", "value": 107}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "path"}, "properties": [{"id": "custom.width", "value": 390}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "seconds"}, "properties": [{"id": "custom.width", "value": 154}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "verb"}, "properties": [{"id": "custom.width", "value": 68}]}]}, "gridPos": {"h": 8, "w": 10, "x": 14, "y": 21}, "id": 6, "maxDataPoints": 300, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "seconds"}]}, "pluginVersion": "9.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "arch1_prod", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select request_verb as verb,\n      regexp_replace(regexp_replace(regexp_replace(request_url, '\\?.*', ''), '\\/sessions\\/\\w+', '\\/sessions\\/{sessionId}'), '\\/\\d+', '\\/{id}') AS path, \n      approx_percentile(target_processing_time,0.95) AS seconds\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by request_verb, regexp_replace(regexp_replace(regexp_replace(request_url, '\\?.*', ''), '\\/sessions\\/\\w+', '\\/sessions\\/{sessionId}'), '\\/\\d+', '\\/{id}')\norder by approx_percentile(target_processing_time,0.95) desc\nlimit 50", "refId": "A", "table": "arch1_prod_alb"}], "title": "Slowest endpoints (p95)", "transformations": [], "type": "table"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 0, "y": 29}, "id": 11, "maxDataPoints": 300, "options": {"displayMode": "gradient", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "showUnfilled": true}, "pluginVersion": "8.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "arch1_prod", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select replace(user_agent, '%20',' ') as user_agent, count(*) as total\nfrom $__table\nwhere  client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by user_agent\norder by total desc", "refId": "A", "table": "arch1_prod_alb"}], "title": "CF - Top user agents", "transformations": [{"id": "renameByRegex", "options": {"regex": "(https:\\/\\/api\\.pod-point\\.com:443)(.*)", "renamePattern": "$2"}}], "type": "bargauge"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user_agent"}, "properties": [{"id": "custom.width", "value": 216}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "client_ip"}, "properties": [{"id": "custom.width", "value": 124}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "request_ip"}, "properties": [{"id": "custom.width", "value": 226}]}]}, "gridPos": {"h": 8, "w": 7, "x": 7, "y": 29}, "id": 12, "maxDataPoints": 300, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "arch1_prod", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select replace(user_agent, '%20',' '), client_ip, count(*) as total\nfrom $__table\nwhere client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by user_agent, client_ip\norder by total desc", "refId": "A", "table": "arch1_prod_alb"}], "title": "CF - Top user agents by IP", "transformations": [{"id": "renameByRegex", "options": {"regex": "(https:\\/\\/api\\.pod-point\\.com:443)(.*)", "renamePattern": "$2"}}], "type": "table"}, {"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "path"}, "properties": [{"id": "custom.width", "value": 365}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.width", "value": 150}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "verb"}, "properties": [{"id": "custom.width", "value": 74}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "count"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "lcd", "type": "gauge"}}]}]}, "gridPos": {"h": 8, "w": 10, "x": 14, "y": 29}, "id": 8, "maxDataPoints": 300, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "arch1_prod", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaPodPoint"}, "format": 1, "rawSQL": "select request_verb as verb,\n      regexp_replace(regexp_replace(request_url, '\\?.*', ''), '\\/\\d+', '\\/{id}') AS path,\n      count(*) as count\nfrom $__table\nwhere $__timeFilter(time, 'yyyy-MM-dd''T''HH:mm:ss.SSSSSS''Z')\nand client_ip like '%$client_ip%'\nand request_url like '%$request_url%'\nand user_agent like '%$user_agent%'\ngroup by request_verb, regexp_replace(regexp_replace(request_url, '\\?.*', ''), '\\/\\d+', '\\/{id}')\norder by count(*) desc\nlimit 50", "refId": "A", "table": "arch1_prod_alb"}], "title": "Most active paths", "transformations": [{"id": "renameByRegex", "options": {"regex": "(https:\\/\\/api\\.pod-point\\.com:443)(.*)", "renamePattern": "$2"}}], "type": "table"}, {"collapsed": true, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 37}, "id": 16, "panels": [{"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 7, "x": 0, "y": 2}, "id": 18, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "vertical", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "8.4.7", "targets": [{"alias": "Pending Task Count", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClusterName": "arch1-prod", "ServiceName": "arch1-prod"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "PendingTaskCount", "metricQueryType": 0, "namespace": "ECS/ContainerInsights", "period": "", "queryMode": "Metrics", "refId": "Pending Task Count", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "Running Task Count", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClusterName": "arch1-prod", "ServiceName": "arch1-prod"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "RunningTaskCount", "metricQueryType": 0, "namespace": "ECS/ContainerInsights", "period": "", "queryMode": "Metrics", "refId": "Running Task Count", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Task Count", "type": "stat"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 7, "y": 2}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClusterName": "arch1-prod", "ServiceName": "arch1-prod"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "Average CPU Utilisation", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClusterName": "arch1-prod", "ServiceName": "arch1-prod"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "Maximum CPU Utilisation", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "CPU Utilisation", "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 9, "x": 15, "y": 2}, "id": 22, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClusterName": "arch1-prod", "ServiceName": "arch1-prod"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "MemoryUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "Memory Utilisation Average", "region": "default", "sqlExpression": "", "statistic": "Average"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClusterName": "arch1-prod", "ServiceName": "arch1-prod"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "MemoryUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "Memory Utilisation Minimum", "region": "default", "sqlExpression": "", "statistic": "Minimum"}, {"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"ClusterName": "arch1-prod", "ServiceName": "arch1-prod"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "MemoryUtilization", "metricQueryType": 0, "namespace": "AWS/ECS", "period": "", "queryMode": "Metrics", "refId": "Memory Utilisation Maximum", "region": "default", "sqlExpression": "", "statistic": "Maximum"}], "title": "Memory Utilisation", "type": "timeseries"}], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "ECS Service", "type": "row"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "name": "client_ip", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": "", "value": ""}, "description": "", "hide": 0, "name": "request_url", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}, {"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "name": "user_agent", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Arch1 Service Production - ECS", "uid": "_66yBvB4z", "version": 14, "weekStart": ""}