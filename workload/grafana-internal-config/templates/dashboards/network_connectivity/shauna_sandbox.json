{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 178, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-athena-datasource", "uid": "AthenaCsConnStagOcppLog"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 15, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.4.7", "targets": [{"connectionArgs": {"catalog": "__default", "database": "__default", "region": "__default"}, "datasource": {"type": "grafana-athena-datasource", "uid": "AthenaCsConnStagOcppLog"}, "format": 1, "rawSQL": "with req as ( \nselect  \nmetadata.ppid, receivedat as request_time, origin as request_origin, metadata.action as request_type, \nevent.payload as request_payload, event.messageid \nfrom ocpp_logs\nwhere event.messagetype = 'REQ'\n-- and metadata.ppid = ${ppid}\n--and metadata.action like '%StartTransaction'\norder by receivedat desc\nlimit 100\n)\n\nselect req.*, resp.receivedat as response_time, resp.origin as response_origin, resp.event.payload as reponse_payload\nfrom req\njoin ocpp_logs resp on (resp.event.messageid = req.messageid and req.ppid = resp.metadata.ppid and resp.event.messagetype != 'REQ')\norder by request_time desc", "refId": "A", "table": "ocpp_logs"}], "title": "Staging - OCPP Requests & Responses", "type": "table"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"Available": {"color": "blue", "index": 0, "text": "Pilot A"}, "Charging": {"color": "green", "index": 1, "text": "Pilot C"}, "Finishing": {"color": "yellow", "index": 4, "text": "B"}, "Preparing": {"color": "yellow", "index": 3, "text": "B"}, "SuspendedEV": {"color": "yellow", "index": 2, "text": "Pilot B"}}, "type": "value"}], "noValue": "bananas", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 13, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "textMode": "auto"}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "hide": false, "metrics": [{"id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "context.chargePointId:\"HIL-2_TEST_17\" AND context.action:\"StatusNotification\"", "refId": "A", "timeField": "timestamp"}], "title": "# Occurrences in each Pilot State", "transformations": [{"id": "convertFieldType", "options": {"conversions": [{"dateFormat": "YYYY-MM-DD hh:mm:ss.N", "destinationType": "time", "targetField": "timestamp"}], "fields": {}}}, {"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "context.connectionId": true, "context.messageId": false, "env": true, "highlight": true, "level": true, "message": true, "project": true, "sort": true}, "indexByName": {}, "renameByName": {}}}, {"id": "extractFields", "options": {"source": "context.payload"}}, {"id": "filterByValue", "options": {"filters": [{"config": {"id": "equal", "options": {"value": 1}}, "fieldName": "connectorId"}, {"config": {"id": "notEqual", "options": {"value": "Finishing"}}, "fieldName": "status"}, {"config": {"id": "notEqual", "options": {"value": "Preparing"}}, "fieldName": "status"}], "match": "all", "type": "include"}}, {"id": "groupBy", "options": {"fields": {"context.messageId": {"aggregations": ["distinctCount"], "operation": "aggregate"}, "status": {"aggregations": [], "operation": "groupby"}, "timestamp": {"aggregations": ["min", "step"]}}}}, {"id": "organize", "options": {"excludeByName": {"connectorId": true, "context.action": true, "context.chargePointId": true, "context.payload": true, "errorCode": true}, "indexByName": {}, "renameByName": {}}}], "type": "stat"}, {"datasource": {"type": "datasource", "uid": "-- Mixed --"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"fillOpacity": 70, "lineWidth": 0, "spanNulls": false}, "mappings": [{"options": {"Available": {"color": "blue", "index": 0, "text": "A"}, "Charging": {"color": "green", "index": 1, "text": "C"}, "Finishing": {"color": "yellow", "index": 5, "text": "B1"}, "Preparing": {"color": "yellow", "index": 4, "text": "B1"}, "SuspendedEV": {"color": "yellow", "index": 2, "text": "B2"}, "SuspendedEVSE": {"color": "yellow", "index": 3, "text": "B1"}}, "type": "value"}], "noValue": "bananas", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 12, "options": {"alignValue": "center", "legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "mergeValues": true, "rowHeight": 0.9, "showValue": "always", "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "hide": false, "metrics": [{"id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "context.chargePointId:\"HIL-2_TEST_17\" AND context.action:\"StatusNotification\"", "refId": "A", "timeField": "timestamp"}], "title": "Pilot State Timeline", "transformations": [{"id": "convertFieldType", "options": {"conversions": [{"dateFormat": "YYYY-MM-DD hh:mm:ss.N", "destinationType": "time", "targetField": "timestamp"}], "fields": {}}}, {"id": "organize", "options": {"excludeByName": {"_id": true, "_index": true, "_type": true, "context.connectionId": true, "context.messageId": true, "env": true, "highlight": true, "level": true, "message": true, "project": true, "sort": true}, "indexByName": {}, "renameByName": {"timestamp": "time"}}}, {"id": "extractFields", "options": {"source": "context.payload"}}, {"id": "filterByValue", "options": {"filters": [{"config": {"id": "equal", "options": {"value": 1}}, "fieldName": "connectorId"}], "match": "all", "type": "include"}}, {"disabled": true, "id": "groupBy", "options": {"fields": {"context.messageId": {"aggregations": ["distinctCount"], "operation": "aggregate"}, "status": {"aggregations": [], "operation": "groupby"}, "timestamp": {"aggregations": [], "operation": "aggregate"}}}}, {"id": "organize", "options": {"excludeByName": {"connectorId": true, "context.action": true, "context.chargePointId": true, "context.payload": true, "errorCode": true}, "indexByName": {}, "renameByName": {}}}, {"id": "prepareTimeSeries", "options": {}}], "type": "state-timeline"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 10, "options": {"footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "metrics": [{"id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "project:\"Pod Point API v3\" AND message:\"<PERSON><PERSON> claimed charge\" AND context.pod:(\"3697\",\"128612\",\"59654\",\"59655\",\"5967\",\"6031\",\"6382\",\"5920\",\"6222\",\"6307\",\"15508\",\"4164\",\"135655\",\"135659\",\"135656\",\"135658\",\"139763\",\"139762\",\"27547\",\"135657\",\"140290\",\"27548\",\"27549\",\"2984\",\"4106\",\"4283\",\"6534\",\"3936\",\"3937\",\"75709\",\"4410\",\"3849\",\"71746\",\"71749\",\"71751\",\"71748\",\"71747\",\"71750\",\"4789\",\"45544\",\"45545\",\"172345\",\"138373\",\"3699\",\"5143\",\"5144\",\"5145\",\"5146\",\"6049\",\"5142\",\"5611\",\"3179\",\"3578\",\"3671\",\"3672\",\"3673\",\"2815\",\"4487\",\"4488\",\"4489\",\"16334\",\"16335\",\"17329\",\"16336\",\"18417\",\"174862\",\"174860\",\"5393\",\"5381\",\"5645\",\"5614\",\"149297\",\"149298\",\"149299\",\"6391\",\"6392\",\"45141\",\"45140\",\"65652\",\"65651\",\"19758\",\"19757\",\"22178\",\"22177\",\"22176\",\"22179\",\"4500\",\"6051\",\"28723\",\"5150\",\"5141\",\"5168\",\"5140\",\"5192\",\"5153\",\"5600\",\"5172\",\"15615\",\"15614\",\"5705\",\"5706\",\"39610\",\"39604\",\"39607\",\"39608\",\"39611\",\"39613\",\"39605\",\"39606\",\"39612\",\"39609\",\"63649\",\"63647\",\"63644\",\"63643\",\"63645\",\"63646\",\"54322\",\"54324\",\"54323\",\"54335\",\"54334\",\"4468\",\"4469\",\"137346\",\"137345\",\"137347\",\"137348\",\"125409\",\"125408\",\"3183\",\"3113\",\"3182\",\"3111\",\"5403\",\"5399\",\"5400\",\"4579\",\"4078\",\"4079\",\"15419\",\"15418\",\"3153\",\"3154\",\"4872\")", "refId": "A", "timeField": "timestamp"}], "title": "Unique users per Location", "transformations": [{"id": "groupBy", "options": {"fields": {"context.authoriser.id": {"aggregations": ["count", "distinctCount"], "operation": "aggregate"}, "context.authoriser.type": {"aggregations": [], "operation": "groupby"}, "context.pod": {"aggregations": [], "operation": "groupby"}}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "context.authoriser.type"}]}}, {"id": "groupBy", "options": {"fields": {"context.authoriser.id (count)": {"aggregations": ["sum"], "operation": "aggregate"}, "context.authoriser.id (distinctCount)": {"aggregations": ["sum"], "operation": "aggregate"}, "context.authoriser.type": {"aggregations": ["allValues"], "operation": "aggregate"}, "context.pod": {"aggregations": [], "operation": "groupby"}}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"context.authoriser.id (count) (sum)": 3, "context.authoriser.id (distinctCount) (sum)": 2, "context.authoriser.type (allValues)": 1, "context.pod": 0}, "renameByName": {"context.authoriser.id (count)": "Total number of claim attempts ", "context.authoriser.id (count) (sum)": "Total claim attempts", "context.authoriser.id (distinctCount)": "Number of Distinct Users", "context.authoriser.id (distinctCount) (sum)": "# Unique Users ", "context.authoriser.type": "Claim type (user/guest/rfid/contactless)", "context.authoriser.type (allValues)": "Claim authorisation type/s", "context.pod": "Pod Location ID"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": true, "field": "# Unique Users "}]}}, {"id": "convertFieldType", "options": {"conversions": [{"destinationType": "string", "targetField": "Claim authorisation type/s"}], "fields": {}}}], "type": "table"}, {"datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 8, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.4.7", "targets": [{"alias": "", "bucketAggs": [], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "hide": false, "metrics": [{"hide": false, "id": "1", "settings": {"size": "500"}, "type": "raw_data"}], "query": "context.action:\"RemoteStartTransaction\" OR context.action:\"StartTransaction\"", "refId": "A", "timeField": "timestamp"}, {"alias": "", "bucketAggs": [{"field": "timestamp", "id": "2", "settings": {"interval": "auto"}, "type": "date_histogram"}], "datasource": {"type": "elasticsearch", "uid": "OsAppLogs"}, "hide": true, "metrics": [{"id": "1", "type": "count"}], "query": "GET _search\n{\n  \"query\": {\n    \"match\": {\n      \"context.action\": \"RemoteStartTransaction\"\n    }\n  }\n}", "refId": "B", "timeField": "timestamp"}], "title": "RemoteStarts", "transformations": [{"id": "extractFields", "options": {"format": "json", "source": "context.payload"}}, {"id": "groupBy", "options": {"fields": {"connectorId": {"aggregations": [], "operation": "groupby"}, "context.action": {"aggregations": [], "operation": "groupby"}, "context.chargePointId": {"aggregations": [], "operation": "groupby"}, "idTag": {"aggregations": [], "operation": "groupby"}, "message": {"aggregations": ["count"], "operation": "aggregate"}, "project": {"aggregations": ["count"]}, "timestamp": {"aggregations": [], "operation": "groupby"}, "timestamp 2": {"aggregations": []}}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"connectorId": 2, "context.action": 4, "context.chargePointId": 1, "idTag": 3, "message (count)": 5, "timestamp": 0, "timestamp 2": 6}, "renameByName": {}}}, {"id": "convertFieldType", "options": {"conversions": [{"destinationType": "string", "targetField": "timestamp 2"}], "fields": {}}}, {"id": "merge", "options": {}}], "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "WebSocketConnectionOpenFor_InMinutes"}, "properties": [{"id": "custom.width", "value": 303}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "connectedAt"}, "properties": [{"id": "custom.width", "value": 149}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "WebSocketConnectionOpenFor_InMinutes (last)"}, "properties": [{"id": "custom.width", "value": 239}]}]}, "gridPos": {"h": 8, "w": 10, "x": 0, "y": 32}, "id": 4, "maxDataPoints": 50000, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "connectedAt (lastNotNull)"}]}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber, \nevent.requestContext.connectionId as WebsocketConnectionId, fromMillis(event.requestContext.connectedAt) as connectedAt, ceil(((event.requestContext.requestTimeEpoch - event.requestContext.connectedAt )/1000)/60) as WebSocketConnectionOpenFor_InMinutes,\nfromMillis(event.requestContext.requestTimeEpoch) as requestTimeEpoch,\nevent.requestContext.eventType as eventType\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n", "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-connect"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Old websocket connections", "transformations": [{"id": "groupBy", "options": {"fields": {"WebSocketConnectionOpenFor_InMinutes": {"aggregations": ["last"], "operation": "aggregate"}, "WebsocketConnectionId": {"aggregations": []}, "connectedAt": {"aggregations": ["lastNotNull"], "operation": "aggregate"}, "requestTimeEpoch": {"aggregations": ["last"]}, "serialNumber": {"aggregations": [], "operation": "groupby"}}}}], "type": "table"}, {"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "WebSocketConnectionOpenFor_InMinutes"}, "properties": [{"id": "custom.width", "value": 303}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "connectedAt"}, "properties": [{"id": "custom.width", "value": 149}]}]}, "gridPos": {"h": 8, "w": 10, "x": 0, "y": 40}, "id": 6, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.4.7", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwCsConnProd"}, "expression": "fields @timestamp, event.requestContext.authorizer.pcbSerialNumber as serialNumber, event.requestContext.connectionId as WebsocketConnectionId, fromMillis(event.requestContext.connectedAt) as connectedAt, ceil(((event.requestContext.requestTimeEpoch - event.requestContext.connectedAt )/1000)/60) as WebSocketConnectionOpenFor_InMinutes\n| filter @type not like 'REPORT' \n| filter @type not like 'START' \n| filter @type not like 'END' \n", "id": "", "logGroupNames": ["/aws/lambda/csms-websockets-handlers-on-message"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Last(can be the current) WebSocket Connection Open for", "transformations": [{"id": "groupBy", "options": {"fields": {"WebSocketConnectionOpenFor_InMinutes": {"aggregations": ["last"], "operation": "aggregate"}, "WebsocketConnectionId": {"aggregations": []}, "serialNumber": {"aggregations": [], "operation": "groupby"}}}}], "type": "table"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": {"type": "datasource", "uid": "grafana"}, "definition": "", "hide": 0, "includeAll": false, "multi": false, "name": "query0", "options": [], "query": "", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Shauna - Sandbox", "uid": "4hbYhk94k", "version": 22, "weekStart": ""}