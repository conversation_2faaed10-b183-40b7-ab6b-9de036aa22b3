{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 11, "links": [], "liveNow": false, "panels": [{"gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "id": 59, "libraryPanel": {"name": "Application Logs Count", "uid": "LibPanAppLogCount"}, "title": "Application Logs Count"}, {"collapsed": false, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 55, "panels": [], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Chargepoint messages", "type": "row"}, {"datasource": {"type": "elasticsearch", "uid": "OsLoggCommsProd"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "# of messages received", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 67, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": 3600000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 9}, "id": 57, "options": {"legend": {"calcs": ["min", "max", "mean", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"alias": "OCPP 1.6", "bucketAggs": [{"field": "timestamp", "id": "2", "settings": {"interval": "5m"}, "type": "date_histogram"}], "datasource": {"type": "elasticsearch", "uid": "OsLoggCommsProd"}, "metrics": [{"id": "1", "type": "count"}], "query": "_exists_:ppid AND NOT _exists_:nonce", "refId": "A", "timeField": "timestamp"}, {"alias": "OCPP 1.5", "bucketAggs": [{"field": "timestamp", "id": "2", "settings": {"interval": "5m"}, "type": "date_histogram"}], "datasource": {"type": "elasticsearch", "uid": "OsLoggCommsProd"}, "hide": false, "metrics": [{"id": "1", "type": "count"}], "query": "_exists_:ppid AND _exists_:nonce", "refId": "B", "timeField": "timestamp"}], "title": "OCPP messages (Production)", "type": "timeseries"}, {"collapsed": true, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 36, "panels": [{"gridPos": {"h": 6, "w": 2, "x": 0, "y": 1}, "id": 34, "libraryPanel": {"name": "EC2 - Health Nodes", "uid": "LibPanEc2HealthNode"}}, {"gridPos": {"h": 6, "w": 2, "x": 2, "y": 1}, "id": 38, "libraryPanel": {"name": "EC2 - Unhealthy Nodes", "uid": "LibPanEc2UnhealthNode"}}, {"gridPos": {"h": 6, "w": 13, "x": 4, "y": 1}, "id": 40, "libraryPanel": {"name": "EC2 - HTTP Response Codes", "uid": "LibPanEc2HttpResponse"}}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The number of requests processed over IPv4 and IPv6. This metric is only incremented for requests where the load balancer node was able to choose a target. Requests rejected before a target is chosen (for example, HTTP 460, HTTP 400, some kinds of HTTP 503 and 500) are not reflected in this metric.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 49, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 3600000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue"}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "DatabaseConnections"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "eu-west-1c"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-yellow", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 7, "x": 17, "y": 1}, "id": 42, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"LoadBalancer": "$service_alb", "TargetGroup": "targetgroup/$service_tg"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "RequestCount", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Service Request Count", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "TargetGroup"}}], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The time elapsed, in seconds, after the request leaves the load balancer until a response from the target is received. This is equivalent to the target_processing_time field in the access logs.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 49, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 7}, "id": 44, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"LoadBalancer": "$service_alb", "TargetGroup": "targetgroup/$service_tg"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "TargetResponseTime", "metricQueryType": 0, "namespace": "AWS/ApplicationELB", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Target Response Time (s)", "type": "timeseries"}, {"gridPos": {"h": 5, "w": 12, "x": 12, "y": 7}, "id": 47, "libraryPanel": {"name": "EC2 - Target Connection Error Count", "uid": "LibPanEc2TargConnErrCnt"}}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The percentage of allocated EC2 compute units that are currently in use on the instance. This metric identifies the processing power required to run an application on a selected instance.\n\nDepending on the instance type, tools in your operating system can show a different percentage than CloudWatch when the instance is not allocated a full processor core.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 49, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 3600000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-green"}, {"color": "dark-green", "value": 0}, {"color": "semi-dark-orange", "value": 60}, {"color": "dark-red", "value": 80}]}, "unit": "percent"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "DatabaseConnections"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 10, "x": 0, "y": 12}, "id": 49, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"InstanceId": "$instance_ids"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "CPUUtilization", "metricQueryType": 0, "namespace": "AWS/EC2", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Node CPU Utilizstion (%)", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "InstanceId"}}], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The number of bytes received by the instance on all network interfaces. This metric identifies the volume of incoming network traffic to a single instance.\n\nThe number reported is the number of bytes received during the period. If you are using basic (5-minute) monitoring and the statistic is Sum, you can divide this number by 300 to find Bytes/second. If you have detailed (1-minute) monitoring and the statistic is Sum, divide it by 60.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 49, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 3600000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue"}]}, "unit": "Bps"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "DatabaseConnections"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 7, "x": 10, "y": 12}, "id": 51, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"InstanceId": "$instance_ids"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "NetworkIn", "metricQueryType": 0, "namespace": "AWS/EC2", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Network In", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "InstanceId"}}], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The number of bytes sent out by the instance on all network interfaces. This metric identifies the volume of outgoing network traffic from a single instance.\n\nThe number reported is the number of bytes sent during the period. If you are using basic (5-minute) monitoring and the statistic is Sum, you can divide this number by 300 to find Bytes/second. If you have detailed (1-minute) monitoring and the statistic is Sum, divide it by 60.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 49, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 3600000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue"}]}, "unit": "Bps"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "DatabaseConnections"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 7, "x": 17, "y": 12}, "id": 53, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"InstanceId": "$instance_ids"}, "expression": "", "id": "", "matchExact": false, "metricEditorMode": 0, "metricName": "NetworkOut", "metricQueryType": 0, "namespace": "AWS/EC2", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Average"}], "title": "Network Out", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "InstanceId"}}], "type": "timeseries"}], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "EC2 / ALB", "type": "row"}, {"collapsed": true, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 26, "panels": [{"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The number of times that your function code is invoked, including successful invocations and invocations that result in a function error. Invocations aren't recorded if the invocation request is throttled or otherwise results in an invocation error. This equals the number of requests billed", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 81, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "string"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["ocpp-lambda-prod-messageHandler"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 6, "w": 11, "x": 0, "y": 2}, "id": 29, "options": {"legend": {"calcs": ["min", "max", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single"}}, "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "$lambda_function"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Invocations", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Invocations", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "FunctionName"}}], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The amount of time that your function code spends processing an event. The billed duration for an invocation is the value of Duration rounded up to the nearest millisecond.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 81, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ms"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["ocpp-lambda-prod-messageHandler"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 6, "w": 13, "x": 11, "y": 2}, "id": 31, "options": {"legend": {"calcs": ["min", "max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "$lambda_function"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Duration", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Duration", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "FunctionName"}}], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "1. Errors:\nThe number of times that your function code is invoked, including successful invocations and invocations that result in a function error. Invocations aren't recorded if the invocation request is throttled or otherwise results in an invocation error. This equals the number of requests billed\n\n2. Dead Letters: For asynchronous invocation, the number of times that Lambda attempts to send an event to a dead-letter queue but fails. Dead-letter errors can occur due to permissions errors, misconfigured resources, or size limits.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 81, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "string"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "CPUUtilization"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-orange", "mode": "fixed"}}]}, {"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["DeadLetterErrors - ocpp-lambda-prod-connectionHandler"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 6, "w": 14, "x": 0, "y": 8}, "id": 30, "options": {"legend": {"calcs": ["min", "max", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single"}}, "targets": [{"alias": "{{metric}} - {{FunctionName}}", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "$lambda_function"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "Errors", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}, {"alias": "{{metric}} - {{FunctionName}}", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "$lambda_function"}, "expression": "", "hide": false, "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "DeadLetterErrors", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "B", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Errors & Dead Letters", "transformations": [], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "The number of function instances that are processing events. If this number reaches your concurrent executions quota for the Region, or the reserved concurrency limit that you configured on the function, Lambda throttles additional invocation requests.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-BlYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 81, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 6, "w": 10, "x": 14, "y": 8}, "id": 32, "options": {"legend": {"calcs": ["min", "max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.2.5", "targets": [{"alias": "", "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "dimensions": {"FunctionName": "$lambda_function"}, "expression": "", "id": "", "matchExact": true, "metricEditorMode": 0, "metricName": "ConcurrentExecutions", "metricQueryType": 0, "namespace": "AWS/Lambda", "period": "", "queryMode": "Metrics", "refId": "A", "region": "default", "sqlExpression": "", "statistic": "Sum"}], "title": "Concurrent Executions", "transformations": [{"id": "labelsToFields", "options": {"valueLabel": "FunctionName"}}], "type": "timeseries"}, {"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "description": "Anything that doesn't match normal behaviour", "gridPos": {"h": 5, "w": 24, "x": 0, "y": 14}, "id": 28, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": true}, "pluginVersion": "8.2.5", "targets": [{"datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "expression": "fields @timestamp, @type, @message, @ingestionTime, @logStream\n| filter @message not like 'INFO' \n| filter @message not like 'REPORT'\n| filter @message not like 'END'\n| filter @message not like 'START'\n| limit 20", "id": "", "logGroupNames": ["/aws/lambda/$lambda_function"], "namespace": "", "queryMode": "Logs", "refId": "A", "region": "default", "statsGroups": []}], "title": "Lambda Anomolies", "transformations": [], "type": "logs"}], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "Lambda", "type": "row"}, {"collapsed": true, "datasource": {"type": "datasource", "uid": "grafana"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 2, "panels": [{"gridPos": {"h": 11, "w": 6, "x": 0, "y": 3}, "id": 6, "libraryPanel": {"name": "RDS - Database Connections (Count)", "uid": "LibPanRdsDbConn"}}, {"gridPos": {"h": 6, "w": 12, "x": 6, "y": 3}, "id": 4, "libraryPanel": {"name": "CPU Utilisation (%)", "uid": "LibPanRdsCpuUtil"}}, {"gridPos": {"h": 5, "w": 6, "x": 18, "y": 3}, "id": 10, "libraryPanel": {"name": "RDS - Free Storage Space (MB)", "uid": "LibPanRdsFreeStor"}}, {"gridPos": {"h": 6, "w": 6, "x": 18, "y": 8}, "id": 12, "libraryPanel": {"name": "RDS - <PERSON><PERSON> (Count)", "uid": "LibPanRdsQueueDepth"}}, {"gridPos": {"h": 5, "w": 12, "x": 6, "y": 9}, "id": 8, "libraryPanel": {"name": "RDS - Freeable Memory (MB)", "uid": "LibPanRdsFreeMem"}}, {"gridPos": {"h": 5, "w": 8, "x": 0, "y": 14}, "id": 24, "libraryPanel": {"name": "RDS - Write Operations (iops)", "uid": "LibPanRdsWriteOps"}}, {"gridPos": {"h": 5, "w": 8, "x": 8, "y": 14}, "id": 20, "libraryPanel": {"name": "RDS - Write Latency (ms)", "uid": "LibPanRdsWriteLat"}}, {"gridPos": {"h": 5, "w": 8, "x": 16, "y": 14}, "id": 22, "libraryPanel": {"name": "RDS - Write Throughput (bytes/sec(SI))", "uid": "LibPanRdsWriteThro"}}, {"gridPos": {"h": 5, "w": 8, "x": 0, "y": 19}, "id": 16, "libraryPanel": {"name": "RDS - Read Operations (iops)", "uid": "LibPanRdsReadOps"}}, {"gridPos": {"h": 5, "w": 8, "x": 8, "y": 19}, "id": 14, "libraryPanel": {"name": "RDS - Read Latency (ms)", "uid": "LibPanRdsReadLat"}}, {"gridPos": {"h": 5, "w": 8, "x": 16, "y": 19}, "id": 18, "libraryPanel": {"name": "RDS - Read Throughput (bytes/sec(SI))", "uid": "LibPanRdsReadThro"}}], "targets": [{"datasource": {"type": "datasource", "uid": "grafana"}, "refId": "A"}], "title": "RDS", "type": "row"}], "refresh": "", "revision": 1, "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": [{"hide": 2, "label": "Service", "name": "service", "query": "ocpp", "skipUrlSync": false, "type": "constant"}, {"current": {"selected": true, "text": "prod", "value": "prod"}, "hide": 0, "includeAll": false, "label": "Stage", "multi": false, "name": "stage", "options": [{"selected": false, "text": "staging", "value": "staging"}, {"selected": true, "text": "prod", "value": "prod"}], "query": "staging,, prod", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"current": {"selected": false, "text": "auto", "value": "auto"}, "description": "A period is the length of time associated with a specific Amazon CloudWatch statistic. Periods are defined in numbers of seconds, and valid values for period are 1, 5, 10, 30, or any multiple of 60.", "hide": 0, "includeAll": false, "label": "RDS Interval", "multi": false, "name": "rds_interval", "options": [{"selected": true, "text": "auto", "value": "auto"}, {"selected": false, "text": "60", "value": "60"}, {"selected": false, "text": "120", "value": "120"}, {"selected": false, "text": "180", "value": "180"}, {"selected": false, "text": "240", "value": "240"}, {"selected": false, "text": "300", "value": "300"}, {"selected": false, "text": "360", "value": "360"}, {"selected": false, "text": "420", "value": "420"}], "query": "auto, 60, 120, 180, 240, 300, 360, 420", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"current": {"selected": false, "text": "ocpp-lambda-prod-messageHandler", "value": "ocpp-lambda-prod-messageHandler"}, "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "definition": "resource_arns(eu-west-1, lambda, {\"Grafana\":[\"$service-$stage\"]})", "description": "Retrieves a list of lambda functions. The functions must have the following tag key <PERSON><PERSON>, and the values must match the current criteria. <service>-<stage>", "hide": 0, "includeAll": true, "label": "Lambda Functions", "multi": true, "name": "lambda_function", "options": [], "query": "resource_arns(eu-west-1, lambda, {\"Grafana\":[\"$service-$stage\"]})", "refresh": 1, "regex": "/(?<value>[^:]+)+$/", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "definition": "resource_arns(eu-west-1, elasticloadbalancing:loadbalancer,{\"Name\":[\"alb-$stage-services\"]})", "description": "The ALB of the service", "hide": 2, "includeAll": false, "label": "Service ALB", "multi": false, "name": "service_alb", "options": [], "query": "resource_arns(eu-west-1, elasticloadbalancing:loadbalancer,{\"Name\":[\"alb-$stage-services\"]})", "refresh": 1, "regex": "/\\/(?<text>.*)/", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "definition": "resource_arns(eu-west-1, elasticloadbalancing:targetgroup,{\"Name\":[\"$service-$stage-service-tg\"]})", "hide": 2, "includeAll": false, "label": "Service Target Group", "multi": false, "name": "service_tg", "options": [], "query": "resource_arns(eu-west-1, elasticloadbalancing:targetgroup,{\"Name\":[\"$service-$stage-service-tg\"]})", "refresh": 1, "regex": "/\\/(?<text>.*)/", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "cloudwatch", "uid": "CwPodPoint"}, "definition": "ec2_instance_attribute(eu-west-1, InstanceId, { \"tag:Grafana\": [\"$service-$stage\"] });", "description": "instance id's for the current service", "hide": 0, "includeAll": true, "label": "Instance IDs", "multi": true, "name": "instance_ids", "options": [], "query": "ec2_instance_attribute(eu-west-1, InstanceId, { \"tag:Grafana\": [\"$service-$stage\"] });", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"hide": 2, "label": "Project Name", "name": "project_name", "query": "Pod Point OCPP Lambda", "skipUrlSync": false, "type": "constant"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "OCPP", "uid": "536BbaB7k", "version": 19, "weekStart": ""}