resource "grafana_folder" "abb_ocpp_1_6" {
  title = "ABB OCPP 1.6 Monitoring"
  uid   = "fldr_abb_ocpp_1_6"
}

resource "grafana_folder" "asset_service" {
  title = "Asset Service"
  uid   = "fldr_asset_service"
}

resource "grafana_folder" "chargepoints_communications" {
  title = "Chargepoint Communications"
  uid   = "fldr_chargepoints_communications"
}

resource "grafana_folder" "charger_network_infrastructure" {
  title = "Charger Network Infrastructure"
  uid   = "fldr_charger_network_infrastructure"
}

resource "grafana_folder" "customer_support" {
  title = "Customer Support"
  uid   = "fldr_customer_support"
}

resource "grafana_folder" "experience" {
  title = "Experience"
  uid   = "fldr_experiences"
}

resource "grafana_folder" "firmware_upgrade" {
  title = "Firmware Upgrade"
  uid   = "fldr_firmware_upgrade"
}

resource "grafana_folder" "mobile_squad" {
  title = "Mobile Squad"
  uid   = "fldr_mobile_squad"
}

resource "grafana_folder" "network_connectivity" {
  title = "Network Connectivity"
  uid   = "fldr_network_connectivity"
}

resource "grafana_folder" "network_domain" {
  title = "Network Domain"
  uid   = "fldr_network_domain"
}

resource "grafana_folder" "network_state" {
  title = "Network State"
  uid   = "fldr_network_state"
}

resource "grafana_folder" "ocpp_comms" {
  title = "OCPP Comms"
  uid   = "fldr_ocpp_comms"
}

resource "grafana_folder" "ownership_domain" {
  title = "Ownership Domain"
  uid   = "fldr_ownership_domain"
}

resource "grafana_folder" "pki" {
  title = "PKI"
  uid   = "fldr_pki"
}
