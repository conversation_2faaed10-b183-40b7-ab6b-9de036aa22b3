resource "aws_route53_record" "this" {
  provider = aws.pod-point

  zone_id         = local.pod_point_com_hosted_zone
  type            = "A"
  name            = "${local.identifier}-dev.${local.pod_point_com_domain_name}"
  allow_overwrite = true

  alias {
    name                   = module.laravel_app_lb.load_balancer_dns_name
    zone_id                = module.laravel_app_lb.load_balancer_zone_id
    evaluate_target_health = true
  }
}
