module "airbyte" {
  source                          = "../../modules/applications/data_team/airbyte"
  environment                     = local.environment
  vpc_id                          = module.vpc.vpc_id
  vpn_cidr                        = local.vpn_cidr
  private_subnet_ids              = module.vpc.private_subnets_ids
  database_identifier             = "airbyte-staging"
  database_allocated_storage      = 10
  database_max_allocated_storage  = 40
  database_engine_version         = "13.20"
  database_port                   = 5432
  database_instance_type          = "db.t3.small"
  maintenance_window              = "sun:04:00-sun:04:30"
  database_subnet_group           = "rds-private-${local.environment}"
  instance_type                   = "c6a.4xlarge"
  max_sync_workers                = 100
  max_spec_workers                = 100
  max_check_workers               = 100
  max_discover_workers            = 100
  job_main_container_cpu_limit    = "15"
  job_main_container_memory_limit = "30g"
  lb_security_group_id            = module.alb.security_group_id
  # To be replaced when an sns topic is created for staging
  #sns_arn                         = "arn:aws:sns:eu-west-1:************:DataEngineeringNotifySlack"
  depends_on = [aws_kms_alias.ec2]
}

module "airbyte_config" {
  source               = "../../modules/applications/data_team/airbyte/airbyte-config"
  environment          = local.environment
  airbyte_instance_arn = module.airbyte.airbyte_instance_arn
  data_account_id_dev  = local.data_account_id_dev
  pod_point_account_id = local.pod_point_account_id
}
