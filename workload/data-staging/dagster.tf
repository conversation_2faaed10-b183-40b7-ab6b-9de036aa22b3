module "dagster" {
  source               = "../../modules/applications/data_team/dagster"
  environment          = local.environment
  github_repo_names    = ["dagster-ecs", "dagster-reports"]
  vpc_id               = module.vpc.vpc_id
  vpn_cidr             = local.vpn_cidr
  vpc_cidr_block       = module.vpc.vpc_cidr_block
  private_subnet_ids   = module.vpc.private_subnets_ids
  domain_url           = "dagster-staging.pod-point.com"
  execution_role_arn   = aws_iam_role.ecs_task_execution.arn
  cloudwatch_log_days  = 30
  target_group_arn     = module.alb.target_group_arns[2]
  lb_security_group_id = module.alb.security_group_id
  daemon               = local.daemon
  dagit                = local.dagit

  dagster_instance_migrate = {
    "cpu"                = 256
    "memory"             = 512
    "desired_count"      = 0
    "port"               = 3000
    "tag"                = "test"
    "ecr_repository_url" = module.ecr_repos.repo_urls["dagster/dagster_instance_migrate"]
  }

  // GRPC - Extend code block
  create_grpc = true

  grpc = {
    "reports" = {
      "name"               = "reports"
      "cpu"                = 1024
      "memory"             = 2048
      "port"               = 4000
      "tag"                = "c896c9f66ec16d581293a2b736c58bde6eaf3ebe-pacesetterplus-2024-01-12-08-32"
      "ecr_repository_url" = module.ecr_repos.repo_urls["dagster/dagster-reports"]
    }
  }

  // Database
  database_multi_az              = false
  database_skip_final_snapshot   = true
  database_subnet_group          = "rds-private-${local.environment}"
  database_identifier            = "dagster-log-storage-${local.environment}"
  database_allocated_storage     = 20
  database_max_allocated_storage = 40
  database_instance_type         = "db.t3.small"
  database_engine_version        = "13.15"
  auto_minor_version_upgrade     = false

  additional_kms_administrators = data.aws_iam_roles.administrator_and_software.arns
}
