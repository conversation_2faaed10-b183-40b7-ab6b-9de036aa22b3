locals {
  development_account_id = "************"
  staging_account_id     = "************"
  production_account_id  = "************"

  charge_sessions_components = [
    "migrations",
    "ocpp16-consumer",
    "transactions-api",
  ]

  default_tags = {
    // Mandatory tags
    "pp:environment"                      = "production"
    "pp:owner"                            = "network:connectivity"
    "pp:domain"                           = "network"
    "pp:service"                          = "charge-sessions"
    "pp:terraformWorkspace"               = "cs-charge-sessions-build"
    "pp:terraformConfigurationRepository" = "pod-point/terraform"
  }
}
