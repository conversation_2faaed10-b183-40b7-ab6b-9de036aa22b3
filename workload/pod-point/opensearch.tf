/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *         Chargepoints
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

module "chargepoints_testing" {
  source      = "../../modules/applications/shared/opensearch/chargepoints"
  environment = "testing"
  access_policy_ips = [
    "*************",
    "***********",  # New org VPN
    "************", # New org VPN
    "*************" # New org VPN
  ]
  access_policy_roles = ["arn:aws:iam::959744386191:role/lambda_basic_execution"]
}

module "chargepoints_staging" {
  source      = "../../modules/applications/shared/opensearch/chargepoints"
  environment = "staging"
  access_policy_ips = [
    "*************",
    "***********",  # New org VPN
    "************", # New org VPN
    "*************" # New org VPN
  ]
  access_policy_roles = [
    "arn:aws:iam::959744386191:role/mis-staging-web-app-ecs-task",
    "arn:aws:iam::959744386191:role/mis-staging-default-consumer-ecs-task",
    "arn:aws:iam::959744386191:role/lambda_basic_execution"
  ]
}

module "chargepoints_prod" {
  source      = "../../modules/applications/shared/opensearch/chargepoints"
  environment = "production"
  access_policy_ips = [
    "***********",  # New org VPN
    "************", # New org VPN
    "*************" # New org VPN
  ]
  access_policy_roles = [
    "arn:aws:iam::959744386191:role/mis-prod-default-consumer-ecs-task",
    "arn:aws:iam::959744386191:role/lambda_basic_execution",
    "arn:aws:iam::959744386191:role/mis-prod-web-app-ecs-task"
  ]
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *      Logging Comms - VPC
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

import {
  to = module.logging_comms_vpc_staging.aws_cloudwatch_log_group.this_firehose
  id = "/aws/kinesisfirehose/logging-comms-vpc-staging-firehose"
}

import {
  to = module.logging_comms_vpc_staging.aws_vpc_security_group_egress_rule.allow_logging_comms_external
  id = "sgr-06b50fa168b90cd21"
}

import {
  to = module.logging_comms_vpc_staging.aws_cloudwatch_log_stream.this_firehose_error_logs
  id = "/aws/kinesisfirehose/logging-comms-vpc-staging-firehose:DestinationDelivery"
}

module "logging_comms_vpc_staging" {
  source                                     = "../../modules/applications/shared/opensearch/logging-comms/vpc/staging"
  endpoint_subnet_ids                        = module.vpc.private_subnets_ids
  endpoint_security_group_mis_web_app        = "sg-08c5a419848781154" # Fargate MIS staging security group
  endpoint_security_group_mis_scheduled_task = "sg-04dc21e0192db835c" # Fargate MIS scheduled task staging security group
  logging_comms_managed_grafana_cidr         = "**********/24"
}


output "logging_comms_vpc_endpoint_staging" {
  description = "The VPC endpoint address for the staging logging comms opensearch cluster"
  value       = module.logging_comms_vpc_staging.vpc_endpoint.endpoint
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *       Application Logs
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

module "application_logs" {
  source = "../../modules/applications/shared/opensearch/application-logs"
  access_policy_ips = [
    "***********",  # New org VPN
    "************", # New org VPN
    "*************" # New org VPN
  ]
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *             eol
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

module "eol_prod" {
  source = "../../modules/applications/shared/opensearch/eol"
  access_policy_ips = [
    "*************/22",
    "************/24",
    "***********",   # New org VPN
    "************",  # New org VPN
    "*************", # New org VPN
    "*************/32",
    "**************/32",
    "***********/32",
    "************/32",
    "************/32"
  ]
}
