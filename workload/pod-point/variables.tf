variable "pod_point_account_id" {
  description = "The account ID to assume when initialising the AWS provider."
  type        = string
  default     = "************"
}

variable "assume_role_name" {
  description = "The role to assume when initialising the AWS provider."
  type        = string
  default     = "terraform-ci"
}

variable "enable_nat_gateway_flow_logs" {
  description = "whether to enable flow logs on the nat gateway interfaces"
  type        = bool
  default     = false
}

variable "keep_nat_gateway_flow_logs" {
  description = "Whether to keep the CW Logs for NAT Gateway interface flow logs."
  type        = bool
  default     = true
}