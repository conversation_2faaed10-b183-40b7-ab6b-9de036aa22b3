import boto3
import json

print('Loading function')

rds = boto3.client('rds')

def lambda_handler(event, context):
    
    source_snapshot_identifier = event['sourceSnapshotIdentifier']
    
    # Delete manual cluster snapshot copy
    rds.delete_db_cluster_snapshot(
        DBClusterSnapshotIdentifier = source_snapshot_identifier
    )
    
    print('Snapshot ', source_snapshot_identifier, 'deletion triggered.')
    
    return {
        'sourceSnapshotIdentifier': source_snapshot_identifier,
        'body': 'Snapshot ' + source_snapshot_identifier + ' deletion triggered.'
    }