data "aws_iam_policy_document" "cdk_dry_run_github_actions_role_assume_role" {
  statement {
    effect = "Allow"
    actions = [
      "sts:AssumeRoleWithWebIdentity"
    ]
    principals {
      type        = "Federated"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/token.actions.githubusercontent.com"]
    }
    condition {
      test     = "StringEquals"
      variable = "token.actions.githubusercontent.com:aud"
      values   = ["sts.amazonaws.com"]
    }
    condition {
      test     = "StringLike"
      variable = "token.actions.githubusercontent.com:sub"
      values = [
        "repo:Pod-Point/ocpi-service:*"
      ]
    }
  }
}

data "aws_iam_policy_document" "cdk_dry_run_github_actions_role_policy" {
  statement {
    effect = "Allow"
    actions = [
      "ec2:DescribeAccountAttributes",
      "ec2:DescribeAddresses",
      "ec2:DescribeCarrierGateways",
      "ec2:DescribeClassicLinkInstances",
      "ec2:DescribeCustomerGateways",
      "ec2:DescribeDhcpOptions",
      "ec2:DescribeEgressOnlyInternetGateways",
      "ec2:DescribeFlowLogs",
      "ec2:DescribeInternetGateways",
      "ec2:DescribeLocalGatewayRouteTables",
      "ec2:DescribeLocalGatewayRouteTableVpcAssociations",
      "ec2:DescribeMovingAddresses",
      "ec2:DescribeNatGateways",
      "ec2:DescribeNetworkAcls",
      "ec2:DescribeNetworkInterfaceAttribute",
      "ec2:DescribeNetworkInterfacePermissions",
      "ec2:DescribeNetworkInterfaces",
      "ec2:DescribePrefixLists",
      "ec2:DescribeRouteTables",
      "ec2:DescribeSecurityGroupReferences",
      "ec2:DescribeSecurityGroupRules",
      "ec2:DescribeSecurityGroups",
      "ec2:DescribeStaleSecurityGroups",
      "ec2:DescribeSubnets",
      "ec2:DescribeTags",
      "ec2:DescribeVpcAttribute",
      "ec2:DescribeVpcClassicLink",
      "ec2:DescribeVpcClassicLinkDnsSupport",
      "ec2:DescribeVpcEndpoints",
      "ec2:DescribeVpcEndpointConnectionNotifications",
      "ec2:DescribeVpcEndpointConnections",
      "ec2:DescribeVpcEndpointServiceConfigurations",
      "ec2:DescribeVpcEndpointServicePermissions",
      "ec2:DescribeVpcEndpointServices",
      "ec2:DescribeVpcPeeringConnections",
      "ec2:DescribeVpcs",
      "ec2:DescribeVpnConnections",
      "ec2:DescribeVpnGateways"
    ]
    resources = [
      "*"
    ]
  }
}

resource "aws_iam_role" "cdk_dry_run_github_actions" {
  name               = "github-actions-cdk-dry-run"
  assume_role_policy = data.aws_iam_policy_document.cdk_dry_run_github_actions_role_assume_role.json
}

resource "aws_iam_policy" "cdk_dry_run_github_actions" {
  name        = "github-actions-cdk-dry-run"
  description = "Policy for GitHub Actions to perform cdk dry runs (cdk synth)"
  policy      = data.aws_iam_policy_document.cdk_dry_run_github_actions_role_policy.json
}

resource "aws_iam_role_policy_attachment" "cdk_dry_run_github_actions" {
  role       = aws_iam_role.cdk_dry_run_github_actions.name
  policy_arn = aws_iam_policy.cdk_dry_run_github_actions.arn
}
