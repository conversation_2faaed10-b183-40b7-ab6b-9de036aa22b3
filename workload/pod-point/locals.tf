locals {
  cs_state_build_account_id        = "************"
  cs_connectivity_build_account_id = "************"
  odp_build_account_id             = "************"
  oa_build_account_id              = "************"
  experience_build_account_id      = "************"
  management_account_id            = data.aws_ssm_parameter.management_account_id.value

  shared_services_account_name = "Shared-services"
  management_account_name      = "Management"

  workload_ou_name = "Workload"

  bucket_random_id = lower(random_id.bucket_suffix.id)

  stackset_template_files = {
    terraform_ci_role        = "stack_templates/terraform-ci.yaml",
    open_id_connect_provider = "stack_templates/open_id_connect_identity_provider.yaml"
    quota_alarms             = "stack_templates/quota_alarms.yaml"
  }
}
