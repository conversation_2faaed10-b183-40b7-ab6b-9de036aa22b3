data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

data "aws_ssm_parameter" "shared_service_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.id}:parameter/account/${local.shared_services_account_name}/id"
}

data "aws_ssm_parameter" "management_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.management_account_name}/id"
}

data "aws_organizations_organization" "this" {
  provider = aws.management
}

data "aws_organizations_organizational_unit" "workload" {
  provider  = aws.management
  parent_id = data.aws_organizations_organization.this.roots[0].id
  name      = local.workload_ou_name
}
