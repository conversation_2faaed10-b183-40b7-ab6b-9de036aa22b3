AWSTemplateFormatVersion: "2010-09-09"
Description: AWS CloudFormation template for setting up an identity provider in all our AWS accounts.

Resources:
  GitHubOpenIDProvider:
    Type: AWS::IAM::OIDCProvider
    Properties:
      Url: https://token.actions.githubusercontent.com
      ClientIdList:
        - sts.amazonaws.com
      ThumbprintList:
        - f879abce0008e4eb126e0097e46620f5aaae26ad
        - 6938fd4d98bab03faadb97b34396831e3780aea1
        - 1c58a3a8518e8759bf075b76b750d4f2df264fcd
      Tags:
        - Key: Owner
          Value: <EMAIL>
        - Key: Environment
          Value: prod
        - Key: Project
          Value: OrganisationInfra
        - Key: "Repo"
          Value: "Pod-Point/terraform"
        - Key: "Workspace"
          Value: "organisation"
