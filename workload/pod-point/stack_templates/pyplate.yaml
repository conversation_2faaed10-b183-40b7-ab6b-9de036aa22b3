---
# This template is based on the PyPlate template from AWS at:
# https://github.com/awslabs/aws-cloudformation-templates/blob/master/aws/services/CloudFormation/MacrosExamples/PyPlate/python.yaml
AWSTemplateFormatVersion: '2010-09-09'

Resources:
  TransformExecutionRole:
    Type: 'AWS::IAM::Role'
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: 'Allow'
            Action:
              - 'sts:AssumeRole'
            Principal:
              Service:
                - 'lambda.amazonaws.com'
      Path: '/'
      Policies:
        - PolicyName: 'root'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              # Allow CloudWatch logging
              - Effect: 'Allow'
                Action:
                  - 'logs:CreateLogGroup'
                  - 'logs:CreateLogStream'
                  - 'logs:PutLogEvents'
                Resource:
                  - !Sub arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/*
              - Effect: 'Allow'
                Action:
                  - 'ec2:DescribeRegions'
                  - 'sts:GetCallerIdentity'
                  - 'cloudformation:ListStackInstances'
                Resource:
                  - '*'

  TransformFunction:
    Type: 'AWS::Lambda::Function'
    Properties:
      Handler: 'index.handler'
      Role: !GetAtt 'TransformExecutionRole.Arn'
      Runtime: 'python3.9'
      Timeout: 30
      Code:
        ZipFile: |
          import traceback
          import json


          def obj_iterate(obj, params):
              if isinstance(obj, dict):
                  for k in obj:
                      obj[k] = obj_iterate(obj[k], params)
              elif isinstance(obj, list):
                  for i, v in enumerate(obj):
                      obj[i] = obj_iterate(v, params)
              elif isinstance(obj, str):
                  if obj.startswith("#!PyPlate"):
                      params['output'] = None
                      exec(obj, params)
                      obj = params['output']

              return obj


          def handler(event, context):
              print(json.dumps(event))

              macro_response = {
                  "requestId": event["requestId"],
                  "status": "success",
              }

              try:
                  params = {
                      "account_id": event["accountId"],
                      "params": event["templateParameterValues"],
                      "region": event["region"],
                      "template": event["fragment"],
                  }

                  response = event["fragment"]
                  macro_response["fragment"] = obj_iterate(response, params)
              except Exception as e:
                  traceback.print_exc()
                  macro_response["status"] = "failure"
                  macro_response["errorMessage"] = str(e)

              return macro_response

  TransformFunctionPermissions:
    Type: 'AWS::Lambda::Permission'
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt 'TransformFunction.Arn'
      Principal: 'cloudformation.amazonaws.com'

  Transform:
    Type: 'AWS::CloudFormation::Macro'
    Properties:
      Name: 'PyPlate'
      Description: 'Processes inline Python in templates'
      FunctionName: !GetAtt 'TransformFunction.Arn'
