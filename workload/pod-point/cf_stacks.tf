

/*
 * Cloudformation stack sets are used to provision resources through CloudFormation to multiple accounts,
 * or even the entire organisation of a management account.
 *
 * NOTE: The management account does not get used for stack sets!
 * ... Therefore; a standard CloudFormation stack must also be provisioned for resources we require in it.
 **/


/*
  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  *             Templates
  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 **/

resource "random_id" "bucket_suffix" {
  byte_length = 8
}

module "stack_set_template_bucket" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.1.2"

  bucket = format("cloudformation-templates-%s", local.bucket_random_id)

  versioning = {
    enabled = true
  }

  server_side_encryption_configuration = {
    rule = {
      bucket_key_enabled = false
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }

  lifecycle_rule = [
    {
      id      = "noncurrent_object_expiration"
      enabled = true

      transitions = {
        first = {
          days          = 30
          storage_class = "STANDARD_IA"
        }
      }

      noncurrent_version_expiration = {
        days = 30
      }
    }
  ]

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_object" "template" {
  for_each = local.stackset_template_files

  bucket                 = module.stack_set_template_bucket.s3_bucket_id
  key                    = format("template/%s.yaml", each.key)
  source                 = each.value
  etag                   = filemd5(each.value)
  server_side_encryption = "AES256"
}

/*
  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  *   CF Stacks - Pod Point / Org AWS Account
  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 **/

resource "aws_cloudformation_stack" "pyplate" {
  name          = "pyplate"
  capabilities  = ["CAPABILITY_NAMED_IAM"]
  template_body = file("stack_templates/pyplate.yaml")
}

resource "aws_cloudformation_stack" "quota_limit_alarms_eu-west-1" {
  name          = "quota-limit-alarms"
  capabilities  = ["CAPABILITY_NAMED_IAM"]
  template_body = file("stack_templates/quota_alarms.yaml")
}

resource "aws_cloudformation_stack" "quota_limit_alarms_us-east-1" {
  provider      = aws.us-east-1
  name          = "quota-limit-alarms"
  capabilities  = ["CAPABILITY_NAMED_IAM"]
  template_body = file("stack_templates/quota_alarms.yaml")
}

resource "aws_cloudformation_stack" "cloud_platform_chatbot" {
  name          = "cloud-platform-chatbot"
  capabilities  = ["CAPABILITY_NAMED_IAM", "CAPABILITY_AUTO_EXPAND"]
  template_body = file("stack_templates/cloud_platform_chatbot.yaml")

  depends_on = [
    aws_cloudformation_stack.pyplate
  ]

  tags = {

    "pp:owner" = "cloud-platform"
  }
}

resource "aws_cloudformation_stack" "data_engineering_chatbot" {
  name          = "data-engineering-chatbot"
  capabilities  = ["CAPABILITY_NAMED_IAM", "CAPABILITY_AUTO_EXPAND"]
  template_body = file("stack_templates/data_engineering_chatbot.yaml")

  depends_on = [
    aws_cloudformation_stack.pyplate
  ]
}

resource "aws_cloudformation_stack" "grid_chatbot" {
  name          = "grid-chatbot"
  capabilities  = ["CAPABILITY_NAMED_IAM", "CAPABILITY_AUTO_EXPAND"]
  template_body = file("stack_templates/grid_chatbot.yaml")

  depends_on = [
    aws_cloudformation_stack.pyplate
  ]
}

resource "aws_cloudformation_stack" "cloud_platform_event_to_sns" {
  name          = "cloud-platform-event-to-sns"
  capabilities  = ["CAPABILITY_NAMED_IAM"]
  template_body = file("stack_templates/cloud_platform_event_to_sns.yaml")

  tags = {

    "pp:owner" = "cloud-platform"
  }
}