locals {
  shared_services_account_name         = "Shared-services"
  experience_account_name              = "experience-prod"
  pod_point_account_name               = "Pod-Point"
  salesforce_account_name              = "salesforce-prod"
  ownership_data_platform_account_name = "ownership-data-platform-prod"
  network_assets_build_account_name    = "network-assets-build"
}

data "aws_caller_identity" "current" {}

data "aws_iam_session_context" "current" {
  arn = data.aws_caller_identity.current.arn
}

data "aws_region" "current" {}

data "terraform_remote_state" "cs_connectivity_prod" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "cs-connectivity-prod"
    }
  }
}

data "terraform_remote_state" "cs_state_prod" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "cs-state-prod"
    }
  }
}

data "aws_iam_roles" "additional_kms_administrators" {
  name_regex = ".*PP-Administrator|PP-Software.*"
}

data "aws_ssm_parameter" "shared_service_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.id}:parameter/account/${local.shared_services_account_name}/id"
}

data "aws_ssm_parameter" "experience_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.experience_account_name}/id"
}

data "aws_ssm_parameter" "salesforce_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.salesforce_account_name}/id"
}

data "aws_ssm_parameter" "ownership_data_platform_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.ownership_data_platform_account_name}/id"
}

data "aws_ssm_parameter" "network_assets_build_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.network_assets_build_account_name}/id"
}
