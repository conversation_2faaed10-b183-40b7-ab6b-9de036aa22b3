data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

data "aws_sns_topic" "cloud_platform_opsgenie" {
  name = "opsgenie-cw-supporting-cloud-platform"
}

data "aws_organizations_organization" "pod_point" {
  provider = aws.org_management_account
}


# These SSM parameters are deployed in the management account via a stackset, you need only a data source to access it.
data "aws_ssm_parameter" "cs_state_dev" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.id}:parameter/account/cs-state-dev/id"
}

data "aws_ssm_parameter" "cs_state_staging" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.id}:parameter/account/cs-state-staging/id"
}

data "aws_ssm_parameter" "cs_state_prod" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.id}:parameter/account/cs-state-prod/id"
}