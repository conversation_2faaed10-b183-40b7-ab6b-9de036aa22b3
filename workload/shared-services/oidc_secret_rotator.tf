/**
 * ~~~~~~~~~~~~~~~~~~~~
 *         ECR
 * ~~~~~~~~~~~~~~~~~~~~
*/

module "oidc_secret_rotator_ecr" {
  source  = "terraform-enterprise.pod-point.com/technology/ecr/aws"
  version = "1.0.0"

  identifier           = local.oidc_secret_rotator_name
  ecr_lifecycle_policy = jsonencode(local.oidc_secret_rotator_ecr_lifecycle)
  # False until terraform enterprise can run docker commands in agents
  create_dummy_image = false

  image_read_access_arns = [
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
  ]

  image_write_access_arns = [
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
  ]

  #   List of github repository names in the pod point organisation that will have access to this ECR.
  repo_names = [local.oidc_secret_rotator_github_repo]

  additional_ecr_policy_statements = [{
    sid = "LambdaECRImageRetrievalPolicy",
    actions = [
      "ecr:BatchGetImage",
      "ecr:GetDownloadUrlForLayer"
    ],
    principals = [
      {
        type        = "Service",
        identifiers = ["lambda.amazonaws.com"]
      }
    ],
    conditions = [
      {
        test     = "StringLike",
        variable = "aws:sourceARN"
        values   = ["arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:*${local.oidc_secret_rotator_name}*"]
      }
    ]
  }]
}

/**
 * ~~~~~~~~~~~~~~~~~~~~
 *       Lambda
 * ~~~~~~~~~~~~~~~~~~~~
*/

module "oidc_secret_rotator_lambda" {
  source     = "terraform-enterprise.pod-point.com/technology/containerised-lambda/aws"
  version    = "1.1.0"
  identifier = local.oidc_secret_rotator_name
  additional_lambda_policy_statements = [
    {
      sid    = "GetAzureAuthSecrets"
      effect = "Allow"
      actions = [
        "secretsmanager:GetSecretValue"
      ]
      resources = [
        aws_secretsmanager_secret.azure_oidc_auth.arn
      ]
    },
    {
      sid    = "GetUpdateAzureOidcAppsSecrets"
      effect = "Allow"
      actions = [
        "secretsmanager:GetSecretValue",
        "secretsmanager:UpdateSecret"
      ]
      resources = values(aws_secretsmanager_secret.azure_oidc_apps)[*].arn
    },
    {
      sid    = "StsAssumeRoles"
      effect = "Allow"
      actions = [
        "sts:AssumeRole"
      ]
      resources = ["arn:aws:iam::*:role/alb-${local.oidc_secret_rotator_name}"]
    }
  ]
  repo_names          = [local.oidc_secret_rotator_github_repo]
  ecr_repo_account_id = local.shared_services_account_id
  ecr_repo_name       = module.oidc_secret_rotator_ecr.ecr_repostiory_name
  description         = "Rotates azure oidc app secrets and updates all loadbalancers using said secret with the new value."
  timeout             = 300
  memory_size         = 1536
  error_alarm = {
    alarm_actions = [data.aws_sns_topic.cloud_platform_opsgenie.arn]
  }
  environment_variables = {
    AZURE_AUTH_SECRETSMANAGER_ID = aws_secretsmanager_secret.azure_oidc_auth.id
    ALB_OIDC_ROTATOR_ROLE_NAME   = "alb-${local.oidc_secret_rotator_name}"
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *      CloudWatch Event Trigger
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

locals {
  oidc_rotator_event = [for app in local.azure_oidc_apps :
    {
      oidcSecretsManagerId = aws_secretsmanager_secret.azure_oidc_apps[app.name].id,
      albAccountIds        = app.albAccountIds
    }
  ]
}

resource "aws_lambda_permission" "allow_cloudwatch_events" {
  statement_id  = "AllowEventsToInvoke"
  action        = "lambda:InvokeFunction"
  function_name = module.oidc_secret_rotator_lambda.lambda_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.oidc_secret_rotator_lambda.arn
}

resource "aws_cloudwatch_event_target" "oidc_secret_rotator_lambda" {
  arn  = module.oidc_secret_rotator_lambda.lambda_arn
  rule = aws_cloudwatch_event_rule.oidc_secret_rotator_lambda.name

  input = jsonencode(local.oidc_rotator_event)
}

resource "aws_cloudwatch_event_rule" "oidc_secret_rotator_lambda" {
  name                = "oidc-secret-rotation"
  description         = "Triggers the oidc secret rotation lambda on a schedule."
  schedule_expression = "cron(0 9 1 * ? *)" # Run at 9:00 am (UTC) every 1st day of the month
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *                        OIDC APP Secrets
 * The values of these secrets must be manually updated after creation
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_secretsmanager_secret" "azure_oidc_apps" {
  for_each = { for app in local.azure_oidc_apps : app.name => app }
  name     = "azure/oidc/${each.value.name}"
}

resource "aws_secretsmanager_secret_version" "azure_oidc_apps" {
  for_each  = { for app in local.azure_oidc_apps : app.name => app }
  secret_id = aws_secretsmanager_secret.azure_oidc_apps[each.key].id
  secret_string = jsonencode({
    azure_app_object_id = "TO_FILL"
    issuer              = "TO_FILL"
    client_id           = "TO_FILL"
    auth_endpoint       = "TO_FILL"
    token_endpoint      = "TO_FILL"
    user_info_endpoint  = "TO_FILL"
    client_secret       = "TO_FILL"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *        OIDC ALB Secret Rotator Roles
 *  For accounts without a dedicated workspace
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

# Pod Point account

module "oidc_alb_secret_rotation_pod_point" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws//modules/oidc_secret_rotation"
  version = "4.1.0"
  providers = {
    aws.alb_account    = aws.pod_point_account_eu_west_1
    aws.lambda_account = aws
  }
}
