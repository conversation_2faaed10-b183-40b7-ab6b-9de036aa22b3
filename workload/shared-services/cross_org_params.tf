/*
SSM Parameters must be "advanced" tier in order to share them.
This incurs a small charge per parameter per month and a small charge per 10,000 API interactions.
If standard tier is ever supported, we can switch these back to standard as the parameters do not use any of the other advanced features.

See for more info:
https://docs.aws.amazon.com/systems-manager/latest/userguide/parameter-store-shared-parameters.html
*/

resource "aws_ssm_parameter" "account_ids" {
  for_each = local.active_accounts
  name     = "/account/${replace(each.value.name, "/\\s+/", "-")}/id" # White space not allowed in ssm path, replace with dashes
  type     = "String"
  value    = each.value.id
  tier     = "Advanced"
}

resource "aws_ram_resource_share" "org_share" {
  name                      = "cross-org"
  allow_external_principals = false
}

resource "aws_ram_principal_association" "org" {
  resource_share_arn = aws_ram_resource_share.org_share.arn
  principal          = data.aws_organizations_organization.pod_point.arn
}

resource "aws_ram_resource_association" "account_ids" {
  for_each           = local.active_accounts
  resource_share_arn = aws_ram_resource_share.org_share.arn
  resource_arn       = aws_ssm_parameter.account_ids[each.value.name].arn
}
