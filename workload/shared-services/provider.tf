provider "aws" {
  region = "eu-west-1"

  assume_role {
    role_arn     = "arn:aws:iam::${local.shared_services_account_id}:role/${local.terraform_role_name}"
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "shared-services"
      "pp:terraformWorkspace"               = "technology/shared-services"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  region = "eu-west-1"
  alias  = "org_management_account"
  assume_role {
    role_arn     = "arn:aws:iam::${local.management_account_id}:role/${local.terraform_role_name}"
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "shared-services"
      "pp:terraformWorkspace"               = "technology/shared-services"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  region = "eu-west-1"
  alias  = "pod_point_account_eu_west_1"
  assume_role {
    role_arn     = "arn:aws:iam::${local.pod_point_account_id}:role/${local.terraform_role_name}"
    session_name = "pod_point_assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "shared-services"
      "pp:terraformWorkspace"               = "technology/shared-services"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  region = "us-east-1"
  alias  = "us-east-1"
  assume_role {
    role_arn     = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/${local.terraform_role_name}"
    session_name = "assume_terraform_management_role_us-east-1"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "shared-services"
      "pp:terraformWorkspace"               = "technology/shared-services"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}
