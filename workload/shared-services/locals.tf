locals {
  region                     = "eu-west-1"
  terraform_role_name        = "terraform-ci"
  shared_services_account_id = "************"
  pod_point_account_id       = "************"
  management_account_id      = "************"
  organisation_id            = "o-4zdu8wtbbw"

  # Used to derive the account id parameters that are shared with the org
  # Shared services account is left out because its id is already shared using a stackset
  active_accounts = {
    for account in data.aws_organizations_organization.pod_point.accounts :
    account.name => account if account.status == "ACTIVE" && account.name != "Shared-services"
  }

  grafana_tags = {
    "pp:service" : "grafana-ws-pod-point"
  }
  token_auto_rotate_identifer = "amg-token-auto-rotate"

  # Azure service principal secret rotator
  azure_sp_secret_rotator_name                = "azure-sp-secret-rotator"
  azure_sp_secret_rotator_github_repo         = "azure-service-principal-secret-rotator"
  azure_sp_secret_rotator_ecr_retention_count = 30
  azure_sp_secret_rotator_ecr_lifecycle = {
    "rules" : [
      {
        "rulePriority" : 1,
        "description" : "Keep last ${local.azure_sp_secret_rotator_ecr_retention_count} images",
        "selection" : {
          "tagStatus" : "any",
          "countType" : "imageCountMoreThan",
          "countNumber" : local.azure_sp_secret_rotator_ecr_retention_count
        },
        "action" : {
          "type" : "expire"
        }
      }
    ]
  }

  # ALB azure OIDC secret rotator
  oidc_secret_rotator_name                = "oidc-secret-rotator"
  oidc_secret_rotator_github_repo         = "lambda-oidc-secret-rotator"
  oidc_secret_rotator_ecr_retention_count = 30
  oidc_secret_rotator_ecr_lifecycle = {
    "rules" : [
      {
        "rulePriority" : 1,
        "description" : "Keep last ${local.oidc_secret_rotator_ecr_retention_count} images",
        "selection" : {
          "tagStatus" : "any",
          "countType" : "imageCountMoreThan",
          "countNumber" : local.oidc_secret_rotator_ecr_retention_count
        },
        "action" : {
          "type" : "expire"
        }
      }
    ]
  }
  azure_oidc_apps = [
    {
      name          = "ad" # OIDC for the entire Azure AD
      albAccountIds = ["************"]
    },
    {
      name          = "experience-commercial"
      albAccountIds = ["************", "************", "************"]
    },
    {
      name          = "assets-embedded-squads"
      albAccountIds = ["************", "************"]
    },
    {
      name          = "mis"
      albAccountIds = ["************"]
    },
    {
      name          = "experience-mobile"
      albAccountIds = ["************"]
    },
    {
      name          = "statement-service-dev"
      albAccountIds = ["************"]
    },
    {
      name          = "statement-service-staging"
      albAccountIds = ["************"]
    },
    {
      name          = "statement-service-prod"
      albAccountIds = ["************"]
    },
    {
      name          = "auth"
      albAccountIds = ["************"]
    },
    {
      name          = "assets-fatp"
      albAccountIds = ["************", "************", "************"]
    },
    {
      name          = "support-tool-dev"
      albAccountIds = ["************"]
    },
    {
      name          = "support-tool-staging"
      albAccountIds = ["************"]
    },
    {
      name          = "support-tool-prod"
      albAccountIds = ["************"]
    },
    {
      name          = "oa-airbyte-dev"
      albAccountIds = ["************"]
    },
    {
      name          = "oa-airbyte-staging"
      albAccountIds = ["************"]
    },
    {
      name = "firmware-upgrade-management-ui"
      albAccountIds = [
        data.aws_ssm_parameter.cs_state_dev.value,
        data.aws_ssm_parameter.cs_state_staging.value,
        data.aws_ssm_parameter.cs_state_prod.value
      ]
    },
    {
      name          = "apache-devlake"
      albAccountIds = ["************"]
    }
  ]
}
