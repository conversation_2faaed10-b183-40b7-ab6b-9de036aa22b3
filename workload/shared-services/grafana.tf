resource "aws_grafana_workspace" "shared" {
  name                      = "pod-point"
  description               = "Monitoring for the Pod Point Core Charging infrastructure."
  grafana_version           = "10.4"
  account_access_type       = "ORGANIZATION"
  authentication_providers  = ["AWS_SSO"] # Note the assignment of SSO users and groups to the grafana workspace is done via the managed grafana console
  permission_type           = "SERVICE_MANAGED"
  notification_destinations = ["SNS"]
  role_arn                  = aws_iam_role.grafana.arn
  stack_set_name            = "grafana-service-role"
  organization_role_name    = "service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd" # Created via the grafana stackset
  organizational_units = [
    "ou-ie8m-15ykr5c9" # Workload ou
  ]
  data_sources = [
    "AMAZON_OPENSEARCH_SERVICE",
    "ATHENA",
    "CLOUDWATCH",
    "PROMETHEUS",
    "REDSHIFT",
    "SITEWISE",
    "TIMESTREAM",
    "XRAY"
  ]

  configuration = jsonencode({
    plugins = {
      pluginAdminEnabled = true
    }
    unifiedAlerting = {
      enabled = true
    }
  })

  tags = local.grafana_tags
}

resource "aws_ssm_parameter" "grafana_workspace_endpoint_url" {
  name        = "/token_auto_rotate_identifer/endpoint_url"
  description = "Endpoint URL of the Grafan workspace. To be used for the grafana-internal-config terraform workspace"
  type        = "String"
  value       = format("https://%s/", aws_grafana_workspace.shared.endpoint)

  tags = local.grafana_tags
}
resource "aws_iam_role" "grafana" {
  name               = "AmazonGrafanaAccountRole"
  assume_role_policy = data.aws_iam_policy_document.grafana_assume_role.json

  # Only the policies that were used within the iam tracking period of the previous grafana in the old organisation have been kept
  managed_policy_arns = [
    "arn:aws:iam::aws:policy/service-role/AmazonGrafanaAthenaAccess"
  ]
  inline_policy {
    name   = "AmazonGrafanaCloudWatch"
    policy = data.aws_iam_policy_document.grafana_cloudwatch.json
  }
  inline_policy {
    name   = "AmazonGrafanaOrgAdmin"
    policy = data.aws_iam_policy_document.grafana_org.json
  }
  inline_policy {
    name   = "AmazonGrafanaSNS"
    policy = data.aws_iam_policy_document.grafana_sns.json
  }

  tags = local.grafana_tags
}

data "aws_iam_policy_document" "grafana_assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["grafana.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

data "aws_iam_policy_document" "grafana_cloudwatch" {
  statement {
    effect = "Allow"

    actions = [
      "cloudwatch:DescribeAlarmsForMetric",
      "cloudwatch:DescribeAlarmHistory",
      "cloudwatch:DescribeAlarms",
      "cloudwatch:ListMetrics",
      "cloudwatch:GetMetricStatistics",
      "cloudwatch:GetMetricData",
      "cloudwatch:GetInsightRuleReport",
      "logs:DescribeLogGroups",
      "logs:GetLogGroupFields",
      "logs:StartQuery",
      "logs:StopQuery",
      "logs:GetQueryResults",
      "logs:GetLogEvents",
      "ec2:DescribeTags",
      "ec2:DescribeInstances",
      "ec2:DescribeRegions",
      "tag:GetResources"
    ]

    resources = [
      "*"
    ]
  }
}

data "aws_iam_policy_document" "grafana_org" {
  statement {
    effect = "Allow"

    actions = [
      "sts:AssumeRole"
    ]

    resources = [
      "arn:aws:iam::*:role/service-role/AmazonGrafanaOrgMemberRole-kE55J5kDd",
      "arn:aws:iam::*:role/AmazonGrafanaESDataSourceAccess"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "organizations:ListAccountsForParent",
      "organizations:ListOrganizationalUnitsForParent"
    ]

    resources = [
      "*"
    ]

    condition {
      test     = "StringEquals"
      variable = "aws:PrincipalOrgID"
      values   = [local.organisation_id]
    }
  }
}

data "aws_iam_policy_document" "grafana_sns" {
  statement {
    effect = "Allow"

    actions = [
      "sns:Publish"
    ]

    resources = [
      "arn:aws:sns:*:${data.aws_caller_identity.current.account_id}:grafana*"
    ]
  }
}

module "amg_token_auto_rotate" {
  source  = "terraform-aws-modules/lambda/aws"
  version = "7.5.0"

  function_name = local.token_auto_rotate_identifer
  description   = "Helps rotate a Grafana service account"
  handler       = "main.lambda_handler"
  runtime       = "python3.12"
  architectures = ["arm64"]

  publish                      = true
  trigger_on_package_timestamp = false
  source_path                  = "grafana-auto-token-rotate/main.py"

  layers = [
    "arn:aws:lambda:eu-west-1:************:layer:Klayers-p312-arm64-boto3:6"
  ]

  cloudwatch_logs_log_group_class   = "INFREQUENT_ACCESS"
  cloudwatch_logs_retention_in_days = 365

  attach_policy_statements = true

  environment_variables = {
    LOG_LEVEL = "ERROR" # Set this to INFO if you need more information for troubleshooting problems with the Lambda.
  }

  logging_log_format            = "JSON"
  logging_application_log_level = "INFO"
  logging_system_log_level      = "INFO"

  policy_statements = {
    secret_manager = {
      effect    = "Allow",
      actions   = ["secretsmanager:PutSecretValue"],
      resources = [aws_secretsmanager_secret.amg_token_auto_rotate.arn]
    }

    grafana = {
      effect = "Allow"
      actions = [
        "grafana:ListWorkspaceServiceAccountTokens",
        "grafana:DeleteWorkspaceServiceAccountToken",
        "grafana:CreateWorkspaceServiceAccountToken"
      ]
      resources = ["*"]
    }
  }

  allowed_triggers = {
    Events = {
      principal  = "events.amazonaws.com"
      source_arn = aws_scheduler_schedule.amg_token_auto_rotate.arn
    }
  }

  tags = local.grafana_tags
}

resource "aws_scheduler_schedule" "amg_token_auto_rotate" {
  name        = local.token_auto_rotate_identifer
  description = "Auto rotate tokens twice each month--Once on the 1st and the second time on the 14th."
  group_name  = "default"
  state       = "ENABLED"

  flexible_time_window {
    mode = "OFF"
  }

  schedule_expression = "cron(0 12 1,14 * ? *)"

  /*
  * An actively used API token with admin permissions will cost us 9$ per month
  * This is why we are scheduling the rotation to occur twice a month.
  * 
  * Max TTL of a token in AMG is 30 days.
  */

  target {
    arn      = module.amg_token_auto_rotate.lambda_function_arn
    role_arn = aws_iam_role.amg_token_auto_rotate_schedule.arn

    input = jsonencode({
      service_account_id = var.service_account_id
      secret_arn         = aws_secretsmanager_secret.amg_token_auto_rotate.arn
      workspace_id       = regex("([^\\/]+$)", aws_grafana_workspace.shared.arn)[0]
    })

    retry_policy {
      maximum_retry_attempts = 0
    }
  }
}

data "aws_iam_policy_document" "amg_token_auto_rotate_schedule" {
  statement {
    sid = "PermitInvokeLambda"

    actions = [
      "lambda:InvokeFunction"
    ]

    resources = [
      module.amg_token_auto_rotate.lambda_function_arn
    ]
  }
}

data "aws_iam_policy_document" "amg_token_auto_rotate_schedule_trust_policy" {
  statement {

    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["scheduler.amazonaws.com"]
    }

    condition {
      test     = "StringEquals"
      variable = "aws:SourceAccount"

      values = [data.aws_caller_identity.current.account_id]
    }
  }
}

resource "aws_iam_role" "amg_token_auto_rotate_schedule" {
  name = format("%s-eventbridge-schedule", local.token_auto_rotate_identifer)

  # Terraform's "jsonencode" function converts a
  # Terraform expression result to valid JSON syntax.
  assume_role_policy = data.aws_iam_policy_document.amg_token_auto_rotate_schedule_trust_policy.json

  inline_policy {
    name   = "lambda_invoke_permissions"
    policy = data.aws_iam_policy_document.amg_token_auto_rotate_schedule.json
  }

  tags = local.grafana_tags
}

resource "aws_secretsmanager_secret" "amg_token_auto_rotate" {
  name                    = local.token_auto_rotate_identifer
  description             = "A secret store for the service account token."
  recovery_window_in_days = 7

  tags = local.grafana_tags
}

resource "aws_secretsmanager_secret_version" "amg_token_auto_rotate" {
  secret_id = aws_secretsmanager_secret.amg_token_auto_rotate.id
  secret_string = jsonencode({
    token = "DUMMY_VAL" # Will be updated manually and regularly updated by the scheduled events.
  })

  lifecycle {
    ignore_changes = [
      secret_string
    ]
  }
}

/*
 * Where OpsGenie related API keys will be stored: required for grafana alerting / notification endpoint setup.
 *
 * Used/ Depended on by the `grafana-internal-config` terraform workspace.
 **/

resource "aws_ssm_parameter" "opsgenie_api_keys_this" {
  for_each = toset([
    "experience-ev-driver-squad",
    "experience-commercial-squad",
    "experience-data-platform-squad",
    "network-grid-squad",
    "network-assets-squad"
  ])

  name        = "/grafana/pod-point-ws/notification/opsgenie/api-key/${each.key}"
  description = "The API Key used by Grafana to authenticate to the OpsGenie endpoint."
  type        = "SecureString"
  value       = "DUMMY_VAL"

  lifecycle {
    # Value to be populated and updated outside Terraform management. 
    ignore_changes = [
      value
    ]
  }
}
