provider "aws" {
  region = local.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.salesforce_dev_account_id, local.terraform_role_name)
    session_name = "terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "development"
      "pp:domain"                           = "internal-systems"
      "pp:owner"                            = "internal-systems"
      "pp:service"                          = "salesforce"
      "pp:terraformWorkspace"               = "technology/salesforce-dev"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}
