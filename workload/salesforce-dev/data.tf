locals {
  shared_services_account_name = "Shared-services"
  network_assets_account_name  = "network-assets-dev"
}

data "aws_region" "current" {}

data "aws_caller_identity" "current" {}

data "aws_ssm_parameter" "shared_service_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.id}:parameter/account/${local.shared_services_account_name}/id"
}

data "aws_ssm_parameter" "network_assets_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.network_assets_account_name}/id"
}
