variable "assume_role_account_id" {
  description = "The account ID to assume when initialising the AWS provider."
  type        = string
  default     = "************" # ownership-acquisition-staging
}

variable "assume_role_name" {
  description = "The role to assume when initialising the AWS provider."
  type        = string
  default     = "terraform-ci"
}

variable "cloudwatch_opsgenie_api_url" {
  type        = string
  description = "#squad-acquisition OpsGenie Cloudwatch integration URL"
  default     = "https://api.opsgenie.com/v1/json/cloudwatch?apiKey=************************************" # Ownership - Acquisition Squad
}

variable "region" {
  description = "The main region the AWS provider will be initialised in."
  type        = string
  default     = "eu-west-1"
}

variable "vpc_cidr_block" {
  default = "*********/16"
}

variable "runner_private_key" {
  description = "Base64 encoded private key of the GitHub App to use as part of the permission allocation to the self hosted github action runner."
  type        = string
  sensitive   = true
}