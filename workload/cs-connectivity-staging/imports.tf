import {
  to = module.connectivity-service.module.commands_api.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-commands-api-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.connectivity-service.module.commands_processor.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-commands-processor-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.connectivity-service.module.connections_pruner.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-connections-pruner-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.connectivity-service.module.connections.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-connections-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.connectivity-service.module.disconnections.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-disconnections-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.connectivity-service.module.migrations.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-migrations-stdln-ecs-task-exec/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.connectivity-service.module.ocpp_broadcaster.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-ocpp-broadcaster-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.connectivity-service.module.ocpp16_responder.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-ocpp16-responder-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.connectivity-service.module.online_status_processor.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-online-status-processor-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.connectivity-service.module.ppcp_connections.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-ppcp-connections-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.connectivity-service.module.ppcp_messages.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-ppcp-messages-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.connectivity-service.module.status_api.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-status-api-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.connectivity-service.module.status_processor.aws_iam_role_policy_attachment.task_execution_policy
  id = "connectivity-service-status-processor-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.mtls-gateway.module.gateway.aws_iam_role_policy_attachment.task_execution_policy
  id = "mtls-gateway-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.mtls-gateway.module.renewer.aws_iam_role_policy_attachment.scheduled_task_execution_policy
  id = "mtls-gateway-renewer-execution-role/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.pod_comms_service.module.consumer_default.aws_iam_role_policy_attachment.task_execution_policy
  id = "pod-comms-staging-consumer-default-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.pod_comms_service.module.consumer_unit_events.aws_iam_role_policy_attachment.task_execution_policy
  id = "pod-comms-staging-consumer-unit-events-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.websockets.module.proxy.module.proxy.aws_iam_role_policy_attachment.task_execution_policy
  id = "ws-proxy-ecs-task-execution/arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

import {
  to = module.websockets.module.proxy.module.ecs_task_patch_retirement[0].aws_iam_role_policy.sfn_proxy_ecs_task_patch_retirement_handler_inline
  id = "ws-proxy-sfn-proxy-ecs-task-patch-retirement-handler:sfn_permissions"
}

import {
  to = module.websockets.module.proxy.module.ecs_task_patch_retirement[0].aws_iam_role_policy.event_target_proxy_ecs_task_patch_retirement_sfn_inline
  id = "ws-proxy-event-target-proxy-ecs-task-patch-retirement-sfn:sfn_permissions"
}

import {
  to = module.websockets.module.proxy.module.crt_renewer.aws_iam_role_policy.lambda_gh_actions_role_inline["github_actions_ssm_policy"]
  id = "ws-proxy-crt-renewer-gh-actions:github_actions_ssm_policy"
}

import {
  to = module.websockets.module.proxy.module.crt_renewer.aws_iam_role_policy.lambda_gh_actions_role_inline["gh_actions_serverless_policy"]
  id = "ws-proxy-crt-renewer-gh-actions:gh_actions_serverless_policy"
}

import {
  to = module.websockets.module.proxy.module.crt_renewer.aws_iam_role_policy.lambda_execution_role_inline["sns_notifications_permissions"]
  id = "ws-proxy-crt-renewer-lambda-role:sns_notifications_permissions"
}

import {
  to = module.websockets.module.proxy.module.crt_renewer.aws_iam_role_policy.lambda_execution_role_inline["serverless_permissions"]
  id = "ws-proxy-crt-renewer-lambda-role:serverless_permissions"
}

import {
  to = module.websockets.module.proxy.module.crt_renewer.aws_iam_role_policy.lambda_execution_role_inline["ecs_permissions"]
  id = "ws-proxy-crt-renewer-lambda-role:ecs_permissions"
}

import {
  to = module.websockets.module.proxy.module.crt_renewer.aws_iam_role_policy.lambda_execution_role_inline["csms_certificate_permissions"]
  id = "ws-proxy-crt-renewer-lambda-role:csms_certificate_permissions"
}

import {
  to = module.websockets.module.proxy.module.crt_renewer.aws_iam_role_policy.lambda_execution_role_inline["cert_service_api_gateway_permissions"]
  id = "ws-proxy-crt-renewer-lambda-role:cert_service_api_gateway_permissions"
}

import {
  to = module.websockets.module.pow_handlers.aws_iam_role_policy.pow_handlers_gh_actions_role_inline["serverless"]
  id = "ws-pow-handlers-github:serverless"
}

import {
  to = module.websockets.module.pow_handlers.aws_iam_role_policy.pow_handlers_gh_actions_role_inline["serverless_ssm_policy"]
  id = "ws-pow-handlers-github:serverless_ssm_policy"
}

import {
  to = module.websockets.module.pow_handlers.aws_iam_role_policy.pow_handlers_execution_role_inline["serverless_permissions"]
  id = "ws-pow-handlers-lambda-role:serverless_permissions"
}

import {
  to = module.websockets.module.pow_handlers.aws_iam_role_policy.pow_handlers_execution_role_inline["arch2_lambda_assume_role"]
  id = "ws-pow-handlers-lambda-role:arch2_lambda_assume_role"
}

import {
  to = module.websockets.module.pow_handlers.aws_iam_role_policy.api_gateway_role_disconnections_inline
  id = "ws-pow-handlers-gateway:allow_publish_to_sns_disconnections_topic"
}

import {
  to = module.ocpp16.module.cloudfront[0].aws_iam_role_policy.function_inline
  id = "ocpp-16-edge-function-staging:AllowCloudWatchLogs"
}

import {
  to = module.ocpp16.aws_iam_role_policy.lambda_execution["xray"]
  id = "ocpp-lambda-staging-execution-role:xray"
}

import {
  to = module.ocpp16.aws_iam_role_policy.lambda_execution["sqs"]
  id = "ocpp-lambda-staging-execution-role:sqs"
}

import {
  to = module.ocpp16.aws_iam_role_policy.lambda_execution["sns"]
  id = "ocpp-lambda-staging-execution-role:sns"
}

import {
  to = module.ocpp16.aws_iam_role_policy.lambda_execution["logging"]
  id = "ocpp-lambda-staging-execution-role:logging"
}

import {
  to = module.ocpp16.aws_iam_role_policy.lambda_execution["lambda-insights"]
  id = "ocpp-lambda-staging-execution-role:lambda-insights"
}

import {
  to = module.ocpp16.aws_iam_role_policy.lambda_execution["apigateway"]
  id = "ocpp-lambda-staging-execution-role:apigateway"
}

import {
  to = module.ocpp16.aws_iam_role_policy.github_actions_inline
  id = "github-ocpp-lambda-staging:serverless-permissions"
}

import {
  to = module.ocpp16.aws_iam_role_policy.api_gateway_role_messages_inline
  id = "ocpp-lambda-staging-gateway:allow_publish_to_sqs"
}

import {
  to = module.notifications.aws_iam_role_policy.slack_notifier_inline
  id = "slack-notifier:Permissions"
}

import {
  to = module.connectivity-service.aws_iam_role_policy.transaction_telemetry_publisher_inline
  id = "transaction-telemetry-publisher:kinesis"
}

import {
  to = module.arch2.aws_iam_role_policy.codebuild_role_inline["serverless_permissions"]
  id = "arch2-lambda-staging-codebuild:serverless_permissions"
}

import {
  to = module.arch2.aws_iam_role_policy.codebuild_role_inline["secret_permissions"]
  id = "arch2-lambda-staging-codebuild:secret_permissions"
}

import {
  to = module.arch2.aws_iam_role_policy.arch2_lambda_invocation_role_inline
  id = "arch2_lambda_invocation_role_staging:arch2_invoke_function"
}

import {
  to = module.arch2.aws_iam_role_policy.arch2_lambda_execution_role_inline["network_connectivity_secrets_handling"]
  id = "arch2_lambda_execution_role_staging:network_connectivity_secrets_handling"
}

import {
  to = module.arch2.aws_iam_role_policy.arch2_lambda_execution_role_inline["mysql_passwword_secrets_handling"]
  id = "arch2_lambda_execution_role_staging:mysql_passwword_secrets_handling"
}

import {
  to = module.arch2.aws_iam_role_policy.arch2_lambda_execution_role_inline["arch2_put_record_kinesis"]
  id = "arch2_lambda_execution_role_staging:arch2_put_record_kinesis"
}

import {
  to = module.arch2.aws_iam_role_policy.arch2_lambda_execution_role_inline["arch2_publish_sns"]
  id = "arch2_lambda_execution_role_staging:arch2_publish_sns"
}

import {
  to = module.arch2.aws_iam_role_policy.arch2_lambda_execution_role_inline["arch2_invoke_certificate_service"]
  id = "arch2_lambda_execution_role_staging:arch2_invoke_certificate_service"
}

import {
  to = module.arch2.aws_iam_role_policy.arch2_lambda_execution_role_inline["arch2_get_appconfig"]
  id = "arch2_lambda_execution_role_staging:arch2_get_appconfig"
}

import {
  to = module.arch2.aws_iam_role_policy.arch2_lambda_execution_role_inline_smart_charging[0]
  id = "arch2_lambda_execution_role_staging:arch2_access_smart_charging_service_db"
}

import {
  to = module.arch2.aws_iam_role_policy.arch2_lambda_execution_role_inline_energy_metrics_stream_cross_account[0]
  id = "arch2_lambda_execution_role_staging:arch2_access_energy_metrics_stream"
}

import {
  to = module.arch2.aws_iam_role_policy.arch2_lambda_execution_role_inline_certificate_service[0]
  id = "arch2_lambda_execution_role_staging:arch2_access_certificate_service_db"
}