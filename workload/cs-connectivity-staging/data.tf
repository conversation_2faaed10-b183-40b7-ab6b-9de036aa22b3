data "aws_caller_identity" "current" {}

data "terraform_remote_state" "network_assets_staging" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "network-assets-staging"
    }
  }
}

data "terraform_remote_state" "cs_charge_sessions_staging" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "cs-charge-sessions-staging"
    }
  }
}

data "aws_iam_roles" "additional_kms_administrators" {
  name_regex = ".*PP-Administrator|PP-Software.*"
}

data "aws_iam_roles" "pod_point_additional_kms_administrators" {
  provider   = aws.pod-point
  name_regex = ".*PP-Administrator|PP-Software.*"
}
