module "websockets" {
  source = "../../modules/applications/cs-connectivity/websockets"

  providers = {
    aws           = aws,
    aws.pod-point = aws.pod-point,
  }

  environment = local.environment

  pod_point_com_route53_hosted_zone_id = local.pod_point_com_route53_hosted_zone_id

  truststore_bundle = file("${path.module}/truststore.bundle.pem")

  kms_admins = data.aws_iam_roles.additional_kms_administrators.arns

  proxy_pod_point_com_sub_domains = [
    "ocpp20-staging",
    "ocpp16-staging"
  ]

  vpc_id                      = module.vpc.vpc.vpc_id
  vpc_private_subnet_ids      = module.vpc.vpc.private_subnets_ids
  vpc_public_subnet_ids       = module.vpc.vpc.public_subnets_ids
  vpc_availability_zone_names = module.vpc.vpc.availability_zone_names

  vpc_endpoint_assets_service_security_group_id = module.vpc.vpc_endpoint_security_group_assets_service.id
  vpc_endpoint_execute_api_security_group_id    = module.vpc.vpc_endpoint_security_group_execute_api.id

  vpc_endpoint_execute_api_dns = module.vpc.endpoints["execute-api"].dns_entry[0].dns_name

  arch2_lambda_execution_role_arn = "arn:aws:iam::************:role/arch2_lambda_invocation_role_staging"
  arch2_lambda_account_id         = local.pod_point_main_account_id

  ocpp_diagnostics_principals = ["arn:aws:iam::************:root"] # cs-state-staging
  ocpp_firmware_principals    = ["arn:aws:iam::************:root"] # cs-state-staging

  certificate_service_api = local.certificate_service_api

  ocpp16_lambda_execution_role   = module.ocpp16.lambda_execution_role
  ocpp16_websocket_apigateway_id = module.ocpp16.websocket_api.api_id

  ocpp_connections_topic_arn    = module.sns.ocpp_connections_arn
  ocpp_disconnections_topic_arn = module.sns.ocpp_disconnections_arn

  assets_service_url = format("http://%s", module.vpc.endpoints["assets-service"].dns_entry[0].dns_name)

  proxy_cpu               = 256
  proxy_memory            = 512
  proxy_nofile_hard_limit = 200000

  proxy_code_deploy_termination_wait_time_in_minutes = 5 // 5m

  notifications_topic_arn = module.notifications.sns_topic_arn
}
