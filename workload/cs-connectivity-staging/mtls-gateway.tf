module "mtls-gateway" {
  providers = {
    aws           = aws,
    aws.pod-point = aws.pod-point,
  }

  source = "../../modules/applications/cs-connectivity/mtls-gateway"

  environment = local.environment

  pod_point_com_route53_hosted_zone_id = local.pod_point_com_route53_hosted_zone_id

  certificate_service_api = local.certificate_service_api

  capacity_fargate_base        = 0
  capacity_fargate_weight      = 0
  capacity_fargate_spot_base   = 1
  capacity_fargate_spot_weight = 1

  cpu    = 256
  memory = 512

  kms_admins = data.aws_iam_roles.additional_kms_administrators.arns

  truststore_bundle = file("${path.module}/truststore.bundle.pem")

  gateway_pod_point_com_sub_domains = {
    provisioning-api = "provision-api-staging",
    sign-mfp         = "sign-mfp.pki-staging"
  }

  gateway_proxy_to = {
    provisioning-api = format("%s:80", module.vpc.endpoints["provisioning-api"].dns_entry[0].dns_name),
    sign-mfp         = "localhost:8080"
  }

  vpc_id                 = module.vpc.vpc.vpc_id
  vpc_private_subnet_ids = module.vpc.vpc.private_subnets_ids
  vpc_public_subnet_ids  = module.vpc.vpc.public_subnets_ids

  vpc_endpoint_execute_api_security_group_id = module.vpc.vpc_endpoint_security_group_execute_api.id
  vpc_endpoint_execute_api_dns               = module.vpc.endpoints["execute-api"].dns_entry[0].dns_name

  vpc_endpoint_provisioning_api_security_group_id = module.vpc.vpc_endpoint_security_group_provision_api.id
}
