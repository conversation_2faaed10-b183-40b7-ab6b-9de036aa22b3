variable "assume_role_account_id" {
  description = "The account ID to assume when initialising the AWS provider."
  type        = string
  default     = "************" # cs-connectivity-staging
}

variable "assume_role_name" {
  description = "The role to assume when initialising the AWS provider."
  type        = string
  default     = "terraform-ci"
}

variable "region" {
  description = "The main region the AWS provider will be initialised in."
  type        = string
  default     = "eu-west-1"
}

variable "slack_webhook_url" {
  description = "The URL used for sending messages to our slack workspace."
  type        = string
}
