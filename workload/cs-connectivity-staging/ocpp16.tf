module "ocpp16" {
  source = "../../modules/applications/cs-connectivity/ocpp16/legacy"

  providers = {
    aws           = aws.pod-point
    aws.us-east-1 = aws.pod-point-us-east-1 # Required for lambda edge functions
  }

  create_cloudfront = true

  environment = local.environment

  kinesis_log_stream_arn = "arn:aws:kinesis:eu-west-1:************:stream/logging"

  pod_unit_events_topic_arn = "arn:aws:sns:eu-west-1:************:pod-unit-event-received-staging"

  //cs-connectivity-staging ocpp connections SNS topic, cannot be resolved by terraform otherwise a circular dependency will be created
  ocpp_connections_sns_topic_arn    = format("arn:aws:sns:eu-west-1:%s:ocpp-connections", var.assume_role_account_id)
  ocpp_disconnections_sns_topic_arn = format("arn:aws:sns:eu-west-1:%s:ocpp-disconnections", var.assume_role_account_id)
  ocpp16_raw_comms_sns_topic_arn    = format("arn:aws:sns:eu-west-1:%s:ocpp16-raw-comms", var.assume_role_account_id)

  firmware_upgrade_api_arn = "arn:aws:execute-api:eu-west-1:************:34q4zjsftb"

  vpc_id = local.pod_point_main_account_vpc_id

  redis_cluster_name       = "ocpp-lambda-staging-v2"
  redis_maintenance_window = "sun:23:30-mon:00:30"
  redis_node_type          = "cache.t3.small"

  connectivity_account_id    = var.assume_role_account_id
  charge_sessions_account_id = local.charge_sessions_account_id

  kms_admins = data.aws_iam_roles.pod_point_additional_kms_administrators.arns

  vpc_endpoint_execute_api_security_group_id = module.main_account_vpc_endpoints.vpc_endpoint_security_group_execute_api.id

  aurora_legacy = {
    kms_key_id = "arn:aws:kms:eu-west-1:************:key/123a186c-4d8a-415e-b7cb-fcf3d9cc85a3"
    enabled_cloudwatch_logs_exports = [
      "error",
      "general",
    ]
    subnet_ids = local.pod_point_main_account_vpc_private_subnet_ids
    cluster_db_parameters = {
      net_read_timeout = {
        value        = 30
        apply_method = "immediate"
      }
      net_write_timeout = {
        value        = 60
        apply_method = "immediate"
      }
      wait_timeout = {
        value        = 28800
        apply_method = "immediate"
      }
      interactive_timeout = {
        value        = 28800
        apply_method = "immediate"
      }
      max_allowed_packet = {
        value        = ********
        apply_method = "immediate"
      }
    }
    instance_db_parameters = {
      net_read_timeout = {
        value = 30
      }
      net_write_timeout = {
        value        = 60
        apply_method = "immediate"
      }
      wait_timeout = {
        value        = 28800
        apply_method = "immediate"
      }
      interactive_timeout = {
        value        = 28800
        apply_method = "immediate"
      }
      max_allowed_packet = {
        value        = ********
        apply_method = "pending-reboot"
      }
      general_log = {
        value        = 1
        apply_method = "immediate"
      }
    }
  }
}

import {
  to = module.ocpp16.module.ocpp_aurora[0].aws_db_parameter_group.this
  id = "ocpp-aurora-instances-staging"
}

import {
  to = module.ocpp16.module.ocpp_aurora[0].aws_db_subnet_group.this[0]
  id = "ocpp-aurora-staging-private"
}

import {
  to = module.ocpp16.module.ocpp_aurora[0].aws_rds_cluster.this
  id = "ocpp-service-staging"
}

import {
  to = module.ocpp16.module.ocpp_aurora[0].aws_rds_cluster_instance.cluster_read_instances[0]
  id = "ocpp-service-staging-read-1"
}

import {
  to = module.ocpp16.module.ocpp_aurora[0].aws_rds_cluster_instance.desiread_writer_instance
  id = "ocpp-service-staging-desired-write"
}

import {
  to = module.ocpp16.module.ocpp_aurora[0].aws_rds_cluster_parameter_group.this
  id = "ocpp-aurora-cluster-staging"
}
