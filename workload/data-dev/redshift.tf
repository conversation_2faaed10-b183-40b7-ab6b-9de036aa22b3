module "redshift" {
  source                     = "../../modules/applications/data_team/redshift"
  environment                = local.environment
  vpc_id                     = module.vpc.vpc_id
  subnet_ids                 = module.vpc.private_subnets_ids
  vpn_cidr                   = local.vpn_cidr
  tableau_cidr               = "18.134.84.240/28"
  airbyte_security_group_id  = module.airbyte.security_group_id
  dms_security_group_id      = module.serverless.security_group_id
  bridge_client_cidr         = "10.0.1.0/24"
  bridge_client_cidr_standby = "10.0.4.0/24"
  terraform_enterprise_cidrs = local.terraform_enterprise_cidrs
  cluster_node_type          = "dc2.large"
  number_of_nodes            = 2
  cluster_master_username    = "pod"
}

module "redshift_iam_attachments" {
  source                      = "../../modules/applications/data_team/redshift/iam_role_attachments"
  redshift_cluster_identifier = module.redshift.redshift_cluster_identifier
  iam_roles = [
    module.redshift.redshift_role_arn,
    module.serverless.endpoint_role_arn
  ]
}

import {
  to = module.redshift.module.redshift_log_bucket.aws_s3_bucket.this[0]
  id = "pp-log-redshift-dev"
}