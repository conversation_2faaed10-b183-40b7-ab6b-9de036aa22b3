locals {
  environment = "dev"
  region      = "eu-west-1"
  vpc_cidr    = "***********/16"
  vpn_cidr = [
    "**********/22",
    "**********/22"
  ]
  terraform_enterprise_cidrs = [
    "********/24",     # TFE VPC private IP CIDR
    "***********/32",  # TFE NAT gateway AZ A
    "************/32", # TFE NAT gateway AZ B
    "*************/32" # TFE NAT gateway AZ C
  ]
  transit_gateway_id        = "tgw-07cb1b85dfd93cc40"
  requester_profile         = "pp-data-dev"
  accepter_profile          = "POD-Point-VPC"
  data_account_id           = "************"
  data_account_id_staging   = "************"
  data_account_id_prod      = "************"
  pod_point_account_id      = "************"
  pod_point_com_domain_name = "pod-point.com"
  pod_point_com_hosted_zone = "ZI1YF8KE9MFAW"
  assume_role_name          = "terraform-ci"


  dagit = {
    "cpu"                = 512
    "memory"             = 1024
    "desired_count"      = 1
    "port"               = 3000
    "tag"                = "91b032425c5ba5a6b4a760062ddf65feb2ee6cd6-BenoitFayolle-2023-12-21"
    "ecr_repository_url" = module.ecr_repos.repo_urls["dagster/dagit"]
  }

  // Daemon
  daemon = {
    "cpu"                = 512
    "memory"             = 1024
    "tag"                = "91b032425c5ba5a6b4a760062ddf65feb2ee6cd6-BenoitFayolle-2023-12-21"
    "ecr_repository_url" = module.ecr_repos.repo_urls["dagster/daemon"]
  }
}
