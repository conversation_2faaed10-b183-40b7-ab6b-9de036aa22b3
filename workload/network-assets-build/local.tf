locals {
  identifier = "network-assets-build"

  environment = "build"
  region      = "eu-west-1"

  development_account_id = "************"
  staging_account_id     = "************"
  production_account_id  = "************"

  cs_connectivity_build_account_id = "************"

  ecr_repositories = [
    {
      name        = "asset-service-api"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "configuration-api"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "assets-queue-consumer"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "configuration-queue-consumer"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "command-responses-consumer"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "event-publisher"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "provisioning-api"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "ocpp-requests-consumer"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "provisioning-queue-consumer"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "asset-service-load-test"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "asset-service-migrations-runner"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "asset-service-smoke-test"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "pact-stub-server-caddy"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "pact-stub-server-commands-api"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "pact-stub-server-connectivity-api"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "fatp"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "model-api"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "lidl-csms-transfer-tool"
      github_repo = "lidl-csms-transfer-tool",
      additional_policy_statements : []
    },
    {
      name        = "install-complete-consumer"
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "asset-creator-api",
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "charger-simulator",
      github_repo = "asset-service",
      additional_policy_statements : []
    },
    {
      name        = "fatp-e2e",
      github_repo = "asset-service",
      additional_policy_statements : [{
        sid = "FatpE2EBaseImageCrossAccountRetrievalPolicy",
        actions = [
          "ecr:BatchGetImage",
          "ecr:GetDownloadUrlForLayer"
        ],
        principals = [
          {
            type        = "Service",
            identifiers = ["codebuild.amazonaws.com"]
          }
        ],
        conditions = [
          {
            test     = "ForAnyValue:StringLike"
            variable = "aws:sourceARN"
            values = [
              "arn:aws:codebuild:${local.region}:${local.staging_account_id}:project/network-assets-fatp-e2e",
            ]
          }
        ]
      }]
    },
    {
      name        = "salesforce-installed-lambda"
      github_repo = "asset-service"
      additional_policy_statements : [{
        sid = "LambdaECRImageCrossAccountRetrievalPolicy",
        actions = [
          "ecr:BatchGetImage",
          "ecr:GetDownloadUrlForLayer"
        ],
        principals = [
          {
            type        = "Service",
            identifiers = ["lambda.amazonaws.com"]
          }
        ],
        conditions = [
          {
            test     = "ForAnyValue:StringLike"
            variable = "aws:sourceARN"
            values = [
              "arn:aws:lambda:${local.region}:${local.development_account_id}:function:*salesforce-installed-lambda*",
              "arn:aws:lambda:${local.region}:${local.staging_account_id}:function:*salesforce-installed-lambda*",
              "arn:aws:lambda:${local.region}:${local.production_account_id}:function:*salesforce-installed-lambda*"
            ]
          }
        ]
      }]
    },
  ]

  ecr_lifecycle = {
    "rules" : [
      {
        "rulePriority" : 1,
        "description" : "Keep last 10 images deployed in prod",
        "selection" : {
          "tagStatus" : "tagged",
          "tagPrefixList" : ["deployed-prod"],
          "countType" : "imageCountMoreThan",
          "countNumber" : 10
        },
        "action" : {
          "type" : "expire"
        }
      },
      {
        "rulePriority" : 2,
        "description" : "Keep last 10 images deployed in staging",
        "selection" : {
          "tagStatus" : "tagged",
          "tagPrefixList" : ["deployed-staging"],
          "countType" : "imageCountMoreThan",
          "countNumber" : 10
        },
        "action" : {
          "type" : "expire"
        }
      },
      {
        "rulePriority" : 3,
        "description" : "Keep last 10 images",
        "selection" : {
          "tagStatus" : "any",
          "countType" : "imageCountMoreThan",
          "countNumber" : 10
        },
        "action" : {
          "type" : "expire"
        }
      }
    ]
  }

  default_tags = {
    "pp:domain"                           = "network"
    "pp:environment"                      = "production"
    "pp:terraformConfigurationRepository" = "pod-point/terraform"
    "pp:terraformWorkspace"               = "network-assets-${local.environment}"
    "pp:owner"                            = "network:assets"
    "pp:service"                          = "asset-service"
  }
}
