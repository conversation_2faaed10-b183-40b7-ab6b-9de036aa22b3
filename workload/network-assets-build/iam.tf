/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *          KMS policy
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
**/

data "aws_iam_policy_document" "custom_kms_policy" {
  statement {
    sid       = "Enable Account Access"
    actions   = ["kms:*"]
    resources = ["*"]
    principals {
      type        = "AWS"
      identifiers = [format("arn:aws:iam::%s:root", data.aws_caller_identity.current.account_id)]
    }
  }

  statement {
    sid    = "AllowCodeArtifactAccess"
    effect = "Allow"
    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:GenerateDataKey",
      "kms:DescribeKey",
      "kms:CreateGrant",
      "kms:RetireGrant"
    ]
    resources = ["*"]
    principals {
      type        = "Service"
      identifiers = ["codeartifact.amazonaws.com"]
    }
  }

  statement {
    sid    = "AllowCrossAccountCodeBuildAccess"
    effect = "Allow"
    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:GenerateDataKey",
      "kms:DescribeKey",
      "kms:CreateGrant",
      "kms:RetireGrant"
    ]
    resources = ["*"]
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${local.staging_account_id}:role/network-assets-smoke-tests"]
    }
  }

  statement {
    sid       = "PermitBreakGlass"
    effect    = "Allow"
    actions   = ["kms:*"]
    resources = ["*"]
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }
    condition {
      test     = "ArnLike"
      variable = "aws:PrincipalArn"
      values = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_PP-AdminBreakglass_*"
      ]
    }
  }
}
