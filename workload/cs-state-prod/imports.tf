import {
  to = module.diagnostics.module.diagnostics_api.aws_cloudwatch_log_group.api_gateway
  id = "API-Gateway-Execution-Logs_p0iohphna9/prod"
}

import {
  to = module.firmware_upgrade.module.api.aws_cloudwatch_log_group.api_gateway
  id = "API-Gateway-Execution-Logs_tu6gatoce8/prod"
}

import {
  to = module.smart_charge.module.charging_optimisation_api.aws_cloudwatch_log_group.api_gateway
  id = "API-Gateway-Execution-Logs_e1f7of25k1/prod"
}

import {
  to = module.smart_charge.module.competitions_api.aws_cloudwatch_log_group.api_gateway
  id = "API-Gateway-Execution-Logs_w7gs2iucp8/prod"
}

import {
  to = module.smart_charge.module.energy_metrics_api.aws_cloudwatch_log_group.api_gateway
  id = "API-Gateway-Execution-Logs_mrg68racb4/prod"
}

import {
  to = module.smart_charge.module.smart_charging_service_api.aws_cloudwatch_log_group.api_gateway
  id = "API-Gateway-Execution-Logs_i4o9mlckoh/prod"
}

import {
  to = module.energy_metrics.aws_cloudwatch_log_group.energy_metrics_ingester
  id = "/aws/lambda/energy-metrics-ingester-prod-ingest-to-timestream"
}

import {
  to = module.mis.aws_elasticache_replication_group.admin_cache[0]
  id = "admin-tool-v2"
}
