import boto3
import os

print('Loading function')

rds = boto3.client('rds')

def lambda_handler(event, context):
    
    source_snapshot_identifier = event['detail']['SourceIdentifier']
    
    target_snapshot_identifier = source_snapshot_identifier.replace(source_snapshot_identifier[:4], '')
    
    sourceSnapshotArn = event['detail']['SourceArn']

    kms_key_id = os.environ.get('kms_key_id')

    # Trigger automated snapshot copy
    rds.copy_db_snapshot(
        SourceDBSnapshotIdentifier = source_snapshot_identifier,
        TargetDBSnapshotIdentifier = target_snapshot_identifier,
        KmsKeyId = kms_key_id
    )
    
    print('Snapshot copy', target_snapshot_identifier, 'successfully triggered.')
    
    return {
        'sourceSnapshotIdentifier': target_snapshot_identifier,
        'sourceSnapshotArn': sourceSnapshotArn,
        'body': 'Snapshot copy ' + target_snapshot_identifier + ' successfully triggered.'
    }