locals {
  platform_build_account_id      = "************"
  rds_custom_metrics_name        = "rds-custom-metrics"
  rds_custom_metrics_github_repo = "rds-custom-metrics"
}

data "aws_rds_cluster" "mis_prod" {
  provider           = aws.pod-point-eu-west-1
  cluster_identifier = "podadmin-prod"
}

data "aws_secretsmanager_secret" "mis_readonly_prod" {
  provider = aws.pod-point-eu-west-1
  name     = "podadmin-prod/mis_readonly/rdsproxy"
}

data "aws_kms_key" "mis_readonly_prod" {
  provider = aws.pod-point-eu-west-1
  key_id   = "alias/rds-prod"
}

data "aws_vpc" "pod_point_vpc" {
  provider = aws.pod-point-eu-west-1
  tags = {
    Name = "pod-point-legacy"
  }
}

data "aws_subnets" "pod_point_vpc_private_subnets" {
  provider = aws.pod-point-eu-west-1

  filter {
    name   = "tag:Name"
    values = ["pod-point-legacy-private-*"]
  }

  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.pod_point_vpc.id]
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~
 *       Lambda
 * ~~~~~~~~~~~~~~~~~~~~
*/

module "rds_custom_metrics_lambda" {
  source  = "terraform-enterprise.pod-point.com/technology/containerised-lambda/aws"
  version = "1.0.0"
  providers = {
    aws = aws.pod-point-eu-west-1
  }
  identifier = "${local.name}-${local.environment}-${local.rds_custom_metrics_name}"
  additional_lambda_policy_statements = [
    {
      sid    = "GetRdsAuthSecrets"
      effect = "Allow"
      actions = [
        "secretsmanager:GetSecretValue"
      ]
      resources = [
        data.aws_secretsmanager_secret.mis_readonly_prod.arn
      ]
    },
    {
      sid    = "GetRdsAuthSecretsDecrypt"
      effect = "Allow"
      actions = [
        "kms:Decrypt"
      ]
      resources = [
        data.aws_kms_key.mis_readonly_prod.arn
      ]
    },
    {
      sid    = "PutCustomMetrics"
      effect = "Allow"
      actions = [
        "cloudwatch:PutMetricData"
      ]
      resources = [
        "*"
      ]
    },
    {
      sid    = "AllowVpcAttachment"
      effect = "Allow"
      actions = [
        "ec2:CreateNetworkInterface",
        "ec2:DescribeNetworkInterfaces",
        "ec2:DeleteNetworkInterface"
      ]
      resources = [
        "*"
      ]
    }
  ]
  repo_names             = [local.rds_custom_metrics_github_repo]
  ecr_repo_account_id    = local.platform_build_account_id
  ecr_repo_name          = local.rds_custom_metrics_name
  description            = "Runs a query against a database that returns a single value and plots it into CloudWatch as a custom metric."
  timeout                = 60
  vpc_subnet_ids         = data.aws_subnets.pod_point_vpc_private_subnets.ids
  vpc_security_group_ids = [aws_security_group.rds_custom_metrics_lambda.id]
}

resource "aws_security_group" "rds_custom_metrics_lambda" {
  provider    = aws.pod-point-eu-west-1
  name        = "${local.name}-${local.environment}-${local.rds_custom_metrics_name}-lambda"
  description = "Security group for RDS Custom Metrics lambda"
  vpc_id      = data.aws_vpc.pod_point_vpc.id
}

resource "aws_security_group_rule" "rds_custom_metrics_lambda_egress" {
  provider          = aws.pod-point-eu-west-1
  description       = "Permit all egress traffic."
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = aws_security_group.rds_custom_metrics_lambda.id
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *      CloudWatch Event Trigger
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

locals {
  rds_custom_metrics_event = [
    {
      Database = {
        Host     = data.aws_rds_cluster.mis_prod.reader_endpoint
        Name     = "podpoint"
        SecretId = data.aws_secretsmanager_secret.mis_readonly_prod.arn
        Query    = "SELECT AVG(power_rating) AS total FROM podpoint.pcb_configurations WHERE power_rating != 0"
      }
      Metric = {
        Name      = "AvgNonZeroPowerRating"
        NameSpace = "RDS/CustomMetrics"
      }
    },
    {
      Database = {
        Host     = data.aws_rds_cluster.mis_prod.reader_endpoint
        Name     = "podpoint"
        SecretId = data.aws_secretsmanager_secret.mis_readonly_prod.arn
        Query    = "SELECT AVG(power_rating) AS power FROM podpoint.pcb_configurations;"
      }
      Metric = {
        Name      = "AvgPowerRating"
        NameSpace = "RDS/CustomMetrics"
      }
  }]
}

resource "aws_lambda_permission" "rds_custom_metrics_lambda" {
  provider      = aws.pod-point-eu-west-1
  statement_id  = "AllowEventsToInvoke"
  action        = "lambda:InvokeFunction"
  function_name = module.rds_custom_metrics_lambda.lambda_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.rds_custom_metrics_lambda.arn
}

resource "aws_cloudwatch_event_target" "rds_custom_metrics_lambda" {
  provider = aws.pod-point-eu-west-1
  arn      = module.rds_custom_metrics_lambda.lambda_arn
  rule     = aws_cloudwatch_event_rule.rds_custom_metrics_lambda.name

  input = jsonencode(local.rds_custom_metrics_event)
}

resource "aws_cloudwatch_event_rule" "rds_custom_metrics_lambda" {
  provider            = aws.pod-point-eu-west-1
  name                = "${local.name}-${local.environment}-${local.rds_custom_metrics_name}"
  description         = "Triggers the ${local.name}-${local.environment}-${local.rds_custom_metrics_name} lambda on a schedule."
  schedule_expression = "rate(1 minute)"
}
