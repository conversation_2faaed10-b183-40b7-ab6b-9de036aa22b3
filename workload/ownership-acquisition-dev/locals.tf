locals {
  build_account_id           = "************" # ownership-acquisition-build
  pod_point_account_id       = "************"
  shared_services_account_id = "************"
  environment                = "dev"
  pod_point_vpc_id           = "vpc-5178d535" # POD Point VPC
  pod_point_vpc_cidr         = "10.0.0.0/16"

  default_tags = {
    "pp:environment"                      = "development"
    "pp:domain"                           = "ownership"
    "pp:owner"                            = "ownership:acquisition"
    "pp:service"                          = "ownership-acquisition"
    "pp:terraformWorkspace"               = "ownership-acquisition-dev"
    "pp:terraformConfigurationRepository" = "github.com/Pod-Point/terraform"
  }

  pod_point_website = {
    environment = local.environment

    fargate_task_level_cpu    = 512
    fargate_task_level_memory = 1024

    vpc_id     = local.pod_point_vpc_id
    enable_waf = true
    ecs_private_subnets_ids = [
      "subnet-2415dc7c", # private-eu-west-1a
      "subnet-a8cc44cc", # private-eu-west-1b
      "subnet-10f73c58"  # private-eu-west-1c
    ]
    alb_public_subnets = [
      "subnet-2515dc7d", # public-eu-west-1a
      "subnet-7748c113", # public-eu-west-1b
      "subnet-ea26989c"  # public-eu-west-1c
    ]

    route53_record_name_dot_com             = "${local.environment}.pod-point.com"
    route53_record_name_dot_no              = "${local.environment}.pod-point.no"
    create_opsworks_weighted_record         = false # no opsworks dev environment so nothing to do here.
    fargate_route53_record_weighted_routing = 100

    acm_certificate_pod_point_com_arn = "arn:aws:acm:us-east-1:************:certificate/f3d747b5-887c-4ffb-85d3-17e12b4140e8"
    acm_certificate_pod_point_no_arn  = "arn:aws:acm:us-east-1:************:certificate/6c3d3008-cd79-446d-b86b-d66b827b2b33"

    s3_bucket_name               = "podpoint-website-dev"
    s3_bucket_cf_distribution_id = "E2S6WTXDV8T034"

    kms_admins = data.aws_iam_roles.pod_point_sso_roles.arns

    ## Alarms
    unhealthy_hosts_alarm_enabled = true
    unhealthy_hosts_threshold     = 0
    target_4xx_alarm_enabled      = true
    target_5xx_alarm_enabled      = true
    error_count_threshold_5xx     = 10
    cloudwatch_opsgenie_api_url   = "https://api.opsgenie.com/v1/json/cloudwatch?apiKey=************************************" # Ownership - Acquisition Squad

    ## Environment variables
    env_app_id                = "${local.environment}.pod-point.com"
    env_environment           = local.environment
    env_open_web_app_url      = "https://charge.pod-point.com"
    env_craft_auto_run_queue  = false
    env_redis_host            = "cms-staging.3ihfkn.ng.0001.euw1.cache.amazonaws.com" # @todo consistent naming
    env_reviewscouk_store     = "pod-point"
    env_site_domain           = "pod-point.com"
    env_site_url              = "https://${local.environment}.pod-point.com"
    env_site_url_no           = "https://${local.environment}.pod-point.no"
    env_email_sender_address  = "<EMAIL>"
    env_email_sender_name     = "Pod Point"
    env_email_hostname        = "smtp.mandrillapp.com"
    env_email_port            = 25
    env_gtm_id_en             = "GTM-TC653C3"
    env_gtm_id_no             = "GTM-TC653C3"
    env_gtm_auth              = "N0hSFPZ-3B3_1XOMZg0alw"
    env_gtm_preview           = "env-148"
    env_awin_cookie_domain    = ".pod-point.com"
    env_storefront_url        = "https://shop-${local.environment}.pod-point.com"
    env_cloudinary_cloud_name = "dhjw0mmbt"

    cache_instance_type           = "cache.t3.small"
    cache_node_group_count        = 1
    cache_replicas_per_node_group = 0

    database_root_user = "podpointcms"
    database_app_user  = "ecs_website" # To be updated as part of migration to aurora
  }

  partner_portal = {
    environment      = "dev"
    build_account_id = local.build_account_id

    enable_waf         = true
    cloudfront_acm_arn = "arn:aws:acm:us-east-1:${local.pod_point_account_id}:certificate/********-04bb-401d-986b-ff0e4d26eb06"

    vpc_id         = local.pod_point_vpc_id
    vpc_cidr       = local.pod_point_vpc_cidr
    enable_lb_logs = false
    ecs_private_subnets_ids = [
      "subnet-2415dc7c", # private-eu-west-1a
      "subnet-a8cc44cc", # private-eu-west-1b
      "subnet-10f73c58"  # private-eu-west-1c
    ]
    alb_public_subnets = [
      "subnet-2515dc7d", # public-eu-west-1a
      "subnet-7748c113", # public-eu-west-1b
      "subnet-ea26989c"  # public-eu-west-1c
    ]

    acm_arn = "arn:aws:acm:${var.region}:${local.pod_point_account_id}:certificate/d2916c8e-2f96-47ec-8107-2bb56f2e6b14"

    route53_record_name                    = "partners-dev.pod-point.com"
    route53_weighted_routing_policy_weight = 100
    legacy_alb_dns_name                    = "alb-pre-prod-services-**********.${var.region}.elb.amazonaws.com"

    installs_opensearch_name = "installs-preprod"

    kms_admins = data.aws_iam_roles.pod_point_sso_roles.arns

    cache_security_group_id = "sg-0e95a6dfe137f25f5"

    request_handler_resources = {
      cpu : "1024"
      memory : "2048"
    }
    request_handler_task_capacity = {
      min : 1
      max : 20
    }

    api_handler_resources = {
      cpu : "1024"
      memory : "2048"
    }
    api_handler_task_capacity = {
      min : 1
      max : 20
    }

    primary_queue_task_capacity = {
      min : 1
      max : 30
    }

    secondary_queue_task_capacity = {
      min : 1
      max : 30
    }

    odp_events_source_environment = local.environment

    ## Alarms
    unhealthy_hosts_alarm_enabled = true
    unhealthy_hosts_threshold     = 0
    target_4xx_alarm_enabled      = true
    target_5xx_alarm_enabled      = true
    error_count_threshold_5xx     = 10
    cloudwatch_opsgenie_api_url   = "https://api.opsgenie.com/v1/json/cloudwatch?apiKey=************************************" # Ownership - Acquisition Squad

    database_root_user = "podpoint"
    database_app_user  = "ecs_partner_portal"

    ## Environment variables (values copied from OpsWorks for Preprod)
    env_app_env                               = "dev"
    env_app_url                               = "https://partners-dev.pod-point.com"
    env_aws_default_region                    = var.region
    env_aws_eventbridge_source                = "com.pod-point.partners.dev"
    env_elasticsearch_host                    = "https://search-installs-preprod.pod-point.com"
    env_firebase_credentials_sdk_client_email = "<EMAIL>"
    env_firebase_credentials_sdk_project_id   = "partners-dev-32a91"
    env_firebase_credentials_web_app_id       = "1:629252716573:web:3d67ea70fbcaf4c5cceef4"
    env_log_channel                           = "single" # @todo upgrade and fix for "stack"
    env_podpoint_auth_key                     = "1"
    env_podpoint_auth_url                     = "http://internal-auth-dev-alb-**********.eu-west-1.elb.amazonaws.com/api/v1"
    env_psa_api_system_id                     = "517"
    env_segment_enabled                       = "true"
    env_sqs_prefix                            = "https://sqs.${var.region}.amazonaws.com/${local.pod_point_account_id}/"
    env_view_asset_cdn_url                    = "https://cdn.pod-point.com"
    env_gtm_id                                = "GTM-TRPCBKZS"
    env_gtm_auth                              = "rZ4SoHHRn6ilY8k3IuH8nQ"
    env_gtm_preview                           = "env-3"

    vehicle_event_source = "com.pod-point.admin-tool.staging"
  }
}
