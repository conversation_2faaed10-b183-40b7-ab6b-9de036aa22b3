module "ownership_acquisiton" {
  providers = {
    aws                     = aws
    aws.oa-us-east-1        = aws.oa-us-east-1
    aws.pod-point           = aws.pod-point
    aws.pod-point-us-east-1 = aws.pod-point-us-east-1
    aws.shared-services     = aws.shared-services
  }

  source = "../../modules/applications/ownership-acquisition"

  environment          = local.environment
  region               = var.region
  build_account_id     = local.build_account_id
  pod_point_account_id = local.pod_point_account_id
  pod_point_vpc_id     = local.pod_point_vpc_id
  vpc_cidr_block       = var.vpc_cidr_block
  single_nat_gateway   = true

  partner_portal    = local.partner_portal
  pod_point_website = local.pod_point_website

  runner_app_id      = "948523"
  runner_private_key = var.runner_private_key

  holistics_tunnel_proxy_port = "30093"

  podenergy_validation_record_name  = "_acme-challenge.dev.podenergy.com"
  podenergy_validation_record_value = "cMAO1XWOEt-kU-0jTHb87uJMj-f4V5If15YqVzYRDds"
  podenergy_site_domain             = "dev.podenergy.com"
  pantheon_cdn_ip4_addresses        = ["**********"]
  pantheon_cdn_ip6_addresses        = ["2620:12a:8000::3", "2620:12a:8001::3"]

  tags = local.default_tags
}
