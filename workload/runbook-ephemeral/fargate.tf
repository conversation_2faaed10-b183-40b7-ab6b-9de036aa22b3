data "aws_caller_identity" "current" {}

# Only needed for services that are routed to privately without a loadbalancer
module "service_discovery" {
  for_each = toset(nonsensitive(data.aws_ssm_parameters_by_path.ephemeral_environments.values))

  source         = "../../modules/service-discovery"
  namespace_name = "${local.identifier}-${each.value}"
  vpc_id         = data.terraform_remote_state.runbook-dev.outputs.vpc_dev.vpc_id
  services = [
    {
      name = "${local.fargate_app_name}-${each.value}"
    }
  ]
}

module "laravel_app_ecs_service" {
  for_each = toset(nonsensitive(data.aws_ssm_parameters_by_path.ephemeral_environments.values))

  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version = "11.1.1"

  service_type                 = "rolling"
  identifier                   = "${local.fargate_app_name}-${each.value}"
  vpc_id                       = data.terraform_remote_state.runbook-dev.outputs.vpc_dev.vpc_id
  cluster_name                 = data.terraform_remote_state.runbook-dev.outputs.laravel_ecs_cluster.name
  cluster_arn                  = data.terraform_remote_state.runbook-dev.outputs.laravel_ecs_cluster.arn
  pipeline_role_name           = data.terraform_remote_state.runbook-dev.outputs.laravel_ecs_cluster.github_role_name
  cpu                          = "256"
  memory                       = "512"
  capacity_fargate_base        = 1
  capacity_fargate_weight      = 1
  capacity_fargate_spot_base   = 0
  capacity_fargate_spot_weight = 0
  container_definitions        = jsonencode(local.container_definitions)
  subnet_ids                   = data.terraform_remote_state.runbook-dev.outputs.vpc_dev.private_subnets_ids
  enable_auto_scaling          = true
  custom_step_scaling = [{
    name = "cpu-utilisation-high",
    alarm = {
      threshold = 60,
      metrics = [{
        id = "cpu_utilisation_high",
        metric = {
          metric_name = "CPUUtilization",
          namespace   = "AWS/ECS",
          period      = "60",
          stat        = "Maximum",
          dimensions = {
            ClusterName = data.terraform_remote_state.runbook-dev.outputs.laravel_ecs_cluster.name
            ServiceName = local.fargate_app_name
          }
        }
        return_data = true
      }]
    },
    scale_out_policy = ({
      cooldown             = 120,
      gradual_lower_bound  = 0,
      gradual_upper_bound  = 20,
      gradual_adjustment   = 1,
      critical_lower_bound = 20,
      critical_adjustment  = 3
    }),
    scale_in_policy = ({
      cooldown    = 120,
      upper_bound = 0,
      adjustment  = -5
    })
  }]

  scaling_min_capacity = 2
  scaling_max_capacity = 2

  load_balancing_configuration = [
    {
      target_group_arn = module.laravel_app_lb.target_group_arns[0]
      container_name   = local.fargate_app_name
      container_port   = 3000
    }
  ]
  additional_kms_administrators = [
    format("arn:aws:iam::%s:role/aws-reserved/sso.amazonaws.com/eu-west-1/AWSReservedSSO_AWS-ADFS-Administrator_44f35f2c77dd63f2", data.aws_caller_identity.current.account_id)
  ]
  service_registry = {
    registry_arn = module.service_discovery[each.value].service_arns["${local.fargate_app_name}-${each.value}"]
  }
}

/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *    Fargate Security Group Rules
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/


locals {
  laravel_app_ecs_ingress_rules = [
    for ephemeral_environment in nonsensitive(data.aws_ssm_parameters_by_path.ephemeral_environments.values) :
    {
      description : "Access permitted from loadbalancer."
      from_port : 3000
      to_port : 3000
      protocol : "TCP"
      source_security_group_id : module.laravel_app_lb[ephemeral_environment].security_group_id
      security_group_id : module.laravel_app_ecs_service[ephemeral_environment].security_group_id
    }
  ]

  laravel_app_ecs_egress_rules = [
    for ephemeral_environment in nonsensitive(data.aws_ssm_parameters_by_path.ephemeral_environments.values) :
    {
      description : "Permit all egress traffic."
      from_port : 0
      to_port : 0
      protocol : "-1"
      ipv4_cidrs : ["0.0.0.0/0"]
      ipv6_cidrs : ["::/0"]
      security_group_id : module.laravel_app_ecs_service[ephemeral_environment].security_group_id
    }
  ]
}

resource "aws_security_group_rule" "laravel_app_ecs_ingress_rules" {
  for_each = { for x, rule in local.laravel_app_ecs_ingress_rules : x => rule }

  type                     = local.laravel_app_ecs_ingress_rules.type
  from_port                = local.laravel_app_ecs_ingress_rules.from_port
  to_port                  = local.laravel_app_ecs_ingress_rules.to_port
  protocol                 = local.laravel_app_ecs_ingress_rules.protocol
  description              = local.laravel_app_ecs_ingress_rules.description
  source_security_group_id = local.laravel_app_ecs_ingress_rules.source_security_group_id
  security_group_id        = local.laravel_app_ecs_ingress_rules.security_group_id
  cidr_blocks              = local.laravel_app_ecs_ingress_rules.cidr_blocks
}

resource "aws_security_group_rule" "laravel_app_ecs_egress_rules" {
  for_each = { for x, rule in local.laravel_app_ecs_egress_rules : x => rule }

  type              = "egress"
  from_port         = local.laravel_app_ecs_egress_rules.from_port
  to_port           = local.laravel_app_ecs_egress_rules.to_port
  description       = local.laravel_app_ecs_egress_rules.description
  protocol          = local.laravel_app_ecs_egress_rules.protocol
  security_group_id = local.laravel_app_ecs_egress_rules.security_group_id
  cidr_blocks       = local.laravel_app_ecs_egress_rules.cidr_blocks
  ipv6_cidr_blocks  = local.laravel_app_ecs_egress_rules.ipv6_cidr_blocks
}
