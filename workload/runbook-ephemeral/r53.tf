resource "aws_route53_record" "ephemeral" {
  for_each = toset(nonsensitive(data.aws_ssm_parameters_by_path.ephemeral_environments.values))

  provider = aws.pod-point

  zone_id         = local.pod_point_com_hosted_zone
  type            = "A"
  name            = "${local.fargate_app_name}-${each.value}.pod-point.com"
  allow_overwrite = true

  alias {
    name                   = module.laravel_app_lb[each.value].load_balancer_dns_name
    zone_id                = module.laravel_app_lb[each.value].load_balancer_zone_id
    evaluate_target_health = true
  }
}