module "laravel_app_lb" {
  for_each = toset(nonsensitive(data.aws_ssm_parameters_by_path.ephemeral_environments.values))

  source  = "terraform-enterprise.pod-point.com/technology/lb/aws"
  version = "2.1.3"

  load_balancer_name = "${local.fargate_app_name}-${each.value}"
  enable_internal_lb = false
  load_balancer_type = "application"

  vpc_id                = data.terraform_remote_state.runbook-dev.outputs.vpc_dev.vpc_id
  load_balancer_subnets = data.terraform_remote_state.runbook-dev.outputs.vpc_dev.public_subnets_ids

  access_logs = {
    enable_logs          = false
    create_bucket_policy = false
  }

  s3_force_destroy = true

  target_groups = [
    {
      name                 = "${local.fargate_app_name}-${each.value}"
      backend_protocol     = "HTTP"
      backend_port         = 3000
      target_type          = "ip"
      deregistration_delay = 60
      vpc_id               = data.terraform_remote_state.runbook-dev.outputs.vpc_dev.vpc_id

      health_check = {
        enabled             = true
        interval            = 10
        path                = ""
        port                = "traffic-port"
        healthy_threshold   = 3
        unhealthy_threshold = 2
        timeout             = 5
        protocol            = "HTTP"
        matcher             = "200"
      }
    }
  ]

  http_tcp_listeners = [
    {
      port        = 80
      protocol    = "HTTP"
      action_type = "redirect"
      redirect = {
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  ]

  https_listeners = [
    {
      port               = 443
      protocol           = "HTTPS"
      certificate_arn    = data.terraform_remote_state.runbook-dev.outputs.acm.arn
      target_group_index = 0
    }
  ]

  security_group_name        = "${local.fargate_app_name}-${each.value}-lb"
  security_group_description = "Security Group for the ${local.fargate_app_name}-${each.value} load balancer."

  security_group_ingress_rules = {
    "port_http_all" = {
      description = "HTTP permitted from the Internet."
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }

    "port_https_all" = {
      description = "HTTPS permitted from the Internet."
      from_port   = 443
      to_port     = 443
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }
}
