locals {
  region                    = "eu-west-1"
  identifier                = "runbook"
  terraform_role_name       = "terraform-ci"
  runbook_build_account_id  = "************"
  runbook_dev_account_id    = "************"
  pod_point_account_id      = "************"
  pod_point_com_domain_name = "pod-point.com"
  pod_point_com_hosted_zone = "ZI1YF8KE9MFAW"

  fargate_app_name           = "laravel-app-ephemeral"
  fargate_app_github_repo    = "fargate-example"
  vpc_cidr_block             = "*********/22"
  enable_custom_step_scaling = false

  container_definitions = [
    {
      "name" : local.fargate_app_name,
      "image" : "${local.runbook_build_account_id}.dkr.ecr.${local.region}.amazonaws.com/laravel-app:latest",
      "essential" : true,
      "networkMode" : "awsvpc",
      "portMappings" : [
        {
          "containerPort" : 3000,
          "hostPort" : 3000
        }
      ],
      "linuxParameters" : {
        "initProcessEnabled" : true
      },
      "environment" : [
        {
          "name" : "ENVIRONMENT",
          "value" : "RUNBOOK"
        }
      ],
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/laravel-app",
          "awslogs-region" : local.region,
          "awslogs-stream-prefix" : "ecs"
        }
      }
    }
  ]
}
