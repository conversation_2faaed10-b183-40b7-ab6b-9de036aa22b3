locals {
  domains = yamldecode(file("./configuration/domains.yaml"))
  glue_job_app_domains = flatten([for domain, attributes in local.domains["Domains"] :
    [for app in attributes["Apps"] :
      {
        app    = app
        domain = domain
      }
    ]
  ])
  domain_array = distinct([for domain, attributes in local.domains["Domains"] :
    domain
  ])
  app_array = distinct([for element in local.glue_job_app_domains :
    element.app
  ])
  domain_string_list = format("%s", join(",", local.domain_array))
  app_string_list    = format("%s", join(",", local.app_array))
}

# Glue infrastructure to run athena against parquet files in bucket
resource "aws_glue_catalog_database" "centralised_logging" {
  name = "centralised_logging"
}

resource "aws_glue_catalog_table" "athena_querying" {
  name          = "athena_queries"
  database_name = aws_glue_catalog_database.centralised_logging.name
  parameters = {
    "EXTERNAL"                    = "TRUE"
    "classification"              = "parquet"
    "projection.enabled"          = "true"
    "projection.namespace.type"   = "enum"
    "projection.name.type"        = "enum"
    "projection.year.type"        = "integer"
    "projection.month.type"       = "integer"
    "projection.day.type"         = "integer"
    "projection.namespace.values" = local.domain_string_list
    "projection.name.values"      = local.app_string_list
    "projection.year.range"       = "2024,2034"
    "projection.month.range"      = "1,12"
    "projection.day.range"        = "1,31"
    "projection.month.digits"     = "2"
    "projection.day.digits"       = "2"
  }
  table_type = "EXTERNAL_TABLE"

  partition_keys {
    name = "namespace"
    type = "string"
  }
  partition_keys {
    name = "name"
    type = "string"
  }
  partition_keys {
    name = "year"
    type = "string"
  }
  partition_keys {
    name = "month"
    type = "string"
  }
  partition_keys {
    name = "day"
    type = "string"
  }

  storage_descriptor {
    additional_locations      = []
    bucket_columns            = []
    compressed                = false
    input_format              = "org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat"
    location                  = "s3://log-storage-20240917143008892300000001/"
    number_of_buckets         = -1
    output_format             = "org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat"
    parameters                = {}
    stored_as_sub_directories = false

    columns {
      name       = "spanid"
      parameters = {}
      type       = "string"
    }
    columns {
      name       = "traceid"
      parameters = {}
      type       = "string"
    }
    columns {
      name       = "body"
      parameters = {}
      type       = "string"
    }
    columns {
      name       = "attributes"
      parameters = {}
      type       = "array<struct<key:string,value:string>>"
    }
    columns {
      name       = "resource"
      parameters = {}
      type       = "struct<name:string,namespace:string,version:string>"
    }
    columns {
      name       = "severitynumber"
      parameters = {}
      type       = "tinyint"
    }
    columns {
      name       = "severitytext"
      parameters = {}
      type       = "string"
    }
    columns {
      name       = "timeunixnano"
      parameters = {}
      type       = "string"
    }

    ser_de_info {
      parameters = {
        "serialization.format" = "1"
      }
      serialization_library = "org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe"
    }

    skewed_info {
      skewed_column_names               = []
      skewed_column_value_location_maps = {}
      skewed_column_values              = []
    }
  }
}

# Destination S3 Bucket
module "log_storage" {
  source        = "terraform-aws-modules/s3-bucket/aws"
  version       = "3.15.1"
  bucket_prefix = "log-storage-"

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true

  versioning = {
    enabled = false
  }

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
}

# Glue job to merge all log files that were made the day before

resource "aws_glue_job" "merge_files" {
  name              = "merge-log-files"
  execution_class   = "STANDARD"
  role_arn          = aws_iam_role.merge_files.arn
  worker_type       = "G.1X"
  number_of_workers = 10
  command {
    script_location = "s3://${module.scripts_bucket.s3_bucket_id}/${aws_s3_bucket_object.merge_log_files_script.key}"
    python_version  = "3"
  }

  default_arguments = {
    # Custom parameters
    "--app_domain_info"    = jsonencode(local.glue_job_app_domains)
    "--centralised_bucket" = module.log_storage.s3_bucket_id

    # General config
    "--TempDir"                          = "s3://${module.scripts_bucket.s3_bucket_id}/temporary/"
    "--enable-continuous-cloudwatch-log" = "true"
    "--enable-job-insights"              = "true"
    "--enable-metrics"                   = "true"
    "--enable-observability-metrics"     = "true"
    "--job-language"                     = "python"
  }
}

resource "aws_glue_trigger" "trigger_merge_log_files" {
  name     = "merge-log-files"
  schedule = "cron(0 4 * * ? *)"
  type     = "SCHEDULED"

  actions {
    job_name = aws_glue_job.merge_files.name
  }
}

data "aws_iam_policy_document" "glue_assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["glue.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

data "aws_iam_policy_document" "merge_log_files_permissions" {
  statement {
    effect = "Allow"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:*Object*"
    ]
    resources = [
      module.log_storage.s3_bucket_arn,
      "${module.log_storage.s3_bucket_arn}/*"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "s3:Get*",
      "s3:List*"
    ]
    resources = [
      module.scripts_bucket.s3_bucket_arn,
      "${module.scripts_bucket.s3_bucket_arn}/*"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]
    resources = [
      "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws-glue/*"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "cloudwatch:PutMetricData"
    ]
    resources = [
      "*"
    ]
  }
}

resource "aws_iam_role" "merge_files" {
  name               = "merge-log-files"
  assume_role_policy = data.aws_iam_policy_document.glue_assume_role.json
}

resource "aws_iam_role_policy" "merge_files" {
  name   = "merge-log-files"
  role   = aws_iam_role.merge_files.id
  policy = data.aws_iam_policy_document.merge_log_files_permissions.json
}

module "scripts_bucket" {
  source        = "terraform-aws-modules/s3-bucket/aws"
  version       = "3.15.1"
  bucket_prefix = "scripts-"

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true

  versioning = {
    enabled = false
  }

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
}

resource "aws_s3_bucket_object" "merge_log_files_script" {
  bucket = module.scripts_bucket.s3_bucket_id
  key    = "glue_jobs/merge-log-files.py"
  source = "${path.module}/glue_jobs/merge-log-files.py"
  etag   = filemd5("${path.module}/glue_jobs/merge-log-files.py")
}

# Glue infrastructure to convert json to parquet

resource "aws_glue_catalog_table" "centralised_logging" {
  name          = "centralised_logging_schema"
  database_name = aws_glue_catalog_database.centralised_logging.name

  table_type = "EXTERNAL_TABLE"

  parameters = {
    "classification" = "parquet"
  }

  storage_descriptor {
    additional_locations = []
    bucket_columns       = []
    compressed           = false
    input_format         = "org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat"
    number_of_buckets    = 0
    output_format        = "org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat"
    parameters = {
      "typeOfData" = "kinesis"
    }
    stored_as_sub_directories = false

    columns {
      name       = "spanid"
      parameters = {}
      type       = "string"
    }
    columns {
      name       = "traceid"
      parameters = {}
      type       = "string"
    }
    columns {
      name       = "body"
      parameters = {}
      type       = "string"
    }
    columns {
      name       = "attributes"
      parameters = {}
      type       = "array<struct<key:string,value:string>>"
    }
    columns {
      name       = "resource"
      parameters = {}
      type       = "struct<name:string,namespace:string,version:string>"
    }
    columns {
      name       = "severitynumber"
      parameters = {}
      type       = "tinyint"
    }
    columns {
      name       = "severitytext"
      parameters = {}
      type       = "string"
    }
    columns {
      name       = "timeunixnano"
      parameters = {}
      type       = "string"
    }

    ser_de_info {
      parameters = {
        "serialization.format" = "1"
      }
      serialization_library = "org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe"
    }
  }

}

# Data Firehose Delivery to S3
data "aws_iam_policy_document" "firehose_assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["firehose.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

data "aws_iam_policy_document" "log_delviery_permissions" {
  statement {
    effect = "Allow"
    actions = [
      "s3:PutObject",
      "s3:PutObjectAcl",
      "s3:ListBucket"
    ]
    resources = [
      module.log_storage.s3_bucket_arn,
      "${module.log_storage.s3_bucket_arn}/*"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "glue:GetTable",
      "glue:GetTableVersion",
      "glue:GetTableVersions"
    ]
    resources = [
      aws_glue_catalog_table.centralised_logging.arn,
      aws_glue_catalog_database.centralised_logging.arn,
      "arn:aws:glue:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:catalog"
    ]
  }
  statement {
    effect = "Allow"
    actions = [
      "glue:GetSchemaVersion"
    ]
    resources = [
      "*"
    ]
  }
}

resource "aws_iam_role" "log_delivery" {
  name               = "log-delivery-to-s3"
  assume_role_policy = data.aws_iam_policy_document.firehose_assume_role.json
}

resource "aws_iam_role_policy" "log_delivery" {
  name   = "firehose-delivery-permissions-s3"
  role   = aws_iam_role.log_delivery.id
  policy = data.aws_iam_policy_document.log_delviery_permissions.json
}

resource "aws_cloudwatch_log_group" "firehose_errors" {
  name = "/aws/kinesisfirehose/log-delivery-to-s3"
}

resource "aws_kinesis_firehose_delivery_stream" "extended_s3_stream" {
  name        = "log-delivery-to-s3"
  destination = "extended_s3"

  extended_s3_configuration {
    role_arn       = aws_iam_role.log_delivery.arn
    bucket_arn     = module.log_storage.s3_bucket_arn
    buffering_size = 128

    cloudwatch_logging_options {
      enabled         = true
      log_group_name  = aws_cloudwatch_log_group.firehose_errors.name
      log_stream_name = "DestinationDelivery"
    }

    dynamic_partitioning_configuration {
      enabled = "true"
    }

    # Example prefix using dynamic partitioning
    # Use hive format for S3 prefix so that we can run "MSCK REPAIR TABLE `table_name`" to load partitions in athena
    prefix              = "namespace=!{partitionKeyFromQuery:namespace}/name=!{partitionKeyFromQuery:name}/year=!{timestamp:yyyy}/month=!{timestamp:MM}/day=!{timestamp:dd}/"
    error_output_prefix = "errors/!{timestamp:yyyy}/!{timestamp:MM}/!{timestamp:dd}/!{firehose:error-output-type}/"

    # Must be enabled when using dynamic partitioning
    processing_configuration {
      enabled = "true"

      processors {
        type = "MetadataExtraction"
        parameters {
          parameter_name  = "JsonParsingEngine"
          parameter_value = "JQ-1.6"
        }
        parameters {
          parameter_name  = "MetadataExtractionQuery"
          parameter_value = "{namespace:.resource.namespace,name:.resource.name}"
        }
      }
    }

    data_format_conversion_configuration {
      input_format_configuration {
        deserializer {
          open_x_json_ser_de {
            case_insensitive                         = true
            column_to_json_key_mappings              = {}
            convert_dots_in_json_keys_to_underscores = false
          }
        }
      }

      output_format_configuration {
        serializer {
          parquet_ser_de {}
        }
      }

      schema_configuration {
        database_name = aws_glue_catalog_table.centralised_logging.database_name
        table_name    = aws_glue_catalog_table.centralised_logging.name
        role_arn      = aws_iam_role.log_delivery.arn
      }
    }
  }
}

# VPC for the fluentd container

module "vpc" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws"
  version = "2.3.0"

  availability_zones_count = 3
  vpc_name                 = "fluentd"
  vpc_cidr_block           = "10.44.0.0/16"
  single_nat_gateway       = true
}

# Firehose VPC endpoint - Keep log routing between fluentd and firehose within the AWS network 

resource "aws_vpc_endpoint" "firehose" {
  vpc_id              = module.vpc.vpc_id
  service_name        = "com.amazonaws.${data.aws_region.current.name}.kinesis-firehose"
  vpc_endpoint_type   = "Interface"
  private_dns_enabled = true
  subnet_ids          = module.vpc.private_subnets_ids
  security_group_ids = [
    aws_security_group.firehose_vpc_endpoint.id
  ]
}

resource "aws_security_group" "firehose_vpc_endpoint" {
  name   = "firehose-vpc-endpoint"
  vpc_id = module.vpc.vpc_id
}

resource "aws_security_group_rule" "firehose_vpc_endpoint_ingress" {
  type                     = "ingress"
  to_port                  = 443
  from_port                = 443
  protocol                 = "TCP"
  source_security_group_id = module.fluentd_aggregator_service.security_group_id
  security_group_id        = aws_security_group.firehose_vpc_endpoint.id
}

resource "aws_security_group_rule" "firehose_vpc_endpoint_egress" {
  type              = "egress"
  to_port           = 0
  from_port         = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = aws_security_group.firehose_vpc_endpoint.id
}

# PrivateLink Service - Allows other VPCs to connect to log aggregator

resource "aws_vpc_endpoint_service" "privatelink" {
  acceptance_required        = false
  network_load_balancer_arns = [module.fluentd_aggregator_lb.load_balancer_arn]
  allowed_principals         = ["arn:aws:iam::************:root"] # cloud-platform-tests-2 account
}

# Fluentd loadbalancer

module "fluentd_aggregator_lb" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws"
  version = "5.0.1"

  load_balancer_name = "fluentd-aggregator"
  enable_internal_lb = true
  load_balancer_type = "network"

  vpc_id                = module.vpc.vpc_id
  load_balancer_subnets = module.vpc.private_subnets_ids

  access_logs = {
    enable_logs          = false
    create_bucket_policy = false
  }

  target_groups = [
    {
      name                 = "fluentd-aggregator"
      backend_protocol     = "TCP"
      backend_port         = 24224
      target_type          = "ip"
      deregistration_delay = 60
      vpc_id               = module.vpc.vpc_id
    }
  ]

  http_tcp_listeners = [
    {
      port               = 24224
      protocol           = "TCP"
      target_group_index = 0
    }
  ]

  security_group_name        = "fluentd-aggregator-lb"
  security_group_description = "Security Group for the fluentd-aggregator load balancer."

  security_group_ingress_rules = {
    "port_tcp_all" = {
      description = "TCP on port 24224 permitted to all."
      from_port   = 24224
      to_port     = 24224
      protocol    = "TCP"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }
}

# Fluentd Fargate

# SSM to control the image version deployed to fargate

resource "aws_ssm_parameter" "fluentd_fargate_version" {
  name  = "fluentd-fargate-version"
  type  = "String"
  value = "TO_FILL"

  lifecycle {
    ignore_changes = [value]
  }
}

data "aws_ssm_parameter" "fluentd_fargate_version" {
  name = aws_ssm_parameter.fluentd_fargate_version.name
}

# Fargate role permissions
data "aws_iam_policy_document" "fluentd_aggregator" {
  statement {
    effect = "Allow"
    actions = [
      "firehose:PutRecord",
      "firehose:PutRecordBatch"
    ]
    resources = [
      "*"
    ]
  }
}

# Fargate cluster
module "fluentd_aggregator_cluster" {
  source  = "terraform-enterprise.pod-point.com/technology/fargate-service/aws//modules/cluster"
  version = "11.2.4"

  identifier         = "fluentd-aggregator"
  repo_names         = ["fluentd-aggregator"]
  container_insights = "enabled"
}


# Fargate service
locals {
  container_definitions = [
    {
      "name" : "fluentd-aggregator",
      "image" : "${data.aws_caller_identity.current.account_id}.dkr.ecr.${data.aws_region.current.name}.amazonaws.com/fluentd-aggregator:${data.aws_ssm_parameter.fluentd_fargate_version.value}",
      "essential" : true,
      "networkMode" : "awsvpc",
      "portMappings" : [
        {
          "containerPort" : 24224,
          "hostPort" : 24224
        }
      ],
      "logConfiguration" : {
        "logDriver" : "awslogs",
        "options" : {
          "awslogs-group" : "/ecs/fluentd-aggregator",
          "awslogs-region" : data.aws_region.current.name,
          "awslogs-stream-prefix" : "ecs"
        }
      }
      "healthCheck" : {
        "command" : ["CMD-SHELL", "curl http://localhost:8888/healthcheck?json=%7B%22log%22%3A+%22health+check%22%7D || exit 1"],
        "startPeriod" : 30
      }
    }
  ]
}

module "fluentd_aggregator_service" {
  source       = "terraform-enterprise.pod-point.com/technology/fargate-service/aws"
  version      = "11.2.4"
  service_type = "rolling"
  identifier   = "fluentd-aggregator"
  vpc_id       = module.vpc.vpc_id
  subnet_ids   = module.vpc.private_subnets_ids
  load_balancing_configuration = [
    {
      target_group_arn = module.fluentd_aggregator_lb.target_group_arns[0]
      container_name   = "fluentd-aggregator"
      container_port   = 24224
    }
  ]

  cluster_name       = module.fluentd_aggregator_cluster.name
  cluster_arn        = module.fluentd_aggregator_cluster.arn
  pipeline_role_name = module.fluentd_aggregator_cluster.github_role_name

  cpu                               = "4096"
  memory                            = "8192"
  attach_custom_ecs_task_iam_policy = true
  ecs_task_custom_policy            = data.aws_iam_policy_document.fluentd_aggregator.json
  container_definitions             = jsonencode(local.container_definitions)

  enable_auto_scaling  = false
  scaling_min_capacity = 0

  capacity_fargate_base        = 1
  capacity_fargate_weight      = 1
  capacity_fargate_spot_base   = 0
  capacity_fargate_spot_weight = 0

  depends_on = [
    module.fluentd_aggregator_cluster,
    module.fluentd_aggregator_lb
  ]
}

resource "aws_security_group_rule" "fluentd_aggregator_ecs_ingress" {
  type                     = "ingress"
  to_port                  = 24224
  from_port                = 24224
  protocol                 = "TCP"
  source_security_group_id = module.fluentd_aggregator_lb.security_group_id
  security_group_id        = module.fluentd_aggregator_service.security_group_id
}

resource "aws_security_group_rule" "fluentd_aggregator_ecs_egress" {
  type              = "egress"
  to_port           = 0
  from_port         = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  ipv6_cidr_blocks  = ["::/0"]
  security_group_id = module.fluentd_aggregator_service.security_group_id
}