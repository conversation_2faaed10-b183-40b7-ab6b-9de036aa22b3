# fluentd image based on alpine
FROM fluent/fluentd:v1.16-1

# Use root account to use apk
USER root

# Installs github.com/awslabs/aws-fluent-plugin-kinesis
# Installs curl for container health check

RUN apk add --update --virtual .build-deps \
        sudo build-base ruby-dev \
 && sudo gem install \
        fluent-plugin-kinesis \
        fluent-plugin-s3 \
 && sudo gem sources --clear-all \
 && apk add curl \
 && apk del .build-deps \
 && rm -rf /var/cache/apk/* \
           /home/<USER>/.gem/ruby/2.4.0/cache/*.gem

COPY fluent.conf /fluentd/etc/
# COPY entrypoint.sh /bin/

# temp removal
# USER fluent