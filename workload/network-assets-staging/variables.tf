variable "assume_role_account_id" {
  description = "The account ID to assume when initialising the AWS provider."
  type        = string
  default     = "************" # network-assets-staging
}

variable "assume_role_name" {
  description = "The role to assume when initialising the AWS provider."
  type        = string
  default     = "terraform-ci"
}

variable "network_assets_cloudwatch_opsgenie_api" {
  type        = string
  description = "The environment specific opsgenie cloudwatch integration URL."
}
