data "aws_kms_alias" "secret_ocpp_rds_credentials" {
  provider = aws.pod-point
  name     = "alias/podpoint/secretsmanager"
}

data "aws_secretsmanager_secret" "ocpp_rds_credentials" {
  provider = aws.pod-point
  name     = "ocpp/production/rds"
}

module "ocpp16_rds_proxy" {
  source = "../../modules/rds/proxy"

  providers = {
    aws = aws.pod-point
  }

  environment                = local.environment
  proxy_id                   = format("%s-%s-proxy", "ocpp-lambda", local.environment)
  proxy_name                 = format("%s-%s-proxy", "ocpp-lambda", local.environment)
  proxy_enable_debug_logging = false
  proxy_db_target_identifier = "ocpp-service-prod"

  proxy_engine_family          = "MYSQL"
  proxy_max_connection_percent = 90
  proxy_idle_client_timeout    = 1800

  proxy_require_tls = false # TODO: - DEVOPS-316

  permit_additional_kms_keys = [
    data.aws_kms_alias.secret_ocpp_rds_credentials.target_key_arn,
  ]

  proxy_security_group = [module.ocpp16.aurora_security_group_id]
  proxy_vpc_subnet_ids = [
    "subnet-2415dc7c", # private-eu-west-1a
    "subnet-a8cc44cc", # private-eu-west-1b
    "subnet-10f73c58"  # private-eu-west-1c
  ]

  is_aurora_cluster_target = true

  proxy_auth_arns = [
    data.aws_secretsmanager_secret.ocpp_rds_credentials.arn,
  ]
}

resource "aws_security_group_rule" "db_permit_proxy" {
  provider = aws.pod-point

  // Proxy & Aurora share the same security group, so we need a rule that allows the security group itself
  security_group_id = module.ocpp16.aurora_security_group_id
  self              = true

  description = "Permit 3306 from Proxy"
  protocol    = "tcp"
  from_port   = 3306
  to_port     = 3306
  type        = "ingress"
}

module "ocpp16" {
  source = "../../modules/applications/cs-connectivity/ocpp16/legacy"

  providers = {
    aws           = aws.pod-point
    aws.us-east-1 = aws.pod-point-us-east-1 # Required for lambda edge functions
  }

  create_cloudfront = true

  environment = local.environment

  kinesis_log_stream_arn = "arn:aws:kinesis:eu-west-1:************:stream/logging"

  pod_unit_events_topic_arn = "arn:aws:sns:eu-west-1:************:pod-unit-event-received"

  //cs-connectivity-prod ocpp SNS connections topic, cannot be resolved by terraform otherwise a circular dependency will be created
  ocpp_connections_sns_topic_arn    = format("arn:aws:sns:eu-west-1:%s:ocpp-connections", var.assume_role_account_id)
  ocpp_disconnections_sns_topic_arn = format("arn:aws:sns:eu-west-1:%s:ocpp-disconnections", var.assume_role_account_id)
  ocpp16_raw_comms_sns_topic_arn    = format("arn:aws:sns:eu-west-1:%s:ocpp16-raw-comms", var.assume_role_account_id)

  firmware_upgrade_api_arn = "arn:aws:execute-api:eu-west-1:************:tu6gatoce8"

  vpc_id = local.pod_point_main_account_vpc_id

  alarm_actions = ["arn:aws:sns:eu-west-1:************:cs-connectivity-opsgenie"]

  redis_cluster_name       = "ocpp-lambda-prod-v2"
  redis_maintenance_window = "sat:00:00-sat:01:00"
  redis_node_type          = "cache.t3.medium"

  connectivity_account_id    = var.assume_role_account_id
  charge_sessions_account_id = local.charge_sessions_account_id

  kms_admins = data.aws_iam_roles.pod_point_additional_kms_administrators.arns

  vpc_endpoint_execute_api_security_group_id = module.main_account_vpc_endpoints.vpc_endpoint_security_group_execute_api.id

  aurora_legacy = {
    kms_key_id = "arn:aws:kms:eu-west-1:************:key/6896732d-88bc-4482-b937-539c1e754384"
    enabled_cloudwatch_logs_exports = [
      "error",
      "general",
      "slowquery",
    ]
    subnet_ids = local.pod_point_main_account_vpc_private_subnet_ids
    cluster_db_parameters = {
      net_read_timeout = {
        value        = 30
        apply_method = "immediate"
      }
      net_write_timeout = {
        value        = 60
        apply_method = "immediate"
      }
      wait_timeout = {
        value        = 28800
        apply_method = "immediate"
      }
      interactive_timeout = {
        value        = 28800
        apply_method = "immediate"
      }
      max_allowed_packet = {
        value        = ********
        apply_method = "immediate"
      }
    }
    instance_db_parameters = {
      net_read_timeout = {
        value = 30
      }
      net_write_timeout = {
        value        = 60
        apply_method = "immediate"
      }
      wait_timeout = {
        value        = 28800
        apply_method = "immediate"
      }
      interactive_timeout = {
        value        = 28800
        apply_method = "immediate"
      }
      max_allowed_packet = {
        value        = ********
        apply_method = "pending-reboot"
      }
      general_log = {
        value        = 1
        apply_method = "immediate"
      }
      slow_query_log = {
        value        = 1
        apply_method = "immediate"
      }
      net_write_timeout = {
        value        = 60
        apply_method = "immediate"
      }
      long_query_time = {
        value        = 10
        apply_method = "immediate"
      }
      log_throttle_queries_not_using_indexes = {
        value        = 20
        apply_method = "immediate"
      }
    }
  }
}

import {
  to = module.ocpp16.module.ocpp_aurora[0].aws_db_parameter_group.this
  id = "ocpp-aurora-instances-prod"
}

import {
  to = module.ocpp16.module.ocpp_aurora[0].aws_db_subnet_group.this[0]
  id = "ocpp-aurora-prod-private"
}

import {
  to = module.ocpp16.module.ocpp_aurora[0].aws_rds_cluster.this
  id = "ocpp-service-prod"
}

import {
  to = module.ocpp16.module.ocpp_aurora[0].aws_rds_cluster_instance.cluster_read_instances[0]
  id = "ocpp-service-prod-read-1"
}

import {
  to = module.ocpp16.module.ocpp_aurora[0].aws_rds_cluster_instance.desiread_writer_instance
  id = "ocpp-service-prod-desired-write"
}

import {
  to = module.ocpp16.module.ocpp_aurora[0].aws_rds_cluster_parameter_group.this
  id = "ocpp-aurora-cluster-prod"
}
