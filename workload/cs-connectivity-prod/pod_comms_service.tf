locals {
  connector_statuses_sns_topic_arn = "arn:aws:sns:eu-west-1:************:pod-connector-statuses"
  ocpi_charges_sns_topic_arn       = "arn:aws:sns:eu-west-1:************:ocpi-charges-prod"
  laravel_env                      = "production"
}

module "pod_comms_service" {
  source = "../../modules/applications/cs-connectivity/pod-comms-service"

  providers = {
    aws          = aws.pod-point,
    aws.cs-state = aws.cs-state-prod
  }

  vpc_id = local.pod_point_main_account_vpc_id

  environment                   = local.environment
  private_subnets_ids           = local.pod_point_main_account_vpc_private_subnet_ids
  podadmin_db_security_group_id = "sg-04a4b434e984333bd"

  fargate_task_level_cpu    = 2048
  fargate_task_level_memory = 4096

  fargate_number_of_base_runners                = 1
  fargate_number_of_base_runners_event_consumer = 6
  fargate_weight                                = 1
  fargate_number_of_spot_base_runners           = 0
  fargate_spot_weight                           = 0

  cache_maintenance_window = "wed:23:30-thu:00:30"
  cache_instance_type      = "cache.t3.small"

  kms_admins = data.aws_iam_roles.pod_point_additional_kms_administrators.arns

  connector_statuses_sns_topic_arn               = local.connector_statuses_sns_topic_arn
  ocpi_charges_sns_topic_arn                     = local.ocpi_charges_sns_topic_arn
  default_sqs_queue_arn                          = "arn:aws:sqs:eu-west-1:************:pod-comms-service"
  default_sqs_queue_name                         = "pod-comms-service"
  unit_events_sqs_queue_arn                      = "arn:aws:sqs:eu-west-1:************:pod-unit-events"
  unit_events_sqs_queue_name                     = "pod-unit-events"
  enable_auto_scaling_sqs                        = true
  unit_event_received_sns_topic_arn              = local.pod_unit_event_sns_topic_arn
  kinesis_logging_stream_arn                     = "arn:aws:kinesis:eu-west-1:************:stream/logging"
  elasticsearch_charge_points_cluster_domain_arn = "arn:aws:es:eu-west-1:************:domain/chargepoints"

  sns_charge_notifications_subscription_allowed_principals = [
    "arn:aws:iam::044494356744:root" // XDP
  ]

  task_def_environments = [
    {
      "name"  = "APP_ENV",
      "value" = local.laravel_env
    },
    {
      "name"  = "SQS_QUEUE",
      "value" = "pod-comms-service"
    },
    {
      "name"  = "SQS_ENDPOINT",
      "value" = "https://sqs.eu-west-1.amazonaws.com/************/"
    },
    {
      "name"  = "QUEUE_DRIVER",
      "value" = "sqs"
    },
    {
      "name"  = "SCOUT_DRIVER",
      "value" = "signed-elasticsearch"
    },
    {
      "name"  = "ELASTICSEARCH_INDEX",
      "value" = "charge-points"
    },
    {
      "name"  = "ELASTICSEARCH_HOST",
      "value" = "https://search-chargepoints.pod-point.com"
    },
    {
      "name"  = "CACHE_DRIVER",
      "value" = "redis"
    },
    {
      "name"  = "SENTRY_ENABLED",
      "value" = "true"
    },
    {
      "name"  = "EVENT_QUEUE",
      "value" = "pod-unit-events"
    },
    {
      "name"  = "BROADCAST_DRIVER",
      "value" = "slack"
    },
    {
      "name"  = "SLACK_CHANNEL",
      "value" = "#pod_comms_bother"
    },
    {
      "name"  = "LOGGING_STREAM",
      "value" = "logging"
    },
    {
      "name"  = "SNS_TOPIC_CHARGES",
      "value" = local.ocpi_charges_sns_topic_arn
    },
    {
      "name"  = "SNS_TOPIC_POD_CONNECTOR_STATUSES",
      "value" = local.connector_statuses_sns_topic_arn
    },
    {
      "name"  = "DB_WRITE_HOST",
      "value" = "podadmin-prod.cluster-chbvibnzxkke.eu-west-1.rds.amazonaws.com"
    },
    {
      "name"  = "DB_READ_HOST",
      "value" = "podadmin-prod.cluster-ro-chbvibnzxkke.eu-west-1.rds.amazonaws.com"
    },
    {
      "name"  = "DB_DATABASE",
      "value" = "podpoint"
    },
    {
      "name"  = "DB_USERNAME",
      "value" = "pod_comms"
    },
  ]

  alarm_actions = ["arn:aws:sns:eu-west-1:************:cs-connectivity-opsgenie"]
}
