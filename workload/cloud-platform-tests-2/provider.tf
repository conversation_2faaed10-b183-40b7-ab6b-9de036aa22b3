provider "aws" {
  region = "eu-west-1"

  assume_role {
    role_arn     = "arn:aws:iam::${local.platform_tests_1_account_id}:role/${local.terraform_role_name}"
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment" = "development"

      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "cloud-platform-tests-1"
      "pp:terraformWorkspace"               = "technology/cloud-platform-tests-1"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}
