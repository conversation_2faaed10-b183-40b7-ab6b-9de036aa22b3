./init/fluent_bit_init_process
# Using sed to generate a new identical configurations as the bootstrip but also ammending the configuration slightly.
# These are stored in new files as docker is preventing any overwriting or replacing commands to the original files.

sed '/\[INPUT\]/a\    storage.type filesystem' /fluent-bit/etc/fluent-bit.conf > /fluent-bit/etc/custom-fluent-bit.conf
sed 's#@INCLUDE /fluent-bit/etc/fluent-bit.conf#@INCLUDE /fluent-bit/etc/custom-fluent-bit.conf#' /init/fluent-bit-init.conf > /init/custom-fluent-bit-init.conf
export FLB_AWS_USER_AGENT=ecs-init

exec /fluent-bit/bin/fluent-bit -e /fluent-bit/firehose.so -e /fluent-bit/cloudwatch.so -e /fluent-bit/kinesis.so -c /init/custom-fluent-bit-init.conf