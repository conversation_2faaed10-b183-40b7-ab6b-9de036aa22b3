module "opencharge-web-app" {
  source = "../../modules/applications/opencharge-web-app"

  providers = {
    aws                 = aws.pod-point-eu-west-1
    aws.us-east-1       = aws.pod-point-us-east-1
    aws.shared-services = aws.shared-services
  }

  build_account_id = local.build_account_id
  environment      = local.environment
  kms_admins       = data.aws_iam_roles.pod_point_administrator_and_software.arns

  vpc_id = "vpc-5178d535"
  ecs_private_subnets_ids = [
    "subnet-2415dc7c", # private-eu-west-1a
    "subnet-a8cc44cc", # private-eu-west-1b
    "subnet-10f73c58"  # private-eu-west-1c
  ]
  load_balancer_subnets = [
    "subnet-2515dc7d", # public-eu-west-1a
    "subnet-7748c113", # public-eu-west-1b
    "subnet-ea26989c"  # public-eu-west-1c
  ]
  enable_internal_lb  = false
  route53_record_name = "charge-${local.environment}.pod-point.com"

  # Monitoring and alarms
  cloudwatch_opsgenie_api_url = var.opencharge_web_app_cloudwatch_opsgenie_api_url

  # Task def environment variables values
  acm_arn    = "arn:aws:acm:${data.aws_region.current.name}:${local.pod_point_account_id}:certificate/d2916c8e-2f96-47ec-8107-2bb56f2e6b14"
  api_domain = "http://internal-api3-dev-alb-********.eu-west-1.elb.amazonaws.com"
  api_prefix = "/v4/"
  app_env    = local.environment
  app_url    = "https://charge-${local.environment}.pod-point.com"
  redis_host = "opencharge-web-app-staging-v2.3ihfkn.ng.0001.euw1.cache.amazonaws.com"

  # Feature flag environment variables
  ff_dc_charge_limit  = "true"
  ff_hide_auth_routes = "true"
}
