module "statement_service" {
  source = "../../modules/applications/experience/commercial/statement-service"

  providers = {
    aws                 = aws
    aws.route53         = aws.pod-point-eu-west-1
    aws.shared-services = aws.shared-services
    aws.us-east-1       = aws.us-east-1
  }

  admin_user_secret_manager_arn        = module.destination.admin_user_secret_manager_arn
  alb_public_subnets                   = module.destination.public_subnets_ids
  api_database_cluster_resource_id     = module.destination.destination_cluster_resource_id
  codebuild_security_group_ids         = module.destination.codebuild_security_group_ids
  destination_cluster_endpoint         = module.destination.destination_cluster_endpoint
  destination_cluster_port             = module.destination.destination_cluster_port
  destination_reader_endpoint          = module.destination.destination_reader_endpoint
  ecs_private_subnets_ids              = module.destination.private_subnets_ids
  experience_cluster_security_group_id = module.destination.experience_cluster_security_group_id
  vpc_id                               = module.destination.vpc_id

  api_desired_task_count          = 1
  queue_worker_desired_task_count = 1
  cloudfront_enable_logging       = true
  cloudfront_realtime_metrics     = "Enabled"
  environment                     = local.environment
  oidc_config_secret_id           = "azure/oidc/statement-service-dev"
  use_spot_capacity               = true
  webapp_desired_task_count       = 1

  api_source_security_group_ids = [
    module.destination.destination_site_admin_api_security_group_id,
    module.destination.internal_site_admin_api_security_group_id
  ]
}
