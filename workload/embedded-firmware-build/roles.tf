# Create an IAM role allowing the GitHub Actions user in the shared account the ability to check images
data "aws_iam_policy_document" "assume_github_actions_role" {
  statement {
    sid    = "AllowOpenIDConnect"
    effect = "Allow"

    actions = [
      "sts:AssumeRoleWithWebIdentity"
    ]

    principals {
      type        = "Federated"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/token.actions.githubusercontent.com"]
    }

    condition {
      test     = "StringEquals"
      variable = "token.actions.githubusercontent.com:aud"
      values   = ["sts.amazonaws.com"]
    }

    condition {
      test     = "ForAnyValue:StringLike"
      variable = "token.actions.githubusercontent.com:sub"
      values = [
        for repoName in local.github_repo_names :
        "repo:Pod-Point/${repoName}:*"
      ]
    }
  }
}

data "aws_iam_policy_document" "github_actions_role_policy" {
  statement {
    sid    = "ListBucketPerms"
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      module.firmware-arch5-build-artefacts-s3.s3_arn,
      module.firmware-arch5-s3.s3_bucket_arn
    ]
  }

  statement {
    sid    = "MultipartUploads"
    effect = "Allow"
    actions = [
      "s3:ListMultipartUploadParts",
      "s3:ListBucketMultipartUploads",
      "s3:AbortMultipartUpload"
    ]

    resources = [
      "${module.firmware-arch5-build-artefacts-s3.s3_arn}/*",
      "${module.firmware-arch5-s3.s3_bucket_arn}/*"
    ]
  }

  statement {
    sid    = "ObjectCRUD"
    effect = "Allow"
    actions = [
      "s3:PutObject",
      "s3:PutObjectACL",
      "s3:PutObjectVersionAcl"
    ]

    resources = [
      "${module.firmware-arch5-build-artefacts-s3.s3_arn}/*",
      "${module.firmware-arch5-s3.s3_bucket_arn}/*"
    ]
  }

  statement {
    sid    = "PermitKMSDecryptKMS"
    effect = "Allow"
    actions = [
      "kms:Decrypt",
      "kms:GenerateDataKey"
    ]
    resources = [
      module.firmware-arch5-kms.arn
    ]
  }
}

resource "aws_iam_role" "github_actions" {
  name               = "github-actions-s3-role"
  assume_role_policy = data.aws_iam_policy_document.assume_github_actions_role.json
}

resource "aws_iam_policy" "github_actions" {
  name        = "github-actions-s3-role-policy"
  description = "Policy for GitHub Actions to access S3"
  policy      = data.aws_iam_policy_document.github_actions_role_policy.json
}

resource "aws_iam_role_policy_attachment" "github_actions" {
  role       = aws_iam_role.github_actions.name
  policy_arn = aws_iam_policy.github_actions.arn
}
