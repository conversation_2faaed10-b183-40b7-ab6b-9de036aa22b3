module "kms_key_arch5_ssh_private_key" {
  source  = "terraform-enterprise.pod-point.com/technology/kms/aws"
  version = "1.0.0"

  alias_name  = "secret-arch5-ssh-private-key"
  description = "Key used for encrypting the secret containing the arch5 ssh private key"
  policy      = data.aws_iam_policy_document.kms_key_arch5_ssh_private_key.json
}

data "aws_iam_policy_document" "kms_key_arch5_ssh_private_key" {
  statement {
    sid = "KeyAdministrators"

    principals {
      type = "AWS"
      identifiers = [
        format("arn:aws:iam::%s:role/terraform-ci", data.aws_caller_identity.current.account_id)
      ]
    }

    actions   = ["kms:*"]
    resources = ["*"]
  }

  statement {
    sid = "AllowFCTHosts"

    principals {
      type        = "AWS"
      identifiers = [local.fct_host_ssm_role_arn]
    }
    actions = [
      "kms:Decrypt",
      "kms:DescribeKey"
    ]
    resources = ["*"]
  }
}

resource "aws_secretsmanager_secret" "arch5_ssh_private_key" {
  name                    = "arch5/ssh/private-key"
  description             = "Private key used for ssh the arch5 device"
  kms_key_id              = module.kms_key_arch5_ssh_private_key.id
  recovery_window_in_days = 30
}

/**
* We could label the secret version with the corresponding firmware version
*   aws secretsmanager update-secret-version-stage \
*      --secret-id <SecretId> \
*      --version-stage v0.56.0 \
*      --move-to-version-id <SecretVersionId>
*/
resource "aws_secretsmanager_secret_version" "arch5_ssh_private_key" {
  secret_id = aws_secretsmanager_secret.arch5_ssh_private_key.id

  // base64 of the secret key
  secret_string = "LS0tLS1CRUdJTiBPUEVOU1NI..."

  lifecycle {
    ignore_changes = [secret_string]
  }
}

resource "aws_secretsmanager_secret_policy" "arch5_ssh_private_key" {
  secret_arn = aws_secretsmanager_secret.arch5_ssh_private_key.arn
  policy     = data.aws_iam_policy_document.secret_arch5_ssh_private_key.json
}

data "aws_iam_policy_document" "secret_arch5_ssh_private_key" {
  statement {
    sid    = "AllowFCTHosts"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = [local.fct_host_ssm_role_arn]
    }

    actions   = ["secretsmanager:GetSecretValue"]
    resources = ["*"]
  }
}
