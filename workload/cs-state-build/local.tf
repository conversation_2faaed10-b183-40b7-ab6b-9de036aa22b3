locals {
  environment            = "build"
  region                 = "eu-west-1"
  assume_role_account_id = "************"
  assume_role_name       = "terraform-ci"

  development_account_id = "************"
  staging_account_id     = "************"
  production_account_id  = "************"

  ecr_account_id = "************"
  ecr_repositories = [
    {
      name        = "firmware-upgrade-api"
      github_repo = "firmware-upgrade",
      additional_policy_statements : []
    },
    {
      name        = "firmware-upgrade-smoke-test"
      github_repo = "firmware-upgrade",
      additional_policy_statements : []
    },
    {
      name        = "firmware-upgrade-update-queue-consumer"
      github_repo = "firmware-upgrade",
      additional_policy_statements : []
    },
    {
      name        = "firmware-upgrade-pod-unit-updates-consumer"
      github_repo = "firmware-upgrade",
      additional_policy_statements : []
    },
    {
      name        = "firmware-upgrade-ocpp-requests-consumer"
      github_repo = "firmware-upgrade",
      additional_policy_statements : []
    },
    {
      name        = "installation-completed-consumer"
      github_repo = "firmware-upgrade",
      additional_policy_statements : []
    },
    {
      name        = "firmware-upgrade-hil-testing"
      github_repo = "firmware-upgrade",
      additional_policy_statements : []
    },
    {
      name        = "firmware-upgrade-fleet-deployer"
      github_repo = "firmware-upgrade",
      additional_policy_statements : []
    },
    {
      name        = "firmware-upgrade-fleet-deployer-queue-consumer"
      github_repo = "firmware-upgrade",
      additional_policy_statements : []
    },
    {
      name        = "firmware-upgrade-fusspot"
      github_repo = "firmware-upgrade",
      additional_policy_statements : []
    },
    {
      name        = "firmware-upgrade-migrations-runner"
      github_repo = "firmware-upgrade",
      additional_policy_statements : []
    },
    {
      name        = "firmware-upgrade-management-ui"
      github_repo = "firmware-upgrade",
      additional_policy_statements : []
    },
    {
      name        = "firmware-upgrade-rollout-batcher"
      github_repo = "firmware-upgrade"
      additional_policy_statements : [{
        sid = "LambdaECRImageCrossAccountRetrievalPolicy",
        actions = [
          "ecr:BatchGetImage",
          "ecr:GetDownloadUrlForLayer"
        ],
        principals = [
          {
            type        = "Service",
            identifiers = ["lambda.amazonaws.com"]
          }
        ],
        conditions = [
          {
            test     = "ForAnyValue:StringLike"
            variable = "aws:sourceARN"
            values = [
              "arn:aws:lambda:${local.region}:${local.development_account_id}:function:*firmware-upgrade-rollout-batcher*",
              "arn:aws:lambda:${local.region}:${local.staging_account_id}:function:*firmware-upgrade-rollout-batcher*",
              "arn:aws:lambda:${local.region}:${local.production_account_id}:function:*firmware-upgrade-rollout-batcher*"
            ]
          }
        ]
      }]
    },
    {
      name        = "diagnostics-service-event-processor"
      github_repo = "diagnostics-service",
      additional_policy_statements : []
    },
    {
      name        = "diagnostics-service-database-migrations"
      github_repo = "diagnostics-service",
      additional_policy_statements : []
    },
    {
      name        = "diagnostics-service-api"
      github_repo = "diagnostics-service",
      additional_policy_statements : []
    },
    {
      name        = "certificate-service"
      github_repo = "certificate-service",
      additional_policy_statements : []
    },
    {
      name        = "diagnostics-service-smoke-test"
      github_repo = "diagnostics-service",
      additional_policy_statements : []
    },
    {
      name        = "flex-request-fulfilment-api"
      github_repo = "smart-charging-service",
      additional_policy_statements : []
    },
    {
      name        = "smart-charging-service-api"
      github_repo = "smart-charging-service",
      additional_policy_statements : []
    },
    {
      name        = "smart-charging-service-database-migrations"
      github_repo = "smart-charging-service",
      additional_policy_statements : []
    },
    {
      name        = "smart-charging-service-smoke-test"
      github_repo = "smart-charging-service",
      additional_policy_statements : []
    },
    {
      name        = "competitions-api"
      github_repo = "smart-charging-service",
      additional_policy_statements : []
    },
    {
      name        = "charging-optimisation-api"
      github_repo = "smart-charging-service",
      additional_policy_statements : []
    },
    {
      name        = "vehicles-api"
      github_repo = "smart-charging-service",
      additional_policy_statements : []
    },
    {
      name        = "tariffs-api"
      github_repo = "smart-charging-service",
      additional_policy_statements : []
    },
    {
      name        = "charging-profiles-api"
      github_repo = "smart-charging-service",
      additional_policy_statements : []
    },
    {
      name        = "energy-metrics-api"
      github_repo = "smart-charging-service",
      additional_policy_statements : []
    },
    {
      name        = "status-history-migrations"
      github_repo = "connectivity",
      additional_policy_statements : []
    },
    {
      name        = "status-history-consumer"
      github_repo = "connectivity",
      additional_policy_statements : []
    },
    {
      name        = "status-history-event-replay-worker"
      github_repo = "connectivity",
      additional_policy_statements : []
    },
    {
      name        = "status-history-event-replay-loader"
      github_repo = "connectivity",
      additional_policy_statements : []
    }
  ]
  ecr_lifecycle = {
    "rules" : [
      {
        "rulePriority" : 1,
        "description" : "Keep last 10 images deployed in prod",
        "selection" : {
          "tagStatus" : "tagged",
          "tagPrefixList" : ["deployed-prod"],
          "countType" : "imageCountMoreThan",
          "countNumber" : 10
        },
        "action" : {
          "type" : "expire"
        }
      },
      {
        "rulePriority" : 2,
        "description" : "Keep last 10 images deployed in staging",
        "selection" : {
          "tagStatus" : "tagged",
          "tagPrefixList" : ["deployed-staging"],
          "countType" : "imageCountMoreThan",
          "countNumber" : 10
        },
        "action" : {
          "type" : "expire"
        }
      },
      {
        "rulePriority" : 3,
        "description" : "Keep last 30 images",
        "selection" : {
          "tagStatus" : "any",
          "countType" : "imageCountMoreThan",
          "countNumber" : 30
        },
        "action" : {
          "type" : "expire"
        }
      }
    ]
  }

  # for our older apps (should move them over to the new pattern as part of sustainability)
  pod_point_account = "************"
}
