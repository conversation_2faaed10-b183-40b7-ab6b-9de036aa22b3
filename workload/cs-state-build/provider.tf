provider "aws" {
  region = local.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.assume_role_account_id, local.assume_role_name)
    session_name = "terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:owner"                            = "network"
      "pp:domain"                           = "network"
      "pp:service"                          = "network"
      "pp:environment"                      = "production"
      "pp:terraformConfigurationRepository" = "pod-point/terraform"
      "pp:terraformWorkspace"               = "cs-state-${local.environment}"
    }
  }
}