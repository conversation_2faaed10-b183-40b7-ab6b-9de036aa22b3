/**
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *   MDM Cloudfront Distribution
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_cloudfront_origin_access_identity" "mdm_bucket_object_store" {
  provider = aws.us-east-1

  comment = "For restricting access to CloudFront only."
}

resource "aws_cloudfront_distribution" "mdm_bucket_object_store" {
  #checkov:skip=CKV_AWS_68:WAF not required for distribution of installation packages
  provider = aws.us-east-1

  origin {
    connection_attempts = 3
    connection_timeout  = 10
    domain_name         = module.pp_mdm_bucket_object_storage.s3_bucket_bucket_regional_domain_name
    origin_id           = local.mdm_origin_id

    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.mdm_bucket_object_store.cloudfront_access_identity_path
    }
  }

  enabled         = true
  is_ipv6_enabled = true
  comment         = "CloudFront Distribution hosting the IT teams mdm storage."
  aliases         = [local.mdm_project_domain]
  price_class     = "PriceClass_100"

  default_cache_behavior {
    compress    = true
    min_ttl     = 0
    default_ttl = 0
    max_ttl     = 0

    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = local.mdm_origin_id
    viewer_protocol_policy = "redirect-to-https"
    cache_policy_id        = data.aws_cloudfront_cache_policy.default.id

    dynamic "lambda_function_association" {
      for_each = local.mdm_lambdas_at_edge_functions
      iterator = lambda

      content {
        event_type   = lambda.value.event_type
        include_body = lambda.value.include_body
        lambda_arn   = lambda.value.lambda_arn
      }
    }
  }

  dynamic "logging_config" {
    for_each = local.mdm_cf_traffic_logging
    iterator = logging

    content {
      include_cookies = logging.value.include_cookies
      bucket          = logging.value.bucket
      prefix          = logging.value.prefix
    }
  }

  dynamic "custom_error_response" {
    for_each = local.mdm_custom_error_handling_rules
    iterator = error_response

    content {
      error_code            = error_response.key
      response_code         = error_response.value.response_code
      response_page_path    = error_response.value.response_page_path
      error_caching_min_ttl = error_response.value.error_caching_min_ttl
    }
  }

  viewer_certificate {
    acm_certificate_arn      = module.acm_mdm_object_store.acm_certificate_arn
    minimum_protocol_version = "TLSv1.2_2021"
    ssl_support_method       = "sni-only"
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
}
