# https://terraform-enterprise.pod-point.com/app/technology/workspaces/ownership-orders-staging/variables
variable "amplify_setup_access_token" {
  type        = string
  default     = "none"
  description = "GitHub Personal Access Token to setup Amplify apps - only required at creation, not stored, can be revoked and removed after first apply."
}

variable "prod_account_id" {
  description = "The account ID for the squads production account."
  type        = string
  default     = "************"
}

variable "assume_role_name" {
  description = "The role to assume when initialising the AWS provider."
  type        = string
  default     = "terraform-ci"
}

variable "pod_point_account_id" {
  description = "The account ID for the Pod Point account."
  type        = string
  default     = "************" # Pod Point
}
