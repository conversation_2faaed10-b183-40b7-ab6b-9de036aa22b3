module "ecommerce_web_app_uk" {
  source = "../../modules/applications/ownership-orders/ecommerce-web-app"

  providers = {
    aws         = aws
    aws.global  = aws.global
    aws.route53 = aws.pod-point-main-global
  }

  access_token                = var.amplify_setup_access_token
  bigcommerce_store_hash      = "mgkttec465"
  bigcommerce_store_subdomain = "payment"
  branch                      = "main"
  country                     = "uk"
  domain                      = "pod-point.com"
  enable_basic_auth           = false
  enable_waf                  = true
  environment                 = "prod"
  manage_domain               = true
  subdomain                   = "shop"
  migrate_domain = {
    from   = "d3ldx02lba5qp1.cloudfront.net"
    active = true
  }

  environment_variables = {
    CYPRESS_USER_AUTH_EMAIL                      = "<EMAIL>"
    GATSBY_BIG_COMMERCE_PAYMENT_API_URL          = "https://payment.pod-point.com"
    GATSBY_BIG_COMMERCE_PROXY_API_URL            = "https://ecommerce-service.pod-point.com"
    GATSBY_BIGCOMMERCE_CHANNEL                   = "1"
    GATSBY_BIGCOMMERCE_STORE_HASH                = "mgkttec465"
    GATSBY_FIREBASE_AUTH_DOMAIN                  = "opencharge-mobile-app-e376b.firebaseapp.com"
    GATSBY_FIREBASE_PROJECT_ID                   = "opencharge-mobile-app-e376b"
    GATSBY_LINKEDIN_HOMECHARGE_MQL_CONVERSION_ID = "********"
    GATSBY_PRISMIC_LOCALE                        = "en-gb"
    GATSBY_VWO_ACCOUNT_API_KEY                   = "a715c10c134734b720314acfbc9ecc34"
    SYNC_WEBHOOKS                                = "0"
    VEHICLE_ENDPOINT_MODELS                      = "https://vehicles-api.pod-point.com/models"
    VEHICLE_ENDPOINT_TOKEN                       = "https://pod-point-vehicles-api.auth.eu-west-1.amazoncognito.com/oauth2/token"
  }

  build_notifications_emails = [
    "<EMAIL>",
  ]
}

module "ecommerce_web_app_ie" {
  source = "../../modules/applications/ownership-orders/ecommerce-web-app"

  providers = {
    aws         = aws
    aws.global  = aws.global
    aws.route53 = aws.pod-point-main-global # unsued for pod-point.ie atm
  }

  access_token      = var.amplify_setup_access_token
  branch            = "main"
  country           = "ie"
  domain            = "pod-point.ie"
  enable_basic_auth = false
  enable_waf        = true
  environment       = "prod"
  manage_domain     = false
  subdomain         = "shop"
  migrate_domain = {
    from   = "dkv0lz0kgmvy9.cloudfront.net"
    active = true
  }

  environment_variables = {
    CYPRESS_USER_AUTH_EMAIL                      = "<EMAIL>"
    GATSBY_BIG_COMMERCE_PAYMENT_API_URL          = "https://payment.pod-point.ie"
    GATSBY_BIG_COMMERCE_PROXY_API_URL            = "https://ecommerce-service.pod-point.ie"
    GATSBY_BIGCOMMERCE_CHANNEL                   = "1134225"
    GATSBY_BIGCOMMERCE_STORE_HASH                = "mgkttec465"
    GATSBY_FIREBASE_AUTH_DOMAIN                  = "opencharge-mobile-app-e376b.firebaseapp.com"
    GATSBY_FIREBASE_PROJECT_ID                   = "opencharge-mobile-app-e376b"
    GATSBY_LINKEDIN_HOMECHARGE_MQL_CONVERSION_ID = "********"
    GATSBY_PRISMIC_LOCALE                        = "en-ie"
    GATSBY_VWO_ACCOUNT_API_KEY                   = "a715c10c134734b720314acfbc9ecc34"
    SYNC_WEBHOOKS                                = "0"
    VEHICLE_ENDPOINT_MODELS                      = "https://vehicles-api.pod-point.com/models"
    VEHICLE_ENDPOINT_TOKEN                       = "https://pod-point-vehicles-api.auth.eu-west-1.amazoncognito.com/oauth2/token"
  }

  build_notifications_emails = [
    "<EMAIL>",
  ]
}
