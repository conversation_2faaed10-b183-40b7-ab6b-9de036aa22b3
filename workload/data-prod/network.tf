# 1 public/private subnet per AZ

module "vpc" {
  source  = "terraform-enterprise.pod-point.com/technology/vpc/aws"
  version = "2.9.3"

  availability_zones_count = 3
  vpc_name                 = "${local.environment}-vpc"
  vpc_cidr_block           = local.vpc_cidr
  subnet_offset            = 1

  # Flow logs enabled to keep track of access to data components via VPN
  enable_flow_logs                                = true
  flow_log_cloudwatch_log_group_retention_in_days = 1096

  enable_vpc_endpoint_ecr_api = true
  enable_vpc_endpoint_ecr_dkr = true
}

##########################################
// ------ DATABASE SUBNET GROUPS ------ //
##########################################

# Create a db subnet group for public/private subnets, to be replaced in future with a subnet per db

resource "aws_db_subnet_group" "public" {
  name       = "rds-public-${local.environment}"
  subnet_ids = module.vpc.public_subnets_ids

  tags = {
    Name = "subnet-group"
    Tier = "public"
  }
}

resource "aws_db_subnet_group" "private" {
  name       = "rds-private-${local.environment}"
  subnet_ids = module.vpc.private_subnets_ids

  tags = {
    Name = "subnet-group"
    Tier = "private"
  }
}

####################################################
// ----------- APPLICATION LOAD BALANCER -------- //
###################################################

// This will be the main dev alb to reference in other tf modules to attach listener rules to.

module "alb" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws"
  version = "2.1.3"

  load_balancer_name = "data-lb-${local.environment}"
  load_balancer_type = "application"
  enable_internal_lb = true

  enable_http2        = true
  deletion_protection = true

  vpc_id                = module.vpc.vpc_id
  load_balancer_subnets = module.vpc.private_subnets_ids

  access_logs = {
    enable_logs          = true
    create_bucket_policy = true
  }

  security_group_name        = "lb-sg-${local.environment}"
  security_group_description = "Data teams application Load-Balancer security group. Inbound from VPN ip addresses (public/private)."
  security_group_ingress_rules = {
    "permit_vpn_https" = {
      description = "Permit HTTPS over the AWS Client VPN."
      from_port   = 443
      to_port     = 443
      protocol    = "TCP"
      ipv4_cidrs  = local.vpn_cidr
    }

    "permit_vpn_http" = {
      description = "Permit HTTP over the AWS Client VPN."
      from_port   = 80
      to_port     = 80
      protocol    = "TCP"
      ipv4_cidrs  = local.vpn_cidr
    }

    "permit_dagster_daemon_443" = {
      description           = "Permit ingress from the dagster daemon ECS task."
      from_port             = 443
      to_port               = 443
      protocol              = "TCP"
      source_security_group = module.dagster.dagster_daemon_fargate_sec_group_id
    }

    "permit_dagster_reports_443" = {
      description           = "Permit ingress from the dagster reports ECS task."
      from_port             = 443
      to_port               = 443
      protocol              = "TCP"
      source_security_group = module.dagster.dagster_reports_common_fargate_sec_group_id
    }
  }

  security_group_egress_rules = {
    "permit_all_external" = {
      description = "Permit all egress traffic."
      from_port   = 0
      to_port     = 0
      protocol    = "-1"
      ipv4_cidrs  = ["0.0.0.0/0"]
      ipv6_cidrs  = ["::/0"]
    }
  }

  // We can have up to 50 Listeners per Load balancers
  // Good method to save some $$
  https_listeners = [
    {
      port            = 443
      protocol        = "HTTPS"
      certificate_arn = module.acm.arn
      ssl_policy      = "ELBSecurityPolicy-2016-08"
      action_type     = "fixed-response"

      fixed_response = {
        content_type = "text/plain"
        message_body = "Resources for front-end applications in ${local.environment} <NAME_EMAIL>."
        status_code  = "200"
      }
    }
  ]

  target_groups = [
    {
      name                 = "airbyte-api-tg-${local.environment}"
      backend_protocol     = "HTTP"
      backend_port         = 8001 # LOOK
      target_type          = "instance"
      deregistration_delay = 300
      slow_start           = "0"
      vpc_id               = module.vpc.vpc_id

      health_check = {
        enabled             = true
        interval            = 20
        path                = "/api/v1/health"
        port                = "traffic-port"
        healthy_threshold   = 3
        unhealthy_threshold = 3
        timeout             = 5
        protocol            = "HTTP"
        matcher             = "200"
      }
      protocol_version = "HTTP1"
      targets = {
        my_ec2 = {
          target_id = module.airbyte.airbyte_server_id
          port      = 8001
        }
      }
    },
    {
      name                 = "airbyte-webapp-tg-${local.environment}"
      backend_protocol     = "HTTP"
      backend_port         = 8000 # LOOK
      target_type          = "instance"
      deregistration_delay = 300
      slow_start           = "0"
      vpc_id               = module.vpc.vpc_id

      health_check = {
        enabled             = true
        interval            = 20
        path                = "/"
        port                = "traffic-port"
        healthy_threshold   = 3
        unhealthy_threshold = 3
        timeout             = 5
        protocol            = "HTTP"
        matcher             = "200"
      }
      protocol_version = "HTTP1"
      targets = {
        my_ec2 = {
          target_id = module.airbyte.airbyte_server_id
          port      = 8000
        }
      }
    },
    {
      name                 = "dagit-2"
      backend_protocol     = "HTTP"
      backend_port         = local.dagit.port
      target_type          = "ip"
      deregistration_delay = 300
      slow_start           = "0"
      vpc_id               = module.vpc.vpc_id

      health_check = {
        enabled             = true
        interval            = 60
        path                = "/dagit_info"
        port                = "traffic-port"
        healthy_threshold   = 3
        unhealthy_threshold = 3
        timeout             = 5
        protocol            = "HTTP"
        matcher             = "200"
      }
    }
  ]

  https_listener_rules = [
    {
      https_listener_index = 0
      priority             = 5
      actions = [{
        type               = "forward"
        target_group_index = 0
      }]

      conditions = [{
        host_headers = [
          aws_route53_record.airbyte.name
        ]
        path_patterns = [
          "/api/v1/*"
        ]
      }]
    },
    {
      https_listener_index = 0
      priority             = 10
      actions = [{
        type               = "forward"
        target_group_index = 1
      }]

      conditions = [{
        host_headers = [
          aws_route53_record.airbyte.name
        ]
        path_patterns = [
          "/*"
        ]
      }]
    },
    {
      https_listener_index = 0
      priority             = 15
      actions = [{
        type               = "forward"
        target_group_index = 2
      }]

      conditions = [{
        host_headers = [
          aws_route53_record.dagster.name
        ]
        path_patterns = [
          "/*"
        ]
      }]
    }
  ]

  http_tcp_listeners = [
    {
      port        = 80
      protocol    = "HTTP"
      action_type = "redirect"
      redirect = {
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    }
  ]

  tags = {
    Deployment = "application-loadbalancer-${local.environment}"
  }
}

module "alb_access_logs_database" {
  source  = "terraform-enterprise.pod-point.com/technology/lb/aws//modules/athena_access_logs_database"
  version = "2.1.3"

  athena_workgroup_identifier          = "data-lb-${local.environment}-access-logs"
  athena_workgroup_description         = "Collection of settings for data-lb-${local.environment}"
  athena_database_identifier           = "data_lb_${local.environment}"
  athena_database_s3_bucket_identifier = "data-lb-${local.environment}-query-execution"
  glue_catalogue_identifier            = "data_lb_${local.environment}_alb_access_logs"
  s3_bucket_name                       = module.alb.s3_access_log_bucket_id
  partition_projection_range           = "2023/10/01,NOW"
}

#############################################
// --------- R53 FORWARD TO LB ---------- //
############################################

// This needs to be created in Pod-Point Main account
// This will be the R53 record for airbyte
// We can scale this for dagster and other applications

resource "aws_route53_record" "airbyte" {
  provider = aws.pod-point
  zone_id  = local.pod_point_com_hosted_zone
  name     = "airbyte-${local.environment}.pod-point.com"
  type     = "A"

  alias {
    name                   = module.alb.load_balancer_dns_name
    zone_id                = module.alb.load_balancer_zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "dagster" {
  provider = aws.pod-point
  zone_id  = local.pod_point_com_hosted_zone
  name     = "dagster.pod-point.com"
  type     = "A"

  alias {
    name                   = module.alb.load_balancer_dns_name
    zone_id                = module.alb.load_balancer_zone_id
    evaluate_target_health = true
  }
}

################################
// ------ Route Tables ------ //
################################

data "aws_route_tables" "private" {
  vpc_id = module.vpc.vpc_id
  filter {
    name   = "tag:Name"
    values = ["${local.environment}-vpc-private*"]
  }
}

## CS-STATE-PROD
resource "aws_route" "cs-state-prod" {
  count          = length(data.aws_route_tables.private.ids)
  route_table_id = sort(data.aws_route_tables.private.ids)[count.index]
  // Destination will be cs-state-prod main vpc
  destination_cidr_block = "*********/16"
  transit_gateway_id     = local.transit_gateway_id
}

## CS-CONNECTIVITY-PROD
resource "aws_route" "cs-connectivity-prod" {
  count          = length(data.aws_route_tables.private.ids)
  route_table_id = sort(data.aws_route_tables.private.ids)[count.index]
  // Destination will be cs-connectivity-prod main vpc
  destination_cidr_block = "10.102.0.0/16"
  transit_gateway_id     = local.transit_gateway_id
}
