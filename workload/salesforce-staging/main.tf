module "salesforce_staging" {
  source      = "../../modules/applications/salesforce"
  environment = "staging"

  odp_account_id           = "************"
  odp_event_bus_arn        = "arn:aws:events:eu-west-1:************:event-bus/salesforce"
  ownership_event_bus_name = "aws.partner/salesforce.com/00DKM000000GyeH2AS/0YLKM000000CaS04AK"

  experience_event_bus_name                        = "arn:aws:events:eu-west-1:************:event-bus/aws.partner/salesforce.com/00DKM000000GyeH2AS/0YL9Z0000000Qs9WAE"
  experience_subscriptions_api_incoming_events_arn = "arn:aws:sqs:eu-west-1:************:subscriptions-api-incoming-events"

  assets_installation_completed_incoming_sns_topic_arn = local.assets_cs_lifecycle_events_topic_arn
}
