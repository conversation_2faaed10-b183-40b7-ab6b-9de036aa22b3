module "firmware_upgrade" {
  source = "../../modules/applications/cs-state/firmware_upgrade"

  providers = {
    aws                     = aws,
    aws.us-east-1           = aws.us-east-1,
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1,
    aws.shared-services     = aws.shared-services
  }

  environment                       = local.environment
  podadmin_read_endpoint            = local.podadmin_read_endpoint
  podadmin_read_write_endpoint      = local.podadmin_read_write_endpoint
  podadmin_user_password            = var.podadmin_user_password
  podadmin_security_group_id        = local.podadmin_security_group_id
  region                            = local.region
  pod_unit_event_received_topic_arn = local.pod_unit_event_received_topic_arn
  private_subnet_ids                = module.vpc.private_subnets_ids
  public_subnet_ids                 = module.vpc.public_subnets_ids
  vpc_id                            = module.vpc.vpc_id
  vpc_cidr_block                    = local.vpc_cidr_block
  additional_kms_administrators     = local.developer_iam_role_arns
  execute_api_vpc_endpoint_id       = module.vpc_endpoints.endpoints["execute-api"].id
  vpc_endpoint_ids = [
    module.vpc_endpoints.endpoints["execute-api"].id,
    local.vpc_endpoint_experience_dev,
    local.vpc_endpoint_experience_staging,
    local.vpc_endpoint_connectivity_dev,
    local.vpc_endpoint_connectivity_staging,
    local.vpc_endpoint_network_assets_staging,
    local.vpc_endpoint_connectivity_execute_api_main_account_staging,
  ]
  vpc_link_sg_name          = "firmware-upgrade-staging-vpc-link"
  api_gateway_ingress_rules = local.common_api_ingress_rules

  firmware_updates_delay_in_seconds = 5

  pod_unit_updates_queue_consumer_scaling_min_capacity = 1
  pod_unit_updates_queue_consumer_scaling_max_capacity = 4

  update_queue_consumer_scaling_min_capacity = 1
  update_queue_consumer_scaling_max_capacity = 4

  pod_point_main_account_id  = local.pod_point_main_account_id
  build_account_id           = local.build_account_id
  embedded_build_account_id  = local.embedded_build_account_id
  cs_connectivity_account_id = local.cs_connectivity_account_id
  experience_account_ids = {
    // retiring assets development means experience development connects to staging
    development = "************"
    staging     = "************"
  }
  firmware_upgrade_vpc_endpoint_service_name = "com.amazonaws.vpce.eu-west-1.vpce-svc-07d05f10917d25fa2"

  should_deploy_fusspot                  = true
  fusspot_commissioning_service_url      = "https://commission-staging.pod-point.com/pcbs"
  fusspot_commissioning_service_username = "<EMAIL>"
  fusspot_arch2_lambda_url               = "https://api-proxy-dev.pod-point.com"

  fleet_deployer_enabled             = true
  fleet_deployer_schedule_expression = "cron(0/5 9-17 ? * MON-FRI *)" // every 5 mins between 09:00 and 17:00, mon-fri

  cloudwatch_alarm_topic_arn = local.assets_opsgenie_topic_arn

  asset_service_api_vpc_endpoint_service_name = data.terraform_remote_state.network_assets_staging.outputs.vpc_endpoint_service_name

  ocpp_cs_requests_topic_arn                        = local.ocpp_cs_requests_topic_arn
  ocpp_requests_queue_consumer_scaling_min_capacity = 1
  ocpp_requests_queue_consumer_scaling_max_capacity = 2

  embedded_firmware_arch_5_bucket_arn         = local.embedded_firmware_arch_5_bucket_arn
  embedded_firmware_arch_5_bucket_name        = local.embedded_firmware_arch_5_bucket_name
  embedded_firmware_arch_5_bucket_kms_key_arn = local.embedded_firmware_arch_5_bucket_kms_key_arn
  arch_5_target_manifest_version              = "A50P-1.23.8"
  rollout_batcher_enabled                     = "ENABLED"
  rollout_batcher_schedule_expression         = "cron(0/1 9-17 ? * MON-FRI *)" // every 1 mins between 09:00 and 17:00, mon-fri

  #Commands AP
  commands_api_vpc_endpoint_service_name = data.terraform_remote_state.cs_connectivity_staging.outputs.commands_api_vpc_endpoint_service_name

  arch5_firmware_bundle_events_topic_arn = local.arch5_firmware_bundle_events_topic_arn

  # Installation Completed Consumer
  installation_completed_events_topic_arn                    = local.installation_completed_events_topic_arn
  installation_completed_queue_consumer_scaling_min_capacity = 1
  installation_completed_queue_consumer_scaling_max_capacity = 2

  # Short URL API
  short_url_api = {
    rest_api_id = "d2kgwjzsdh"
    endpoint    = "https://short-staging.pod-point.com"
  }

  certificate_service_api = {
    rest_api_id = "4ntzj4s3jb"
    endpoint    = local.certificate_service_url
  }

  log_level = "debug"
}
