provider "aws" {
  region = local.region

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.assume_role_account_id, local.assume_role_name)
    session_name = "terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  alias  = "us-east-1"
  region = "us-east-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.assume_role_account_id, local.assume_role_name)
    session_name = "terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  alias  = "pod-point-eu-west-1"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.pod_point_main_account_id, local.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  alias  = "shared-services"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.shared_services_account_id, local.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  alias  = "pod-point-us-east-1"
  region = "us-east-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.pod_point_main_account_id, local.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  region = local.region
  alias  = "pp-network-hub"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", "************", local.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  region = local.region
  alias  = "vpn"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", "************", local.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}
