module "commissioning-service" {
  source = "../../modules/applications/cs-state/commissioning_service"

  providers = {
    aws                     = aws.pod-point-eu-west-1
    aws.pod-point-us-east-1 = aws.pod-point-us-east-1
  }

  commissioning_api_domain_name = "commission-staging.pod-point.com"
  acm_arn                       = "arn:aws:acm:eu-west-1:${local.pod_point_main_account_id}:certificate/d2916c8e-2f96-47ec-8107-2bb56f2e6b14"

  aws_region = local.region

  ecs_private_subnets_ids = [
    "subnet-2415dc7c", # private-eu-west-1a
    "subnet-a8cc44cc", # private-eu-west-1b
    "subnet-10f73c58"  # private-eu-west-1c
  ]

  alb_public_subnets_ids = [
    "subnet-2515dc7d", # public-eu-west-1a
    "subnet-7748c113", # public-eu-west-1b
    "subnet-ea26989c"  # public-eu-west-1c
  ]

  environment = "staging"
  initial_task_def_environments = [
    {
      "name" : "DB_HOST",
      "value" : "write-podadmin.aws.staging"
    },
    {
      "name" : "SLACK_CHANNEL",
      "value" : "#pcb-commissioning-tst"
    },
    {
      "name" : "ADMIN_TOOL_BASE_URL",
      "value" : "https://admin-staging.pod-point.com"
    },
    {
      "name" : "ADMIN_TOOL_PCBS_ROUTE",
      "value" : "/pcbs"
    },
    {
      "name" : "LOGGING_STREAM",
      "value" : "logging"
    },
    {
      "name" : "DB_WRITE_HOST",
      "value" : "write-podadmin.aws.staging"
    },
    {
      "name" : "DB_READ_HOST",
      "value" : "read-podadmin.rds.aws.staging"
    }
  ]
  vpc_id = "vpc-5178d535"

  podadmin_db_security_group_id = "sg-0ef05fc0f6219f8cd"

  cloudwatch_alarm_topic_arn = module.opsgenie.topic_arn

  api_scaling_min_capacity               = 2
  api_scaling_max_capacity               = 2
  api_deployment_minimum_healthy_percent = 50

  enable_waf                  = true
  cloudfront_enable_logging   = true
  cloudfront_realtime_metrics = "Enabled"

  additional_kms_administrators = local.developer_iam_role_arns
}
