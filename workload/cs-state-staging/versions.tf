terraform {
  backend "remote" {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"

    workspaces {
      name = "cs-state-staging"
    }
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.6.2, < 6.0.0"
    }
    random = {
      source  = "hashicorp/random"
      version = ">= 3.5.1, < 4.0.0"
    }
  }

  required_version = ">= 1.3.5"
}
