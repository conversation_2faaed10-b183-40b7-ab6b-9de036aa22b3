module "mis" {
  source = "../../modules/applications/cs-state/mis"

  providers = {
    aws                 = aws.pod-point-eu-west-1,
    aws.us-east-1       = aws.pod-point-us-east-1,
    aws.shared-services = aws.shared-services
  }

  web_app_domain_name     = "admin-staging.pod-point.com"
  static_page_domain_name = "manage-staging.pod-point.com"
  acm_arn                 = "arn:aws:acm:eu-west-1:${local.pod_point_main_account_id}:certificate/d2916c8e-2f96-47ec-8107-2bb56f2e6b14"
  acm_arn_cloudfront      = "arn:aws:acm:us-east-1:${local.pod_point_main_account_id}:certificate/f3d747b5-887c-4ffb-85d3-17e12b4140e8"
  aws_region              = local.region

  ecs_private_subnets_ids = [
    "subnet-2415dc7c", # private-eu-west-1a
    "subnet-a8cc44cc", # private-eu-west-1b
    "subnet-10f73c58"  # private-eu-west-1c
  ]

  alb_public_subnets_ids = [
    "subnet-2515dc7d", # public-eu-west-1a
    "subnet-7748c113", # public-eu-west-1b
    "subnet-ea26989c"  # public-eu-west-1c
  ]

  environment = "staging"
  initial_task_def_environments = [
    {
      "name" : "API_DOMAIN",
      "value" : "http://internal-api3-stage-alb-**********.eu-west-1.elb.amazonaws.com"
    },
    {
      "name" : "API_USER_AGENT",
      "value" : "MIS"
    },
    {
      "name" : "APP_ENV",
      "value" : "staging"
    },
    {
      "name" : "APP_URL",
      "value" : "http://admin-staging.pod-point.com/"
    },
    {
      "name" : "AWS_ENABLED",
      "value" : "1"
    },
    {
      "name" : "AWS_EVENTBRIDGE_SOURCE",
      "value" : "com.pod-point.admin-tool.staging"
    },
    {
      "name" : "AWS_CHARGING_STATION_PROVISIONED_SQS_SNS_TOPIC_ARN",
      "value" : "arn:aws:sns:eu-west-1:${local.network_assets_account_id}:charging-station-provisioning-events"
    },
    {
      "name" : "AWS_CHARGING_STATION_PROVISIONING_SQS_SNS_TOPIC_ARN",
      "value" : "arn:aws:sns:eu-west-1:${local.network_assets_account_id}:charging-station-provisioning-events"
    },
    {
      "name" : "AWS_INSTALLATIONS_SQS_SNS_TOPIC_ARN",
      "value" : "arn:aws:sns:eu-west-1:${local.pod_point_main_account_id}:installation-events-staging"
    },
    {
      "name" : "AWS_ORGANISATION_INVITATIONS_SQS_SNS_TOPIC_ARN",
      "value" : "arn:aws:sns:eu-west-1:${local.pod_point_main_account_id}:accounts-service-organisation-invitation-events-staging"
    },
    {
      "name" : "AWS_ORGANISATION_USERS_SQS_SNS_TOPIC_ARN",
      "value" : "arn:aws:sns:eu-west-1:${local.pod_point_main_account_id}:accounts-service-organisation-user-events-staging"
    },
    {
      "name" : "AWS_ORGANISATIONS_SQS_SNS_TOPIC_ARN",
      "value" : "arn:aws:sns:eu-west-1:${local.pod_point_main_account_id}:accounts-service-organisation-events-staging"
    },
    {
      "name" : "AWS_REGION",
      "value" : "eu-west-1"
    },
    {
      "name" : "AWS_SQS_SNS_QUEUE",
      "value" : "admin-tool-pub-sub-events-staging"
    },
    {
      "name" : "AWS_SQS_SNS_SUB_QUEUE_PREFIX",
      "value" : "https://sqs.eu-west-1.amazonaws.com/${local.pod_point_main_account_id}/"
    },
    {
      "name" : "AWS_USER",
      "value" : "admin-tool-staging"
    },
    {
      "name" : "CLICKATELL_API_ID",
      "value" : "api_id"
    },
    {
      "name" : "CLICKATELL_CALLBACK_USERNAME",
      "value" : "username"
    },
    {
      "name" : "COMMS_INDEX_PREFIX",
      "value" : "comms-"
    },
    {
      "name" : "COMMS_LAST_CONTACT_TOLERANCE",
      "value" : "30"
    },
    {
      "name" : "COMMS_PARTITION_MAX_SIZE",
      "value" : "5000"
    },
    {
      "name" : "DB_DATABASE",
      "value" : "podpoint"
    },
    {
      "name" : "DB_HOST",
      "value" : "write-podadmin.aws.staging"
    },
    {
      "name" : "DB_READ_HOST",
      "value" : "write-podadmin.aws.staging"
    },
    {
      "name" : "DB_USERNAME",
      "value" : "mis"
    },
    {
      "name" : "DB_WRITE_HOST",
      "value" : "write-podadmin.aws.staging"
    },
    {
      "name" : "DECOMMISSIONING_GROUP_IDS",
      "value" : "1,1220,218" // software team, data team, PD team
    },
    {
      "name" : "DIAGNOSTICS_SERVICE_API_BASE_URL",
      "value" : "https://ssaxwy2lte-vpce-0876136c82b062f17.execute-api.eu-west-1.amazonaws.com/staging"
    },
    {
      "name" : "ELASTICSEARCH_INDEX",
      "value" : "pod-units-staging"
    },
    {
      "name" : "ENROLMENT_SERVICE_API_DRIVER",
      "value" : "default"
    },
    {
      "name" : "ENROLMENT_SERVICE_API_BASE_URL",
      "value" : "i9gjmrhyj4-vpce-0876136c82b062f17.execute-api.eu-west-1.amazonaws.com/staging"
    },
    {
      "name" : "ENROLMENT_SERVICE_ENABLED",
      "value" : "true"
    },
    {
      "name" : "FEATURES_POD_LOCATION_EVENT_LOG_ENABLED",
      "value" : "true"
    },
    {
      "name" : "FEATURES_POD_UNIT_EVENT_LOG_ENABLED",
      "value" : "true"
    },
    {
      "name" : "FEATURES_POD_COMMISSIONING_RFID_CREATE_LOCATION_ENABLED",
      "value" : "false"
    },
    {
      "name" : "FIRMWARE_UPGRADE_API_BASE_URL",
      "value" : "https://34q4zjsftb-vpce-0876136c82b062f17.execute-api.eu-west-1.amazonaws.com/staging"
    },
    {
      "name" : "INSTALLS_URL",
      "value" : "http://ordering-staging.pod-point.com/"
    },
    {
      "name" : "INSTALLS_USERNAME",
      "value" : "<EMAIL>"
    },
    {
      "name" : "KIBANA_ENDPOINT",
      "value" : "search-logging-comms-staging.pod-point.com"
    },
    {
      "name" : "KIBANA_PCB_COMMS_VIEW_UUID",
      "value" : "35635460-6e19-11e8-a173-7757a892d02c"
    },
    {
      "name" : "KIBANA_UNIT_COMMS_VIEW_UUID",
      "value" : "35635460-6e19-11e8-a173-7757a892d02c"
    },
    {
      "name" : "LOGGING_COMMS_ELASTICSEARCH_HOST",
      "value" : "https://${data.terraform_remote_state.pod_point.outputs.logging_comms_vpc_endpoint_staging}"
    },
    {
      "name" : "LOGGING_COMMS_ELASTICSEARCH_INDEX",
      "value" : "comms-*"
    },
    {
      "name" : "NEXMO_KEY",
      "value" : "nexmo_key"
    },
    {
      "name" : "NEXMO_PHONE_NUMBER",
      "value" : "nexmo_phone_number"
    },
    {
      "name" : "PERFORMANCE_DB_DATABASE",
      "value" : "performance"
    },
    {
      "name" : "PERFORMANCE_DB_USERNAME",
      "value" : "podpoint"
    },
    {
      "name" : "POD_UNITS_SCOUT_INDEX",
      "value" : "charge-points"
    },
    {
      "name" : "PODPOINT_AUTH_KEY",
      "value" : "8"
    },
    {
      "name" : "PODPOINT_AUTH_URL",
      "value" : "http://internal-auth-stage-alb-1383344453.eu-west-1.elb.amazonaws.com"
    },
    {
      "name" : "PODPOINT_AUTH_FRONT_END_URL",
      "value" : "https://auth-staging.pod-point.com"
    },
    {
      "name" : "READ_REPLICA_DB_DATABASE",
      "value" : "podpoint"
    },
    {
      "name" : "READ_REPLICA_DB_HOST",
      "value" : "write-podadmin.aws.staging"
    },
    {
      "name" : "READ_REPLICA_DB_USERNAME",
      "value" : "mis_readonly"
    },
    {
      "name" : "REDIS_HOST",
      "value" : "admin-tool-staging-v2.3ihfkn.ng.0001.euw1.cache.amazonaws.com"
    },
    {
      "name" : "S3_BUCKET",
      "value" : "podpoint-admin-tool-dev"
    },
    {
      "name" : "S3_REGION",
      "value" : "eu-west-1"
    },
    {
      "name" : "SCOUT_HOST",
      "value" : "https://search-chargepoints-staging.pod-point.com"
    },
    {
      "name" : "SNS_TOPIC_LOCATIONS",
      "value" : "arn:aws:sns:eu-west-1:${local.pod_point_main_account_id}:ocpi-locations-preprod"
    },
    {
      "name" : "SQS_QUEUE",
      "value" : "https://sqs.eu-west-1.amazonaws.com/${local.pod_point_main_account_id}/admin-staging"
    },
    {
      "name" : "SQS_QUEUE_POD_CONNECTOR_STATUSES",
      "value" : "https://sqs.eu-west-1.amazonaws.com/${local.pod_point_main_account_id}/pod-connector-statuses-staging"
    },
    {
      "name" : "STREAM_BASE_URL",
      "value" : "https://api.stream-communications.com/v3.1/"
    },
    {
      "name" : "SMART_CHARGING_SERVICE_BASE_URL",
      "value" : "yxolbd8730-vpce-0876136c82b062f17.execute-api.eu-west-1.amazonaws.com/staging"
    },
    {
      "name" : "SMART_CHARGING_SERVICE_ENABLED",
      "value" : "true"
    }
  ]
  vpc_id                            = "vpc-5178d535"
  ecs_task_role_additional_policies = local.mis_additional_policy_arns

  podadmin_db_security_group_id = "sg-0ef05fc0f6219f8cd"
  redis_cache_security_group_id = "sg-0e95a6dfe137f25f5"

  cloudwatch_alarm_topic_arn           = module.opsgenie.topic_arn
  cloudwatch_alarm_topic_arn_us_east_1 = module.opsgenie.topic_arn_us_east_1

  cs_state_scheduled_tasks_opsgenie_api = var.cs_state_scheduled_tasks_opsgenie_api

  pod_connector_statuses_sns_topic_arn           = "arn:aws:sns:eu-west-1:${local.pod_point_main_account_id}:pod-connector-statuses-staging"
  elasticsearch_logging_comms_cluster_domain_arn = "arn:aws:es:eu-west-1:${local.pod_point_main_account_id}:domain/logging-comms-staging"
  elasticsearch_charge_points_cluster_domain_arn = "arn:aws:es:eu-west-1:${local.pod_point_main_account_id}:domain/chargepoints-staging"

  web_app_scaling_min_capacity         = 2
  web_app_scaling_max_capacity         = 2
  web_app_capacity_fargate_spot_weight = 1
  web_app_force_new_deployment         = true

  default_queue_consumer_scaling_min_capacity = 1
  default_queue_consumer_scaling_max_capacity = 2

  enable_scheduled_tasks = true

  oidc_bypass_ips = [
    "************", // Celestica Romania
    "************", // Celestica Romania
    "***********"   // Leo home
  ]

  additional_kms_administrators = local.developer_iam_role_arns

  asset_service_api_vpc_endpoint_service_name                   = data.terraform_remote_state.network_assets_staging.outputs.vpc_endpoint_service_name
  connectivity_ocpp16_wbs_api_gateway_authoriser_security_group = data.terraform_remote_state.cs_connectivity_staging.outputs.ocpp16.ocpp16_wbs_api_gateway_authoriser_security_group

  charging_station_provisioning_events_topic_arn = local.charging_station_provisioning_events_topic_arn

  admin_cache_cluster_name       = "admin-tool-staging-v2"
  admin_cache_engine_version     = "4.0.10"
  admin_cache_maintenance_window = "thu:00:30-thu:01:30"
  admin_cache_node_type          = "cache.t2.micro"
  admin_cache_parameter_group    = "default.redis4.0"
  admin_cache_subnet_group       = "vpc-private-subnets"
}
