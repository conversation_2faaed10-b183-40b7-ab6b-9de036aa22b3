output "aurora" {
  description = "Aurora cluster attributes."
  value = {
    cluster_id        = module.firmware_upgrade.aurora.cluster_id
    cluster_endpoint  = module.firmware_upgrade.aurora.cluster_endpoint
    reader_endpoint   = module.firmware_upgrade.aurora.reader_endpoint
    engine            = module.firmware_upgrade.aurora.engine
    port              = module.firmware_upgrade.aurora.port
    cluster_instances = module.firmware_upgrade.aurora.cluster_instances
  }
}

output "sqs" {
  description = "The SQS queue attributes"
  value = {
    id  = module.firmware_upgrade.sqs.id
    arn = module.firmware_upgrade.sqs.arn
  }
}

output "firmware_api_gateway_vpce_url" {
  description = "The VPC endpoint URL to use when invoking firmware-upgrade API through API Gateway"
  value       = module.firmware_upgrade.api_gateway_vpce_url
}

output "firmware_api_gateway_id" {
  description = "The ID of the firmware upgrade API Gateway"
  value       = module.firmware_upgrade.api_gateway_id
}

output "firmware_api_gateway_stage_name" {
  description = "The stage name of the firmware upgrade API Gateway"
  value       = module.firmware_upgrade.api_gateway_stage_name
}

output "firmware_api_vpc_endpoint_service_name" {
  description = "The VPC endpoint service name of the firmware upgrade API"
  value       = "com.amazonaws.vpce.eu-west-1.vpce-svc-07d05f10917d25fa2"
}

output "diagnostics_api_gateway_vpce_url" {
  description = "The VPC endpoint URL to use when invoking diagnostics API through API Gateway"
  value       = module.diagnostics.api_gateway_vpce_url
}

output "diagnostics_api_vpc_endpoint_service_name" {
  description = "The VPC endpoint service name of the diagnostics API"
  value       = "com.amazonaws.vpce.eu-west-1.vpce-svc-0c0a61ff8e2ec5253"
}

output "smart_charging_service_api_gateway_vpce_url" {
  description = "The VPC endpoint URL to use when invoking the smart charging service API through API Gateway"
  value       = module.smart_charge.api_gateway_vpce_urls.smart_charging_service_api
}

output "smart_charging_service_vpc_endpoint_service_name" {
  description = "The VPC endpoint service name of the smart charging service"
  value       = "com.amazonaws.vpce.eu-west-1.vpce-svc-0098a39274b2a67e4"
}

output "competitions_api_gateway_vpce_url" {
  description = "The VPC endpoint URL to use when invoking the competitions API through API Gateway"
  value       = module.smart_charge.api_gateway_vpce_urls.competitions_api
}

output "competitions_api_vpc_endpoint_service_name" {
  description = "The VPC endpoint service name of the smart charging service"
  value       = "com.amazonaws.vpce.eu-west-1.vpce-svc-0e829d50dd1ededde"
}

output "client_iam_policy_arn" {
  description = "The ARN of the policy that gives access to the cs-state APIs"
  value       = aws_iam_policy.api_client.arn
}

output "certificate_service_fargate_task_role_arn" {
  description = "The Amazon Resource Name of the IAM role used by the Certificate Service tasks"
  value       = module.certificate_service.fargate_task_role_arn
}

output "diagnostics_servie_credentials_attributes" {
  description = "The secrets manager attributes of the username and password for the diagnostics service user for podadmin"
  value       = module.diagnostics.diagnostics_servie_credentials
}

output "energy_metrics_apu_gateway_vpce_url" {
  description = "The VPC endpoint URL to use when invoking the energy metrics API through API Gateway"
  value       = module.smart_charge.energy_metrics_api.api_gateway_vpce_url
}

output "charging_profiles_api_gateway_vpce_url" {
  description = "The VPC endpoint URL to use when invoking the charging profiles API through API Gateway"
  value       = module.smart_charge.api_gateway_vpce_urls.charging_profiles_api
}
