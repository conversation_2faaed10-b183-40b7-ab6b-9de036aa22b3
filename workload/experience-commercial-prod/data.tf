locals {
  shared_services_account_name         = "Shared-services"
  ownership_data_platform_account_name = "ownership-data-platform-prod"
  ownership_orders_account_name        = "ownership-orders-prod"
  grid_account_name                    = "cs-state-prod"
  network_assets_account_name          = "network-assets-prod"
  pod_point_account_name               = "Pod-Point"
  salesforce_account_name              = "salesforce-prod"
}

data "aws_region" "current" {}

data "aws_caller_identity" "current" {}

data "terraform_remote_state" "cs_connectivity_prod" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces   = { name = "cs-connectivity-prod" }
  }
}

data "terraform_remote_state" "cs_state_prod" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces   = { name = "cs-state-prod" }
  }
}

data "terraform_remote_state" "cs_charge_sessions_prod" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces   = { name = "cs-charge-sessions-prod" }
  }
}

data "terraform_remote_state" "network_assets_prod" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces   = { name = "network-assets-prod" }
  }
}

data "aws_iam_roles" "pod_point_administrator_and_software" {
  provider   = aws.pod-point-eu-west-1
  name_regex = ".*PP-Administrator|PP-Software.*"
}


data "aws_ssm_parameter" "shared_service_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.id}:parameter/account/${local.shared_services_account_name}/id"
}

data "aws_ssm_parameter" "ownership_data_platform_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.ownership_data_platform_account_name}/id"
}

data "aws_ssm_parameter" "ownership_orders_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.ownership_orders_account_name}/id"
}

data "aws_ssm_parameter" "grid_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.grid_account_name}/id"
}

data "aws_ssm_parameter" "network_assets_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.network_assets_account_name}/id"
}

data "aws_ssm_parameter" "pod_point_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.pod_point_account_name}/id"
}

data "aws_ssm_parameter" "salesforce_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/${local.salesforce_account_name}/id"
}
