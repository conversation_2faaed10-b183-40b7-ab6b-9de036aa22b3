locals {
  default_tags = {
    "pp:environment"                      = "production"
    "pp:domain"                           = "experience"
    "pp:owner"                            = "experience"
    "pp:service"                          = "experience"
    "pp:terraformWorkspace"               = "experience-commercial-prod"
    "pp:terraformConfigurationRepository" = "github.com/Pod-Point/terraform"
  }
}

provider "aws" {
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", var.assume_role_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  alias  = "us-east-1"
  region = "us-east-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", var.assume_role_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  alias  = "pod-point-eu-west-1"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", ************, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  alias  = "pod-point-us-east-1"
  region = "us-east-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.pod_point_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}


provider "aws" {
  alias  = "shared-services"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", local.shared_services_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  alias  = "cs-connectivity"
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", "************", var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  region = "eu-west-1"
  alias  = "pp-network-hub"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", "************", var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  region = "eu-west-1"
  alias  = "vpn"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", "************", var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}

provider "aws" {
  region = "eu-west-1"
  alias  = "sso-admin"

  assume_role {
    role_arn     = "arn:aws:iam::980052802627:role/terraform-ci-identity-center-read-only"
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = local.default_tags
  }
}
