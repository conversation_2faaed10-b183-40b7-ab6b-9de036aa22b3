module "support_tool" {
  source = "../../modules/applications/experience/support-tool"

  providers = {
    aws                 = aws
    aws.route53         = aws.pod-point-eu-west-1
    aws.shared-services = aws.shared-services
    aws.us-east-1       = aws.us-east-1
    aws.vpn             = aws.vpn
  }

  privatelink_client_service_names = {
    assets_api                   = data.terraform_remote_state.network_assets_prod.outputs.vpc_endpoint_service_name
    assets_configuration_api     = data.terraform_remote_state.network_assets_prod.outputs.configuration_api_vpc_endpoint_service_name
    assets_provisioning_api      = data.terraform_remote_state.network_assets_prod.outputs.provisioning_api_vpc_endpoint_service_name
    connectivity_commands_api    = data.terraform_remote_state.cs_connectivity_prod.outputs.commands_api_vpc_endpoint_service_name
    connectivity_status_api      = data.terraform_remote_state.cs_connectivity_prod.outputs.status_api_vpc_endpoint_service_name
    state_competitions_api       = data.terraform_remote_state.cs_state_prod.outputs.competitions_api_vpc_endpoint_service_name
    state_diagnostics_api        = data.terraform_remote_state.cs_state_prod.outputs.diagnostics_api_vpc_endpoint_service_name
    state_firmware_api           = data.terraform_remote_state.cs_state_prod.outputs.firmware_api_vpc_endpoint_service_name
    state_smart_charging_service = data.terraform_remote_state.cs_state_prod.outputs.smart_charging_service_vpc_endpoint_service_name
    state_tariffs_api            = "com.amazonaws.vpce.eu-west-1.vpce-svc-06c6b2f9e09619fed"
  }

  alb_public_subnets           = module.destination.public_subnets_ids
  codebuild_security_group_ids = module.destination.codebuild_security_group_ids
  ecs_private_subnets_ids      = module.destination.private_subnets_ids
  vpc_id                       = module.destination.vpc_id

  api_desired_task_count        = 2
  api_source_security_group_ids = [module.ocpi_service.api_security_group_id, module.ocpi_service.queue_worker_security_group_id]
  cloudfront_enable_logging     = true
  cloudfront_realtime_metrics   = "Enabled"
  environment                   = local.environment
  oidc_config_secret_id         = "azure/oidc/support-tool-prod"
  route53_record_name           = "support.pod-point.com"
  use_spot_capacity             = false
  webapp_desired_task_count     = 2
}
