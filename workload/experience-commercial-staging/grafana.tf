locals {
  grafana_role_association_permissions = {
    viewers = [
      "<PERSON>@pod-point.com",
      "<PERSON>@pod-point.com",
      "<PERSON><PERSON>@pod-point.com"
    ]

    # editors cost more than viewers. See AMG pricing
    editors = []

    # administrators cost more than viewers and editors. See AMG pricing
    administrators = [
      "<PERSON><PERSON>@pod-point.com",
      "<PERSON>@pod-point.com"
    ]
  }

  identity_store_id = tolist(data.aws_ssoadmin_instances.sso_instances.identity_store_ids)[0]
}

data "aws_ssoadmin_instances" "sso_instances" {
  provider = aws.sso-admin
}

data "aws_identitystore_user" "managed_grafana_user_map_viewer" {
  for_each = toset(local.grafana_role_association_permissions.viewers)
  provider = aws.sso-admin

  identity_store_id = local.identity_store_id

  alternate_identifier {
    unique_attribute {
      attribute_path  = "UserName"
      attribute_value = each.value
    }
  }
}

data "aws_identitystore_user" "managed_grafana_user_map_editor" {
  for_each = toset(local.grafana_role_association_permissions.editors)
  provider = aws.sso-admin

  identity_store_id = local.identity_store_id

  alternate_identifier {
    unique_attribute {
      attribute_path  = "UserName"
      attribute_value = each.value
    }
  }
}

data "aws_identitystore_user" "managed_grafana_user_map_administrator" {
  for_each = toset(local.grafana_role_association_permissions.administrators)
  provider = aws.sso-admin

  identity_store_id = local.identity_store_id

  alternate_identifier {
    unique_attribute {
      attribute_path  = "UserName"
      attribute_value = each.value
    }
  }
}

resource "aws_grafana_workspace" "managed_grafana" {
  name                     = var.managed_grafana_name
  description              = "Managed Grafana for the ${var.managed_grafana_name} account."
  account_access_type      = "CURRENT_ACCOUNT"
  authentication_providers = ["AWS_SSO"]
  permission_type          = "SERVICE_MANAGED"
  role_arn                 = aws_iam_role.managed_grafana.arn
  grafana_version          = "9.4"

  data_sources = [
    "CLOUDWATCH",
    "ATHENA"
  ]

  vpc_configuration {
    security_group_ids = [aws_security_group.managed_grafana.id]
    subnet_ids         = module.destination.vpc[0].private_subnets_ids
  }

  configuration = jsonencode({
    plugins = {
      pluginAdminEnabled = false
    }
    unifiedAlerting = {
      enabled = true
    }
  })
}

resource "aws_grafana_role_association" "read" {
  role = "VIEWER"
  user_ids = [
    for user in data.aws_identitystore_user.managed_grafana_user_map_viewer :
    user.user_id
  ]
  workspace_id = aws_grafana_workspace.managed_grafana.id
}

resource "aws_grafana_role_association" "editor" {
  count = signum(length(local.grafana_role_association_permissions.editors))

  role = "EDITOR"
  user_ids = [
    for user in data.aws_identitystore_user.managed_grafana_user_map_editor :
    user.user_id
  ]
  workspace_id = aws_grafana_workspace.managed_grafana.id
}

resource "aws_grafana_role_association" "admin" {
  role = "ADMIN"
  user_ids = [
    for user in data.aws_identitystore_user.managed_grafana_user_map_administrator :
    user.user_id
  ]
  workspace_id = aws_grafana_workspace.managed_grafana.id
}

resource "aws_iam_role" "managed_grafana" {
  name = "managed-grafana-assume"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Sid    = "ManagedGrafanaAssume"
        Principal = {
          Service = "grafana.amazonaws.com"
        }
      },
    ]
  })
}

resource "aws_security_group" "managed_grafana" {
  name        = "managed-grafana"
  description = "Security group used by managed grafana to access RDS"
  vpc_id      = module.destination.vpc[0].vpc_id

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_security_group_rule" "managed_grafana_to_postgresql" {
  type              = "egress"
  description       = "Allow outgoing traffic to experience aurora database."
  from_port         = 5432
  to_port           = 5432
  protocol          = "tcp"
  cidr_blocks       = module.destination.vpc[0].private_subnets_cidr_blocks
  security_group_id = aws_security_group.managed_grafana.id
}

resource "aws_security_group_rule" "allow_all_egress" {
  type              = "egress"
  description       = "Allow outgoing traffic to the Internet. Required in order to allow the AMG to reach AWS Service APIs like CloudWatch."
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.managed_grafana.id
}
