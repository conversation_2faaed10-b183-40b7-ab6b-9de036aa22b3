module "destination" {
  source = "../../modules/applications/destination"

  providers = {
    aws                     = aws
    aws.us-east-1           = aws.us-east-1
    aws.pod-point-eu-west-1 = aws.pod-point-eu-west-1
    aws.pod-point-us-east-1 = aws.pod-point-us-east-1
    aws.shared-services     = aws.shared-services
    aws.cs-connectivity     = aws.cs-connectivity
    aws.pp-network-hub      = aws.pp-network-hub
    aws.vpn                 = aws.vpn
  }

  build_account_id = local.build_account_id
  environment      = local.environment
  vpc_cidr_block   = local.vpc_cidr_block
  kms_admins       = local.kms_admins

  api3_endpoint_service_permitted_account_ids = [
    "************", // experience-commercial-dev (allow dev account to connect to stage account endpoint service)
    "************"  // ownership-data-platform staging
  ]

  data_platform_api_capacity_fargate_base = local.data_platform_api_capacity_fargate_base
  data_platform_api_source_security_group_ids = [
    module.ocpi_service.queue_worker_security_group_id,
    module.support_tool.api_security_group_id
  ]

  data_platform_events_queue_worker_capacity_fargate_base  = local.data_platform_events_queue_worker_capacity_fargate_base
  data_platform_events_queue_worker_max_autoscale_capacity = local.data_platform_events_queue_worker_max_autoscale_capacity

  data_platform_api_max_autoscale_capacity = local.data_platform_api_max_autoscale_capacity

  data_platform_async_processor_capacity_fargate_base  = local.data_platform_async_processor_capacity_fargate_base
  data_platform_async_processor_max_autoscale_capacity = local.data_platform_async_processor_max_autoscale_capacity

  data_platform_billing_api_base_url = local.data_platform_billing_api_base_url

  data_platform_podadmin_db_database = local.data_platform_podadmin_db_database
  data_platform_podadmin_db_host     = local.data_platform_podadmin_db_host
  data_platform_podadmin_db_host_ro  = local.data_platform_podadmin_db_host_ro
  data_platform_podadmin_db_port     = local.data_platform_podadmin_db_port

  data_platform_podadmin_api_db_username = local.data_platform_podadmin_api_db_username
  data_platform_podadmin_api_db_password = local.data_platform_podadmin_api_db_password

  data_platform_podadmin_queue_worker_db_username = local.data_platform_podadmin_queue_worker_db_username
  data_platform_podadmin_queue_worker_db_password = local.data_platform_podadmin_queue_worker_db_password

  data_platform_podadmin_async_processor_db_username = local.data_platform_podadmin_async_processor_db_username
  data_platform_podadmin_async_processor_db_password = local.data_platform_podadmin_async_processor_db_password

  data_platform_podadmin_data_feed_db_username = local.data_platform_podadmin_data_feed_db_username
  data_platform_podadmin_data_feed_db_password = local.data_platform_podadmin_data_feed_db_password

  data_platform_podadmin_data_feed_repl_instance_class             = local.data_platform_podadmin_data_feed_repl_instance_class
  data_platform_podadmin_data_feed_repl_instance_multi_az          = local.data_platform_podadmin_data_feed_repl_instance_multi_az
  data_platform_podadmin_data_feed_repl_instance_apply_immediately = local.data_platform_podadmin_data_feed_repl_instance_apply_immediately

  data_platform_replication_task_opsgenie_api = local.data_platform_replication_task_opsgenie_api
  data_platform_cloudwatch_opsgenie_api       = local.data_platform_cloudwatch_opsgenie_api

  data_platform_charge_notifications_sns_topic_subscription_enabled = true
  data_platform_charge_notifications_sns_account_id                 = "************"

  auth_accounts_service_users_sns_topic_arn_subscription_enabled = true
  auth_accounts_service_users_sns_topic_arn                      = "arn:aws:sns:eu-west-1:************:accounts-service-user-events-staging"

  data_platform_oxr_app_id = local.data_platform_oxr_app_id

  destination_site_admin_api_db_password          = local.destination_site_admin_api_db_password
  destination_site_admin_api_firebase_private_key = local.destination_site_admin_api_firebase_private_key

  destination_site_admin_webapp_configcat_sdk_key    = local.destination_site_admin_webapp_configcat_sdk_key
  destination_site_admin_webapp_firebase_private_key = local.destination_site_admin_webapp_firebase_private_key
  destination_site_admin_webapp_google_cloud_api_key = local.destination_site_admin_webapp_google_cloud_api_key
  destination_site_admin_webapp_nextauth_secret      = local.destination_site_admin_webapp_nextauth_secret
  destination_site_admin_realtime_metrics            = "Enabled"
  destination_site_admin_webapp_recaptcha_site_key   = "6Ld0CMAnAAAAACPSWkWqYFLHGyDESedQyB2Z1yNx"
  destination_site_admin_webapp_firebase_app_id      = "1:*************:web:91d09b0dcef5d043400deb"

  experience_db_cluster_apply_immediately = local.experience_db_cluster_apply_immediately
  experience_db_cluster_instance_class    = local.experience_db_cluster_instance_class
  experience_db_cluster_instance_count    = local.experience_db_cluster_instance_count

  internal_site_admin_api_db_password          = local.internal_site_admin_api_db_password
  internal_site_admin_api_firebase_private_key = local.internal_site_admin_api_firebase_private_key

  internal_site_admin_webapp_configcat_sdk_key    = local.internal_site_admin_webapp_configcat_sdk_key
  internal_site_admin_webapp_firebase_private_key = local.internal_site_admin_webapp_firebase_private_key
  internal_site_admin_webapp_google_cloud_api_key = local.internal_site_admin_webapp_google_cloud_api_key
  internal_site_admin_webapp_nextauth_secret      = local.internal_site_admin_webapp_nextauth_secret
  internal_site_admin_realtime_metrics            = "Enabled"
  internal_site_admin_webapp_recaptcha_site_key   = "6LfQ_L8nAAAAALYoGYwLN9z6MzcXmPOlYUXIjjlv"
  internal_site_admin_webapp_firebase_app_id      = "1:*************:web:657ce3d216db50cb400deb"

  commercial_admin_firebase_api_key             = "AIzaSyAUOIAG-wFnQO_04TUAECUGvn2_237rots"
  commercial_admin_firebase_auth_domain         = "commercial-admin---staging.firebaseapp.com"
  commercial_admin_firebase_client_email        = "<EMAIL>"
  commercial_admin_firebase_messaging_sender_id = "*************"
  commercial_admin_firebase_project_id          = "commercial-admin---staging"
  commercial_admin_firebase_storage_bucket      = "commercial-admin---staging.appspot.com"

  site_admin_api_read_only_group_uids = "2f1bc186-b5c3-43fc-91e8-178394db99d9"

  site_admin_webapp_clarity_tracking_code   = "kj5inu3kx9"
  site_admin_webapp_feedback_form_url       = "https://forms.office.com/e/PZKkFujnCT"
  site_admin_webapp_google_analytics_tag_id = "G-CXSBD4LDV5"

  diagnostics_service_base_url = "https://ssaxwy2lte.execute-api.eu-west-1.amazonaws.com/staging"
  firmware_upgrade_base_url    = "https://34q4zjsftb.execute-api.eu-west-1.amazonaws.com/staging"

  pod_point_eu_west_1_account_id                                  = local.pod_point_eu_west_1_account_id
  pod_point_eu_west_1_privatelink_data_platform_api_ingress_rules = local.pod_point_eu_west_1_privatelink_data_platform_api_ingress_rules
  pod_point_eu_west_1_privatelink_subnet_ids                      = local.pod_point_eu_west_1_privatelink_subnet_ids
  pod_point_eu_west_1_vpc_id                                      = local.pod_point_eu_west_1_vpc_id
  pod_point_eu_west_1_api3_sg                                     = local.pod_point_eu_west_1_api3_sg
  pod_point_eu_west_1_auth_sg                                     = local.pod_point_eu_west_1_auth_sg

  cs_state_account_id        = local.cs_state_account_id
  cs_connectivity_account_id = local.cs_connectivity_account_id

  ownership_orders_account_id = local.ownership_orders_account_id

  data_vpc_cidr                   = local.data_vpc_cidr
  ownership_acquisitions_vpc_cidr = local.ownership_acquisitions_vpc_cidr

  #mobile-api
  mobile_api_loqate_api_key          = local.mobile_api_loqate_api_key
  mobile_api_sentry_dsn              = local.mobile_api_sentry_dsn
  mobile_api_driver_auth_issuer_url  = local.mobile_api_driver_auth_issuer_url
  mobile_api_driver_auth_audience    = local.mobile_api_driver_auth_audience
  mobile_api_driver_auth_jwks        = local.mobile_api_driver_auth_jwks
  mobile_api_driver_auth_private_key = local.mobile_api_driver_auth_private_key
  mobile_api_endpoint_service_names = {
    asset_service_api          = data.terraform_remote_state.network_assets_staging.outputs.vpc_endpoint_service_name
    competitions_api           = "com.amazonaws.vpce.eu-west-1.vpce-svc-0e829d50dd1ededde"
    smart_charging_service_api = "com.amazonaws.vpce.eu-west-1.vpce-svc-0098a39274b2a67e4"
    firmware_upgrade_api       = "com.amazonaws.vpce.eu-west-1.vpce-svc-07d05f10917d25fa2"
    configuration_api          = data.terraform_remote_state.network_assets_staging.outputs.configuration_api_vpc_endpoint_service_name
    assets_provisioning_api    = data.terraform_remote_state.network_assets_staging.outputs.provisioning_api_vpc_endpoint_service_name
    vehicles_api               = "com.amazonaws.vpce.eu-west-1.vpce-svc-074434b666abdbefb"
    tariffs_api                = "com.amazonaws.vpce.eu-west-1.vpce-svc-0bd67c6d968d5d7b8"
  }
  mobile_firebase_private_key          = local.mobile_firebase_private_key
  mobile_api3_client_id                = local.mobile_api3_client_id
  mobile_api3_client_secret            = local.mobile_api3_client_secret
  mobile_api_db_database               = local.mobile_api_db_database
  mobile_api_db_host                   = local.mobile_api_db_host
  mobile_api_db_host_ro                = local.mobile_api_db_host_ro
  mobile_api_db_password               = local.mobile_api_db_password
  mobile_api_db_port                   = local.mobile_api_db_port
  mobile_api_db_username               = local.mobile_api_db_username
  mobile_api_cloudfront_enable_logging = true
  mobile_api_enode_client_id           = local.mobile_api_enode_client_id
  mobile_api_enode_client_secret       = local.mobile_api_enode_client_secret

  #installer-api
  installer_api_sentry_dsn                = local.installer_api_sentry_dsn
  installer_api_source_security_group_ids = [module.destination.installer_bff_security_group_id, module.support_tool.api_security_group_id, module.destination.installer_webapp_security_group_id]
  installer_api_segment_write_key         = local.installer_api_segment_write_key

  #installer-bff
  installer_bff_sentry_dsn                = local.installer_bff_sentry_dsn
  installer_bff_cloudfront_enable_logging = true
  installer_firebase_private_key          = local.installer_firebase_private_key

  #driver-account-api
  driver_account_api_sentry_dsn                   = var.driver_account_api_sentry_dsn
  driver_account_api_auth_db_host                 = module.auth_service.db_writer_endpoint
  driver_account_api_auth_db_database             = module.auth_service.db_schema
  driver_account_api_auth_db_username             = var.driver_account_api_auth_db_username
  driver_account_api_auth_db_password             = var.driver_account_api_auth_db_password
  driver_account_api_auth_db_port                 = module.auth_service.db_port
  driver_account_api_cloudwatch_opsgenie_api      = local.driver_account_api_cloudwatch_opsgenie_api
  driver_account_api_client_id                    = local.driver_account_api_client_id
  driver_account_api_client_secret                = local.driver_account_api_client_secret
  driver_account_api_source_security_group_ids    = [module.support_tool.api_security_group_id]
  driver_account_api_oxr_app_id                   = local.driver_account_api_oxr_app_id
  driver_account_api_segment_write_key            = local.driver_account_api_segment_write_key
  driver_account_api_auth_service_user_url        = local.driver_account_api_auth_service_user_url
  driver_account_api_user_queue_url               = local.driver_account_api_user_queue_url
  driver_account_api_user_queue_disabled          = local.driver_account_api_user_queue_disabled
  driver_account_api_firebase_project_id          = local.driver_account_api_firebase_project_id
  driver_account_api_firebase_api_key             = local.driver_account_api_firebase_api_key
  driver_account_api_firebase_client_email        = local.driver_account_api_firebase_client_email
  driver_account_api_identity_base_url            = local.driver_account_api_identity_base_url
  driver_account_api_api3_base_url                = local.driver_account_api_api3_base_url
  driver_account_api_podadmin_host                = local.driver_account_api_podadmin_host
  driver_account_api_podadmin_host_ro             = local.driver_account_api_podadmin_host_ro
  driver_account_api_experience_event_bus_arn     = local.driver_account_api_experience_event_bus_arn
  driver_account_api_podadmin_user_secret_arn     = local.driver_account_api_podadmin_user_secret_arn
  driver_account_api_podadmin_password_secret_arn = local.driver_account_api_podadmin_password_secret_arn


  #driver-account-webapp
  driver_account_webapp_cloudfront_enable_logging = true

  #installer-account-webapp
  installer_account_webapp_cloudfront_enable_logging = true

  managed_grafana_sg = aws_security_group.managed_grafana.id

  single_nat_gateway = true

  cloudfront_enable_logging = true

  # ARN of the AWS Certificate Manager certificate that you wish to use with the Cloudfront distribution. The ACM certificate must be in US-EAST-1.
  acm_certificate_arn = "arn:aws:acm:us-east-1:************:certificate/c99e164e-8aec-4926-9006-89cc42d2d44e"

  # OCPI
  identifier_ocpi                  = "ocpi-preprod"
  ocpi_route53_record_name         = "ocpi-preprod.pod-point.com"
  enable_waf_ocpi                  = false
  ocpi_load_balancer_dns_name      = "OcpiS-OcpiL-1A5VGPQ2QB79U-**********.eu-west-1.elb.amazonaws.com"
  cloudfront_enable_logging_ocpi   = true
  cloudfront_realtime_metrics_ocpi = "Enabled"

  statement_api_security_group_id = module.statement_service.api_security_group_id
  internal_site_admin_api_source_security_group_ids = [
    module.statement_service.api_security_group_id,
    module.statement_service.queue_worker_security_group_id,
    module.support_tool.api_security_group_id,
    module.support_tool.webapp_security_group_id,
  ]

  # The environment of the podadmin database. Required purely to adhere to our secret manager secret naming convention.
  podadmin_environment = "staging"

  destination_database_user_kms_adminstrator = data.aws_iam_roles.administrator_and_software.arns

  xray_default_reservoir              = local.xray_default_reservoir
  xray_default_fixed_rate             = local.xray_default_fixed_rate
  async_processor_xray_reservoir      = local.async_processor_xray_reservoir
  async_processor_xray_fixed_rate     = local.async_processor_xray_fixed_rate
  events_queue_worker_xray_reservoir  = local.events_queue_worker_xray_reservoir
  events_queue_worker_xray_fixed_rate = local.events_queue_worker_xray_fixed_rate

  ownership_event_bus_arn                        = "arn:aws:events:eu-west-1:${local.ownership_data_platform_account_id}:event-bus/ownership-data-platform"
  ownership_data_platform_account_id             = local.ownership_data_platform_account_id
  grid_account_id                                = local.grid_account_id
  grid_charge_events_queue_arn                   = module.home_app.charge_events_queue_arn
  enode_credential_intervention_events_queue_arn = module.home_app.enode_credential_intervention_queue_arn
  enode_vehicle_intervention_events_queue_arn    = module.home_app.enode_vehicle_intervention_queue_arn
  network_assets_account_id                      = local.network_assets_account_id
  profile_users_queue_arn                        = module.home_app.profile_users_queue_arn
  rewards_api_security_group_id                  = module.home_app.rewards_api_security_group_id
  subscriptions_api_security_group_id            = module.home_app.subscriptions_api_security_group_id
  subscriptions_api_event_queue_arn              = module.home_app.subscriptions_api_events_queue_arn
  rewards_api_event_queue_arn                    = module.home_app.rewards_api_events_queue_arn
  payments_api_security_group_id                 = module.home_app.payments_api_security_group_id

  privatelink_target_transactions_api = data.terraform_remote_state.cs_charge_sessions_staging.outputs.transactions_api.vpc_endpoint_service_name
}
