module "onboarding_service" {
  source = "../../modules/applications/experience/commercial/onboarding-service"

  providers = {
    aws                 = aws
    aws.route53         = aws.pod-point-eu-west-1
    aws.shared-services = aws.shared-services
    aws.us-east-1       = aws.us-east-1
  }

  alb_public_subnets           = module.destination.public_subnets_ids
  codebuild_security_group_ids = module.destination.codebuild_security_group_ids
  ecs_private_subnets_ids      = module.destination.private_subnets_ids
  vpc_id                       = module.destination.vpc_id

  cloudfront_enable_logging   = true
  cloudfront_realtime_metrics = "Enabled"
  environment                 = local.environment
  use_spot_capacity           = true
  webapp_desired_task_count   = 1
}
