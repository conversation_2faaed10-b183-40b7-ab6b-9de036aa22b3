data "aws_ssm_parameter" "charger_status_updates_account_id" {
  name = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_ssm_parameter.shared_service_account_id.value}:parameter/account/cs-state-staging/id"
}

module "ocpi_service" {
  source = "../../modules/applications/experience/commercial/ocpi-service"

  providers = {
    aws           = aws
    aws.route53   = aws.pod-point-eu-west-1
    aws.us-east-1 = aws.us-east-1
  }

  admin_user_secret_manager_arn        = module.destination.admin_user_secret_manager_arn
  alb_public_subnets                   = module.destination.public_subnets_ids
  api_database_cluster_resource_id     = module.destination.destination_cluster_resource_id
  codebuild_security_group_ids         = module.destination.codebuild_security_group_ids
  destination_cluster_endpoint         = module.destination.destination_cluster_endpoint
  destination_cluster_port             = module.destination.destination_cluster_port
  destination_reader_endpoint          = module.destination.destination_reader_endpoint
  ecs_private_subnets_ids              = module.destination.private_subnets_ids
  experience_cluster_security_group_id = module.destination.experience_cluster_security_group_id
  vpc_id                               = module.destination.vpc_id

  charger_status_updates_account_id = data.aws_ssm_parameter.charger_status_updates_account_id.value
  privatelink_client_service_names = {
    connectivity_commands_api = data.terraform_remote_state.cs_connectivity_staging.outputs.commands_api_vpc_endpoint_service_name
    connectivity_status_api   = data.terraform_remote_state.cs_connectivity_staging.outputs.status_api_vpc_endpoint_service_name
  }

  api_desired_task_count          = 2
  enable_scheduled_tasks          = true
  environment                     = local.environment
  podadmin_host                   = "podadmin-staging.cluster-chbvibnzxkke.eu-west-1.rds.amazonaws.com"
  podadmin_host_ro                = "podadmin-staging.cluster-ro-chbvibnzxkke.eu-west-1.rds.amazonaws.com"
  queue_worker_desired_task_count = 1
  use_spot_capacity               = true
}
