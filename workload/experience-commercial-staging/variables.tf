variable "assume_role_account_id" {
  description = "The account ID to assume when initialising the AWS provider."
  type        = string
  default     = "************" # experience-staging
}

variable "assume_role_name" {
  description = "The role to assume when initialising the AWS provider."
  type        = string
  default     = "terraform-ci"
}

variable "auth_service_cloudwatch_opsgenie_api_url" {
  type        = string
  description = "Opsgenie Cloudwatch integration URL."
}

variable "data_platform_events_queue_worker_max_autoscale_capacity" {
  type        = number
  description = "Maximum scaling capacity for data-platform-events-queue-worker"
  default     = 2
}

variable "data_platform_oxr_app_id" {
  type        = string
  description = "The api key for openexchangerates.org"
}

variable "data_platform_podadmin_api_db_password" {
  type        = string
  description = "Password for the podadmin database used by the data-platform-api service."
}

variable "data_platform_podadmin_api_db_username" {
  type        = string
  description = "Username for the podadmin database used by the data-platform-api service."
}

variable "data_platform_podadmin_queue_worker_db_password" {
  type        = string
  description = "Password for the podadmin database used by the data-platform-events-queue-worker service."
}

variable "data_platform_podadmin_queue_worker_db_username" {
  type        = string
  description = "Username for the podadmin database used by the data-platform-events-queue-worker service."
}

variable "data_platform_podadmin_async_processor_db_password" {
  type        = string
  description = "Password for the podadmin database used by the data-platform-async-processor service."
}

variable "data_platform_podadmin_async_processor_db_username" {
  type        = string
  description = "Username for the podadmin database used by the data-platform-async-processor service."
}

variable "data_platform_podadmin_data_feed_db_password" {
  type        = string
  description = "Password for the podadmin database the data platform data feed connects."
}

variable "data_platform_podadmin_data_feed_db_username" {
  type        = string
  description = "Username for the podadmin database the data platform data feed connects."
}

variable "data_platform_podadmin_data_feed_repl_instance_class" {
  type        = string
  description = "The compute and memory capacity of the replication instance as specified by the replication instance class."
}

variable "data_platform_podadmin_data_feed_repl_instance_multi_az" {
  type        = bool
  description = "Whether the replication instance is a multi-az deployment."
}

variable "data_platform_podadmin_data_feed_repl_instance_apply_immediately" {
  type        = bool
  description = "Apply replication instance changes immediately and not wait for maintenance window."
}

variable "experience_db_cluster_apply_immediately" {
  type        = bool
  description = "Apply changes to all instances in the cluster immediately and not wait for maintenance window."
}

variable "destination_site_admin_api_db_password" {
  type        = string
  description = "Password for the database to which the site admin api connects."
}

variable "destination_site_admin_api_firebase_private_key" {
  type        = string
  description = "Private key for use with the Firebase Admin SDK."
}

variable "destination_site_admin_webapp_configcat_sdk_key" {
  type        = string
  description = "Config Cat SDK key for the site admin webapp."
}

variable "destination_site_admin_webapp_firebase_private_key" {
  type        = string
  description = "Private key for use with the Firebase Admin SDK."
}

variable "destination_site_admin_webapp_google_cloud_api_key" {
  type        = string
  description = "Google Cloud API key for the site admin webapp."
}

variable "destination_site_admin_webapp_nextauth_secret" {
  type        = string
  description = "NextAuth.js secret for the site admin webapp."
}

variable "experience_db_cluster_instance_class" {
  type        = string
  description = "The instance class to use for the cluster instances of the experience db cluster."
}

variable "experience_db_cluster_instance_count" {
  type        = number
  description = "An integer that specifies how many instances exist in the experience db cluster."
}

variable "internal_site_admin_api_db_password" {
  type        = string
  description = "Password for the database to which the internal site admin api connects."
}

variable "internal_site_admin_api_firebase_private_key" {
  type        = string
  description = "Private key for use with the Firebase Admin SDK."
}

variable "internal_site_admin_webapp_configcat_sdk_key" {
  type        = string
  description = "Config Cat SDK key for the internal site admin webapp."
}

variable "internal_site_admin_webapp_firebase_private_key" {
  type        = string
  description = "Private key for use with the Firebase Admin SDK."
}

variable "internal_site_admin_webapp_google_cloud_api_key" {
  type        = string
  description = "Google Cloud API key for the internal site admin webapp."
}

variable "internal_site_admin_webapp_nextauth_secret" {
  type        = string
  description = "NextAuth.js secret for the internal site admin webapp."
}

variable "data_platform_replication_task_opsgenie_api" {
  type        = string
  description = "The environment specific opsgenie cloudwatch events integration URL."
}

variable "data_platform_cloudwatch_opsgenie_api" {
  type        = string
  description = "The environment specific opsgenie cloudwatch integration URL. Different integration type to data_platform_replication_task_opsgenie_api"
}

variable "data_platform_api3_service_cloudwatch_opsgenie_api" {
  type        = string
  description = "The environment specific opsgenie cloudwatch integration URL for api3-service. Separated to allow finer control of priority compared to other alerts."
}

variable "opencharge_web_app_cloudwatch_opsgenie_api_url" {
  description = "Opsgenie Cloudwatch integration URL for the OpenCharge web app"
  type        = string
}

variable "mobile_api_loqate_api_key" {
  type        = string
  description = "Loqate API key for mobile API"
}

variable "mobile_api_sentry_dsn" {
  type        = string
  description = "Sentry DSN for mobile API"
}

variable "mobile_api_driver_auth_issuer_url" {
  type        = string
  description = "Account service issuer URL"
}

variable "mobile_api_driver_auth_audience" {
  type        = string
  description = "Account service audience"
}

variable "mobile_api_driver_auth_jwks" {
  type        = string
  description = "Account service JWKS"
}

variable "mobile_api_driver_auth_private_key" {
  type        = string
  description = "Auth service private key"
}

variable "managed_grafana_name" {
  type        = string
  description = "Managed Grafana workspace name"
  default     = "experience_staging"
}

variable "installer_api_sentry_dsn" {
  type        = string
  description = "Sentry DSN for installer API"
}

variable "installer_bff_sentry_dsn" {
  type        = string
  description = "Sentry DSN for installer BFF"
}

variable "installer_firebase_private_key" {
  description = "The firebase admin private key"
  type        = string
}

variable "driver_account_api_sentry_dsn" {
  type        = string
  description = "Sentry DSN for driver account manager API"
}

variable "driver_account_api_cloudwatch_opsgenie_api" {
  type        = string
  description = "The environment specific opsgenie cloudwatch integration URL for driver account API."
}

variable "driver_account_api_auth_db_username" {
  type        = string
  description = "Database username of the auth service db."
}

variable "driver_account_api_auth_db_password" {
  type        = string
  description = "Database password of the auth service db."
}

variable "driver_account_api_client_id" {
  type        = string
  description = "Oauth client id for driver account manager API"
}

variable "driver_account_api_client_secret" {
  type        = string
  description = "Oauth client secret for driver account manager API"
}

variable "mobile_firebase_private_key" {
  description = "The firebase admin private key"
  type        = string
}

variable "mobile_api3_client_id" {
  description = "The client id for getting tokens from API3"
  type        = string
}

variable "mobile_api3_client_secret" {
  description = "The secret for getting tokens from API3"
  type        = string
}

variable "mobile_api_db_password" {
  type        = string
  description = "Password for the database to which the mobile api connects."
}

variable "mobile_api_db_username" {
  type        = string
  description = "Username for the database to which the mobile api connects."
}

variable "commercial_cloudwatch_opsgenie_api_url" {
  description = "Opsgenie CloudWatch integration URL"
  type        = string
}

variable "commercial_cloudwatch_events_opsgenie_api_url" {
  description = "Opsgenie CloudWatch Events integration URL"
  type        = string
}

variable "xray_default_reservoir" {
  type        = number
  description = "A fixed number of matching requests to instrument per second, prior to applying the fixed rate. The reservoir is not used directly by services, but applies to all services using the rule collectively."
  default     = 0
}

variable "xray_default_fixed_rate" {
  type        = number
  description = "The percentage of matching requests to instrument, after the reservoir is exhausted. To represent 5%, put 0.05. Setting this and xray_default_reservoir to 0 disables tracing for anything not caught by a higher priority rule."
  default     = 0.0
}

variable "async_processor_xray_reservoir" {
  type        = number
  description = "Applies only to data-platform-async-processor. A fixed number of matching requests to instrument per second, prior to applying the fixed rate."
  default     = 0
}

variable "async_processor_xray_fixed_rate" {
  type        = number
  description = "Applies only to data-platform-async-processor. The percentage of matching requests to instrument, after the reservoir is exhausted. To represent 5%, put 0.05. Setting this and async_processor_xray_reservoir to 0 disables tracing for anything not caught by a higher priority rule."
  default     = 0.0
}

variable "events_queue_worker_xray_reservoir" {
  type        = number
  description = "Applies specifically to the application data-platform-events-queue-worker. A fixed number of matching requests to instrument per second, prior to applying the fixed rate."
  default     = 0
}

variable "events_queue_worker_xray_fixed_rate" {
  type        = number
  description = "Applies specifically to the application data-platform-events-queue-worker. The percentage of matching requests to instrument, after the reservoir is exhausted. To represent 5%, put 0.05. Setting this and events_queue_worker_xray_reservoir to 0 disables tracing for anything not caught by a higher priority rule."
  default     = 0.0
}

variable "driver_account_api_oxr_app_id" {
  type        = string
  description = "The APP ID for OpenExchangeRates"
}

variable "mobile_api_enode_client_id" {
  type        = string
  description = "The Client ID for the Enode API"
}

variable "mobile_api_enode_client_secret" {
  type        = string
  description = "The Client Secret for the Enode API"
}

variable "driver_account_api_segment_write_key" {
  type        = string
  description = "The segment write key for the Driver Account API"
}

variable "installer_api_segment_write_key" {
  type        = string
  description = "The segment write key for the Installer API"
}

variable "driver_account_api_firebase_api_key" {
  type        = string
  description = "The firebase API key used by the Driver Account API"
}
