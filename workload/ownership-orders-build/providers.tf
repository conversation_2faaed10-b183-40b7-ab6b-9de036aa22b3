provider "aws" {
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", var.build_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:domain"                           = "ownership"
      "pp:owner"                            = "ownership:orders"
      "pp:service"                          = "ownership-orders-build"
      "pp:terraformWorkspace"               = "workload/ownership-orders-build"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}

provider "aws" {
  region = "us-east-1"
  alias  = "global"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", var.build_account_id, var.assume_role_name)
    session_name = "assume_terraform_management_role"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:domain"                           = "ownership"
      "pp:owner"                            = "ownership:orders"
      "pp:service"                          = "ownership-orders-build"
      "pp:terraformWorkspace"               = "workload/ownership-orders-build"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}
