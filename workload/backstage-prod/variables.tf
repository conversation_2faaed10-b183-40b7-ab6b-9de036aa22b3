variable "assume_role_account_id" {
  description = "The account ID to assume when initialising the AWS provider."
  type        = string
  default     = "************"
}

variable "assume_role_name" {
  description = "The role to assume when initialising the AWS provider."
  type        = string
  default     = "terraform-ci"
}

variable "rds_engine_version" {
  description = "The postgresql rds database engine version."
  type        = string
  default     = "14.10"
}

variable "rds_instance_class" {
  description = "The instance class to assign to the database."
  type        = string
  default     = "db.t4g.medium"
}

variable "rds_storage_size" {
  description = "The allocated storage in gibibytes. If rds_max_storage is configured, this argument represents the initial storage allocation and differences from the configuration will be ignored automatically when Storage Autoscaling occurs."
  type        = string
  default     = 20
}

variable "rds_max_storage" {
  description = "When configured, the upper limit to which Amazon RDS can automatically scale the storage of the DB instance. Configuring this will automatically ignore differences to allocated_storage. Must be greater than or equal to allocated_storage or 0 to disable Storage Autoscaling."
  type        = string
  default     = 100
}

variable "rds_parameter_group_family" {
  description = "The family of the DB cluster parameter group. Must be compatible with the engine version."
  type        = string
  default     = "postgres14"
}

variable "rds_parameters" {
  description = "A map of the parameters for this RDS instance."
  type = map(object({
    value        = string
    apply_method = string
  }))
  default = {}
}
