module "vpc" {
  source = "../../modules/applications/cs-connectivity/vpc"

  vpc_cidr_block     = "10.15.0.0/16"
  single_nat_gateway = true

  vpc_endpoint_service_name_assets_service   = data.terraform_remote_state.network_assets_dev.outputs.vpc_endpoint_service_name
  vpc_endpoint_service_name_provision_api    = data.terraform_remote_state.network_assets_dev.outputs.provisioning_api_vpc_endpoint_service_name
  vpc_endpoint_service_name_transactions_api = data.terraform_remote_state.cs_charge_sessions_dev.outputs.transactions_api.vpc_endpoint_service_name

  ocpp16_responder_sg_id = module.connectivity-service.ocpp16_responder_sg_id
  service_names = {
    charge_data_platform_api = local.charge_data_api_privatelink_service_name
  }

  environment = local.environment
}

/*
* The following resources creates an additional VPC endpoint to the assets service staging so that we can allow OCPP 1.6 authoriser and POW Handlers.
*/
module "vpc_endpoints" {
  source  = "terraform-aws-modules/vpc/aws//modules/vpc-endpoints"
  version = "5.0.0"

  vpc_id = module.vpc.vpc.vpc_id

  endpoints = {
    "assets-service-staging" = {
      service_name = data.terraform_remote_state.network_assets_staging.outputs.vpc_endpoint_service_name
      service_type = "Interface"
      security_group_ids = [
        aws_security_group.vpc_endpoint_assets_service_staging.id
      ]
      subnet_ids = module.vpc.vpc.private_subnets_ids
      tags = {
        Name = "assets-service-staging"
      }
    }
  }

  tags = {
    "pp:service" = "ocpp16"
  }
}

resource "aws_security_group" "vpc_endpoint_assets_service_staging" {
  name_prefix = "vpce-assets-service-staging-sg-"
  description = "Security group for the assets service staging VPC endpoint."
  vpc_id      = module.vpc.vpc.vpc_id

  lifecycle {
    create_before_destroy = true
  }
}
