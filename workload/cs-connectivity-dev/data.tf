data "aws_caller_identity" "current" {}

data "terraform_remote_state" "network_assets_dev" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "network-assets-dev"
    }
  }
}

data "terraform_remote_state" "network_assets_staging" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "network-assets-staging"
    }
  }
}

data "terraform_remote_state" "cs_charge_sessions_dev" {
  backend = "remote"

  config = {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"
    workspaces = {
      name = "cs-charge-sessions-dev"
    }
  }
}

data "aws_iam_roles" "additional_kms_administrators" {
  name_regex = ".*PP-Administrator|PP-Software.*"
}
