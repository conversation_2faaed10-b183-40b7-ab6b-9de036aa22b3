![We Are Pod Point Banner](https://d3h256n3bzippp.cloudfront.net/we-are-pod-point.svg)

# Contributing Guide

Thank you for investing your time in contributing to our project!

## New contributor guide

- [Finding ways to contribute to open source on GitHub](https://docs.github.com/en/get-started/exploring-projects-on-github/finding-ways-to-contribute-to-open-source-on-github)
- [Set up Git](https://docs.github.com/en/get-started/quickstart/set-up-git)
- [GitHub flow](https://docs.github.com/en/get-started/quickstart/github-flow)
- [Collaborating with pull requests](https://docs.github.com/en/github/collaborating-with-pull-requests)

### Making changes

- Install [pre-commit](https://pre-commit.com/). We use `pre-commit` to automatically perform checks whenever we are ready to submit our planned changes within our local development environment.

- Run pre-commit install to set up the git hook scripts (our hook rules are defined in `.pre-commit-config.yaml`).

- Ensure any terraform code is formatted (`terraform fmt -recursive`). pre-commit will handle this for you if you forget.

### Pull Request

When creating pull requests. Please indicate the type of change you are making, what issue you are trying to solve, and any steps that could be followed by an owner in order to assist the code review process.
