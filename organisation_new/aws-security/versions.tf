terraform {
  backend "remote" {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"

    workspaces {
      name = "aws-security"
    }
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.53.0, < 6.0.0"
    }

    random = {
      source  = "hashicorp/random"
      version = ">= 3.6.2, < 4.0.0"
    }

    archive = {
      source  = "hashicorp/archive"
      version = ">= 2.4.2, < 3.0.0"
    }

    time = {
      source  = "hashicorp/time"
      version = "0.11.2"
    }
  }

  required_version = ">= 1.5.5"
}
