#S3 bucket to store the tag compliance reports
module "tag_compliance_report" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "3.14.1"

  providers = {
    aws = aws.us-east-1
  }

  bucket = "organisation-tag-policy-compliance-report"

  versioning = {
    enabled = false
  }

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  attach_policy = true
  policy        = data.aws_iam_policy_document.bucket_policy.json

  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }
}

# Enabling S3 event notifications for Eventbridge
module "tag_compliance_report_s3_bucket_notification" {
  source  = "terraform-aws-modules/s3-bucket/aws//modules/notification"
  version = "4.1.2"

  providers = {
    aws = aws.us-east-1
  }

  bucket      = "organisation-tag-policy-compliance-report"
  eventbridge = true

}

data "aws_iam_policy_document" "bucket_policy" {
  statement {
    sid = "TagPolicyACL"
    principals {
      type        = "Service"
      identifiers = ["tagpolicies.tag.amazonaws.com"]
    }
    actions = [
      "s3:GetBucketAcl"
    ]
    resources = [
      module.tag_compliance_report.s3_bucket_arn
    ]
    condition {
      test     = "StringLike"
      variable = "aws:SourceArn"
      values   = ["arn:aws:tag:us-east-1:${data.aws_caller_identity.current.account_id}:*"]
    }
    condition {
      test     = "StringLike"
      variable = "aws:SourceAccount"
      values   = [data.aws_caller_identity.current.account_id]
    }
  }
  statement {
    sid = "TagPolicyBucketDelivery"
    principals {
      type        = "Service"
      identifiers = ["tagpolicies.tag.amazonaws.com"]
    }
    actions = [
      "s3:PutObject",
      "s3:PutObjectAcl"
    ]
    resources = [
      "${module.tag_compliance_report.s3_bucket_arn}/AwsTagPolicies/o-4zdu8wtbbw/*",
    ]
    condition {
      test     = "StringLike"
      variable = "aws:SourceArn"
      values   = ["arn:aws:tag:us-east-1:${data.aws_caller_identity.current.account_id}:*"]
    }
    condition {
      test     = "StringLike"
      variable = "aws:SourceAccount"
      values   = [data.aws_caller_identity.current.account_id]
    }
  }
  statement {
    sid = "AllowAccessTagComplianceLambda"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::************:role/tag-compliance-output-lambda-role"]
    }
    actions = [
      "s3:GetObject",
      "s3:GetBucketLocation",
      "s3:ListBucket"
    ]
    resources = [
      "${module.tag_compliance_report.s3_bucket_arn}/non-compliant-resources-report/*",
      "${module.tag_compliance_report.s3_bucket_arn}"
    ]
  }
}

# role allowing cross account access for tag compliance service lambda
resource "aws_iam_role" "cross_account_s3" {
  name = "cross-account-s3"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal : {
          AWS : "arn:aws:iam::************:root"
        },
        Action : "sts:AssumeRole"
      }
    ]
  })
}

# IAM Policy role for cross account access for tag compliance service lambda
resource "aws_iam_policy" "cross_account_s3_policy" {
  name        = "cross-account-s3-policy"
  description = "Policy for tag compliance output lambda"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "s3:GetObject",
          "s3:GetBucketLocation",
          "s3:ListBucket"
        ],
        Resource = ["arn:aws:s3:::organisation-tag-policy-compliance-report/*", "arn:aws:s3:::organisation-tag-policy-compliance-report"]
      },
    ]
  })
}

# role policy attachment for cross account access for tag compliance service lambda
resource "aws_iam_role_policy_attachment" "cross_account_s3_attachment" {
  policy_arn = aws_iam_policy.cross_account_s3_policy.arn
  role       = aws_iam_role.cross_account_s3.name
}

#Lambda IAM role and policy
resource "aws_iam_role" "tag_compliance_report_role" {
  name = "tag-compliance-report"

  assume_role_policy = data.aws_iam_policy_document.tag_compliance_report_assume.json
}

data "aws_iam_policy_document" "tag_compliance_report_assume" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
  }
}

resource "aws_iam_policy" "tag_compliance_report_policy" {
  name        = "tag-compliance-report"
  description = "Policy needed for the Tag compliance report Lambda function."
  policy      = data.aws_iam_policy_document.tag_compliance_report.json
}

data "aws_iam_policy_document" "tag_compliance_report" {
  statement {
    actions = [
      "logs:CreateLogGroup"
    ]
    resources = [
      "arn:aws:logs:us-east-1:${data.aws_caller_identity.current.account_id}:*"
    ]
  }
  statement {
    actions = [
      "logs:CreateLogStream",
      "logs:PutLogEvents"
    ]
    resources = ["arn:aws:logs:us-east-1:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/tag-non-compliant:*"]
  }
  statement {
    actions = [
      "s3:ListBucket",
      "s3:PutObject",
      "s3:GetObject",
      "s3:GetBucketAcl",
      "s3:GetObjectAcl"
    ]
    resources = [
      module.tag_compliance_report.s3_bucket_arn,
      "${module.tag_compliance_report.s3_bucket_arn}/*"
    ]
  }
  statement {
    actions = [
      "organizations:ListTagsForResource"
    ]
    resources = [
      "*"
    ]
  }
}

resource "aws_iam_role_policy_attachment" "tag_compliance_report" {
  role       = aws_iam_role.tag_compliance_report_role.name
  policy_arn = aws_iam_policy.tag_compliance_report_policy.arn
}

#Convert py code file to zip to be used in the lambda function
data "archive_file" "tag_compliance_report" {
  type        = "zip"
  source_file = "./lambda_functions/tag_compliance_report.py"
  output_path = "./lambda_functions/tag_compliance_report.zip"
}

#Tag compliance report Lambda function
resource "aws_lambda_function" "tag_compliance_report" {
  provider = aws.us-east-1

  filename         = data.archive_file.tag_compliance_report.output_path
  source_code_hash = data.archive_file.tag_compliance_report.output_base64sha256
  function_name    = "tag-compliance-report"
  description      = "Generate report with resources not compliant with the tagging policy."
  role             = aws_iam_role.tag_compliance_report_role.arn
  runtime          = "python3.10"
  handler          = "tag_compliance_report.lambda_handler"
  timeout          = 60
  memory_size      = 256
}

#Tag compliance report Step Functions IAM role and policy
resource "aws_iam_role" "sf_tag_compliance_report_role" {
  name = "sf-tag-compliance-report"

  assume_role_policy = data.aws_iam_policy_document.step_functions_assume_role_policy.json
}

data "aws_iam_policy_document" "step_functions_assume_role_policy" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["states.amazonaws.com"]
    }
  }
}

resource "aws_iam_policy" "sf_tag_compliance_report_policy" {
  name        = "sf-tag-compliance-report"
  description = "Policy needed by the Step Functions State Machine"
  policy      = data.aws_iam_policy_document.sf_tag_compliance_report.json
}

data "aws_iam_policy_document" "sf_tag_compliance_report" {
  statement {
    actions = [
      "lambda:InvokeFunction"
    ]
    resources = [
      aws_lambda_function.tag_compliance_report.arn,
      "${aws_lambda_function.tag_compliance_report.arn}:*"
    ]
  }
  statement {
    actions = [
      "tag:StartReportCreation"
    ]
    resources = [
      "*"
    ]
  }
  statement {
    actions = [
      "logs:CreateLogDelivery",
      "logs:GetLogDelivery",
      "logs:UpdateLogDelivery",
      "logs:DeleteLogDelivery",
      "logs:ListLogDeliveries",
      "logs:PutResourcePolicy",
      "logs:DescribeResourcePolicies",
      "logs:DescribeLogGroups"
    ]
    resources = [
      "*"
    ]
  }
}

resource "aws_iam_role_policy_attachment" "sf_tag_compliance_report" {
  role       = aws_iam_role.sf_tag_compliance_report_role.name
  policy_arn = aws_iam_policy.sf_tag_compliance_report_policy.arn
}

#Tag compliance report step functions
resource "aws_sfn_state_machine" "tag_compliance_report" {
  provider = aws.us-east-1

  name     = "tag-compliance-report"
  role_arn = aws_iam_role.sf_tag_compliance_report_role.arn

  definition = jsonencode(
    {
      "Comment" : "Generate a report with resources not compliant with the tagging policy.",
      "StartAt" : "StartReportCreation",
      "States" : {
        "StartReportCreation" : {
          "Type" : "Task",
          "Parameters" : {
            "S3Bucket" : "organisation-tag-policy-compliance-report"
          },
          "Resource" : "arn:aws:states:::aws-sdk:resourcegroupstaggingapi:startReportCreation",
          "Next" : "Wait"
        },
        "Wait" : {
          "Type" : "Wait",
          "Seconds" : 60,
          "Next" : "Lambda Invoke"
        },
        "Lambda Invoke" : {
          "Type" : "Task",
          "Resource" : "arn:aws:states:::lambda:invoke",
          "OutputPath" : "$.Payload",
          "Parameters" : {
            "FunctionName" : "${aws_lambda_function.tag_compliance_report.arn}"
          },
          "Retry" : [
            {
              "ErrorEquals" : [
                "Lambda.ServiceException",
                "Lambda.AWSLambdaException",
                "Lambda.SdkClientException",
                "Lambda.TooManyRequestsException"
              ],
              "IntervalSeconds" : 2,
              "MaxAttempts" : 6,
              "BackoffRate" : 2
            }
          ],
          "End" : true
        }
      }
    }
  )
}

#EventBridge scheduled rule to trigger the Step Functions
resource "aws_cloudwatch_event_rule" "tag_compliance_report" {
  provider = aws.us-east-1

  name                = "tag-compliance-report"
  description         = "Triggers the tag compliance Step Functions on a schedule."
  schedule_expression = "cron(0 8 ? * 2 *)" # Run at 8:00 am (UTC) mondays
  state               = "DISABLED"
}

resource "aws_cloudwatch_event_target" "tag_compliance_report" {
  provider = aws.us-east-1

  arn  = aws_sfn_state_machine.tag_compliance_report.arn
  rule = aws_cloudwatch_event_rule.tag_compliance_report.name

  role_arn = aws_iam_role.eb_tag_compliance_report_role.arn
}

data "aws_iam_policy_document" "assume_role" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

# role to invoke event bus in Audit account
resource "aws_iam_role" "event_bus_invoke_audit_event_bus_role" {
  name               = "event_bus_invoke_audit_event_bus_role"
  assume_role_policy = data.aws_iam_policy_document.assume_role.json
}

data "aws_iam_policy_document" "event_bus_invoke_audit_event_bus_policy" {
  statement {
    effect    = "Allow"
    actions   = ["events:PutEvents"]
    resources = ["arn:aws:events:eu-west-1:************:event-bus/default"]
  }
}

resource "aws_iam_policy" "event_bus_invoke_audit_event_bus_policy" {
  name   = "event_bus_invoke_audit_event_bus_policy"
  policy = data.aws_iam_policy_document.event_bus_invoke_audit_event_bus_policy.json
}

resource "aws_iam_role_policy_attachment" "event_bus_invoke_audit_event_bus" {
  role       = aws_iam_role.event_bus_invoke_audit_event_bus_role.name
  policy_arn = aws_iam_policy.event_bus_invoke_audit_event_bus_policy.arn
}

# EventBridge rule to trigger tag compliance service step function in Audit account
resource "aws_cloudwatch_event_rule" "tag_compliance_report_output_rule" {
  provider = aws.us-east-1

  name        = "tag_compliance_report_output_rule"
  description = "Triggers the tag compliance Step Functions when a new object is placed in s3 bucket."
  event_pattern = jsonencode({
    source : ["aws.s3"],
    detail-type : ["Object Created"],
    detail : {
      bucket : {
        name : ["organisation-tag-policy-compliance-report"]
      },
      object : {
        key : [
          {
            prefix : "non-compliant-resources-report/"
        }]
      }
    }
  })
}

# Event target - tag compliance service step function in Audit account
resource "aws_cloudwatch_event_target" "tag_compliance_event_target" {
  provider = aws.us-east-1

  rule     = "tag_compliance_report_output_rule"
  arn      = "arn:aws:events:eu-west-1:************:event-bus/default"
  role_arn = aws_iam_role.event_bus_invoke_audit_event_bus_role.arn
}

resource "aws_iam_role" "eb_tag_compliance_report_role" {
  name = "eb-tag-compliance-report"

  assume_role_policy = data.aws_iam_policy_document.eb_tag_compliance_report_assume.json
}

data "aws_iam_policy_document" "eb_tag_compliance_report_assume" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["events.amazonaws.com"]
    }
  }
}

resource "aws_iam_policy" "eb_tag_compliance_report_policy" {
  name        = "eb-tag-compliance-report"
  description = "Policy needed for the Tag compliance report Lambda function."
  policy      = data.aws_iam_policy_document.eb_tag_compliance_report.json
}

data "aws_iam_policy_document" "eb_tag_compliance_report" {
  statement {
    actions = [
      "states:StartExecution"
    ]
    resources = [
      aws_sfn_state_machine.tag_compliance_report.arn
    ]
  }
}

resource "aws_iam_role_policy_attachment" "eb_tag_compliance_report" {
  role       = aws_iam_role.eb_tag_compliance_report_role.name
  policy_arn = aws_iam_policy.eb_tag_compliance_report_policy.arn
}
