import boto3
import datetime
from collections import defaultdict
import csv
import os

cost_explorer_client = boto3.client('ce')
s3_client = boto3.client('s3')

today = datetime.date.today()
first_day_current_month = today.replace(day=1)
last_day_previous_month = first_day_current_month - datetime.timedelta(days=1)
first_day_previous_month = last_day_previous_month.replace(day=1)

def lambda_handler(event, context):
    start_date = first_day_previous_month
    end_date = last_day_previous_month

    usage_types = get_usage_types(start_date, end_date)
    usage_costs = get_cloudwatch_costs_by_owner(usage_types, start_date, end_date)
    generate_csv(usage_costs, today)

def get_usage_types(start_date, end_date):
    response = cost_explorer_client.get_dimension_values(
        TimePeriod={
            'Start': start_date.strftime('%Y-%m-%d'),
            'End': end_date.strftime('%Y-%m-%d')
        },
        Dimension='USAGE_TYPE',
        Filter={
                'Dimensions': {
                    'Key': 'SERVICE',
                    'Values': ['AmazonCloudWatch'],
                    },
                }
    )

    usage_types = []
    suffixes =  ("DataProcessing-Bytes", "DataProcessingIA-Bytes", "IngestedBytes")

    filtered_usage_types = [item['Value'] for item in response['DimensionValues'] if any(item['Value'].endswith(suffix) for suffix in suffixes)]

    for usage_type in filtered_usage_types:
        usage_types.append(usage_type)

    return usage_types

def get_cloudwatch_costs_by_owner(usage_types, start_date, end_date):
    response = cost_explorer_client.get_cost_and_usage(
        TimePeriod={
            'Start': start_date.strftime('%Y-%m-%d'),
            'End': end_date.strftime('%Y-%m-%d')
        },
        Granularity='MONTHLY',
        Metrics=['UnblendedCost'],
        GroupBy=[
            {
                'Type': 'DIMENSION',
                'Key': 'LINKED_ACCOUNT'
            },
            {
                'Type': 'TAG',
                'Key': 'pp:owner'
             },
        ],
        Filter={
            'And': [
                {
                'Dimensions': {
                    'Key': 'SERVICE',
                    'Values': ['AmazonCloudWatch'],
                    },
                },
                {
                'Dimensions': {
                    'Key': 'USAGE_TYPE',
                    'Values': usage_types
                },
                },
            ]
        }
    )

    return response

def generate_csv(usage_costs, today):
    bucket_name='cw-log-ingestion-report'
    s3_key='report/'+str(today)+'_cw_log_ingestion_cost_report.csv'
    file_name='/tmp/'+str(today)+'_cw_log_ingestion_cost_report.csv'

    fieldnames = [
        'owner',
        'accountId',
        'amount (USD)'
        ]

    with open(file_name, mode='w', newline='') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()

        for group in usage_costs['ResultsByTime'][0]['Groups']:
            owner='Untagged' if group['Keys'][1]== 'pp:owner$' else group['Keys'][1]

            accountId=group['Keys'][0]
            cost=group['Metrics']['UnblendedCost']['Amount']

            writer.writerow({
                'owner': owner,
                'accountId': accountId,
                'amount (USD)':cost
                })

    # Upload report to S3
    s3_client.upload_file(file_name, bucket_name, s3_key)
