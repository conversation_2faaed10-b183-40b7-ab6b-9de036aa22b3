# https://docs.aws.amazon.com/cur/latest/userguide/cur-s3.html

data "aws_region" "current" {}

# Bucket
module "data_exports_s3_bucket" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "4.1.2"

  bucket_prefix = "data-exports-"
  force_destroy = true
}

# Policy
data "aws_iam_policy_document" "data_exports_bucket_policy" {
  statement {
    principals {
      type        = "Service"
      identifiers = ["billingreports.amazonaws.com"]
    }

    actions = [
      "s3:GetBucketAcl",
      "s3:GetBucketPolicy"
    ]

    resources = [
      module.data_exports_s3_bucket.s3_bucket_arn
    ]
  }
  statement {
    principals {
      type        = "Service"
      identifiers = ["billingreports.amazonaws.com"]
    }

    actions = [
      "s3:PutObject",
    ]

    resources = [
      "${module.data_exports_s3_bucket.s3_bucket_arn}/*",
    ]
  }
}

resource "aws_s3_bucket_policy" "data_exports_" {
  bucket = module.data_exports_s3_bucket.s3_bucket_id
  policy = data.aws_iam_policy_document.data_exports_bucket_policy.json
}

# Cost and Usage Report
resource "aws_cur_report_definition" "hourly_cur_report" {
  provider                   = aws.us-east-1
  report_name                = "hourly_report_athena"
  time_unit                  = "HOURLY"
  format                     = "Parquet"
  compression                = "Parquet"
  additional_schema_elements = ["RESOURCES"]
  s3_bucket                  = module.data_exports_s3_bucket.s3_bucket_id
  s3_prefix                  = "cost_reports"
  s3_region                  = data.aws_region.current.name
  additional_artifacts       = ["ATHENA"]
  report_versioning          = "OVERWRITE_REPORT"
}

# Cost and Usage Report
resource "aws_cur_report_definition" "hourly_cur_report_csv" {
  provider                   = aws.us-east-1
  report_name                = "hourly_report_csv"
  time_unit                  = "HOURLY"
  format                     = "textORcsv"
  compression                = "GZIP"
  additional_schema_elements = ["RESOURCES"]
  s3_bucket                  = module.data_exports_s3_bucket.s3_bucket_id
  s3_prefix                  = "cost_reports"
  s3_region                  = data.aws_region.current.name
  additional_artifacts       = []
  report_versioning          = "CREATE_NEW_REPORT"
}