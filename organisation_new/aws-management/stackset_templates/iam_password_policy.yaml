AWSTemplateFormatVersion: '2010-09-09'
Description: 'StackSet for AWS Config Rule with SSM auto-remediation for password policy'

Resources:
  # IAM Role for SSM Automation
  AutomationRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ssm.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AmazonSSMAutomationRole
      Policies:
        - PolicyName: PasswordPolicyManagement
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - iam:UpdateAccountPasswordPolicy
                Resource: '*'

  # SSM remediation document
  RemediationDocument:
    Type: AWS::SSM::Document
    Properties:
      DocumentType: Automation
      Content:
        schemaVersion: '0.3'
        description: 'Auto remediate non-compliant password policy'
        assumeRole: '{{AutomationAssumeRole}}'
        parameters:
          AutomationAssumeRole:
            type: String
            description: Role for automation to assume
          ResourceId:
            type: String
            description: Resource ID
            default: NA  
        mainSteps:
          - name: RemediatePasswordPolicy
            action: 'aws:executeAwsApi'
            inputs:
              Service: iam
              Api: UpdateAccountPasswordPolicy
              MinimumPasswordLength: 14
              RequireSymbols: true
              RequireNumbers: true
              RequireUppercaseCharacters: true
              RequireLowercaseCharacters: true
              AllowUsersToChangePassword: true
              MaxPasswordAge: 90
              PasswordReusePrevention: 24
              HardExpiry: false

  # IAM password policy config rule
  PasswordPolicyConfigRule:
    Type: AWS::Config::ConfigRule
    Properties:
      ConfigRuleName: iam-password-policy
      Description: 'Checks if the account password policy meets specified requirements'
      Source:
        Owner: AWS
        SourceIdentifier: IAM_PASSWORD_POLICY
      InputParameters:
        RequireUppercaseCharacters: true
        RequireLowercaseCharacters: true
        RequireSymbols: true
        RequireNumbers: true
        MinimumPasswordLength: 14
        PasswordReusePrevention: 24
        MaxPasswordAge: 90

  # Remediation Configuration
  RemediationConfiguration:
    Type: AWS::Config::RemediationConfiguration
    Properties:
      ConfigRuleName: !Ref PasswordPolicyConfigRule
      TargetType: SSM_DOCUMENT
      TargetId: !Ref RemediationDocument
      Automatic: true
      Parameters:
        AutomationAssumeRole:
          StaticValue:
            Values:
              - !GetAtt AutomationRole.Arn
      ExecutionControls:
        SsmControls:
          ConcurrentExecutionRatePercentage: 10
          ErrorPercentage: 10
      MaximumAutomaticAttempts: 5
      RetryAttemptSeconds: 60

  # IAM Role for AWS Config
  ConfigRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: config.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWS_ConfigRole
      Policies:
        - PolicyName: RemediationPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:StartAutomationExecution
                  - iam:UpdateAccountPasswordPolicy
                Resource: '*'