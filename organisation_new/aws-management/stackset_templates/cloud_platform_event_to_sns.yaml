# vi:ft=cloudformation

AWSTemplateFormatVersion: 2010-09-09
Description: >
  Creates an SNS topic and an Eventbridge rule to forward Cloud Platform notifications to it.
  This is deployed in the Shared Services account which hosts AWS Chatbot.

Parameters:
  SNSTopicName:
    Description: Name of the SNS topic to create.
    Type: String
    Default: CloudPlatformNotifySlack
  OrgId:
    Description: Organization ID
    Type: String

Resources:
  CloudPlatformNotifySlackTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Ref SNSTopicName
      Subscription:
        - Endpoint: https://global.sns-api.chatbot.amazonaws.com
          Protocol: https
      Tags:
        - Key: pp:environment
          Value: production
        - Key: pp:owner
          Value: cloud-platform
        - Key: pp:service
          Value: management
        - Key: pp:terraformWorkspace
          Value: aws-management
        - Key: pp:terraformConfigurationRepository
          Value: Pod-Point/terraform

  CloudPlatformNotifySlackTopicPolicy:
    Type: AWS::SNS::TopicPolicy
    Properties:
      PolicyDocument:
        Id: CloudPlatformNotifySlackTopicPolicy
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: events.amazonaws.com
            Action: sns:Publish
            Resource: "*"
      Topics:
        - !Ref CloudPlatformNotifySlackTopic

  EventBusPolicy:
    Type: AWS::Events::EventBusPolicy
    Properties:
      StatementId: CloudPlatformEventsFromOrg
      Statement:
        Effect: Allow
        Principal:
          AWS: "*"
        Action: events:PutEvents
        Resource: !Sub "arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/default"
        Condition:
          StringEquals:
            aws:PrincipalOrgID: !Ref OrgId

# Event rule to notify when ssm session manager is being used
  SsmSessionStartNotifySlackRule:
    Type: AWS::Events::Rule
    Properties:
      Description: SsmSessionStartNotifySlack
      EventPattern:
        source:
        - aws.ssm
        detail-type:
        - AWS API Call via CloudTrail
        detail:
          eventSource:
          - ssm.amazonaws.com
          eventName:
          - StartSession
      State: "ENABLED"
      Targets:
        - Arn: !Ref CloudPlatformNotifySlackTopic
          Id: !Ref SNSTopicName

  # Event rule to notify on Root login
  RootLoginNotifySlackRule:
    Type: AWS::Events::Rule
    Properties:
      Description: RootLoginNotifySlack
      EventPattern:
        detail-type:
          - "AWS Console Sign In via CloudTrail"
        detail:
          userIdentity:
            type:
            - "Root"
      State: "ENABLED"
      Targets:
        - Arn: !Ref CloudPlatformNotifySlackTopic
          Id: !Ref SNSTopicName

  # Event rule to notify on AWS Health events
  AWSHealthNotifySlackRule:
    Type: AWS::Events::Rule
    Properties:
      Description: AWSHealthNotifySlack
      EventPattern:
        detail-type:
          - "AWS Health Event"
        source:
          - "aws.health"
      State: "ENABLED"
      Targets:
        - Arn: !Ref CloudPlatformNotifySlackTopic
          Id: !Ref SNSTopicName

  # Event rule to notify on Backups status
  BackupsNotifySlackRule:
    Type: AWS::Events::Rule
    Properties:
      Description: BackupsNotifySlack
      EventPattern:
        source:
          - aws.backup
        detail-type:
          - "Backup Job State Change"
          - "Copy Job State Change"
        detail:
          state:
            - "FAILED"
            - "ABORTED"
            - "EXPIRED"
      State: "ENABLED"
      Targets:
        - Arn: !Ref CloudPlatformNotifySlackTopic
          Id: !Ref SNSTopicName

  # Event rule to notify when a route53 record targets a static website hosting s3 bucket.
  R53s3StaticWebsiteNotifySlackRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "R53s3StaticWebsiteNotifySlack"
      EventPattern:
        detail-type:
          - "AWS API Call via CloudTrail"
        detail:
          eventSource:
            - route53.amazonaws.com
          eventName:
            - ChangeResourceRecordSets
          requestParameters:
            changeBatch:
              changes:
                action:
                  - CREATE
                  - UPSERT
                resourceRecordSet:
                  type:
                    - A
                    - AAAA
                    - CNAME
                  resourceRecords:
                    value:
                      - wildcard: "*.s3-website-*.amazonaws.com"
                      - wildcard: "*.s3-website.*.amazonaws.com"
          responseElements:
            changeInfo:
              status:
                - PENDING
      State: "ENABLED"
      Targets:
        - Arn: !Ref CloudPlatformNotifySlackTopic
          Id: !Ref SNSTopicName
