# vi:ft=cloudformation

AWSTemplateFormatVersion: 2010-09-09
Description: Enables CloudWatch in the shared services account to assume permissions to view CloudWatch data in the current account.

Parameters:
  SharedServicesAccountId:
    Description: 'Account ID of the shared services account'
    Type: 'String'
    AllowedPattern: '^[0-9]{12}$'

  Policy:
    Description: The level of access to give to the Monitoring accounts
    Type: String
    Default: View-Access-for-all-services
    AllowedValues:
      - CloudWatch-and-AutomaticDashboards
      - CloudWatch-and-ServiceLens
      - CloudWatch-AutomaticDashboards-and-ServiceLens
      - CloudWatch-core-permissions
      - View-Access-for-all-services

Conditions:
  DoFullReadOnly: !Equals [ !Ref Policy, View-Access-for-all-services ]
  DoAutomaticDashboards: !Equals [ !Ref Policy, CloudWatch-and-AutomaticDashboards ]
  DoServiceLens: !Equals [ !Ref Policy, CloudWatch-and-ServiceLens ]
  DoServiceLensAndAutomaticDashboards: !Equals [ !Ref Policy, CloudWatch-AutomaticDashboards-and-ServiceLens ]

Resources:
  CWCrossAccountSharingRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: CloudWatch-CrossAccountSharingRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub
                - 'arn:aws:iam::${SharedServicesAccountId}:root'
                - SharedServicesAccountId: !Ref 'SharedServicesAccountId'
            Action:
              - sts:AssumeRole
      Path: "/"
      ManagedPolicyArns: !If
        - DoFullReadOnly
        -
          - arn:aws:iam::aws:policy/CloudWatchReadOnlyAccess
          - arn:aws:iam::aws:policy/CloudWatchAutomaticDashboardsAccess
          - arn:aws:iam::aws:policy/job-function/ViewOnlyAccess
          - arn:aws:iam::aws:policy/AWSXrayReadOnlyAccess
        - !If
          - DoAutomaticDashboards
          -
            - arn:aws:iam::aws:policy/CloudWatchReadOnlyAccess
            - arn:aws:iam::aws:policy/CloudWatchAutomaticDashboardsAccess
          - !If
            - DoServiceLens
            -
              - arn:aws:iam::aws:policy/CloudWatchReadOnlyAccess
              - arn:aws:iam::aws:policy/AWSXrayReadOnlyAccess
            - !If
              - DoServiceLensAndAutomaticDashboards
              -
                - arn:aws:iam::aws:policy/CloudWatchReadOnlyAccess
                - arn:aws:iam::aws:policy/CloudWatchAutomaticDashboardsAccess
                - arn:aws:iam::aws:policy/AWSXrayReadOnlyAccess
              -
                - arn:aws:iam::aws:policy/CloudWatchReadOnlyAccess
      Tags:
        - Key: pp:environment
          Value: production
        - Key: pp:owner
          Value: cloud-platform
        - Key: pp:service
          Value: management
        - Key: pp:terraformWorkspace
          Value: aws-management
        - Key: pp:terraformConfigurationRepository
          Value: Pod-Point/terraform
