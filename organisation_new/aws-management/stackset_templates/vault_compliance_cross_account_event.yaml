
AWSTemplateFormatVersion: 2010-09-09
Description: >
  Sets up an Eventbridge rule to forward vault compliance events cross account to
  the shared services event bus, where they will be picked up by AWS Chatbot.
  This is deployed in the spoke accounts.

Parameters:
  SharedServicesAccountId:
    Type: String
    Description: Shared Services account ID (i.e. the account running AWS Chatbot).
    AllowedPattern: '^[0-9]{12}$'
    ConstraintDescription: The Account ID must be a 12 character string.
    MinLength: 12
    MaxLength: 12

Resources:
  VaultComplianceEventBusRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action: sts:AssumeRole
            Principal:
              Service:
                - events.amazonaws.com
      Policies:
        - PolicyName: VaultComplianceEvent
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - events:PutEvents
                Resource: !Sub "arn:aws:events:${AWS::Region}:${SharedServicesAccountId}:event-bus/default"
      Description: Role used by Eventbridge for forwarding vault compliance events to shared services account event bus.
      Tags:
        - Key: pp:environment
          Value: production
        - Key: pp:owner
          Value: cloud-platform
        - Key: pp:service
          Value: management
        - Key: pp:terraformWorkspace
          Value: aws-management
        - Key: pp:terraformConfigurationRepository
          Value: Pod-Point/terraform

  VaultComplianceNotifySlackRule:
    Type: AWS::Events::Rule
    Properties:
      Description: "VaultComplianceNotifySlack"
      EventPattern:
        source:
          - aws.ssm
        detail-type:
          - "Configuration Compliance State Change"
        detail:
          compliance-status:
            - "non_compliant"
      State: "ENABLED"
      RoleArn: !GetAtt VaultComplianceEventBusRole.Arn
      Targets:
        - Arn: !Sub 'arn:aws:events:${AWS::Region}:${SharedServicesAccountId}:event-bus/default'
          Id: SharedServicesEventBus
          RoleArn: !GetAtt VaultComplianceEventBusRole.Arn 