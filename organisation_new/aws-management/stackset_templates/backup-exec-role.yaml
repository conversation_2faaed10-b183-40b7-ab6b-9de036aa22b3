AWSTemplateFormatVersion: 2010-09-09
Description: >
  Creates AWS Backup execution role.

Resources:
  AWSBackupRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action: sts:AssumeRole
            Principal:
              Service:
                - backup.amazonaws.com
      ManagedPolicyArns:
        - 'arn:aws:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForBackup'
        - 'arn:aws:iam::aws:policy/service-role/AWSBackupServiceRolePolicyForRestores'
      Policies: 
        - PolicyName: 'S3BucketBackupPermissions'
          PolicyDocument: 
            Version: '2012-10-17'
            Statement: 
              - Effect: 'Allow'
                Action: 
                  - s3:GetInventoryConfiguration
                  - s3:PutInventoryConfiguration
                  - s3:ListBucketVersions
                  - s3:ListBucket
                  - s3:GetBucketAcl
                  - s3:GetBucketVersioning
                  - s3:GetBucketNotification
                  - s3:PutBucketNotification
                  - s3:GetBucketLocation
                  - s3:GetBucketTagging
                Resource: arn:aws:s3:::*
        - PolicyName: S3ObjectBackupPermissions
          PolicyDocument: 
            Version: '2012-10-17'
            Statement: 
              - Effect: Allow
                Action:
                  - s3:GetObjectAcl
                  - s3:GetObject
                  - s3:GetObjectVersionTagging
                  - s3:GetObjectVersionAcl
                  - s3:GetObjectTagging
                  - s3:GetObjectVersion
                Resource: arn:aws:s3:::*/*
        - PolicyName: S3GlobalPermissions
          PolicyDocument: 
            Version: '2012-10-17'
            Statement: 
              - Effect: Allow
                Action: s3:ListAllMyBuckets
                Resource: '*'
        - PolicyName: EventsPermissions
          PolicyDocument: 
            Version: '2012-10-17'
            Statement: 
              - Effect: Allow
                Action:
                  - events:DescribeRule
                  - events:EnableRule
                  - events:PutRule
                  - events:DeleteRule
                  - events:PutTargets
                  - events:RemoveTargets
                  - events:ListTargetsByRule
                  - events:DisableRule
                Resource: arn:aws:events:*:*:rule/AwsBackupManagedRule*
        - PolicyName: EventsMetricsGlobalPermissions
          PolicyDocument: 
            Version: '2012-10-17'
            Statement: 
              - Effect: Allow
                Action:
                  - cloudwatch:GetMetricData
                  - events:ListRules
                Resource: '*'
        - PolicyName: S3BucketRestorePermissions
          PolicyDocument: 
            Version: '2012-10-17'
            Statement: 
              - Effect: Allow
                Action:
                  - s3:CreateBucket
                  - s3:ListBucketVersions
                  - s3:ListBucket
                  - s3:GetBucketVersioning
                  - s3:GetBucketLocation
                  - s3:PutBucketVersioning
                  - s3:PutBucketOwnershipControls
                  - s3:GetBucketOwnershipControls
                Resource: arn:aws:s3:::*
        - PolicyName: S3ObjectRestorePermissions
          PolicyDocument: 
            Version: '2012-10-17'
            Statement: 
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:GetObjectVersion
                  - s3:DeleteObject
                  - s3:PutObjectVersionAcl
                  - s3:GetObjectVersionAcl
                  - s3:GetObjectTagging
                  - s3:PutObjectTagging
                  - s3:GetObjectAcl
                  - s3:PutObjectAcl
                  - s3:PutObject
                  - s3:ListMultipartUploadParts
                Resource: arn:aws:s3:::*/*
        - PolicyName: KMSBackupPermissions
          PolicyDocument: 
            Version: '2012-10-17'
            Statement: 
              - Effect: Allow
                Action:
                  - kms:Decrypt
                  - kms:DescribeKey
                  - kms:GenerateDataKey
                Resource: '*'
                Condition:
                  StringLike:
                    kms:ViaService: s3.*.amazonaws.com
      RoleName: AWSBackupExecutionRole
      Description: Role used by AWS Backup to execute backups.
      Tags:
        - Key: Managed_by
          Value: cloudformation