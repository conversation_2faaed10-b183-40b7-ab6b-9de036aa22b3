/*
  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  *             Templates
  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 **/

resource "random_id" "bucket_suffix" {
  byte_length = 8
}

module "stack_templates_bucket" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "3.3.0"

  bucket = format("cloudformation-templates-%s", local.bucket_random_id)
  acl    = "private"

  versioning = {
    enabled = true
  }

  server_side_encryption_configuration = {
    rule = {
      bucket_key_enabled = false
      apply_server_side_encryption_by_default = {
        sse_algorithm = "AES256"
      }
    }
  }

  lifecycle_rule = [
    {
      id      = "noncurrent_object_expiration"
      enabled = true

      transitions = {
        first = {
          days          = 30
          storage_class = "STANDARD_IA"
        }
      }

      noncurrent_version_expiration = {
        days = 30
      }
    }
  ]

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_object" "template" {
  for_each = local.stack_template_files

  bucket                 = module.stack_templates_bucket.s3_bucket_id
  key                    = format("template/%s.yaml", each.key)
  source                 = each.value
  etag                   = filemd5(each.value)
  server_side_encryption = "AES256"
}

/*
  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
  *             CF Stacks
  * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 **/

#AWS CloudFormation template to create centralized AWS backup vault
resource "aws_cloudformation_stack" "backup_vault_cross_account" {
  name         = "backup-vault-cross-account"
  capabilities = ["CAPABILITY_NAMED_IAM"]
  template_url = format("https://%s/%s", module.stack_templates_bucket.s3_bucket_bucket_domain_name, aws_s3_object.template["backup_vault_cross_account"].id)
}