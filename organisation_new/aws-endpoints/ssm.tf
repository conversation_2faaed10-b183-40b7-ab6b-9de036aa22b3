resource "aws_iam_role" "ec2_run_command_role" {
  name               = "AmazonEC2RunCommandRoleForManagedInstances"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AssumeRole",
      "Effect": "Allow",
      "Principal": {
        "Service": "ssm.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy" "ssm_policy" {
  name = "AmazonEC2RunCommandRolePolicy"
  role = aws_iam_role.ec2_run_command_role.id

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "ManagedInstances",
      "Effect": "Allow",
      "Action": [
        "ssm:UpdateManagedInstanceRole",
        "ssm:UpdateAssociationStatus",
        "ssm:GetCommandInvocation",
        "ssm:CancelCommand",
        "ssm:ListCommandInvocations",
        "ssm:SendCommand",
        "ec2messages:AcknowledgeMessage",
        "ec2messages:DeleteMessage",
        "ec2messages:FailMessage",
        "ec2messages:GetEndpoint",
        "ec2messages:GetMessages",
        "ec2messages:SendReply",
        "ec2:CreateTags"
      ],
      "Resource": "*"
    }
  ]
}
EOF
}

