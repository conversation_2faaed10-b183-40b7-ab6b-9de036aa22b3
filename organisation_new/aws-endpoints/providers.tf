provider "aws" {
  region = "eu-west-1"

  assume_role {
    role_arn     = format("arn:aws:iam::%s:role/%s", 133585413044, "terraform-ci")
    session_name = "terraform_enterprise_workspace_endpoints"
    external_id  = "ci-tooling"
  }

  default_tags {
    tags = {
      "pp:environment"                      = "production"
      "pp:owner"                            = "cloud-platform"
      "pp:service"                          = "endpoints"
      "pp:terraformWorkspace"               = "podpoint/aws-endpoints"
      "pp:terraformConfigurationRepository" = "Pod-Point/terraform"
    }
  }
}
