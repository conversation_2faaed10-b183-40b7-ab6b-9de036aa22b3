module "patch_manager_setup" {
  source  = "terraform-enterprise.pod-point.com/technology/patch-manager/aws//modules/system-manager-essentials"
  version = "1.1.0"
}

module "patch_manager_scan" {
  source  = "terraform-enterprise.pod-point.com/technology/patch-manager/aws"
  version = "1.0.1"

  identifier                              = local.identifier
  schedule                                = "cron(10 13 ? * * *)"
  description                             = "Daily ${local.identifier} scan event at 1310 UTC"
  patch_baseline_id                       = module.patch_manager_setup.ubuntu_baseline_id
  maintenance_window_service_iam_role_arn = module.patch_manager_setup.ssm_servive_linked_role_id
}

module "patch_manager_install" {
  source  = "terraform-enterprise.pod-point.com/technology/patch-manager/aws"
  version = "1.0.1"

  operation                               = "Install"
  identifier                              = local.identifier
  schedule                                = "cron(15 13 ? * * *)"
  description                             = "Daily ${local.identifier} install event at 1315 UTC"
  patch_baseline_id                       = module.patch_manager_setup.ubuntu_baseline_id
  maintenance_window_service_iam_role_arn = module.patch_manager_setup.ssm_servive_linked_role_id
}
