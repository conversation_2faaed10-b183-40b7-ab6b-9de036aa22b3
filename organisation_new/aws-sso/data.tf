data "aws_caller_identity" "current" {}

data "aws_ssoadmin_instances" "auth_identifier" {}

data "aws_identitystore_user" "cloud_platform" {
  for_each          = local.groups["Groups"]["Cloud Platform"]["Members"]
  identity_store_id = tolist(data.aws_ssoadmin_instances.auth_identifier.identity_store_ids)[0]

  alternate_identifier {
    unique_attribute {
      attribute_path  = "UserName"
      attribute_value = each.key
    }
  }
}

data "aws_identitystore_user" "software_engineer" {
  for_each = {
    for entry in local.software_engineers :
    "${entry.user}" => entry
  }

  identity_store_id = tolist(data.aws_ssoadmin_instances.auth_identifier.identity_store_ids)[0]

  alternate_identifier {
    unique_attribute {
      attribute_path  = "UserName"
      attribute_value = each.value.user
    }
  }
}

data "aws_identitystore_user" "product_owners" {
  for_each = {
    for entry in local.product_owners :
    "${entry.user}" => entry
  }

  identity_store_id = tolist(data.aws_ssoadmin_instances.auth_identifier.identity_store_ids)[0]

  alternate_identifier {
    unique_attribute {
      attribute_path  = "UserName"
      attribute_value = each.value.user
    }
  }
}

data "aws_identitystore_user" "agile_delivery_manager" {
  for_each = {
    for entry in local.agile_delivery_manager :
    "${entry.user}" => entry
  }

  identity_store_id = tolist(data.aws_ssoadmin_instances.auth_identifier.identity_store_ids)[0]

  alternate_identifier {
    unique_attribute {
      attribute_path  = "UserName"
      attribute_value = each.value.user
    }
  }
}
