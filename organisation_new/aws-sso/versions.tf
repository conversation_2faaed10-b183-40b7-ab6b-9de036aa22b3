terraform {
  backend "remote" {
    hostname     = "terraform-enterprise.pod-point.com"
    organization = "technology"

    workspaces {
      name = "aws-sso"
    }
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 5.94.0, < 6.0.0"
    }

    random = {
      source  = "hashicorp/random"
      version = ">= 3.4.3, < 4.0.0"
    }
  }

  required_version = ">= 1.3.0"
}
