Permission Sets:
  PP-CloudPlatform:
    description: "Cloud Platform access."
    session_duration: "PT8H"
    managed_policy_arn:
      - "ReadOnlyAccess"
      - "job-function/Billing"
      - "AWSSupportAccess"
    customer_managed_policy_name: []
    inline_policy_document: null

  PP-AdminBreakglass:
    description: "Break glass Administrator access."
    session_duration: "PT1H"
    managed_policy_arn:
      - "AdministratorAccess"
    customer_managed_policy_name: []
    inline_policy_document: null

  PP-Administrator:
    description: "Administrator access."
    session_duration: "PT1H"
    managed_policy_arn:
      - "AdministratorAccess"
    customer_managed_policy_name: []
    inline_policy_document: null

  PP-FullAccess:
    description: "Full access - power user."
    session_duration: "PT8H"
    managed_policy_arn:
      - "PowerUserAccess"
    customer_managed_policy_name: []
    inline_policy_document: '${pp_iam_limited_read_write}'

  PP-Software:
    description: "Read only access."
    session_duration: "PT8H"
    managed_policy_arn:
      - "ReadOnlyAccess"
      - "AWSSupportAccess"
      - "AmazonTimestreamReadOnlyAccess"
      - "AmazonS3ReadOnlyAccess"
      - "AmazonAthenaFullAccess"
      - "AmazonOpenSearchServiceReadOnlyAccess"
      - "AWSWAFConsoleReadOnlyAccess"
    customer_managed_policy_name: []
    inline_policy_document: '${pp_software}'

  PP-Software-Quicksight:
    description: "Temp role providing read only access + Quicksight"
    session_duration: "PT8H"
    managed_policy_arn:
      - "ReadOnlyAccess"
      - "job-function/Billing"
      - "AWSSupportAccess"
      - "AmazonTimestreamReadOnlyAccess"
      - "AmazonS3ReadOnlyAccess"
      - "AmazonAthenaFullAccess"
    customer_managed_policy_name: []
    inline_policy_document: '${pp_acquisition_development}'

  PP-SoftwareBuild:
    description: "Read only access."
    session_duration: "PT8H"
    managed_policy_arn:
      - "ReadOnlyAccess"
      - "AWSSupportAccess"
    customer_managed_policy_name: []
    inline_policy_document: '${pp_software_build}'

  PP-Security:
    description: "Security audit access."
    session_duration: "PT8H"
    managed_policy_arn:
      - "SecurityAudit"
    customer_managed_policy_name: []
    inline_policy_document: null

  PP-Finance:
    description: "Finance/Billing read only access."
    session_duration: "PT8H"
    managed_policy_arn: []
    customer_managed_policy_name: []
    inline_policy_document: '${finance_read_only}'

  PP-VaultLimited:
    description: "Vault limited access."
    session_duration: "PT8H"
    managed_policy_arn:
      - "job-function/ViewOnlyAccess"
    customer_managed_policy_name: []
    inline_policy_document: '${vault_limited}'

  PP-EmbeddedFirmwareBuild :
    description: "Limited embedded firmware build access."
    session_duration: "PT8H"
    managed_policy_arn:
      - "CloudWatchLogsReadOnlyAccess"
      - "AmazonS3ReadOnlyAccess"
      - "AmazonEC2ContainerRegistryReadOnly"
      - "AmazonAthenaFullAccess"
    customer_managed_policy_name: []
    inline_policy_document: '${embedded_firmware_build}'

  PP-FctEndpoints:
    description: "Limited access to embedded fct endpoints account only."
    session_duration: "PT8H"
    managed_policy_arn:
      - "ReadOnlyAccess"
      - "AmazonS3ReadOnlyAccess"
      - "AWSSupportAccess"
      - "AmazonSSMFullAccess"
    customer_managed_policy_name: []
    inline_policy_document: null

  PP-Hardware:
    description: "Read only access."
    session_duration: "PT8H"
    managed_policy_arn:
      - "CloudWatchReadOnlyAccess"
      - "AmazonAthenaFullAccess"
    customer_managed_policy_name: []
    inline_policy_document: null

  PP-Budgets:
    description: "Billing, Cost RO + Budgets Full access."
    session_duration: "PT8H"
    managed_policy_arn:
      - "AWSSupportAccess"
    customer_managed_policy_name: []
    inline_policy_document: '${pp_budgets}'

  PP-Monitoring:
    description: "Access to monitoring, logging and tools"
    session_duration: "PT8H"
    managed_policy_arn:
      - "CloudWatchReadOnlyAccess"
      - "AmazonAthenaFullAccess"
      - "AmazonRDSPerformanceInsightsReadOnly"
      - "AWSLambda_ReadOnlyAccess"
      - "AWSCodeDeployReadOnlyAccess"
      - "job-function/ViewOnlyAccess"
      - "AWSWAFConsoleReadOnlyAccess"
      - "AmazonKinesisReadOnlyAccess"
      - "AWSSupportAccess"
      # Do not exceed 10 managed policy arns!
    customer_managed_policy_name: []
    inline_policy_document: '${pp_software_prod_monitoring}'

  PP-GDPRAccess:
    description: "Access to execute GDPR actions"
    session_duration: "PT8H"
    managed_policy_arn:
      - IAMReadOnlyAccess
      - IAMUserSSHKeys
      - PowerUserAccess
    customer_managed_policy_name: []
    inline_policy_document: '${gdpr_policy}'

  PP-BillingAndCost:
    description: "Access to the Billing and Cost console"
    session_duration: "PT8H"
    managed_policy_arn:
      - "AWSSupportAccess"
    customer_managed_policy_name: []
    inline_policy_document: '${billing_role_read}'

  PP-Tasman-Access: # Based on PP-Software-Quicksight
    description: "Temp role providing read only access + Quicksight"
    session_duration: "PT8H"
    managed_policy_arn:
      - "ReadOnlyAccess"
      - "AmazonTimestreamReadOnlyAccess"
      - "AmazonS3ReadOnlyAccess"
    customer_managed_policy_name: []
    inline_policy_document: null

  PP-CDN-ACCESS:
    description: "Provides access to the CDN bucket"
    session_duration: "PT8H"
    managed_policy_arn:
      - "ReadOnlyAccess"
      - "AmazonS3ReadOnlyAccess"
      - "CloudFrontReadOnlyAccess"
    customer_managed_policy_name: []
    inline_policy_document: '${cdn_bucket_policy}'

# --------------------------------------------
  PP-Management-Access:
    description: "Management acccess"
    session_duration: "PT8H"
    managed_policy_arn:
      - "job-function/ViewOnlyAccess"
      - "job-function/Billing"
      - "SecurityAudit"
      - "ReadOnlyAccess"
      - "AWSSupportAccess"
      - "AmazonAthenaFullAccess"
      - "AWSLambda_ReadOnlyAccess"
    customer_managed_policy_name: []
    inline_policy_document: '${management_policy}'
