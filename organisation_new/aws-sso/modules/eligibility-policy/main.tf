data "aws_identitystore_user" "user" {
  identity_store_id = var.sso_instance_id

  alternate_identifier {
    unique_attribute {
      attribute_path  = "UserName"
      attribute_value = var.user_name
    }
  }
}

locals {
  eligibility_policies = {
    "id" : data.aws_identitystore_user.user.id
    "accounts" : var.accounts,
    "approvalRequired" : false,
    "createdAt" : time_static.current.id,
    "duration" : "4",
    "modifiedBy" : "terraform-ci",
    "name" : var.user_name,
    "ous" : [], # Only supports accounts directly in the ou
    "permissions" : [
      {
        "id" : var.admin_permission_set_arn,
        "name" : var.admin_permission_set_name
      }
    ],
    "ticketNo" : "",
    "type" : "User",
    "__typename" : "Eligibility"
  }
  policies_updated = {
    "updatedAt" : time_static.policy_updated.id
  }
}

# time_static resources are used to keep the "updated at" and "created at" fields in the eligiblity policy accurate to when Terraform makes changes
resource "time_static" "current" {}

resource "time_static" "policy_updated" {
  triggers = {
    signature = sha256(jsonencode(local.eligibility_policies))
  }
}

# Using json2dynamodb provider as a workaround as Terraform does not natively convert JSON to DynamoDB JSON
data "json2dynamodb" "convert_eligibility_policies" {
  count = var.deploy_eligibility_policies == true ? 1 : 0
  json  = jsonencode(merge(local.eligibility_policies, local.policies_updated))
}

# Pass the table name into Terraform as the eligibility_policy_table_name variable once the application is deployed
data "aws_dynamodb_table" "eligibility_policies" {
  count = var.deploy_eligibility_policies == true ? 1 : 0
  name  = var.eligibility_policy_table_name
}

resource "aws_dynamodb_table_item" "eligibility_policies" {
  count      = var.deploy_eligibility_policies == true ? 1 : 0
  table_name = data.aws_dynamodb_table.eligibility_policies[0].name
  hash_key   = data.aws_dynamodb_table.eligibility_policies[0].hash_key

  item = data.json2dynamodb.convert_eligibility_policies[0].result
}