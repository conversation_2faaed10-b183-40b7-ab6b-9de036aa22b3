/*
 * Please read the documentation for the `aws_route53domains_registered_domain` carefully. This is an advanced resource and has special caveats to be aware of when using it.
 *
 * One main point to be aware of is `terraform destroy` operations will not de-register the domain from your account. It will only remove the resource from your terraform state.
 */


locals {
  registrant_contact = {
    contact_type      = "COMPANY"
    organization_name = "POD Point Ltd"
    first_name        = "Cloud Platform"
    last_name         = "Team"
    email             = "<EMAIL>"
    country_code      = "GB"
    phone_number      = "+44.**********"
    address_line_1    = "222 Grays Inn Road"
    address_line_2    = "6th Floor"
    city              = "London"
    zip_code          = "WC1X 8HB"
  }

  admin_contact = {
    contact_type      = "COMPANY"
    organization_name = "POD Point Ltd"
    first_name        = "Cloud Platform"
    last_name         = "Team"
    email             = "<EMAIL>"
    country_code      = "GB"
    phone_number      = "+44.**********"
    address_line_1    = "222 Grays Inn Road"
    address_line_2    = "6th Floor"
    city              = "London"
    zip_code          = "WC1X 8HB"
  }

  tech_contact = {
    contact_type      = "COMPANY"
    organization_name = "POD Point Ltd"
    first_name        = "Cloud Platform"
    last_name         = "Team"
    email             = "<EMAIL>"
    country_code      = "GB"
    phone_number      = "+44.**********"
    address_line_1    = "222 Grays Inn Road"
    address_line_2    = "6th Floor"
    city              = "London"
    zip_code          = "WC1X 8HB"
  }

  billing_contact = {
    contact_type      = "COMPANY"
    organization_name = "POD Point Ltd"
    first_name        = "Cloud Platform"
    last_name         = "Team"
    email             = "<EMAIL>"
    country_code      = "GB"
    phone_number      = "+44.**********"
    address_line_1    = "222 Grays Inn Road"
    address_line_2    = "6th Floor"
    city              = "London"
    zip_code          = "WC1X 8HB"
  }
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~
 *       pod-point.com
 * ~~~~~~~~~~~~~~~~~~~~~~~~~
 **/

resource "aws_route53domains_registered_domain" "pod_point_com" {
  domain_name        = "pod-point.com"
  auto_renew         = true
  transfer_lock      = true
  registrant_privacy = false
  admin_privacy      = false
  tech_privacy       = false
  billing_privacy    = false

  registrant_contact {
    contact_type      = local.registrant_contact.contact_type
    organization_name = local.registrant_contact.organization_name
    first_name        = local.registrant_contact.first_name
    last_name         = local.registrant_contact.last_name
    email             = local.registrant_contact.email
    country_code      = local.registrant_contact.country_code
    phone_number      = local.registrant_contact.phone_number
    address_line_1    = local.registrant_contact.address_line_1
    address_line_2    = local.registrant_contact.address_line_2
    city              = local.registrant_contact.city
    zip_code          = local.registrant_contact.zip_code
  }

  admin_contact {
    contact_type      = local.admin_contact.contact_type
    organization_name = local.admin_contact.organization_name
    first_name        = local.admin_contact.first_name
    last_name         = local.admin_contact.last_name
    email             = local.admin_contact.email
    country_code      = local.admin_contact.country_code
    phone_number      = local.admin_contact.phone_number
    address_line_1    = local.admin_contact.address_line_1
    address_line_2    = local.admin_contact.address_line_2
    city              = local.admin_contact.city
    zip_code          = local.admin_contact.zip_code
  }

  tech_contact {
    contact_type      = local.tech_contact.contact_type
    organization_name = local.tech_contact.organization_name
    first_name        = local.tech_contact.first_name
    last_name         = local.tech_contact.last_name
    email             = local.tech_contact.email
    country_code      = local.tech_contact.country_code
    phone_number      = local.tech_contact.phone_number
    address_line_1    = local.tech_contact.address_line_1
    address_line_2    = local.tech_contact.address_line_2
    city              = local.tech_contact.city
    zip_code          = local.tech_contact.zip_code
  }

  billing_contact {
    contact_type      = local.billing_contact.contact_type
    organization_name = local.billing_contact.organization_name
    first_name        = local.billing_contact.first_name
    last_name         = local.billing_contact.last_name
    email             = local.billing_contact.email
    country_code      = local.billing_contact.country_code
    phone_number      = local.billing_contact.phone_number
    address_line_1    = local.billing_contact.address_line_1
    address_line_2    = local.billing_contact.address_line_2
    city              = local.billing_contact.city
    zip_code          = local.billing_contact.zip_code
  }

  name_server {
    name = "ns-1523.awsdns-62.org"
  }

  name_server {
    name = "ns-712.awsdns-25.net"
  }

  name_server {
    name = "ns-58.awsdns-07.com"
  }

  name_server {
    name = "ns-1857.awsdns-40.co.uk"
  }
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~
 *       pod-point.net
 *        Legacy API
 * Waiting for this domain to expire as after DVO-1586, we no longer intend to use it. Once expired it can be removed from terraform.
 * ~~~~~~~~~~~~~~~~~~~~~~~~~
 **/

resource "aws_route53domains_registered_domain" "pod_point_net" {
  domain_name        = "pod-point.net"
  auto_renew         = false
  transfer_lock      = true
  registrant_privacy = false
  admin_privacy      = false
  tech_privacy       = false
  billing_privacy    = false

  registrant_contact {
    contact_type      = local.registrant_contact.contact_type
    organization_name = local.registrant_contact.organization_name
    first_name        = local.registrant_contact.first_name
    last_name         = local.registrant_contact.last_name
    email             = local.registrant_contact.email
    country_code      = local.registrant_contact.country_code
    phone_number      = local.registrant_contact.phone_number
    address_line_1    = local.registrant_contact.address_line_1
    address_line_2    = local.registrant_contact.address_line_2
    city              = local.registrant_contact.city
    zip_code          = local.registrant_contact.zip_code
  }

  admin_contact {
    contact_type      = local.admin_contact.contact_type
    organization_name = local.admin_contact.organization_name
    first_name        = local.admin_contact.first_name
    last_name         = local.admin_contact.last_name
    email             = local.admin_contact.email
    country_code      = local.admin_contact.country_code
    phone_number      = local.admin_contact.phone_number
    address_line_1    = local.admin_contact.address_line_1
    address_line_2    = local.admin_contact.address_line_2
    city              = local.admin_contact.city
    zip_code          = local.admin_contact.zip_code
  }

  tech_contact {
    contact_type      = local.tech_contact.contact_type
    organization_name = local.tech_contact.organization_name
    first_name        = local.tech_contact.first_name
    last_name         = local.tech_contact.last_name
    email             = local.tech_contact.email
    country_code      = local.tech_contact.country_code
    phone_number      = local.tech_contact.phone_number
    address_line_1    = local.tech_contact.address_line_1
    address_line_2    = local.tech_contact.address_line_2
    city              = local.tech_contact.city
    zip_code          = local.tech_contact.zip_code
  }

  billing_contact {
    contact_type      = local.billing_contact.contact_type
    organization_name = local.billing_contact.organization_name
    first_name        = local.billing_contact.first_name
    last_name         = local.billing_contact.last_name
    email             = local.billing_contact.email
    country_code      = local.billing_contact.country_code
    phone_number      = local.billing_contact.phone_number
    address_line_1    = local.billing_contact.address_line_1
    address_line_2    = local.billing_contact.address_line_2
    city              = local.billing_contact.city
    zip_code          = local.billing_contact.zip_code
  }
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *                       pod-point-dev.com
 *  We want to remove this ASAP!!! Not ideal and we are not routing
 *  efficiently for what is required!
 *
 *  Initially registered so that our software engineers have a way
 *  to map map records to local development (127.0.0.1)..
 *
 *  Mainly relied on by the Pod-Point/pod-init GitHub repository.
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 **/

resource "aws_route53domains_registered_domain" "pod_point_dev_com" {
  domain_name        = "pod-point-dev.com"
  auto_renew         = true
  transfer_lock      = true
  registrant_privacy = false
  admin_privacy      = false
  tech_privacy       = false
  billing_privacy    = false

  registrant_contact {
    contact_type      = local.registrant_contact.contact_type
    organization_name = local.registrant_contact.organization_name
    first_name        = local.registrant_contact.first_name
    last_name         = local.registrant_contact.last_name
    email             = local.registrant_contact.email
    country_code      = local.registrant_contact.country_code
    phone_number      = local.registrant_contact.phone_number
    address_line_1    = local.registrant_contact.address_line_1
    address_line_2    = local.registrant_contact.address_line_2
    city              = local.registrant_contact.city
    zip_code          = local.registrant_contact.zip_code
  }

  admin_contact {
    contact_type      = local.admin_contact.contact_type
    organization_name = local.admin_contact.organization_name
    first_name        = local.admin_contact.first_name
    last_name         = local.admin_contact.last_name
    email             = local.admin_contact.email
    country_code      = local.admin_contact.country_code
    phone_number      = local.admin_contact.phone_number
    address_line_1    = local.admin_contact.address_line_1
    address_line_2    = local.admin_contact.address_line_2
    city              = local.admin_contact.city
    zip_code          = local.admin_contact.zip_code
  }

  tech_contact {
    contact_type      = local.tech_contact.contact_type
    organization_name = local.tech_contact.organization_name
    first_name        = local.tech_contact.first_name
    last_name         = local.tech_contact.last_name
    email             = local.tech_contact.email
    country_code      = local.tech_contact.country_code
    phone_number      = local.tech_contact.phone_number
    address_line_1    = local.tech_contact.address_line_1
    address_line_2    = local.tech_contact.address_line_2
    city              = local.tech_contact.city
    zip_code          = local.tech_contact.zip_code
  }

  billing_contact {
    contact_type      = local.billing_contact.contact_type
    organization_name = local.billing_contact.organization_name
    first_name        = local.billing_contact.first_name
    last_name         = local.billing_contact.last_name
    email             = local.billing_contact.email
    country_code      = local.billing_contact.country_code
    phone_number      = local.billing_contact.phone_number
    address_line_1    = local.billing_contact.address_line_1
    address_line_2    = local.billing_contact.address_line_2
    city              = local.billing_contact.city
    zip_code          = local.billing_contact.zip_code
  }

  name_server {
    name = "ns-422.awsdns-52.com"
  }

  name_server {
    name = "ns-865.awsdns-44.net"
  }

  name_server {
    name = "ns-1211.awsdns-23.org"
  }

  name_server {
    name = "ns-1662.awsdns-15.co.uk"
  }
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *                       podpoint.co.uk
 *  We should consider whether we want to continue to pay for this.
 *
 * Why?
 *
 * 1. This domain does not follow the pod-point prefix we have for other
 *    registered domains.
 *
 * 2  It doesn't look like we have any hosted zone or records for this
 *    registered domain.
 *
 * 3. We have a registered domain for pod-point.co.uk with EuroDNS. This
 *    has some valid records and seems like a more valid domain name for
 *    a .co.uk top level domain for pod point.
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 **/

resource "aws_route53domains_registered_domain" "podpoint_co_uk" {
  domain_name        = "podpoint.co.uk"
  auto_renew         = true
  transfer_lock      = true
  registrant_privacy = false
  admin_privacy      = false
  tech_privacy       = false
  billing_privacy    = false

  registrant_contact {
    contact_type      = local.registrant_contact.contact_type
    organization_name = local.registrant_contact.organization_name
    first_name        = local.registrant_contact.first_name
    last_name         = local.registrant_contact.last_name
    email             = local.registrant_contact.email
    country_code      = local.registrant_contact.country_code
    phone_number      = local.registrant_contact.phone_number
    address_line_1    = local.registrant_contact.address_line_1
    address_line_2    = local.registrant_contact.address_line_2
    city              = local.registrant_contact.city
    zip_code          = local.registrant_contact.zip_code
    extra_params = {
      UK_CONTACT_TYPE   = "LTD"
      UK_COMPANY_NUMBER = "06851754"
    }
  }

  admin_contact {
    contact_type      = local.admin_contact.contact_type
    organization_name = local.admin_contact.organization_name
    first_name        = local.admin_contact.first_name
    last_name         = local.admin_contact.last_name
    email             = local.admin_contact.email
    country_code      = local.admin_contact.country_code
    phone_number      = local.admin_contact.phone_number
    address_line_1    = local.admin_contact.address_line_1
    address_line_2    = local.admin_contact.address_line_2
    city              = local.admin_contact.city
    zip_code          = local.admin_contact.zip_code
    extra_params = {
      UK_CONTACT_TYPE   = "LTD"
      UK_COMPANY_NUMBER = "06851754"
    }
  }

  tech_contact {
    contact_type      = local.tech_contact.contact_type
    organization_name = local.tech_contact.organization_name
    first_name        = local.tech_contact.first_name
    last_name         = local.tech_contact.last_name
    email             = local.tech_contact.email
    country_code      = local.tech_contact.country_code
    phone_number      = local.tech_contact.phone_number
    address_line_1    = local.tech_contact.address_line_1
    address_line_2    = local.tech_contact.address_line_2
    city              = local.tech_contact.city
    zip_code          = local.tech_contact.zip_code
    extra_params = {
      UK_CONTACT_TYPE   = "LTD"
      UK_COMPANY_NUMBER = "06851754"
    }
  }

  billing_contact {
    contact_type      = local.billing_contact.contact_type
    organization_name = local.billing_contact.organization_name
    first_name        = local.billing_contact.first_name
    last_name         = local.billing_contact.last_name
    email             = local.billing_contact.email
    country_code      = local.billing_contact.country_code
    phone_number      = local.billing_contact.phone_number
    address_line_1    = local.billing_contact.address_line_1
    address_line_2    = local.billing_contact.address_line_2
    city              = local.billing_contact.city
    zip_code          = local.billing_contact.zip_code
    extra_params = {
      UK_CONTACT_TYPE   = "LTD"
      UK_COMPANY_NUMBER = "06851754"
    }
  }

  name_server {
    name = "ns-494.awsdns-61.com"
  }

  name_server {
    name = "ns-908.awsdns-49.net"
  }

  name_server {
    name = "ns-1718.awsdns-22.co.uk"
  }

  name_server {
    name = "ns-1379.awsdns-44.org"
  }
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *                        pod-point.co.za
 * Waiting for this domain to expire as after DVO-1586, we no longer intend to use it. Once expired it can be removed from terraform.
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_route53domains_registered_domain" "pod_point_co_za" {
  domain_name        = "pod-point.co.za"
  auto_renew         = false
  transfer_lock      = false
  registrant_privacy = true
  admin_privacy      = true
  tech_privacy       = true
  billing_privacy    = true

  registrant_contact {
    contact_type      = local.registrant_contact.contact_type
    organization_name = local.registrant_contact.organization_name
    first_name        = local.registrant_contact.first_name
    last_name         = local.registrant_contact.last_name
    email             = local.registrant_contact.email
    country_code      = local.registrant_contact.country_code
    phone_number      = local.registrant_contact.phone_number
    address_line_1    = local.registrant_contact.address_line_1
    address_line_2    = local.registrant_contact.address_line_2
    city              = local.registrant_contact.city
    zip_code          = local.registrant_contact.zip_code
  }

  admin_contact {
    contact_type      = local.admin_contact.contact_type
    organization_name = local.admin_contact.organization_name
    first_name        = local.admin_contact.first_name
    last_name         = local.admin_contact.last_name
    email             = local.admin_contact.email
    country_code      = local.admin_contact.country_code
    phone_number      = local.admin_contact.phone_number
    address_line_1    = local.admin_contact.address_line_1
    address_line_2    = local.admin_contact.address_line_2
    city              = local.admin_contact.city
    zip_code          = local.admin_contact.zip_code
  }

  tech_contact {
    contact_type      = local.tech_contact.contact_type
    organization_name = local.tech_contact.organization_name
    first_name        = local.tech_contact.first_name
    last_name         = local.tech_contact.last_name
    email             = local.tech_contact.email
    country_code      = local.tech_contact.country_code
    phone_number      = local.tech_contact.phone_number
    address_line_1    = local.tech_contact.address_line_1
    address_line_2    = local.tech_contact.address_line_2
    city              = local.tech_contact.city
    zip_code          = local.tech_contact.zip_code
  }

  billing_contact {
    contact_type      = local.billing_contact.contact_type
    organization_name = local.billing_contact.organization_name
    first_name        = local.billing_contact.first_name
    last_name         = local.billing_contact.last_name
    email             = local.billing_contact.email
    country_code      = local.billing_contact.country_code
    phone_number      = local.billing_contact.phone_number
    address_line_1    = local.billing_contact.address_line_1
    address_line_2    = local.billing_contact.address_line_2
    city              = local.billing_contact.city
    zip_code          = local.billing_contact.zip_code
  }

  lifecycle {
    ignore_changes = [
      billing_contact,
      tech_contact,
      admin_contact,
      registrant_contact
    ]
  }
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *                        pod-point.fi
 * Waiting for this domain to expire as after DVO-1586, we no longer intend to use it. Once expired it can be removed from terraform.
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_route53domains_registered_domain" "pod_point_fi" {
  domain_name        = "pod-point.fi"
  auto_renew         = false
  transfer_lock      = false
  registrant_privacy = false
  admin_privacy      = true
  tech_privacy       = true
  billing_privacy    = true

  registrant_contact {
    contact_type      = local.registrant_contact.contact_type
    organization_name = local.registrant_contact.organization_name
    first_name        = local.registrant_contact.first_name
    last_name         = local.registrant_contact.last_name
    email             = local.registrant_contact.email
    country_code      = local.registrant_contact.country_code
    phone_number      = local.registrant_contact.phone_number
    address_line_1    = local.registrant_contact.address_line_1
    address_line_2    = local.registrant_contact.address_line_2
    city              = local.registrant_contact.city
    zip_code          = local.registrant_contact.zip_code
  }

  admin_contact {
    contact_type      = local.admin_contact.contact_type
    organization_name = local.admin_contact.organization_name
    first_name        = local.admin_contact.first_name
    last_name         = local.admin_contact.last_name
    email             = local.admin_contact.email
    country_code      = local.admin_contact.country_code
    phone_number      = local.admin_contact.phone_number
    address_line_1    = local.admin_contact.address_line_1
    address_line_2    = local.admin_contact.address_line_2
    city              = local.admin_contact.city
    zip_code          = local.admin_contact.zip_code
  }

  tech_contact {
    contact_type      = local.tech_contact.contact_type
    organization_name = local.tech_contact.organization_name
    first_name        = local.tech_contact.first_name
    last_name         = local.tech_contact.last_name
    email             = local.tech_contact.email
    country_code      = local.tech_contact.country_code
    phone_number      = local.tech_contact.phone_number
    address_line_1    = local.tech_contact.address_line_1
    address_line_2    = local.tech_contact.address_line_2
    city              = local.tech_contact.city
    zip_code          = local.tech_contact.zip_code
  }

  billing_contact {
    contact_type      = local.billing_contact.contact_type
    organization_name = local.billing_contact.organization_name
    first_name        = local.billing_contact.first_name
    last_name         = local.billing_contact.last_name
    email             = local.billing_contact.email
    country_code      = local.billing_contact.country_code
    phone_number      = local.billing_contact.phone_number
    address_line_1    = local.billing_contact.address_line_1
    address_line_2    = local.billing_contact.address_line_2
    city              = local.billing_contact.city
    zip_code          = local.billing_contact.zip_code
  }

  lifecycle {
    ignore_changes = [
      billing_contact,
      tech_contact,
      admin_contact,
      registrant_contact
    ]
  }
}


/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *                          podpoint.fi
 * Waiting for this domain to expire as after DVO-1586, we no longer intend to use it. Once expired it can be removed from terraform.
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

resource "aws_route53domains_registered_domain" "podpoint_fi" {
  domain_name        = "podpoint.fi"
  auto_renew         = false
  transfer_lock      = false
  registrant_privacy = false
  admin_privacy      = true
  tech_privacy       = true
  billing_privacy    = true

  registrant_contact {
    contact_type      = local.registrant_contact.contact_type
    organization_name = local.registrant_contact.organization_name
    first_name        = local.registrant_contact.first_name
    last_name         = local.registrant_contact.last_name
    email             = local.registrant_contact.email
    country_code      = local.registrant_contact.country_code
    phone_number      = local.registrant_contact.phone_number
    address_line_1    = local.registrant_contact.address_line_1
    address_line_2    = local.registrant_contact.address_line_2
    city              = local.registrant_contact.city
    zip_code          = local.registrant_contact.zip_code
  }

  admin_contact {
    contact_type      = local.admin_contact.contact_type
    organization_name = local.admin_contact.organization_name
    first_name        = local.admin_contact.first_name
    last_name         = local.admin_contact.last_name
    email             = local.admin_contact.email
    country_code      = local.admin_contact.country_code
    phone_number      = local.admin_contact.phone_number
    address_line_1    = local.admin_contact.address_line_1
    address_line_2    = local.admin_contact.address_line_2
    city              = local.admin_contact.city
    zip_code          = local.admin_contact.zip_code
  }

  tech_contact {
    contact_type      = local.tech_contact.contact_type
    organization_name = local.tech_contact.organization_name
    first_name        = local.tech_contact.first_name
    last_name         = local.tech_contact.last_name
    email             = local.tech_contact.email
    country_code      = local.tech_contact.country_code
    phone_number      = local.tech_contact.phone_number
    address_line_1    = local.tech_contact.address_line_1
    address_line_2    = local.tech_contact.address_line_2
    city              = local.tech_contact.city
    zip_code          = local.tech_contact.zip_code
  }

  billing_contact {
    contact_type      = local.billing_contact.contact_type
    organization_name = local.billing_contact.organization_name
    first_name        = local.billing_contact.first_name
    last_name         = local.billing_contact.last_name
    email             = local.billing_contact.email
    country_code      = local.billing_contact.country_code
    phone_number      = local.billing_contact.phone_number
    address_line_1    = local.billing_contact.address_line_1
    address_line_2    = local.billing_contact.address_line_2
    city              = local.billing_contact.city
    zip_code          = local.billing_contact.zip_code
  }

  lifecycle {
    ignore_changes = [
      billing_contact,
      tech_contact,
      admin_contact,
      registrant_contact
    ]
  }
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 *                          infracharge.co.uk
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*/

import {
  to = aws_route53domains_registered_domain.infracharge_co_uk
  id = "infracharge.co.uk"
}

resource "aws_route53domains_registered_domain" "infracharge_co_uk" {
  domain_name        = "infracharge.co.uk"
  auto_renew         = true
  transfer_lock      = true
  registrant_privacy = false
  admin_privacy      = false
  tech_privacy       = false
  billing_privacy    = false

  registrant_contact {
    contact_type      = local.registrant_contact.contact_type
    organization_name = local.registrant_contact.organization_name
    first_name        = local.registrant_contact.first_name
    last_name         = local.registrant_contact.last_name
    email             = local.registrant_contact.email
    country_code      = local.registrant_contact.country_code
    phone_number      = local.registrant_contact.phone_number
    address_line_1    = local.registrant_contact.address_line_1
    address_line_2    = local.registrant_contact.address_line_2
    city              = local.registrant_contact.city
    zip_code          = local.registrant_contact.zip_code
    extra_params = {
      UK_CONTACT_TYPE   = "LTD"
      UK_COMPANY_NUMBER = "06851754"
    }
  }

  admin_contact {
    contact_type      = local.admin_contact.contact_type
    organization_name = local.admin_contact.organization_name
    first_name        = local.admin_contact.first_name
    last_name         = local.admin_contact.last_name
    email             = local.admin_contact.email
    country_code      = local.admin_contact.country_code
    phone_number      = local.admin_contact.phone_number
    address_line_1    = local.admin_contact.address_line_1
    address_line_2    = local.admin_contact.address_line_2
    city              = local.admin_contact.city
    zip_code          = local.admin_contact.zip_code
    extra_params = {
      UK_CONTACT_TYPE   = "LTD"
      UK_COMPANY_NUMBER = "06851754"
    }
  }

  tech_contact {
    contact_type      = local.tech_contact.contact_type
    organization_name = local.tech_contact.organization_name
    first_name        = local.tech_contact.first_name
    last_name         = local.tech_contact.last_name
    email             = local.tech_contact.email
    country_code      = local.tech_contact.country_code
    phone_number      = local.tech_contact.phone_number
    address_line_1    = local.tech_contact.address_line_1
    address_line_2    = local.tech_contact.address_line_2
    city              = local.tech_contact.city
    zip_code          = local.tech_contact.zip_code
    extra_params = {
      UK_CONTACT_TYPE   = "LTD"
      UK_COMPANY_NUMBER = "06851754"
    }
  }

  billing_contact {
    contact_type      = local.billing_contact.contact_type
    organization_name = local.billing_contact.organization_name
    first_name        = local.billing_contact.first_name
    last_name         = local.billing_contact.last_name
    email             = local.billing_contact.email
    country_code      = local.billing_contact.country_code
    phone_number      = local.billing_contact.phone_number
    address_line_1    = local.billing_contact.address_line_1
    address_line_2    = local.billing_contact.address_line_2
    city              = local.billing_contact.city
    zip_code          = local.billing_contact.zip_code
    extra_params = {
      UK_CONTACT_TYPE   = "LTD"
      UK_COMPANY_NUMBER = "06851754"
    }
  }

  dynamic "name_server" {
    for_each = toset(aws_route53_zone.infracharge_co_uk.name_servers)
    content {
      name = name_server.value
    }
  }
}